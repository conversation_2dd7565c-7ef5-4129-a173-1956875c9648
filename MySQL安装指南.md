# MySQL安装指南

## 问题诊断

您的系统上没有安装MySQL服务，这是导致连接失败的根本原因。

## 解决方案

### 方案1：安装MySQL Community Server（推荐）

#### 步骤1：下载MySQL

1. 访问MySQL官方下载页面：https://dev.mysql.com/downloads/mysql/
2. 选择"MySQL Community Server"
3. 选择Windows版本（推荐MySQL 8.0）
4. 下载MSI安装包

#### 步骤2：安装MySQL

1. 运行下载的MSI文件
2. 选择"Typical"或"Custom"安装
3. 在配置阶段设置root密码为：`ZCaini10000nian`
4. 完成安装

#### 步骤3：验证安装

```bash
# 检查MySQL服务
sc query mysql80

# 启动MySQL服务
net start mysql80

# 测试连接
mysql -u root -p
```

### 方案2：使用XAMPP（简单快速）

#### 步骤1：下载XAMPP

1. 访问：https://www.apachefriends.org/download.html
2. 下载Windows版本的XAMPP
3. 运行安装程序

#### 步骤2：启动MySQL

1. 打开XAMPP Control Panel
2. 点击MySQL旁边的"Start"按钮
3. 设置root密码（可选）

#### 步骤3：配置项目

修改项目配置文件中的端口（XAMPP默认使用3306，通常不需要修改）

### 方案3：使用Docker（开发环境推荐）

#### 步骤1：安装Docker Desktop

1. 下载Docker Desktop for Windows
2. 安装并启动Docker

#### 步骤2：运行MySQL容器

```bash
docker run --name mysql-xinjie -e MYSQL_ROOT_PASSWORD=ZCaini10000nian -e MYSQL_DATABASE=xinjie_mall -p 3306:3306 -d mysql:8.0
```

#### 步骤3：导入数据库结构

```bash
# 复制SQL文件到容器
docker cp 数据库建表语句.sql mysql-xinjie:/tmp/

# 执行SQL文件
docker exec -it mysql-xinjie mysql -u root -p xinjie_mall < /tmp/数据库建表语句.sql
```

## 安装后的配置

### 1. 创建数据库

```sql
CREATE DATABASE IF NOT EXISTS xinjie_mall DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2. 导入数据库结构

```bash
mysql -u root -p xinjie_mall < 数据库建表语句.sql
```

### 3. 验证连接

```bash
cd xinjie.mall-admin
node test-db-connection.js
```

## 常见问题

### 问题1：安装后服务无法启动

**解决方案：**

1. 检查端口3306是否被占用
2. 以管理员身份运行命令提示符
3. 重新安装MySQL

### 问题2：忘记root密码

**解决方案：**

1. 停止MySQL服务
2. 以安全模式启动MySQL
3. 重置密码

### 问题3：连接被拒绝

**解决方案：**

1. 检查防火墙设置
2. 确保MySQL服务正在运行
3. 验证连接参数

## 推荐安装方式

对于开发环境，我推荐使用**XAMPP**，因为：

1. 安装简单，一键启动
2. 包含MySQL、Apache、PHP等完整环境
3. 图形化界面，易于管理
4. 适合快速开发和测试

## 安装完成后的下一步

1. 启动MySQL服务
2. 创建xinjie_mall数据库
3. 导入数据库建表语句
4. 运行连接测试脚本
5. 启动项目：`npm run dev`

## 联系支持

如果在安装过程中遇到问题，请提供：

1. 错误信息截图
2. 操作系统版本
3. 安装方式
4. 具体的错误步骤
