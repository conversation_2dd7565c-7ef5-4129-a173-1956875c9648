#!/bin/bash

# ========================================
# 心洁茶叶商城一键自动化部署脚本
# 支持：环境检测、依赖安装、SSL配置、服务启动
# ========================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量（请根据实际情况修改）
DOMAIN_API="api.xinjie-tea.com"
DOMAIN_ADMIN="admin.xinjie-tea.com"
SERVER_IP="**************"  # 请替换为您的服务器IP
DEPLOY_PATH="/var/www/xinjie-tea"
DB_NAME="xinjie_mall"
DB_USER="root"
DB_PASSWORD="ZCaini10000nian!"  # 请设置安全密码（包含大小写、数字和特殊字符）

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo bash auto-deploy.sh"
        exit 1
    fi
}

# 系统环境检测
check_system() {
    log_info "检查系统环境..."
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        log_error "无法识别操作系统"
        exit 1
    fi
    
    source /etc/os-release
    if [[ "$ID" != "ubuntu" ]]; then
        log_warning "建议使用Ubuntu系统，当前系统: $PRETTY_NAME"
    fi
    
    log_success "系统检查完成: $PRETTY_NAME"
}

# 更新系统
update_system() {
    log_info "更新系统包..."
    apt update && apt upgrade -y
    log_success "系统更新完成"
}

# 安装基础依赖
install_dependencies() {
    log_info "安装基础依赖..."
    
    # 安装基础工具
    apt install -y curl wget git vim unzip software-properties-common
    
    # 安装Node.js 18.x
    log_info "安装Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
    apt install -y nodejs
    
    # 验证Node.js安装
    NODE_VERSION=$(node --version)
    NPM_VERSION=$(npm --version)
    log_success "Node.js安装完成: $NODE_VERSION, npm: $NPM_VERSION"
    
    # 安装MySQL
    log_info "安装MySQL..."
    apt install -y mysql-server
    systemctl start mysql
    systemctl enable mysql
    
    # 安装Redis
    log_info "安装Redis..."
    apt install -y redis-server
    systemctl start redis-server
    systemctl enable redis-server
    
    # 安装Nginx
    log_info "安装Nginx..."
    apt install -y nginx
    systemctl start nginx
    systemctl enable nginx
    
    # 安装PM2
    log_info "安装PM2..."
    npm install -g pm2
    
    # 由于使用已购买的SSL证书，跳过certbot安装
    log_info "跳过certbot安装（使用已购买的SSL证书）..."
    
    log_success "所有依赖安装完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    ufw --force reset
    ufw allow ssh
    ufw allow 'Nginx Full'
    ufw allow 4000
    ufw allow 8081
    ufw --force enable
    
    log_success "防火墙配置完成"
}

# 配置MySQL数据库
configure_database() {
    log_info "配置MySQL数据库..."
    
    # 创建数据库和用户
    mysql -u root <<EOF
CREATE DATABASE IF NOT EXISTS $DB_NAME DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASSWORD';
GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';
FLUSH PRIVILEGES;
EOF
    
    log_success "数据库配置完成"
}

# 部署代码
deploy_code() {
    log_info "部署应用代码..."
    
    # 创建部署目录
    mkdir -p $DEPLOY_PATH
    cd $DEPLOY_PATH
    
    # 如果目录不为空，先备份
    if [ "$(ls -A $DEPLOY_PATH)" ]; then
        log_warning "目录不为空，创建备份..."
        cp -r $DEPLOY_PATH /var/backups/xinjie-tea-backup-$(date +%Y%m%d-%H%M%S)
    fi
    
    if [[ -f "$DEPLOY_PATH/mall-server/package.json" && -f "$DEPLOY_PATH/xinjie.mall-admin/package.json" ]]; then
        log_info "检测到项目代码已存在，自动跳过上传确认"
    else
    log_info "请将您的项目代码上传到: $DEPLOY_PATH"
    log_info "您可以使用以下方式之一："
    log_info "1. Git克隆: git clone YOUR_REPO_URL ."
    log_info "2. SCP上传: scp -r /local/path/* root@$SERVER_IP:$DEPLOY_PATH/"
    log_info "3. 手动上传到服务器"
    read -p "代码上传完成后，按回车键继续..." -n1 -s
    echo
    fi
    
    # 检查关键文件是否存在
    if [[ ! -f "$DEPLOY_PATH/mall-server/package.json" ]]; then
        log_error "未找到后端项目文件，请确认代码上传正确"
        exit 1
    fi
    
    if [[ ! -f "$DEPLOY_PATH/xinjie.mall-admin/package.json" ]]; then
        log_error "未找到管理后台项目文件，请确认代码上传正确"
        exit 1
    fi
    
    log_success "代码部署检查完成"
}

# 安装项目依赖
install_project_dependencies() {
    log_info "安装项目依赖..."
    
    # 安装后端依赖（如已安装则跳过）
    log_info "安装后端依赖..."
    cd $DEPLOY_PATH/mall-server
    if [[ -d "node_modules" ]]; then
        log_info "检测到 mall-server/node_modules 已存在，跳过安装"
    else
    npm install --production
    fi
    
    # 安装管理后台依赖（需要开发依赖以进行打包）
    log_info "安装管理后台依赖..."
    cd $DEPLOY_PATH/xinjie.mall-admin
    if [[ -d "node_modules" ]]; then
        log_info "检测到 xinjie.mall-admin/node_modules 已存在，跳过安装"
    else
    if [[ -f "package-lock.json" ]]; then
        npm ci
    else
        npm install
        fi
    fi
    
    # 如已存在 dist，且无需强制重建，则跳过构建（支持覆盖变量 FORCE_REBUILD=1）
    if [[ -d "dist" && "${FORCE_REBUILD:-0}" != "1" ]]; then
        log_info "检测到管理后台 dist 已存在，跳过构建（设置 FORCE_REBUILD=1 可强制重建）"
    else
    # 构建管理后台
    log_info "构建管理后台..."
        export NODE_OPTIONS="--max-old-space-size=2048"
        # 先给生产构建设定超时（10分钟），防止在 Terser 阶段长时间无响应
        timeout 600 npm run build -- --progress --profile || {
            log_warning "生产构建失败或超时，尝试快速构建回退(禁用压缩+SourceMap)"
            FAST_BUILD=1 npm run build -- --no-minimize --devtool false || {
                log_warning "快速构建仍失败，最后尝试开发构建以推进部署"
                npm run build:dev
            }
        }
    # 构建完成后移除开发依赖，减小体积
    npm prune --production || true
    fi
    
    log_success "项目依赖安装完成"
}

# 配置环境变量
configure_environment() {
    log_info "配置环境变量..."
    
    # 后端环境配置
    cat > $DEPLOY_PATH/mall-server/.env <<EOF
NODE_ENV=production
PORT=4000

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=$DB_NAME
DB_USER=$DB_USER
DB_PASSWORD=$DB_PASSWORD

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT配置
JWT_SECRET=$(openssl rand -base64 32)

# 微信小程序配置
WX_APP_ID=wx8792033d9e7052f1
WX_APP_SECRET=5623b74c771d82f6184ec72d319688d4

# 文件上传配置
UPLOAD_PATH=/var/www/xinjie-tea/uploads
UPLOAD_URL=https://$DOMAIN_API/uploads
EOF

    # 管理后台环境配置
    cat > $DEPLOY_PATH/xinjie.mall-admin/.env <<EOF
NODE_ENV=production
PORT=8081
API_URL=https://$DOMAIN_API
DB_HOST=localhost
DB_PORT=3306
DB_NAME=$DB_NAME
DB_USER=$DB_USER
DB_PASSWORD=$DB_PASSWORD
SESSION_SECRET=$(openssl rand -base64 32)
EOF

    # 创建必要目录
    mkdir -p $DEPLOY_PATH/uploads/{products,banners,categories,avatars}
    mkdir -p $DEPLOY_PATH/logs
    chown -R www-data:www-data $DEPLOY_PATH/uploads
    chmod -R 755 $DEPLOY_PATH/uploads
    
    log_success "环境配置完成"
}

# 导入数据库表结构
import_database() {
    log_info "导入数据库表结构..."

    # 若核心表已存在，则自动跳过导入（避免 ERROR 1050: Table already exists）
    EXIST_COUNT=$(mysql -N -B -u $DB_USER -p$DB_PASSWORD -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='$DB_NAME' AND table_name IN ('users','products','orders');")
    if [[ "$EXIST_COUNT" -ge 1 ]]; then
        log_warning "检测到数据库 $DB_NAME 已存在核心表（users/products/orders），自动跳过建表导入"
        return 0
    fi
    
    if [[ -f "$DEPLOY_PATH/数据库建表语句.sql" ]]; then
        mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME < $DEPLOY_PATH/数据库建表语句.sql
        log_success "数据库表结构导入完成"
    else
        log_warning "未找到数据库建表文件，请手动导入"
    fi
}

# 配置Nginx
configure_nginx() {
    log_info "配置Nginx..."
    
    # 创建Nginx配置文件
    cat > /etc/nginx/sites-available/xinjie-tea <<EOF
# 上游服务器配置
upstream xinjie_api {
    server 127.0.0.1:4000;
    keepalive 32;
}

upstream xinjie_admin {
    server 127.0.0.1:8081;
    keepalive 32;
}

# API服务器配置
server {
    listen 80;
    server_name $DOMAIN_API;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN_API;

    # SSL证书配置
    ssl_certificate /etc/ssl/xinjie-tea/cert.pem;
    ssl_certificate_key /etc/ssl/xinjie-tea/key.pem;

    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # API代理
    location /api/ {
        proxy_pass http://xinjie_api;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    # 健康检查
    location /health {
        proxy_pass http://xinjie_api;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
    }

    # 静态文件服务
    location /uploads/ {
        alias $DEPLOY_PATH/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }
}

# 管理后台配置
server {
    listen 80;
    server_name $DOMAIN_ADMIN;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN_ADMIN;

    # SSL证书配置
    ssl_certificate /etc/ssl/xinjie-tea/cert.pem;
    ssl_certificate_key /etc/ssl/xinjie-tea/key.pem;

    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    location / {
        proxy_pass http://xinjie_admin;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF

    # 启用站点
    ln -sf /etc/nginx/sites-available/xinjie-tea /etc/nginx/sites-enabled/
    rm -f /etc/nginx/sites-enabled/default
    
    # 测试配置
    nginx -t
    systemctl reload nginx
    
    log_success "Nginx配置完成"
}

# 配置已购买的SSL证书
configure_ssl() {
    log_info "配置已购买的SSL证书..."

    # 创建SSL证书目录
    mkdir -p /etc/ssl/xinjie-tea

    log_warning "请将您购买的SSL证书文件上传到服务器："
    log_info "证书文件路径："
    log_info "  - 证书文件: /etc/ssl/xinjie-tea/cert.pem"
    log_info "  - 私钥文件: /etc/ssl/xinjie-tea/key.pem"
    log_info "  - 证书链文件: /etc/ssl/xinjie-tea/fullchain.pem (如果有)"
    echo
    log_info "您可以使用以下命令上传证书："
    log_info "scp your-cert.pem root@$SERVER_IP:/etc/ssl/xinjie-tea/cert.pem"
    log_info "scp your-key.pem root@$SERVER_IP:/etc/ssl/xinjie-tea/key.pem"
    echo

    read -p "SSL证书文件上传完成后，按回车键继续..." -n1 -s
    echo

    # 检查证书文件是否存在
    if [[ ! -f "/etc/ssl/xinjie-tea/cert.pem" ]] || [[ ! -f "/etc/ssl/xinjie-tea/key.pem" ]]; then
        log_error "SSL证书文件未找到，请确认文件已正确上传"
        log_info "需要的文件："
        log_info "  - /etc/ssl/xinjie-tea/cert.pem"
        log_info "  - /etc/ssl/xinjie-tea/key.pem"
        exit 1
    fi

    # 设置证书文件权限
    chmod 600 /etc/ssl/xinjie-tea/*.pem
    chown root:root /etc/ssl/xinjie-tea/*.pem

    log_success "SSL证书配置完成"
}

# 启动应用服务
start_services() {
    log_info "启动应用服务..."
    
    cd $DEPLOY_PATH
    
    # 如 PM2 应用已经存在，则执行 reload；否则创建配置文件并启动
    if pm2 describe xinjie-api >/dev/null 2>&1; then
        log_info "检测到 PM2 应用已存在，执行 reload"
        pm2 reload xinjie-api || true
        pm2 reload xinjie-admin || true
        pm2 save
        return 0
    fi
    
    # 创建PM2配置文件
    cat > ecosystem.config.js <<EOF
module.exports = {
  apps: [
    {
      name: 'xinjie-api',
      script: './mall-server/app.js',
      instances: 2,
      exec_mode: 'cluster',
      env_production: {
        NODE_ENV: 'production',
        PORT: 4000
      },
      error_file: './logs/api-error.log',
      out_file: './logs/api-out.log',
      log_file: './logs/api-combined.log',
      time: true
    },
    {
      name: 'xinjie-admin',
      script: './xinjie.mall-admin/app.js',
      instances: 1,
      env_production: {
        NODE_ENV: 'production',
        PORT: 8081
      },
      error_file: './logs/admin-error.log',
      out_file: './logs/admin-out.log',
      log_file: './logs/admin-combined.log',
      time: true
    }
  ]
};
EOF

    # 启动应用
    pm2 start ecosystem.config.js --env production
    pm2 save
    pm2 startup
    
    log_success "应用服务启动完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署结果..."
    
    # 等待服务启动
    sleep 10
    
    # 检查服务状态
    log_info "检查服务状态..."
    systemctl status nginx --no-pager
    systemctl status mysql --no-pager
    systemctl status redis --no-pager
    pm2 status
    
    # 测试API接口
    log_info "测试API接口..."
    if curl -f -s https://$DOMAIN_API/health > /dev/null; then
        log_success "API服务正常"
    else
        log_error "API服务异常"
    fi
    
    # 测试管理后台
    log_info "测试管理后台..."
    if curl -f -s https://$DOMAIN_ADMIN > /dev/null; then
        log_success "管理后台正常"
    else
        log_error "管理后台异常"
    fi
    
    log_success "部署验证完成"
}

# 显示部署结果
show_deployment_info() {
    echo
    log_success "🎉 心洁茶叶商城部署完成！"
    echo
    echo "📋 部署信息："
    echo "  API服务地址: https://$DOMAIN_API"
    echo "  管理后台地址: https://$DOMAIN_ADMIN"
    echo "  部署路径: $DEPLOY_PATH"
    echo "  数据库: $DB_NAME"
    echo
    echo "🔧 常用命令："
    echo "  查看服务状态: pm2 status"
    echo "  查看日志: pm2 logs"
    echo "  重启服务: pm2 restart all"
    echo "  查看Nginx状态: systemctl status nginx"
    echo
    echo "📱 小程序配置："
    echo "  请在微信公众平台配置以下域名："
    echo "  request合法域名: https://$DOMAIN_API"
    echo "  uploadFile合法域名: https://$DOMAIN_API"
    echo "  downloadFile合法域名: https://$DOMAIN_API"
    echo
    echo "⚠️ 重要提醒："
    echo "  1. 请修改小程序代码中的API地址为: https://$DOMAIN_API"
    echo "  2. 请在微信公众平台配置服务器域名"
    echo "  3. 建议定期备份数据库和代码"
    echo "  4. 监控服务器资源使用情况"
    echo
}

# 主函数
main() {
    echo "========================================="
    echo "    心洁茶叶商城自动化部署脚本"
    echo "========================================="
    echo
    
    # 检查配置
    if [[ "$SERVER_IP" == "YOUR_SERVER_IP" ]]; then
        log_error "请先修改脚本中的配置变量"
        log_info "需要修改: SERVER_IP, DB_PASSWORD 等"
        exit 1
    fi
    
    check_root
    check_system
    
    log_info "开始自动化部署流程..."
    
    update_system
    install_dependencies
    configure_firewall
    configure_database
    deploy_code
    install_project_dependencies
    configure_environment
    import_database
    configure_nginx
    configure_ssl
    start_services
    verify_deployment
    show_deployment_info
    
    log_success "🚀 部署完成！"
}

# 执行主函数
main "$@"
