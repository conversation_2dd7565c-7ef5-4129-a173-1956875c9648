#!/bin/bash

# ========================================
# 心洁茶叶商城后台管理系统部署脚本
# ========================================

set -e  # 遇到错误立即退出

echo "🚀 开始部署后台管理系统..."

# 配置变量
PROJECT_NAME="xinjie-mall-admin"
DEPLOY_PATH="/var/www/xinjie-tea/xinjie.mall-admin"
BACKUP_PATH="/var/backups/xinjie-tea-admin"
DOMAIN="admin.xinjie-tea.com"
PORT=8081

# 1. 检查系统环境
echo "📋 检查系统环境..."
node --version || { echo "❌ Node.js未安装"; exit 1; }
npm --version || { echo "❌ npm未安装"; exit 1; }
nginx -v || { echo "❌ Nginx未安装"; exit 1; }

# 2. 创建备份
echo "💾 创建备份..."
if [ -d "$DEPLOY_PATH" ]; then
    sudo mkdir -p $BACKUP_PATH
    sudo cp -r $DEPLOY_PATH $BACKUP_PATH/backup-$(date +%Y%m%d-%H%M%S)
    echo "✅ 备份完成"
fi

# 3. 创建部署目录
echo "📁 创建部署目录..."
sudo mkdir -p $DEPLOY_PATH
sudo chown -R $USER:$USER $DEPLOY_PATH

# 4. 进入项目目录
cd $DEPLOY_PATH

# 5. 安装依赖
echo "📦 安装依赖..."
npm install --production

# 6. 构建前端项目
echo "🔨 构建前端项目..."
npm run build

# 7. 配置环境变量
echo "⚙️ 配置环境变量..."
cp .env.production .env

# 8. 创建必要目录
echo "📁 创建必要目录..."
mkdir -p logs public/uploads dist
chmod 755 logs public/uploads

# 9. 配置Nginx
echo "🌐 配置Nginx..."
sudo cp nginx-admin.conf /etc/nginx/sites-available/xinjie-admin
sudo ln -sf /etc/nginx/sites-available/xinjie-admin /etc/nginx/sites-enabled/

# 10. 测试Nginx配置
echo "🧪 测试Nginx配置..."
sudo nginx -t

if [ $? -ne 0 ]; then
    echo "❌ Nginx配置测试失败"
    exit 1
fi

# 11. 重启Nginx
echo "🔄 重启Nginx..."
sudo systemctl reload nginx

# 12. 安装PM2（如果未安装）
if ! command -v pm2 &> /dev/null; then
    echo "🔧 安装PM2..."
    sudo npm install -g pm2
fi

# 13. 创建PM2配置文件
echo "📝 创建PM2配置..."
cat > ecosystem.admin.config.js << EOF
module.exports = {
  apps: [
    {
      name: 'xinjie-mall-admin',
      script: 'app.js',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        PORT: $PORT
      },
      error_file: './logs/admin-error.log',
      out_file: './logs/admin-out.log',
      log_file: './logs/admin-combined.log',
      time: true,
      max_memory_restart: '512M',
      watch: false,
      ignore_watch: ['node_modules', 'logs', 'public/uploads', 'dist'],
      max_restarts: 10,
      min_uptime: '10s'
    }
  ]
};
EOF

# 14. 启动应用
echo "🚀 启动后台管理系统..."
pm2 start ecosystem.admin.config.js
pm2 save

# 15. 设置开机自启
pm2 startup

# 16. 配置SSL证书（如果不存在）
if [ ! -f "/etc/ssl/xinjie-tea/admin-cert.pem" ]; then
    echo "🔒 配置SSL证书..."
    sudo mkdir -p /etc/ssl/xinjie-tea
    
    # 使用Let's Encrypt申请证书
    if command -v certbot &> /dev/null; then
        sudo certbot --nginx -d $DOMAIN
    else
        echo "⚠️ 请手动配置SSL证书到 /etc/ssl/xinjie-tea/"
        echo "   或安装certbot: sudo apt install certbot python3-certbot-nginx"
    fi
fi

# 17. 验证部署
echo "🧪 验证部署..."
sleep 5

# 检查PM2进程
if pm2 list | grep -q "xinjie-mall-admin"; then
    echo "✅ PM2进程运行正常"
else
    echo "❌ PM2进程启动失败"
    pm2 logs xinjie-mall-admin --lines 20
    exit 1
fi

# 检查端口监听
if netstat -tlnp | grep -q ":$PORT"; then
    echo "✅ 端口 $PORT 监听正常"
else
    echo "❌ 端口 $PORT 未监听"
    exit 1
fi

# 检查HTTP响应
if curl -f -s http://localhost:$PORT/health > /dev/null; then
    echo "✅ HTTP服务响应正常"
else
    echo "⚠️ HTTP服务响应异常，请检查日志"
fi

echo ""
echo "🎉 后台管理系统部署完成！"
echo "📊 访问地址: https://$DOMAIN"
echo "🔧 管理命令:"
echo "   查看状态: pm2 status"
echo "   查看日志: pm2 logs xinjie-mall-admin"
echo "   重启服务: pm2 restart xinjie-mall-admin"
echo "   停止服务: pm2 stop xinjie-mall-admin"
echo ""
echo "📝 下一步操作:"
echo "   1. 访问 https://$DOMAIN 测试系统"
echo "   2. 使用默认管理员账号登录"
echo "   3. 修改默认密码"
echo "   4. 配置系统参数"
echo ""

# 18. 显示系统信息
echo "📊 系统信息:"
echo "   Node.js版本: $(node --version)"
echo "   PM2版本: $(pm2 --version)"
echo "   Nginx版本: $(nginx -v 2>&1)"
echo "   部署路径: $DEPLOY_PATH"
echo "   日志路径: $DEPLOY_PATH/logs"
echo "   上传路径: $DEPLOY_PATH/public/uploads"
