#!/bin/bash

# ========================================
# 心洁茶叶商城完整生产环境部署脚本
# 包含：基础设施、安全配置、监控、支付功能
# ========================================

set -e  # 遇到错误立即退出

echo "🚀 开始完整部署心洁茶叶商城到生产环境..."

# 配置变量
PROJECT_NAME="xinjie-tea"
DEPLOY_PATH="/var/www/xinjie-tea"
BACKUP_PATH="/var/backups/xinjie-tea"
DOMAIN="api.xinjie-tea.com"
DB_NAME="xinjie_mall_prod"
DB_USER="xinjie_prod_user"

# 1. 检查系统环境
echo "📋 检查系统环境..."
node --version || { echo "❌ Node.js未安装"; exit 1; }
npm --version || { echo "❌ npm未安装"; exit 1; }
mysql --version || { echo "❌ MySQL未安装"; exit 1; }
redis-cli --version || { echo "❌ Redis未安装"; exit 1; }
nginx -v || { echo "❌ Nginx未安装"; exit 1; }

# 2. 创建备份
echo "💾 创建备份..."
if [ -d "$DEPLOY_PATH" ]; then
    sudo mkdir -p $BACKUP_PATH
    sudo cp -r $DEPLOY_PATH $BACKUP_PATH/backup-$(date +%Y%m%d-%H%M%S)
    echo "✅ 备份完成"
fi

# 3. 创建部署目录
echo "📁 创建部署目录..."
sudo mkdir -p $DEPLOY_PATH
sudo chown -R $USER:$USER $DEPLOY_PATH

# 4. 上传代码（假设代码已经通过git或其他方式上传）
echo "📦 部署代码..."
cd $DEPLOY_PATH

# 如果使用git部署
# git clone https://github.com/your-username/xinjie-tea.git .
# git checkout main

# 5. 安装依赖
echo "📦 安装后端依赖..."
cd $DEPLOY_PATH/mall-server
npm install --production

# 6. 配置环境变量
echo "⚙️ 配置环境变量..."
cp .env.production .env

# 7. 创建必要目录
echo "📁 创建必要目录..."
mkdir -p logs uploads/products uploads/banners uploads/categories uploads/avatars
chmod 755 uploads logs

# 8. 数据库迁移
echo "🗄️ 执行数据库迁移..."
npm run migrate

# 9. 配置SSL证书
echo "🔒 配置SSL证书..."
sudo mkdir -p /etc/ssl/xinjie-tea
# 这里需要手动配置SSL证书
echo "⚠️ 请手动配置SSL证书到 /etc/ssl/xinjie-tea/"

# 10. 配置Nginx
echo "🌐 配置Nginx..."
sudo tee /etc/nginx/sites-available/xinjie-tea > /dev/null <<EOF
server {
    listen 80;
    server_name $DOMAIN;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN;

    ssl_certificate /etc/ssl/xinjie-tea/cert.pem;
    ssl_certificate_key /etc/ssl/xinjie-tea/key.pem;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    location / {
        proxy_pass http://127.0.0.1:4000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    location /uploads/ {
        alias $DEPLOY_PATH/mall-server/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# 启用站点
sudo ln -sf /etc/nginx/sites-available/xinjie-tea /etc/nginx/sites-enabled/
sudo nginx -t && sudo systemctl reload nginx

# 11. 安装PM2
echo "🔧 安装PM2..."
sudo npm install -g pm2

# 12. 启动应用
echo "🚀 启动应用..."
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup

echo "✅ 部署完成！"
echo "🌐 访问地址: https://$DOMAIN"
echo "📊 监控面板: pm2 monit"
echo "📝 查看日志: pm2 logs"
