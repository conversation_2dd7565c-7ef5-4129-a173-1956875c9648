# 🚀 心洁茶叶商城完整部署指南

## 📋 部署前准备清单

### 1. 服务器要求
- **操作系统**: Ubuntu 20.04 LTS（推荐）
- **配置**: 2核4GB内存，40GB SSD硬盘
- **网络**: 5Mbps带宽，固定公网IP
- **权限**: root用户权限

### 2. 域名要求
- **主域名**: 已购买并实名认证
- **DNS解析**: 指向服务器IP地址
- **子域名规划**:
  - `api.xinjie-tea.com` → API服务
  - `admin.xinjie-tea.com` → 管理后台

### 3. 必要信息准备
```bash
# 请准备以下信息
服务器IP地址: _______________
域名: _______________
数据库密码: _______________（请设置复杂密码）
```

## 🎯 一键自动部署（推荐）

### 步骤1: 连接服务器
```bash
# Windows用户使用PowerShell或下载PuTTY
ssh root@您的服务器IP

# Mac/Linux用户使用终端
ssh root@您的服务器IP
```

### 步骤2: 下载部署脚本
```bash
# 下载自动部署脚本
wget https://raw.githubusercontent.com/your-repo/xinjie-tea/main/auto-deploy.sh

# 或者手动创建脚本文件
nano auto-deploy.sh
# 将auto-deploy.sh的内容复制粘贴进去
```

### 步骤3: 修改配置
```bash
# 编辑脚本配置
nano auto-deploy.sh

# 修改以下变量
DOMAIN_API="api.xinjie-tea.com"        # 改为您的API域名
DOMAIN_ADMIN="admin.xinjie-tea.com"    # 改为您的管理后台域名
SERVER_IP="123.456.789.10"             # 改为您的服务器IP
DB_PASSWORD="your_secure_password"      # 设置安全的数据库密码
```

### 步骤4: 执行部署
```bash
# 给脚本执行权限
chmod +x auto-deploy.sh

# 执行自动部署
sudo bash auto-deploy.sh
```

### 步骤5: 上传代码
当脚本提示时，使用以下方式之一上传代码：

**方式1: Git克隆（推荐）**
```bash
cd /var/www/xinjie-tea
git clone https://github.com/your-username/xinjie-tea.git .
```

**方式2: SCP上传**
```bash
# 在本地电脑执行
scp -r /path/to/xinjie-tea/* root@服务器IP:/var/www/xinjie-tea/
```

**方式3: 使用FTP工具**
- 使用FileZilla等FTP工具
- 上传到 `/var/www/xinjie-tea/` 目录

## 🔧 手动部署步骤

如果自动部署遇到问题，可以按以下步骤手动部署：

### 1. 系统环境准备
```bash
# 更新系统
apt update && apt upgrade -y

# 安装基础工具
apt install -y curl wget git vim unzip

# 安装Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt install -y nodejs

# 安装MySQL
apt install -y mysql-server
systemctl start mysql
systemctl enable mysql

# 安装Redis
apt install -y redis-server
systemctl start redis-server
systemctl enable redis-server

# 安装Nginx
apt install -y nginx
systemctl start nginx
systemctl enable nginx

# 安装PM2
npm install -g pm2

# 安装SSL工具
apt install -y certbot python3-certbot-nginx
```

### 2. 配置数据库
```bash
# 登录MySQL
mysql -u root -p

# 创建数据库和用户
CREATE DATABASE xinjie_mall DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'xinjie_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON xinjie_mall.* TO 'xinjie_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# 导入表结构
mysql -u xinjie_user -p xinjie_mall < 数据库建表语句.sql
```

### 3. 部署代码
```bash
# 创建部署目录
mkdir -p /var/www/xinjie-tea
cd /var/www/xinjie-tea

# 上传或克隆代码
# ... 上传代码到此目录 ...

# 安装后端依赖
cd mall-server
npm install --production

# 安装管理后台依赖
cd ../xinjie.mall-admin
npm install --production
npm run build
```

### 4. 配置环境变量
```bash
# 后端环境配置
cat > /var/www/xinjie-tea/mall-server/.env <<EOF
NODE_ENV=production
PORT=4000
DB_HOST=localhost
DB_PORT=3306
DB_NAME=xinjie_mall
DB_USER=xinjie_user
DB_PASSWORD=your_password
JWT_SECRET=$(openssl rand -base64 32)
WX_APP_ID=wx8792033d9e7052f1
WX_APP_SECRET=5623b74c771d82f6184ec72d319688d4
EOF

# 管理后台环境配置
cat > /var/www/xinjie-tea/xinjie.mall-admin/.env <<EOF
NODE_ENV=production
PORT=8081
API_URL=https://api.xinjie-tea.com
DB_HOST=localhost
DB_PORT=3306
DB_NAME=xinjie_mall
DB_USER=xinjie_user
DB_PASSWORD=your_password
SESSION_SECRET=$(openssl rand -base64 32)
EOF
```

### 5. 配置Nginx
```bash
# 创建Nginx配置
nano /etc/nginx/sites-available/xinjie-tea

# 复制nginx配置内容（见auto-deploy.sh中的配置）

# 启用站点
ln -s /etc/nginx/sites-available/xinjie-tea /etc/nginx/sites-enabled/
rm /etc/nginx/sites-enabled/default
nginx -t
systemctl reload nginx
```

### 6. 申请SSL证书
```bash
# 为API域名申请证书
certbot --nginx -d api.xinjie-tea.com

# 为管理后台申请证书
certbot --nginx -d admin.xinjie-tea.com

# 设置自动续期
crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 7. 启动服务
```bash
cd /var/www/xinjie-tea

# 创建PM2配置文件
nano ecosystem.config.js
# 复制PM2配置内容

# 启动应用
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

## 🧪 部署验证

### 1. 检查服务状态
```bash
# 检查系统服务
systemctl status nginx
systemctl status mysql
systemctl status redis

# 检查应用服务
pm2 status
pm2 logs
```

### 2. 测试API接口
```bash
# 健康检查
curl https://api.xinjie-tea.com/health

# 测试分类接口
curl https://api.xinjie-tea.com/api/front/category/list

# 测试轮播图接口
curl https://api.xinjie-tea.com/api/front/banner/list
```

### 3. 测试管理后台
```bash
# 访问管理后台
curl -I https://admin.xinjie-tea.com
```

## 📱 小程序配置

### 1. 修改API地址
编辑 `xinjie-mall-miniprogram/config/api.js`:
```javascript
const config = {
  baseURL: 'https://api.xinjie-tea.com/api',
  imageBaseURL: 'https://api.xinjie-tea.com/uploads',
  timeout: 10000
};
```

### 2. 微信公众平台配置
登录微信公众平台，在"开发管理"中配置：

**服务器域名配置:**
- request合法域名: `https://api.xinjie-tea.com`
- uploadFile合法域名: `https://api.xinjie-tea.com`
- downloadFile合法域名: `https://api.xinjie-tea.com`

## 🔍 故障排查

### 常见问题及解决方案

**1. 域名无法访问**
```bash
# 检查DNS解析
nslookup api.xinjie-tea.com
ping api.xinjie-tea.com

# 检查防火墙
ufw status
```

**2. SSL证书申请失败**
```bash
# 检查域名解析是否正确
# 确保80端口可访问
# 重新申请证书
certbot delete --cert-name api.xinjie-tea.com
certbot --nginx -d api.xinjie-tea.com
```

**3. 应用无法启动**
```bash
# 查看详细日志
pm2 logs
pm2 describe xinjie-api

# 检查端口占用
netstat -tlnp | grep :4000
```

**4. 数据库连接失败**
```bash
# 检查MySQL服务
systemctl status mysql

# 测试数据库连接
mysql -u xinjie_user -p xinjie_mall
```

## 📊 监控和维护

### 1. 服务监控
```bash
# 查看服务状态
pm2 monit

# 查看系统资源
htop
df -h
free -h
```

### 2. 日志管理
```bash
# 查看应用日志
pm2 logs

# 查看Nginx日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

### 3. 备份策略
```bash
# 数据库备份
mysqldump -u xinjie_user -p xinjie_mall > backup_$(date +%Y%m%d).sql

# 代码备份
tar -czf code_backup_$(date +%Y%m%d).tar.gz /var/www/xinjie-tea
```

## 🎉 部署完成

恭喜！您的心洁茶叶商城已成功部署上线！

**访问地址:**
- API服务: https://api.xinjie-tea.com
- 管理后台: https://admin.xinjie-tea.com

**下一步:**
1. 配置微信小程序域名
2. 测试所有功能
3. 设置监控告警
4. 准备小程序审核材料

如有问题，请查看故障排查部分或联系技术支持。
