# ========================================
# 心洁茶叶商城 - 生产环境配置
# 请根据实际情况修改以下配置
# ========================================

# 基础配置
NODE_ENV=production
PORT=4000
HTTPS_PORT=443
API_PREFIX=/api

# 数据库配置（请修改为您的实际配置）
DB_HOST=localhost
DB_PORT=3306
DB_NAME=xinjie_mall
DB_USER=root
DB_PASSWORD= 

# 读写分离配置
DB_READ_HOST=your_read_db_host
DB_READ_PORT=3306
DB_READ_NAME=xinjie_mall_prod
DB_READ_USER=your_read_db_user
DB_READ_PASSWORD=your_read_db_password
DB_WRITE_HOST=your_write_db_host
DB_WRITE_PORT=3306
DB_WRITE_NAME=xinjie_mall_prod
DB_WRITE_USER=your_write_db_user
DB_WRITE_PASSWORD=your_write_db_password

# Redis配置
REDIS_HOST=your_redis_host
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# JWT配置
JWT_SECRET=your_production_jwt_secret_key_very_long_and_secure
JWT_EXPIRES_IN=7d

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=5242880

# 邮件配置
MAIL_HOST=smtp.qq.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your_production_email_password

# 短信配置
SMS_ACCESS_KEY=your_production_sms_access_key
SMS_SECRET_KEY=your_production_sms_secret_key
SMS_SIGN_NAME=心洁茶叶
SMS_TEMPLATE_CODE=your_production_template_code

# 微信支付配置
WECHAT_APP_ID=your_production_wechat_app_id
WECHAT_MCH_ID=your_production_wechat_mch_id
WECHAT_API_KEY=your_production_wechat_api_key
WECHAT_NOTIFY_URL=https://your-production-domain.com/api/payment/wechat/notify

# 支付宝配置
ALIPAY_APP_ID=your_production_alipay_app_id
ALIPAY_PRIVATE_KEY=your_production_alipay_private_key
ALIPAY_PUBLIC_KEY=your_production_alipay_public_key
ALIPAY_NOTIFY_URL=https://your-production-domain.com/api/payment/alipay/notify
ALIPAY_RETURN_URL=https://your-production-domain.com/payment/return

# 微信小程序配置
WX_APP_ID=wx8792033d9e7052f1
WX_APP_SECRET=5623b74c771d82f6184ec72d319688d4

# 日志配置
LOG_LEVEL=warn
LOG_PATH=./logs

# 缓存配置
BANNER_CACHE_EXPIRE=3600

# CDN配置
CDN_ENABLED=true
CDN_DOMAIN=https://your-cdn-domain.com
CDN_IMAGE_PATH=/images
CDN_FALLBACK_URL=https://your-production-domain.com
CDN_CACHE_CONTROL=public, max-age=31536000