# 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 上传文件
uploads/
!uploads/.gitkeep

# 临时文件
tmp/
temp/

# 操作系统
.DS_Store
Thumbs.db

# IDE
.vscode/
.idea/
*.swp
*.swo

# 测试覆盖率
coverage/

# 构建文件
dist/
build/

# PM2
.pm2/

# 数据库
*.sqlite
*.db

# 其他
.cache/
.nyc_output/ 