# 心洁茶叶商城后端服务

## 项目简介
这是一个基于Node.js + Express + MySQL的茶叶商城后端API服务，为微信小程序和后台管理系统提供数据支持。

## 技术栈
- **后端框架**: Node.js + Express
- **数据库**: MySQL + Sequelize ORM
- **缓存**: Redis
- **认证**: JWT
- **文件上传**: Multer
- **HTTPS**: 自签名SSL证书

## 主要功能模块
- 用户认证与授权
- 商品管理
- 分类管理
- 购物车功能
- 订单管理
- 轮播图管理
- 地址管理
- 缓存管理

## 安装与运行

### 环境要求
- Node.js >= 14
- MySQL >= 5.7
- Redis >= 4.0

### 安装依赖
```bash
npm install
```

### 配置数据库
1. 复制 `src/config/database.example.js` 为 `src/config/database.js`
2. 修改数据库连接配置

### 初始化数据库
```bash
npm run migrate
npm run seed
```

### 启动服务
```bash
# 开发环境
npm run dev

# 生产环境
npm start
```

## API文档

### 基础URL
- 开发环境: `http://localhost:4000`
- 生产环境: `https://localhost:4443`

### 认证
大部分API需要在请求头中包含JWT token:
```
Authorization: Bearer <token>
```

### 主要接口

#### 用户相关
- `POST /api/front/auth/login` - 用户登录
- `POST /api/front/auth/register` - 用户注册
- `GET /api/front/auth/profile` - 获取用户信息

#### 商品相关
- `GET /api/front/product/list` - 获取商品列表
- `GET /api/front/product/detail/:id` - 获取商品详情
- `GET /api/front/product/hot` - 获取热门商品
- `GET /api/front/product/recommend` - 获取推荐商品

#### 分类相关
- `GET /api/front/category/list` - 获取分类列表
- `GET /api/front/category/detail/:id` - 获取分类详情

#### 购物车相关
- `GET /api/front/cart/list` - 获取购物车列表
- `POST /api/front/cart/add` - 添加商品到购物车
- `PUT /api/front/cart/update` - 更新购物车商品数量
- `DELETE /api/front/cart/delete/:id` - 删除购物车商品

#### 订单相关
- `GET /api/front/order/list` - 获取订单列表
- `POST /api/front/order/create` - 创建订单
- `GET /api/front/order/detail/:id` - 获取订单详情

#### 轮播图相关
- `GET /api/front/banner/list` - 获取轮播图列表

#### 地址相关
- `GET /api/front/address/list` - 获取地址列表
- `POST /api/front/address/add` - 添加地址
- `PUT /api/front/address/update` - 更新地址
- `DELETE /api/front/address/delete/:id` - 删除地址

## 管理后台API

### 管理员认证
- `POST /api/admin/auth/login` - 管理员登录
- `GET /api/admin/auth/profile` - 获取管理员信息

### 商品管理
- `GET /api/admin/product/list` - 获取商品列表
- `POST /api/admin/product/add` - 添加商品
- `PUT /api/admin/product/update/:id` - 更新商品
- `DELETE /api/admin/product/delete/:id` - 删除商品

### 分类管理
- `GET /api/admin/category/list` - 获取分类列表
- `POST /api/admin/category/add` - 添加分类
- `PUT /api/admin/category/update/:id` - 更新分类
- `DELETE /api/admin/category/delete/:id` - 删除分类

### 轮播图管理
- `GET /api/admin/banner/list` - 获取轮播图列表
- `POST /api/admin/banner/add` - 添加轮播图
- `PUT /api/admin/banner/update/:id` - 更新轮播图
- `DELETE /api/admin/banner/delete/:id` - 删除轮播图

## 缓存策略
- 使用Redis缓存热点数据
- 商品列表、分类列表、轮播图等数据缓存5分钟
- 支持手动清除缓存

## 文件上传
- 支持图片文件上传
- 文件存储在 `uploads/` 目录
- 自动生成唯一文件名
- 支持文件类型验证

## 错误处理
- 统一的错误响应格式
- 详细的错误日志记录
- 友好的错误提示信息

## 性能优化
- 数据库查询优化
- Redis缓存策略
- 图片懒加载支持
- 分页查询

## 安全措施
- JWT身份验证
- 密码加密存储
- SQL注入防护
- XSS防护
- 文件上传安全验证

## 部署说明
1. 配置生产环境变量
2. 安装PM2: `npm install -g pm2`
3. 启动服务: `pm2 start app.js`
4. 配置Nginx反向代理
5. 配置SSL证书

## 开发指南
- 遵循ESLint代码规范
- 编写单元测试
- 使用Git Flow工作流
- 定期更新依赖包

## 常见问题
1. 数据库连接失败 - 检查数据库配置和网络连接
2. Redis连接失败 - 检查Redis服务状态
3. 文件上传失败 - 检查目录权限和磁盘空间
4. JWT验证失败 - 检查token是否过期或无效

## 更新日志

### 2025-01-18
- 修复微信小程序分类页面商品描述显示问题
- 修复选规格按钮事件处理错误（TypeError: e.stopPropagation is not a function）
- 优化规格选择弹窗UI设计，按照用户提供的设计图实现
- 为商品添加description字段数据
- 实现规格选择弹窗的完整功能：
  - 商品信息展示（名称、编码、价格、购买单位）
  - 数量选择（加减按钮和输入框）
  - 库存显示和限制
  - 总价计算
  - 加入购物车功能
- 修复事件处理中的stopPropagation调用问题
- 清除API缓存确保数据更新

### 技术栈
- 后端：Node.js + Express + MySQL + Redis
- 前端：微信小程序 + React管理后台
- 数据库：MySQL 8.0
- 缓存：Redis
- 文件存储：本地文件系统

### 修改的文件
- `xinjie-mall-miniprogram/pages/category/category.js` - 修复事件处理和添加规格选择功能
- `xinjie-mall-miniprogram/pages/category/category.wxml` - 更新规格选择弹窗UI
- `xinjie-mall-miniprogram/pages/category/category.wxss` - 优化弹窗样式设计
- 数据库商品表 - 添加商品描述数据

### 用户体验改进
- 商品卡片现在正确显示描述信息
- 选规格按钮点击后弹出美观的规格选择界面
- 支持数量选择和库存检查
- 实时计算总价
- 一键加入购物车功能
- 完整的错误处理和用户提示 "" 
"" 
