# 🛡️ 安全版本使用说明

## 启动方式

### 方式1：使用npm脚本（推荐）
```bash
# 启动安全版本
npm run start:secure

# 开发模式（自动重启）
npm run dev:secure

# 启动原版本（如需回退）
npm run start:original
```

### 方式2：直接启动
```bash
# 启动安全版本
node src/app-secure-fixed.js

# 启动原版本
node src/app-original-backup.js
```

## 安全功能

✅ SQL注入防护 (智能检测)
✅ XSS防护 (脚本过滤)  
✅ 接口限流 (内存存储)
✅ 输入验证 (格式检查)
✅ 请求大小限制 (10MB)
✅ 安全头设置
✅ 错误处理

## 限流配置

• 全局限流: 100次/分钟
• 登录限流: 5次/5分钟  
• 支付限流: 10次/分钟

## 环境配置

复制 .env.secure 为 .env 并填入配置：
```bash
cp .env.secure .env
```

## 注意事项

- 开发环境已跳过部分安全检查
- 生产环境请设置 NODE_ENV=production
- 如有问题可随时切回原版本
