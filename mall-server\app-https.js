const Koa = require('koa');
const helmet = require('koa-helmet');
const compress = require('koa-compress');
const logger = require('koa-logger');
const static = require('koa-static');
const bodyParser = require('koa-bodyparser');
const cors = require('koa-cors');
const path = require('path');
const https = require('https');
const http = require('http');
const fs = require('fs');
require('dotenv').config();
const mount = require('koa-mount');

const config = require('./src/config');
const routes = require('./src/routes');

const app = new Koa();

// 请求体解析中间件（必须放在最前面）
app.use(bodyParser({
  enableTypes: ['json', 'form', 'text'],
  jsonLimit: '10mb',
  formLimit: '10mb',
  textLimit: '10mb',
  strict: false,
  onerror: function (err, ctx) {
    console.error('Body parser error:', err);
    ctx.throw(422, 'body parse error');
  }
}));

// CORS中间件
app.use(cors({
  origin: '*',
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization', 'Accept'],
  credentials: true
}));

// 安全中间件
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", 'data:', 'blob:', 'https:', 'http:'],
    },
  },
}));

// 压缩中间件
app.use(compress());

// 日志中间件
if (config.env !== 'test') {
  app.use(logger());
}

// 认证中间件
const authMiddleware = require('./src/middleware/auth');
app.use(authMiddleware);

// 静态文件服务
const uploadsPath = path.join(__dirname, 'uploads');
console.log('📁 mall-server HTTPS 静态资源目录:', uploadsPath);
app.use(mount('/uploads', static(uploadsPath)));
app.use(mount('/images', static(path.join(__dirname, '../xinjie-mall-miniprogram/images'))));

// 应用路由
app.use(routes.routes());
app.use(routes.allowedMethods());

// 错误处理
app.on('error', (err, ctx) => {
  console.error('Server Error:', err);
  ctx.status = err.status || 500;
  ctx.body = {
    code: 500,
    message: '服务器内部错误',
    error: config.env === 'development' ? err.message : undefined
  };
});

// SSL证书配置
const certDir = path.join(__dirname, 'ssl');
const certPath = path.join(certDir, 'localhost.pem');
const keyPath = path.join(certDir, 'localhost-key.pem');

let httpsOptions = {};
if (fs.existsSync(certPath) && fs.existsSync(keyPath)) {
  httpsOptions = {
  cert: fs.readFileSync(certPath),
  key: fs.readFileSync(keyPath)
};
} else {
  console.warn('⚠️ SSL证书不存在，HTTPS服务将无法启动');
}

// 启动服务器
const PORT = 4000;
const HTTPS_PORT = 4443; // HTTPS端口

// 启动HTTP服务器
const httpServer = http.createServer(app.callback());
httpServer.listen(PORT, () => {
  console.log(`🚀 心洁茶叶商城HTTP后端服务启动成功`);
  console.log(`📍 HTTP服务地址: http://localhost:${PORT}`);
  console.log(`🌍 环境: ${config.env}`);
  console.log(`⏰ 时间: ${new Date().toLocaleString()}`);
  console.log(`📝 API文档: http://localhost:${PORT}/api/docs`);
  console.log(`📸 图片访问: http://localhost:${PORT}/uploads/categories/`);
  console.log(`🔧 微信配置: AppID=${config.wxAppId}, AppSecret长度=${config.wxAppSecret ? config.wxAppSecret.length : 0}`);
  console.log(`💡 开发环境: 微信小程序使用此HTTP服务`);
});

// 启动HTTPS服务器（如果证书存在）
if (Object.keys(httpsOptions).length > 0) {
  const httpsServer = https.createServer(httpsOptions, app.callback());
  httpsServer.listen(HTTPS_PORT, () => {
    console.log(`🔒 HTTPS服务地址: https://localhost:${HTTPS_PORT}`);
    console.log(`📝 HTTPS API文档: https://localhost:${HTTPS_PORT}/api/docs`);
    console.log(`📸 HTTPS图片访问: https://localhost:${HTTPS_PORT}/uploads/categories/`);
    console.log(`🌐 生产环境: 微信小程序使用此HTTPS服务`);
  });
} else {
  console.log(`⚠️ HTTPS服务未启动（缺少SSL证书）`);
  console.log(`💡 提示: 生产环境需要配置SSL证书以启用HTTPS服务`);
}

module.exports = app; 