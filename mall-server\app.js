const Koa = require('koa');
const helmet = require('koa-helmet');
const compress = require('koa-compress');
const logger = require('koa-logger');
const static = require('koa-static');
const path = require('path');
require('dotenv').config();
const mount = require('koa-mount');

const config = require('./src/config');
const routes = require('./src/routes');

const app = new Koa();

// 安全中间件
app.use(helmet({
  contentSecurityPolicy: false
}));

// 压缩中间件
app.use(compress());

// 日志中间件
if (config.env !== 'test') {
  app.use(logger());
}

// 静态文件服务
const uploadsPath = path.join(__dirname, 'uploads');
console.log('📁 mall-server 静态资源目录:', uploadsPath);
app.use(mount('/uploads', static(uploadsPath)));
app.use(mount('/images', static(path.join(__dirname, '../xinjie-mall-miniprogram/images'))));

// 裸 /health 健康检查（与 Nginx 的 /health 对齐）
app.use(async (ctx, next) => {
  if (ctx.path === '/health') {
    ctx.status = 200;
    ctx.body = {
      code: 200,
      message: 'ok',
      timestamp: new Date().toISOString()
    };
    return;
  }
  await next();
});

// 应用路由
app.use(routes.routes());
app.use(routes.allowedMethods());

// 错误处理
app.on('error', (err, ctx) => {
  console.error('Server Error:', err);
  ctx.status = err.status || 500;
  ctx.body = {
    code: 500,
    message: '服务器内部错误',
    error: config.env === 'development' ? err.message : undefined
  };
});

// 启动服务器
const PORT = config.port || 4000;
app.listen(PORT, () => {
  console.log(`🚀 心洁茶叶商城后端服务启动成功`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`🌍 环境: ${config.env}`);
  console.log(`⏰ 时间: ${new Date().toLocaleString()}`);
  console.log(`📝 API文档: http://localhost:${PORT}/api/docs`);
});

module.exports = app; 