module.exports = {
  "database": {
    "host": "localhost",
    "port": 3306,
    "database": "xinjie_mall",
    "username": "xinjie_user",
    "password": "",
    "dialect": "mysql",
    "timezone": "+08:00",
    "logging": false,
    "pool": {
      "max": 20,
      "min": 5,
      "acquire": 30000,
      "idle": 10000
    }
  },
  "redis": {
    "host": "localhost",
    "port": 6379,
    "password": null,
    "db": 0
  },
  "jwt": {
    "secret": "xinjie_mall_production_jwt_secret_2024",
    "expiresIn": "7d",
    "algorithm": "HS256"
  },
  "wxAppId": "",
  "wxAppSecret": "",
  "payment": {
    "wechat": {
      "appId": "",
      "mchId": "",
      "apiKey": "",
      "notifyUrl": ""
    }
  },
  "upload": {
    "path": "./uploads",
    "maxFileSize": 5242880,
    "allowedTypes": [
      "image/jpeg",
      "image/png",
      "image/gif",
      "image/webp"
    ]
  },
  "log": {
    "level": "error",
    "path": "./logs"
  },
  "security": {
    "rateLimit": {
      "windowMs": 900000,
      "max": 100
    }
  }
};