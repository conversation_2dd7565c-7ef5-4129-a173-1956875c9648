'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('users', {
      id: {
        type: Sequelize.BIGINT,
        primaryKey: true,
        autoIncrement: true,
        comment: '用户ID'
      },
      username: {
        type: Sequelize.STRING(50),
        allowNull: false,
        unique: true,
        comment: '用户名'
      },
      password: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: '密码'
      },
      nickname: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: '昵称'
      },
      avatar: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: '头像URL'
      },
      gender: {
        type: Sequelize.TINYINT,
        defaultValue: 0,
        comment: '性别(0未知 1男 2女)'
      },
      phone: {
        type: Sequelize.STRING(20),
        allowNull: true,
        comment: '手机号'
      },
      email: {
        type: Sequelize.STRING(100),
        allowNull: true,
        comment: '邮箱'
      },
      status: {
        type: Sequelize.TINYINT,
        defaultValue: 1,
        comment: '状态(0禁用 1正常)'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });
    await queryInterface.addIndex('users', ['username']);
    await queryInterface.addIndex('users', ['phone']);
    await queryInterface.addIndex('users', ['email']);
  },
  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('users');
  }
}; 