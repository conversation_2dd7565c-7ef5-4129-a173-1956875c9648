'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 检查 user_info 字段是否存在
    const tableDescription = await queryInterface.describeTable('users');
    
    if (!tableDescription.user_info) {
      // 添加 user_info 字段
      await queryInterface.addColumn('users', 'user_info', {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '用户信息（头像、昵称等）JSON格式'
      });
    }
    
    // 检查其他必要字段
    if (!tableDescription.openid) {
      await queryInterface.addColumn('users', 'openid', {
        type: Sequelize.STRING(100),
        allowNull: true,
        unique: true,
        comment: '微信用户唯一标识'
      });
    }
    
    if (!tableDescription.unionid) {
      await queryInterface.addColumn('users', 'unionid', {
        type: Sequelize.STRING(100),
        allowNull: true,
        comment: '微信开放平台唯一标识'
      });
    }
    
    if (!tableDescription.last_login_at) {
      await queryInterface.addColumn('users', 'last_login_at', {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '最后登录时间'
      });
    }
    
    if (!tableDescription.level) {
      await queryInterface.addColumn('users', 'level', {
        type: Sequelize.INTEGER,
        defaultValue: 1,
        comment: '用户等级'
      });
    }
    
    if (!tableDescription.points) {
      await queryInterface.addColumn('users', 'points', {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        comment: '用户积分'
      });
    }
    
    if (!tableDescription.remark) {
      await queryInterface.addColumn('users', 'remark', {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '备注信息'
      });
    }
    
    // 修改 status 字段为 ENUM 类型（如果需要）
    if (tableDescription.status && tableDescription.status.type !== 'ENUM') {
      await queryInterface.changeColumn('users', 'status', {
        type: Sequelize.ENUM('active', 'inactive', 'banned'),
        defaultValue: 'active',
        comment: '用户状态'
      });
    }
  },

  down: async (queryInterface, Sequelize) => {
    // 回滚操作
    await queryInterface.removeColumn('users', 'user_info');
    await queryInterface.removeColumn('users', 'openid');
    await queryInterface.removeColumn('users', 'unionid');
    await queryInterface.removeColumn('users', 'last_login_at');
    await queryInterface.removeColumn('users', 'level');
    await queryInterface.removeColumn('users', 'points');
    await queryInterface.removeColumn('users', 'remark');
  }
};
