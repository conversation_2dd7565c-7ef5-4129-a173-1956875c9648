'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('admin_users', {
      id: {
        type: Sequelize.BIGINT,
        primaryKey: true,
        autoIncrement: true,
        comment: '管理员ID'
      },
      username: {
        type: Sequelize.STRING(50),
        allowNull: false,
        unique: true,
        comment: '用户名'
      },
      password: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: '密码'
      },
      nickname: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: '昵称'
      },
      avatar: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: '头像URL'
      },
      role_id: {
        type: Sequelize.BIGINT,
        allowNull: false,
        comment: '角色ID'
      },
      status: {
        type: Sequelize.TINYINT,
        defaultValue: 1,
        comment: '状态(0禁用 1正常)'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });
    await queryInterface.addIndex('admin_users', ['username']);
    await queryInterface.addIndex('admin_users', ['role_id']);
  },
  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('admin_users');
  }
}; 