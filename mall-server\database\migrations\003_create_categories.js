'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('categories', {
      id: {
        type: Sequelize.BIGINT,
        primaryKey: true,
        autoIncrement: true,
        comment: '分类ID'
      },
      name: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: '分类名称'
      },
      parent_id: {
        type: Sequelize.BIGINT,
        allowNull: false,
        defaultValue: 0,
        comment: '父级ID'
      },
      icon: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: '分类图标'
      },
      sort_order: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        comment: '排序序号'
      },
      status: {
        type: Sequelize.TINYINT,
        defaultValue: 1,
        comment: '状态(0禁用 1正常)'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });
    await queryInterface.addIndex('categories', ['parent_id']);
    await queryInterface.addIndex('categories', ['status']);
  },
  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('categories');
  }
}; 