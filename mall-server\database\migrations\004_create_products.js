'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('products', {
      id: {
        type: Sequelize.BIGINT,
        primaryKey: true,
        autoIncrement: true,
        comment: '商品ID'
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: '商品名称'
      },
      category_id: {
        type: Sequelize.BIGINT,
        allowNull: false,
        comment: '分类ID'
      },
      image_url: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: '主图URL'
      },
      price: {
        type: Sequelize.DECIMAL(10,2),
        allowNull: false,
        comment: '价格'
      },
      stock: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '库存'
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '商品描述'
      },
      status: {
        type: Sequelize.TINYINT,
        defaultValue: 1,
        comment: '状态(0下架 1上架)'
      },
      sort_order: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        comment: '排序序号'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });
    await queryInterface.addIndex('products', ['category_id']);
    await queryInterface.addIndex('products', ['status']);
  },
  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('products');
  }
}; 