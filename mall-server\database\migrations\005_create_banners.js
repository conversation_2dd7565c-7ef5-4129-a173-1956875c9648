'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('banners', {
      id: {
        type: Sequelize.BIGINT,
        primaryKey: true,
        autoIncrement: true,
        comment: '轮播图ID'
      },
      title: {
        type: Sequelize.STRING(100),
        allowNull: true,
        comment: '标题'
      },
      image_url: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: '图片URL'
      },
      link_url: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: '跳转链接'
      },
      sort_order: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        comment: '排序序号'
      },
      start_time: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '开始时间'
      },
      end_time: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '结束时间'
      },
      status: {
        type: Sequelize.TINYINT,
        defaultValue: 1,
        comment: '状态(0:禁用 1:启用)'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 添加索引
    await queryInterface.addIndex('banners', ['status']);
    await queryInterface.addIndex('banners', ['sort_order']);
    await queryInterface.addIndex('banners', ['start_time']);
    await queryInterface.addIndex('banners', ['end_time']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('banners');
  }
}; 