'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 1. 增强管理员表
    await queryInterface.addColumn('admin_users', 'department', {
      type: Sequelize.STRING(50),
      comment: '部门'
    });

    await queryInterface.addColumn('admin_users', 'position', {
      type: Sequelize.STRING(50),
      comment: '职位'
    });

    await queryInterface.addColumn('admin_users', 'login_attempts', {
      type: Sequelize.INTEGER,
      defaultValue: 0,
      comment: '登录失败次数'
    });

    await queryInterface.addColumn('admin_users', 'password_changed_at', {
      type: Sequelize.DATE,
      comment: '密码修改时间'
    });

    await queryInterface.addColumn('admin_users', 'password_expires_at', {
      type: Sequelize.DATE,
      comment: '密码过期时间'
    });

    await queryInterface.addColumn('admin_users', 'two_factor_enabled', {
      type: Sequelize.TINYINT,
      defaultValue: 0,
      comment: '是否启用双因素认证'
    });

    await queryInterface.addColumn('admin_users', 'two_factor_secret', {
      type: Sequelize.STRING(100),
      comment: '双因素认证密钥'
    });

    await queryInterface.addColumn('admin_users', 'created_by', {
      type: Sequelize.BIGINT,
      comment: '创建人ID'
    });

    // 2. 创建角色表
    await queryInterface.createTable('roles', {
      id: {
        type: Sequelize.BIGINT,
        primaryKey: true,
        autoIncrement: true,
        comment: '角色ID'
      },
      name: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: '角色名称'
      },
      code: {
        type: Sequelize.STRING(50),
        allowNull: false,
        unique: true,
        comment: '角色代码'
      },
      description: {
        type: Sequelize.TEXT,
        comment: '角色描述'
      },
      level: {
        type: Sequelize.INTEGER,
        defaultValue: 1,
        comment: '角色级别(1:超级管理员 2:管理员 3:操作员)'
      },
      status: {
        type: Sequelize.TINYINT,
        defaultValue: 1,
        comment: '状态(0:禁用 1:正常)'
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      }
    });

    // 3. 创建权限表
    await queryInterface.createTable('permissions', {
      id: {
        type: Sequelize.BIGINT,
        primaryKey: true,
        autoIncrement: true,
        comment: '权限ID'
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: '权限名称'
      },
      code: {
        type: Sequelize.STRING(100),
        allowNull: false,
        unique: true,
        comment: '权限代码'
      },
      type: {
        type: Sequelize.STRING(20),
        defaultValue: 'menu',
        comment: '权限类型(menu:菜单 page:页面 button:按钮 api:接口)'
      },
      parent_id: {
        type: Sequelize.BIGINT,
        defaultValue: 0,
        comment: '父权限ID'
      },
      path: {
        type: Sequelize.STRING(200),
        comment: '路径'
      },
      component: {
        type: Sequelize.STRING(200),
        comment: '组件'
      },
      icon: {
        type: Sequelize.STRING(50),
        comment: '图标'
      },
      sort_order: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        comment: '排序'
      },
      status: {
        type: Sequelize.TINYINT,
        defaultValue: 1,
        comment: '状态(0:禁用 1:正常)'
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      }
    });

    // 4. 创建角色权限关联表
    await queryInterface.createTable('role_permissions', {
      id: {
        type: Sequelize.BIGINT,
        primaryKey: true,
        autoIncrement: true,
        comment: '关联ID'
      },
      role_id: {
        type: Sequelize.BIGINT,
        allowNull: false,
        comment: '角色ID'
      },
      permission_id: {
        type: Sequelize.BIGINT,
        allowNull: false,
        comment: '权限ID'
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      }
    });

    // 5. 创建操作日志表
    await queryInterface.createTable('admin_operation_logs', {
      id: {
        type: Sequelize.BIGINT,
        primaryKey: true,
        autoIncrement: true,
        comment: '日志ID'
      },
      admin_id: {
        type: Sequelize.BIGINT,
        allowNull: false,
        comment: '管理员ID'
      },
      username: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: '用户名'
      },
      action: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: '操作类型'
      },
      resource: {
        type: Sequelize.STRING(100),
        comment: '操作资源'
      },
      resource_id: {
        type: Sequelize.STRING(50),
        comment: '资源ID'
      },
      description: {
        type: Sequelize.TEXT,
        comment: '操作描述'
      },
      ip_address: {
        type: Sequelize.STRING(50),
        comment: 'IP地址'
      },
      user_agent: {
        type: Sequelize.TEXT,
        comment: '用户代理'
      },
      request_data: {
        type: Sequelize.TEXT,
        comment: '请求数据'
      },
      response_data: {
        type: Sequelize.TEXT,
        comment: '响应数据'
      },
      status: {
        type: Sequelize.TINYINT,
        defaultValue: 1,
        comment: '状态(1:成功 0:失败)'
      },
      error_message: {
        type: Sequelize.TEXT,
        comment: '错误信息'
      },
      execution_time: {
        type: Sequelize.INTEGER,
        comment: '执行时间(毫秒)'
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      }
    });

    // 6. 创建登录日志表
    await queryInterface.createTable('admin_login_logs', {
      id: {
        type: Sequelize.BIGINT,
        primaryKey: true,
        autoIncrement: true,
        comment: '日志ID'
      },
      admin_id: {
        type: Sequelize.BIGINT,
        comment: '管理员ID'
      },
      username: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: '用户名'
      },
      ip_address: {
        type: Sequelize.STRING(50),
        comment: 'IP地址'
      },
      user_agent: {
        type: Sequelize.TEXT,
        comment: '用户代理'
      },
      login_type: {
        type: Sequelize.STRING(20),
        defaultValue: 'password',
        comment: '登录类型(password:密码 2fa:双因素)'
      },
      status: {
        type: Sequelize.TINYINT,
        defaultValue: 1,
        comment: '状态(1:成功 0:失败)'
      },
      failure_reason: {
        type: Sequelize.STRING(200),
        comment: '失败原因'
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      }
    });

    // 7. 创建系统配置表
    await queryInterface.createTable('system_configs', {
      id: {
        type: Sequelize.BIGINT,
        primaryKey: true,
        autoIncrement: true,
        comment: '配置ID'
      },
      config_key: {
        type: Sequelize.STRING(100),
        allowNull: false,
        unique: true,
        comment: '配置键'
      },
      config_value: {
        type: Sequelize.TEXT,
        comment: '配置值'
      },
      config_type: {
        type: Sequelize.STRING(20),
        defaultValue: 'string',
        comment: '配置类型(string:字符串 json:JSON number:数字 boolean:布尔)'
      },
      description: {
        type: Sequelize.TEXT,
        comment: '配置描述'
      },
      is_system: {
        type: Sequelize.TINYINT,
        defaultValue: 0,
        comment: '是否系统配置(0:否 1:是)'
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      }
    });

    // 添加索引
    await queryInterface.addIndex('admin_users', ['role_id']);
    await queryInterface.addIndex('admin_users', ['email']);
    await queryInterface.addIndex('role_permissions', ['role_id', 'permission_id'], { unique: true });
    await queryInterface.addIndex('admin_operation_logs', ['admin_id']);
    await queryInterface.addIndex('admin_operation_logs', ['action']);
    await queryInterface.addIndex('admin_operation_logs', ['created_at']);
    await queryInterface.addIndex('admin_login_logs', ['admin_id']);
    await queryInterface.addIndex('admin_login_logs', ['username']);
    await queryInterface.addIndex('admin_login_logs', ['status']);
    await queryInterface.addIndex('admin_login_logs', ['created_at']);
  },

  down: async (queryInterface, Sequelize) => {
    // 删除新增的列
    await queryInterface.removeColumn('admin_users', 'department');
    await queryInterface.removeColumn('admin_users', 'position');
    await queryInterface.removeColumn('admin_users', 'login_attempts');
    await queryInterface.removeColumn('admin_users', 'password_changed_at');
    await queryInterface.removeColumn('admin_users', 'password_expires_at');
    await queryInterface.removeColumn('admin_users', 'two_factor_enabled');
    await queryInterface.removeColumn('admin_users', 'two_factor_secret');
    await queryInterface.removeColumn('admin_users', 'created_by');

    // 删除表
    await queryInterface.dropTable('role_permissions');
    await queryInterface.dropTable('permissions');
    await queryInterface.dropTable('roles');
    await queryInterface.dropTable('admin_operation_logs');
    await queryInterface.dropTable('admin_login_logs');
    await queryInterface.dropTable('system_configs');
  }
}; 