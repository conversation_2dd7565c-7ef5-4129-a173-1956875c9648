-- 数据分析功能相关表结构
-- 执行前请备份数据库

-- 1. 用户行为分析表
CREATE TABLE IF NOT EXISTS user_behaviors (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '行为记录ID',
    user_id BIGINT NULL COMMENT '用户ID（可为空，支持匿名用户）',
    openid VARCHAR(100) NULL COMMENT '微信openid',
    behavior_type ENUM('view', 'search', 'add_cart', 'order', 'pay', 'share', 'favorite') NOT NULL COMMENT '行为类型',
    target_type ENUM('product', 'category', 'order', 'page') NOT NULL COMMENT '目标类型',
    target_id BIGINT NULL COMMENT '目标ID',
    page_path VARCHAR(255) NULL COMMENT '页面路径',
    search_keyword VARCHAR(100) NULL COMMENT '搜索关键词',
    session_id VARCHAR(100) NULL COMMENT '会话ID',
    ip_address VARCHAR(45) NULL COMMENT 'IP地址',
    user_agent TEXT NULL COMMENT '用户代理',
    extra_data JSON NULL COMMENT '额外数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_user_id (user_id),
    INDEX idx_openid (openid),
    INDEX idx_behavior_type (behavior_type),
    INDEX idx_target_type_id (target_type, target_id),
    INDEX idx_created_at (created_at),
    INDEX idx_search_keyword (search_keyword),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) COMMENT '用户行为分析表';

-- 2. 库存预警表
CREATE TABLE IF NOT EXISTS stock_alerts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '库存预警ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    alert_type ENUM('low_stock', 'out_of_stock', 'overstock') NOT NULL COMMENT '预警类型',
    current_stock INT NOT NULL COMMENT '当前库存',
    threshold_value INT NOT NULL COMMENT '阈值',
    alert_level ENUM('low', 'medium', 'high', 'critical') NOT NULL COMMENT '预警级别',
    status ENUM('active', 'resolved', 'ignored') DEFAULT 'active' COMMENT '预警状态',
    message TEXT NULL COMMENT '预警消息',
    resolved_at TIMESTAMP NULL COMMENT '解决时间',
    resolved_by BIGINT NULL COMMENT '解决人ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_product_id (product_id),
    INDEX idx_alert_type (alert_type),
    INDEX idx_alert_level (alert_level),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
) COMMENT '库存预警表';

-- 3. 销售报表表
CREATE TABLE IF NOT EXISTS sales_reports (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '报表ID',
    report_date DATE NOT NULL COMMENT '报表日期',
    report_type ENUM('daily', 'weekly', 'monthly', 'yearly') NOT NULL COMMENT '报表类型',
    total_orders INT DEFAULT 0 COMMENT '总订单数',
    total_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '总销售额',
    paid_orders INT DEFAULT 0 COMMENT '已支付订单数',
    paid_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '已支付金额',
    refund_orders INT DEFAULT 0 COMMENT '退款订单数',
    refund_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '退款金额',
    new_users INT DEFAULT 0 COMMENT '新增用户数',
    active_users INT DEFAULT 0 COMMENT '活跃用户数',
    conversion_rate DECIMAL(5,4) DEFAULT 0.0000 COMMENT '转化率',
    avg_order_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '平均订单金额',
    top_products JSON NULL COMMENT '热销商品TOP10',
    top_categories JSON NULL COMMENT '热销分类TOP10',
    user_analysis JSON NULL COMMENT '用户分析数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_report_date_type (report_date, report_type),
    INDEX idx_report_type (report_type),
    INDEX idx_report_date (report_date),
    INDEX idx_created_at (created_at)
) COMMENT '销售报表表';

-- 4. 自动发货记录表
CREATE TABLE IF NOT EXISTS auto_shipping (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '自动发货记录ID',
    order_id BIGINT NOT NULL COMMENT '订单ID',
    shipping_rule_id BIGINT NULL COMMENT '发货规则ID',
    shipping_company VARCHAR(50) NOT NULL COMMENT '快递公司',
    tracking_number VARCHAR(50) NOT NULL COMMENT '快递单号',
    shipping_status ENUM('pending', 'processing', 'shipped', 'delivered', 'failed') DEFAULT 'pending' COMMENT '发货状态',
    auto_generated BOOLEAN DEFAULT TRUE COMMENT '是否自动生成',
    shipping_time TIMESTAMP NULL COMMENT '发货时间',
    estimated_delivery TIMESTAMP NULL COMMENT '预计送达时间',
    actual_delivery TIMESTAMP NULL COMMENT '实际送达时间',
    shipping_cost DECIMAL(10,2) DEFAULT 0.00 COMMENT '运费成本',
    error_message TEXT NULL COMMENT '错误信息',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    last_retry_at TIMESTAMP NULL COMMENT '最后重试时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_order_id (order_id),
    INDEX idx_shipping_status (shipping_status),
    INDEX idx_tracking_number (tracking_number),
    INDEX idx_shipping_time (shipping_time),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
) COMMENT '自动发货记录表';

-- 5. 为现有表添加缺失字段（如果不存在）

-- 为商品表添加浏览量字段
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS view_count INT DEFAULT 0 COMMENT '浏览量' AFTER sales;

-- 为订单表添加发货相关字段（如果不存在）
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS delivery_company VARCHAR(50) NULL COMMENT '快递公司' AFTER receiver_address,
ADD COLUMN IF NOT EXISTS delivery_no VARCHAR(50) NULL COMMENT '快递单号' AFTER delivery_company,
ADD COLUMN IF NOT EXISTS delivery_time TIMESTAMP NULL COMMENT '发货时间' AFTER delivery_no;

-- 为用户表添加最后登录时间字段（如果不存在）
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS last_login_at TIMESTAMP NULL COMMENT '最后登录时间' AFTER updatedAt;

-- 6. 创建索引优化查询性能

-- 订单表索引优化
CREATE INDEX IF NOT EXISTS idx_orders_pay_status ON orders(pay_status);
CREATE INDEX IF NOT EXISTS idx_orders_order_status ON orders(order_status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_orders_pay_time ON orders(pay_time);

-- 商品表索引优化
CREATE INDEX IF NOT EXISTS idx_products_status ON products(status);
CREATE INDEX IF NOT EXISTS idx_products_category_id ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_sales ON products(sales);
CREATE INDEX IF NOT EXISTS idx_products_view_count ON products(view_count);

-- 用户表索引优化
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(createdAt);
CREATE INDEX IF NOT EXISTS idx_users_last_login_at ON users(last_login_at);

-- 7. 插入初始化数据

-- 插入一些示例用户行为数据（可选）
INSERT IGNORE INTO user_behaviors (user_id, openid, behavior_type, target_type, page_path, created_at) VALUES
(1, 'test_openid_1', 'view', 'page', '/pages/index/index', NOW() - INTERVAL 1 DAY),
(1, 'test_openid_1', 'view', 'product', '/pages/product/detail', NOW() - INTERVAL 1 DAY),
(1, 'test_openid_1', 'search', 'product', '/pages/search/index', NOW() - INTERVAL 1 DAY);

-- 更新商品浏览量（基于现有数据）
UPDATE products SET view_count = FLOOR(RAND() * 1000) + sales * 5 WHERE view_count = 0;

-- 8. 创建视图方便查询

-- 创建销售统计视图
CREATE OR REPLACE VIEW v_sales_stats AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as order_count,
    SUM(CASE WHEN pay_status = 1 THEN pay_amount ELSE 0 END) as paid_amount,
    SUM(CASE WHEN pay_status = 1 THEN 1 ELSE 0 END) as paid_count,
    AVG(CASE WHEN pay_status = 1 THEN pay_amount ELSE NULL END) as avg_amount
FROM orders 
WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- 创建商品销售排行视图
CREATE OR REPLACE VIEW v_product_sales_ranking AS
SELECT 
    p.id,
    p.name,
    p.price,
    p.main_image,
    p.sales,
    p.view_count,
    COALESCE(SUM(oi.quantity), 0) as total_sold,
    COALESCE(SUM(oi.quantity * oi.price), 0) as total_revenue
FROM products p
LEFT JOIN order_items oi ON p.id = oi.product_id
LEFT JOIN orders o ON oi.order_id = o.id AND o.pay_status = 1
WHERE p.status = 1
GROUP BY p.id
ORDER BY total_sold DESC, p.sales DESC;

-- 创建库存预警汇总视图
CREATE OR REPLACE VIEW v_stock_alert_summary AS
SELECT 
    alert_type,
    alert_level,
    COUNT(*) as alert_count,
    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_count
FROM stock_alerts
GROUP BY alert_type, alert_level;

COMMIT;

-- 执行完成提示
SELECT '数据分析功能表结构创建完成！' as message;
