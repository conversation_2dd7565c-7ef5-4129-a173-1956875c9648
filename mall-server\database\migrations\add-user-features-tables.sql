-- 用户功能相关表结构（收藏、浏览历史、对比、分享、客服）
-- 执行前请备份数据库

-- 1. 商品收藏表
CREATE TABLE IF NOT EXISTS favorites (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '收藏ID',
    user_id INT NOT NULL COMMENT '用户ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
    UNIQUE KEY uk_user_product (user_id, product_id),
    INDEX idx_user_id (user_id),
    INDEX idx_product_id (product_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
) COMMENT '商品收藏表';

-- 2. 浏览历史表
CREATE TABLE IF NOT EXISTS browse_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '浏览记录ID',
    user_id INT NOT NULL COMMENT '用户ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    browse_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '浏览时间',
    browse_duration INT DEFAULT 0 COMMENT '浏览时长(秒)',
    source ENUM('search', 'category', 'recommend', 'share', 'direct') DEFAULT 'direct' COMMENT '浏览来源',
    device_info JSON NULL COMMENT '设备信息',
    INDEX idx_user_product (user_id, product_id),
    INDEX idx_user_time (user_id, browse_time),
    INDEX idx_product_id (product_id),
    INDEX idx_browse_time (browse_time),
    INDEX idx_source (source),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
) COMMENT '浏览历史表';

-- 3. 商品对比表
CREATE TABLE IF NOT EXISTS product_compare (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '对比ID',
    user_id INT NOT NULL COMMENT '用户ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    compare_group VARCHAR(50) NOT NULL DEFAULT 'default' COMMENT '对比组标识',
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
    sort_order INT DEFAULT 0 COMMENT '排序序号',
    UNIQUE KEY uk_user_product_group (user_id, product_id, compare_group),
    INDEX idx_user_group (user_id, compare_group),
    INDEX idx_product_id (product_id),
    INDEX idx_added_at (added_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
) COMMENT '商品对比表';

-- 4. 分享记录表
CREATE TABLE IF NOT EXISTS share_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分享记录ID',
    user_id INT NOT NULL COMMENT '分享用户ID',
    share_type ENUM('product', 'category', 'page', 'order') NOT NULL COMMENT '分享类型',
    target_id BIGINT NULL COMMENT '分享目标ID',
    share_platform ENUM('wechat', 'moments', 'qq', 'weibo', 'copy_link') NOT NULL COMMENT '分享平台',
    share_title VARCHAR(200) NULL COMMENT '分享标题',
    share_desc TEXT NULL COMMENT '分享描述',
    share_image VARCHAR(255) NULL COMMENT '分享图片',
    share_url VARCHAR(500) NULL COMMENT '分享链接',
    click_count INT DEFAULT 0 COMMENT '点击次数',
    conversion_count INT DEFAULT 0 COMMENT '转化次数',
    share_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分享时间',
    extra_data JSON NULL COMMENT '额外数据',
    INDEX idx_user_id (user_id),
    INDEX idx_share_type_target (share_type, target_id),
    INDEX idx_share_platform (share_platform),
    INDEX idx_share_time (share_time),
    INDEX idx_click_count (click_count),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT '分享记录表';

-- 5. 客服会话表
CREATE TABLE IF NOT EXISTS customer_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '会话ID',
    user_id INT NOT NULL COMMENT '用户ID',
    admin_id BIGINT NULL COMMENT '客服ID',
    session_status ENUM('waiting', 'active', 'closed', 'timeout') DEFAULT 'waiting' COMMENT '会话状态',
    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal' COMMENT '优先级',
    category ENUM('product', 'order', 'payment', 'delivery', 'refund', 'other') DEFAULT 'other' COMMENT '问题分类',
    title VARCHAR(200) NULL COMMENT '会话标题',
    tags JSON NULL COMMENT '标签',
    satisfaction INT NULL COMMENT '满意度评分(1-5)',
    feedback TEXT NULL COMMENT '用户反馈',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    closed_at TIMESTAMP NULL COMMENT '关闭时间',
    INDEX idx_user_id (user_id),
    INDEX idx_admin_id (admin_id),
    INDEX idx_session_status (session_status),
    INDEX idx_priority (priority),
    INDEX idx_category (category),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT '客服会话表';

-- 6. 客服消息表
CREATE TABLE IF NOT EXISTS customer_messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '消息ID',
    session_id BIGINT NOT NULL COMMENT '会话ID',
    sender_type ENUM('user', 'admin', 'system') NOT NULL COMMENT '发送者类型',
    sender_id BIGINT NOT NULL COMMENT '发送者ID',
    message_type ENUM('text', 'image', 'file', 'product', 'order', 'system') DEFAULT 'text' COMMENT '消息类型',
    content TEXT NOT NULL COMMENT '消息内容',
    extra_data JSON NULL COMMENT '额外数据(图片URL、文件信息等)',
    is_read BOOLEAN DEFAULT FALSE COMMENT '是否已读',
    read_at TIMESTAMP NULL COMMENT '阅读时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
    INDEX idx_session_id (session_id),
    INDEX idx_sender (sender_type, sender_id),
    INDEX idx_message_type (message_type),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (session_id) REFERENCES customer_sessions(id) ON DELETE CASCADE
) COMMENT '客服消息表';

-- 7. 为现有表添加字段

-- 为商品表添加收藏数字段
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS favorite_count INT DEFAULT 0 COMMENT '收藏数' AFTER view_count;

-- 为用户表添加最后活跃时间
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS last_active_at TIMESTAMP NULL COMMENT '最后活跃时间' AFTER last_login_at;

-- 8. 创建索引优化查询性能

-- 收藏表复合索引
CREATE INDEX IF NOT EXISTS idx_favorites_user_created ON favorites(user_id, created_at DESC);

-- 浏览历史表复合索引
CREATE INDEX IF NOT EXISTS idx_browse_user_time ON browse_history(user_id, browse_time DESC);
CREATE INDEX IF NOT EXISTS idx_browse_product_time ON browse_history(product_id, browse_time DESC);

-- 对比表复合索引
CREATE INDEX IF NOT EXISTS idx_compare_user_group_order ON product_compare(user_id, compare_group, sort_order);

-- 分享记录表复合索引
CREATE INDEX IF NOT EXISTS idx_share_user_time ON share_records(user_id, share_time DESC);
CREATE INDEX IF NOT EXISTS idx_share_type_target_time ON share_records(share_type, target_id, share_time DESC);

-- 客服表复合索引
CREATE INDEX IF NOT EXISTS idx_session_status_priority ON customer_sessions(session_status, priority DESC, created_at ASC);
CREATE INDEX IF NOT EXISTS idx_session_user_status ON customer_sessions(user_id, session_status, created_at DESC);

-- 客服消息表复合索引
CREATE INDEX IF NOT EXISTS idx_message_session_time ON customer_messages(session_id, created_at ASC);

-- 9. 创建视图方便查询

-- 用户收藏统计视图
CREATE OR REPLACE VIEW v_user_favorite_stats AS
SELECT 
    u.id as user_id,
    u.nickName,
    COUNT(f.id) as favorite_count,
    MAX(f.created_at) as last_favorite_time
FROM users u
LEFT JOIN favorites f ON u.id = f.user_id
GROUP BY u.id;

-- 商品收藏排行视图
CREATE OR REPLACE VIEW v_product_favorite_ranking AS
SELECT 
    p.id,
    p.name,
    p.price,
    p.main_image,
    COUNT(f.id) as favorite_count,
    p.sales,
    p.rating
FROM products p
LEFT JOIN favorites f ON p.id = f.product_id
WHERE p.status = 1
GROUP BY p.id
ORDER BY favorite_count DESC, p.sales DESC;

-- 用户浏览统计视图
CREATE OR REPLACE VIEW v_user_browse_stats AS
SELECT 
    user_id,
    COUNT(*) as browse_count,
    COUNT(DISTINCT product_id) as unique_products,
    MAX(browse_time) as last_browse_time,
    AVG(browse_duration) as avg_duration
FROM browse_history
WHERE browse_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY user_id;

-- 客服工作量统计视图
CREATE OR REPLACE VIEW v_admin_workload_stats AS
SELECT 
    admin_id,
    COUNT(*) as total_sessions,
    COUNT(CASE WHEN session_status = 'closed' THEN 1 END) as closed_sessions,
    AVG(satisfaction) as avg_satisfaction,
    AVG(TIMESTAMPDIFF(MINUTE, created_at, closed_at)) as avg_duration_minutes
FROM customer_sessions
WHERE admin_id IS NOT NULL
AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY admin_id;

-- 10. 插入初始化数据

-- 插入一些示例FAQ数据（如果需要的话）
INSERT IGNORE INTO customer_messages (session_id, sender_type, sender_id, message_type, content) VALUES
(0, 'system', 0, 'system', '欢迎使用心洁茶叶客服系统！');

-- 11. 触发器：自动更新商品收藏数
DELIMITER $$

CREATE TRIGGER IF NOT EXISTS tr_favorite_insert 
AFTER INSERT ON favorites
FOR EACH ROW
BEGIN
    UPDATE products SET favorite_count = favorite_count + 1 WHERE id = NEW.product_id;
END$$

CREATE TRIGGER IF NOT EXISTS tr_favorite_delete 
AFTER DELETE ON favorites
FOR EACH ROW
BEGIN
    UPDATE products SET favorite_count = GREATEST(favorite_count - 1, 0) WHERE id = OLD.product_id;
END$$

DELIMITER ;

-- 12. 存储过程：清理旧数据
DELIMITER $$

CREATE PROCEDURE IF NOT EXISTS sp_cleanup_old_data(IN days_old INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE cleanup_date DATE;
    
    SET cleanup_date = DATE_SUB(CURDATE(), INTERVAL days_old DAY);
    
    -- 清理旧的浏览历史
    DELETE FROM browse_history WHERE browse_time < cleanup_date;
    
    -- 清理旧的分享记录
    DELETE FROM share_records WHERE share_time < cleanup_date AND click_count = 0;
    
    -- 清理旧的已关闭客服会话
    DELETE FROM customer_sessions WHERE session_status = 'closed' AND closed_at < cleanup_date;
    
    SELECT CONCAT('清理完成，清理了', days_old, '天前的数据') as result;
END$$

DELIMITER ;

COMMIT;

-- 执行完成提示
SELECT '用户功能表结构创建完成！包括：收藏、浏览历史、商品对比、分享功能、客服系统' as message;
