-- 购物车表结构创建脚本
-- 执行前请备份数据库

-- 1. 创建购物车表
CREATE TABLE IF NOT EXISTS cart (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '购物车ID',
    user_id INT NOT NULL COMMENT '用户ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    sku_id BIGINT NULL COMMENT 'SKU ID',
    quantity INT NOT NULL DEFAULT 1 COMMENT '商品数量',
    selected BOOLEAN DEFAULT TRUE COMMENT '是否选中',
    price DECIMAL(10, 2) NOT NULL COMMENT '加入购物车时的价格',
    specifications JSON NULL COMMENT '商品规格信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    UNIQUE KEY uk_user_product_sku (user_id, product_id, sku_id),
    INDEX idx_user_id (user_id),
    INDEX idx_product_id (product_id),
    INDEX idx_created_at (created_at),
    INDEX idx_selected (selected),
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
) COMMENT '购物车表';

-- 2. 为商品表添加购物车相关字段（如果不存在）
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS max_buy_count INT DEFAULT NULL COMMENT '最大购买数量' AFTER stock;

-- 3. 创建购物车统计视图
CREATE OR REPLACE VIEW v_cart_statistics AS
SELECT 
    u.id as user_id,
    u.nickName,
    COUNT(c.id) as cart_items_count,
    SUM(c.quantity) as total_quantity,
    SUM(c.price * c.quantity) as total_amount,
    COUNT(CASE WHEN c.selected = TRUE THEN 1 END) as selected_items_count,
    SUM(CASE WHEN c.selected = TRUE THEN c.quantity ELSE 0 END) as selected_quantity,
    SUM(CASE WHEN c.selected = TRUE THEN c.price * c.quantity ELSE 0 END) as selected_amount,
    MAX(c.updated_at) as last_updated
FROM users u
LEFT JOIN cart c ON u.id = c.user_id
LEFT JOIN products p ON c.product_id = p.id AND p.status = 1
GROUP BY u.id;

-- 4. 创建购物车商品详情视图
CREATE OR REPLACE VIEW v_cart_items_detail AS
SELECT 
    c.id as cart_id,
    c.user_id,
    c.product_id,
    c.sku_id,
    c.quantity,
    c.selected,
    c.price as cart_price,
    c.specifications,
    c.created_at as added_at,
    c.updated_at,
    p.name as product_name,
    p.price as current_price,
    p.original_price,
    p.main_image,
    p.stock,
    p.status as product_status,
    p.sales,
    p.rating,
    p.max_buy_count,
    p.weight,
    cat.name as category_name,
    (c.price * c.quantity) as item_total,
    CASE 
        WHEN p.status != 1 THEN 'product_unavailable'
        WHEN c.quantity > p.stock THEN 'insufficient_stock'
        WHEN p.max_buy_count IS NOT NULL AND c.quantity > p.max_buy_count THEN 'exceed_max_buy'
        ELSE 'available'
    END as item_status
FROM cart c
JOIN products p ON c.product_id = p.id
JOIN categories cat ON p.category_id = cat.id
ORDER BY c.updated_at DESC;

-- 5. 创建触发器：自动清理无效购物车商品
DELIMITER $$

CREATE TRIGGER IF NOT EXISTS tr_cart_cleanup_invalid_products
AFTER UPDATE ON products
FOR EACH ROW
BEGIN
    -- 当商品状态变为下架时，删除购物车中的该商品
    IF NEW.status != 1 AND OLD.status = 1 THEN
        DELETE FROM cart WHERE product_id = NEW.id;
    END IF;
END$$

DELIMITER ;

-- 6. 创建存储过程：购物车数据维护
DELIMITER $$

CREATE PROCEDURE IF NOT EXISTS sp_cart_maintenance()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    
    -- 清理无效商品（商品已删除或下架）
    DELETE c FROM cart c
    LEFT JOIN products p ON c.product_id = p.id
    WHERE p.id IS NULL OR p.status != 1;
    
    -- 自动调整超出库存的商品数量
    UPDATE cart c
    JOIN products p ON c.product_id = p.id
    SET c.quantity = p.stock
    WHERE c.quantity > p.stock AND p.stock > 0;
    
    -- 删除库存为0的商品
    DELETE c FROM cart c
    JOIN products p ON c.product_id = p.id
    WHERE p.stock = 0;
    
    -- 更新价格（可选：同步最新商品价格）
    -- UPDATE cart c
    -- JOIN products p ON c.product_id = p.id
    -- SET c.price = p.price
    -- WHERE ABS(c.price - p.price) > 0.01;
    
    SELECT 'Cart maintenance completed' as result;
END$$

DELIMITER ;

-- 7. 创建存储过程：获取购物车摘要信息
DELIMITER $$

CREATE PROCEDURE IF NOT EXISTS sp_get_cart_summary(IN p_user_id INT)
BEGIN
    SELECT 
        COUNT(*) as total_items,
        SUM(quantity) as total_quantity,
        SUM(price * quantity) as total_amount,
        COUNT(CASE WHEN selected = TRUE THEN 1 END) as selected_items,
        SUM(CASE WHEN selected = TRUE THEN quantity ELSE 0 END) as selected_quantity,
        SUM(CASE WHEN selected = TRUE THEN price * quantity ELSE 0 END) as selected_amount,
        COUNT(CASE WHEN p.stock < c.quantity THEN 1 END) as stock_issues,
        COUNT(CASE WHEN p.status != 1 THEN 1 END) as unavailable_items
    FROM cart c
    LEFT JOIN products p ON c.product_id = p.id
    WHERE c.user_id = p_user_id;
END$$

DELIMITER ;

-- 8. 创建函数：检查购物车商品是否可购买
DELIMITER $$

CREATE FUNCTION IF NOT EXISTS fn_check_cart_item_availability(p_cart_id BIGINT)
RETURNS VARCHAR(50)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_result VARCHAR(50) DEFAULT 'unknown';
    DECLARE v_product_status INT DEFAULT 0;
    DECLARE v_stock INT DEFAULT 0;
    DECLARE v_quantity INT DEFAULT 0;
    DECLARE v_max_buy_count INT DEFAULT NULL;
    
    SELECT 
        p.status, p.stock, c.quantity, p.max_buy_count
    INTO 
        v_product_status, v_stock, v_quantity, v_max_buy_count
    FROM cart c
    JOIN products p ON c.product_id = p.id
    WHERE c.id = p_cart_id;
    
    IF v_product_status != 1 THEN
        SET v_result = 'product_unavailable';
    ELSEIF v_stock < v_quantity THEN
        SET v_result = 'insufficient_stock';
    ELSEIF v_max_buy_count IS NOT NULL AND v_quantity > v_max_buy_count THEN
        SET v_result = 'exceed_max_buy';
    ELSE
        SET v_result = 'available';
    END IF;
    
    RETURN v_result;
END$$

DELIMITER ;

-- 9. 插入测试数据（可选）
-- INSERT INTO cart (user_id, product_id, quantity, price, selected) VALUES
-- (1, 1, 2, 99.00, TRUE),
-- (1, 2, 1, 158.00, TRUE),
-- (2, 1, 1, 99.00, FALSE);

-- 10. 创建定时任务事件（需要开启事件调度器）
-- SET GLOBAL event_scheduler = ON;

-- CREATE EVENT IF NOT EXISTS ev_cart_daily_maintenance
-- ON SCHEDULE EVERY 1 DAY
-- STARTS CURRENT_TIMESTAMP
-- DO
--   CALL sp_cart_maintenance();

COMMIT;

-- 执行完成提示
SELECT '购物车表结构创建完成！包括：数据表、索引、视图、触发器、存储过程' as message;
