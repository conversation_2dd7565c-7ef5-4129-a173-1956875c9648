-- 折扣功能相关数据库表创建脚本
-- 创建时间: 2025-07-24

-- 1. 折扣规则表
CREATE TABLE IF NOT EXISTS discounts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '折扣ID',
    name VARCHAR(100) NOT NULL COMMENT '折扣名称',
    description TEXT COMMENT '折扣描述',
    type TINYINT NOT NULL DEFAULT 1 COMMENT '折扣类型(1:百分比折扣 2:固定金额折扣 3:满减折扣 4:买N送M)',
    value DECIMAL(10,2) NOT NULL COMMENT '折扣值(百分比折扣为0-100，固定金额为具体金额)',
    min_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '最低消费金额(满减折扣使用)',
    max_discount DECIMAL(10,2) DEFAULT NULL COMMENT '最大折扣金额(百分比折扣时限制最大优惠)',
    buy_quantity INT DEFAULT NULL COMMENT '购买数量(买N送M使用)',
    get_quantity INT DEFAULT NULL COMMENT '赠送数量(买N送M使用)',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',
    usage_limit INT DEFAULT NULL COMMENT '使用次数限制(NULL为无限制)',
    used_count INT DEFAULT 0 COMMENT '已使用次数',
    user_limit INT DEFAULT NULL COMMENT '单用户使用次数限制(NULL为无限制)',
    status TINYINT DEFAULT 1 COMMENT '状态(0:禁用 1:启用 2:已过期)',
    priority INT DEFAULT 0 COMMENT '优先级(数字越大优先级越高)',
    stackable TINYINT DEFAULT 0 COMMENT '是否可叠加(0:不可叠加 1:可叠加)',
    applicable_to TINYINT DEFAULT 1 COMMENT '适用范围(1:全部商品 2:指定商品 3:指定分类)',
    created_by BIGINT COMMENT '创建人ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time),
    INDEX idx_end_time (end_time),
    INDEX idx_priority (priority),
    INDEX idx_applicable_to (applicable_to)
) COMMENT '折扣规则表';

-- 2. 商品折扣关联表
CREATE TABLE IF NOT EXISTS product_discounts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    discount_id BIGINT NOT NULL COMMENT '折扣ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_discount_product (discount_id, product_id),
    INDEX idx_discount_id (discount_id),
    INDEX idx_product_id (product_id),
    FOREIGN KEY (discount_id) REFERENCES discounts(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
) COMMENT '商品折扣关联表';

-- 3. 分类折扣关联表
CREATE TABLE IF NOT EXISTS category_discounts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    discount_id BIGINT NOT NULL COMMENT '折扣ID',
    category_id BIGINT NOT NULL COMMENT '分类ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_discount_category (discount_id, category_id),
    INDEX idx_discount_id (discount_id),
    INDEX idx_category_id (category_id),
    FOREIGN KEY (discount_id) REFERENCES discounts(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
) COMMENT '分类折扣关联表';

-- 4. 用户折扣使用记录表
CREATE TABLE IF NOT EXISTS user_discount_usage (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '使用记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    discount_id BIGINT NOT NULL COMMENT '折扣ID',
    order_id BIGINT COMMENT '订单ID',
    discount_amount DECIMAL(10,2) NOT NULL COMMENT '折扣金额',
    used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '使用时间',
    INDEX idx_user_id (user_id),
    INDEX idx_discount_id (discount_id),
    INDEX idx_order_id (order_id),
    INDEX idx_used_at (used_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (discount_id) REFERENCES discounts(id) ON DELETE CASCADE,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL
) COMMENT '用户折扣使用记录表';

-- 5. 为现有表添加折扣相关字段

-- 为商品表添加折扣相关字段
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS discount_price DECIMAL(10,2) DEFAULT NULL COMMENT '折扣价格' AFTER price,
ADD COLUMN IF NOT EXISTS has_discount TINYINT DEFAULT 0 COMMENT '是否有折扣(0:无 1:有)' AFTER discount_price,
ADD COLUMN IF NOT EXISTS discount_start_time DATETIME DEFAULT NULL COMMENT '折扣开始时间' AFTER has_discount,
ADD COLUMN IF NOT EXISTS discount_end_time DATETIME DEFAULT NULL COMMENT '折扣结束时间' AFTER discount_start_time;

-- 为订单表添加折扣相关字段
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS discount_ids TEXT COMMENT '使用的折扣ID列表(JSON格式)' AFTER discount_amount,
ADD COLUMN IF NOT EXISTS discount_details TEXT COMMENT '折扣详情(JSON格式)' AFTER discount_ids;

-- 为订单商品表添加折扣相关字段
ALTER TABLE order_items 
ADD COLUMN IF NOT EXISTS original_price DECIMAL(10,2) COMMENT '原价' AFTER price,
ADD COLUMN IF NOT EXISTS discount_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '折扣金额' AFTER original_price,
ADD COLUMN IF NOT EXISTS discount_id BIGINT COMMENT '使用的折扣ID' AFTER discount_amount;

-- 6. 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_products_discount ON products(has_discount, discount_start_time, discount_end_time);
CREATE INDEX IF NOT EXISTS idx_products_discount_price ON products(discount_price);

-- 7. 创建折扣相关视图

-- 有效折扣视图
CREATE OR REPLACE VIEW v_active_discounts AS
SELECT 
    d.*,
    CASE 
        WHEN d.end_time < NOW() THEN 'expired'
        WHEN d.start_time > NOW() THEN 'pending'
        WHEN d.status = 0 THEN 'disabled'
        WHEN d.usage_limit IS NOT NULL AND d.used_count >= d.usage_limit THEN 'exhausted'
        ELSE 'active'
    END as discount_status
FROM discounts d
WHERE d.status = 1;

-- 商品折扣详情视图
CREATE OR REPLACE VIEW v_product_discounts AS
SELECT 
    p.id as product_id,
    p.name as product_name,
    p.price as original_price,
    p.discount_price,
    p.has_discount,
    d.id as discount_id,
    d.name as discount_name,
    d.type as discount_type,
    d.value as discount_value,
    d.start_time as discount_start_time,
    d.end_time as discount_end_time,
    CASE 
        WHEN d.type = 1 THEN ROUND(p.price * (100 - d.value) / 100, 2)  -- 百分比折扣
        WHEN d.type = 2 THEN GREATEST(p.price - d.value, 0.01)           -- 固定金额折扣
        ELSE p.price
    END as calculated_discount_price
FROM products p
LEFT JOIN product_discounts pd ON p.id = pd.product_id
LEFT JOIN v_active_discounts d ON pd.discount_id = d.id AND d.discount_status = 'active'
WHERE p.status = 1;

-- 8. 插入示例折扣数据
INSERT INTO discounts (name, description, type, value, start_time, end_time, status, applicable_to, priority) VALUES
('新用户专享9折', '新用户首次购买享受9折优惠', 1, 10.00, '2025-01-01 00:00:00', '2025-12-31 23:59:59', 1, 1, 10),
('满100减20', '单笔订单满100元减20元', 3, 20.00, '2025-01-01 00:00:00', '2025-12-31 23:59:59', 1, 1, 5),
('绿茶专区8折', '绿茶分类商品8折优惠', 1, 20.00, '2025-01-01 00:00:00', '2025-06-30 23:59:59', 1, 3, 8);

-- 设置满减折扣的最低消费金额
UPDATE discounts SET min_amount = 100.00 WHERE name = '满100减20';
