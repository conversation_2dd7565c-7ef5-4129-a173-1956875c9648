-- 用户功能数据库优化脚本 - 智能增强版
-- 执行前请备份数据库

-- 1. 添加高级索引优化
-- 收藏表优化索引
CREATE INDEX IF NOT EXISTS idx_favorites_user_created_product ON favorites(user_id, created_at DESC, product_id);
CREATE INDEX IF NOT EXISTS idx_favorites_product_created ON favorites(product_id, created_at DESC);

-- 浏览历史表优化索引
CREATE INDEX IF NOT EXISTS idx_browse_user_time_duration ON browse_history(user_id, browse_time DESC, browse_duration);
CREATE INDEX IF NOT EXISTS idx_browse_product_time_source ON browse_history(product_id, browse_time DESC, source);
CREATE INDEX IF NOT EXISTS idx_browse_source_time ON browse_history(source, browse_time DESC);

-- 商品对比表优化索引
CREATE INDEX IF NOT EXISTS idx_compare_user_group_added ON product_compare(user_id, compare_group, added_at DESC);
CREATE INDEX IF NOT EXISTS idx_compare_product_added ON product_compare(product_id, added_at DESC);

-- 分享记录表优化索引
CREATE INDEX IF NOT EXISTS idx_share_user_platform_time ON share_records(user_id, share_platform, share_time DESC);
CREATE INDEX IF NOT EXISTS idx_share_type_target_platform ON share_records(share_type, target_id, share_platform);
CREATE INDEX IF NOT EXISTS idx_share_click_conversion ON share_records(click_count DESC, conversion_count DESC);

-- 客服会话表优化索引
CREATE INDEX IF NOT EXISTS idx_session_status_priority_created ON customer_sessions(session_status, priority DESC, created_at ASC);
CREATE INDEX IF NOT EXISTS idx_session_admin_status_updated ON customer_sessions(admin_id, session_status, updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_session_category_satisfaction ON customer_sessions(category, satisfaction DESC);

-- 客服消息表优化索引
CREATE INDEX IF NOT EXISTS idx_message_session_created_type ON customer_messages(session_id, created_at ASC, message_type);
CREATE INDEX IF NOT EXISTS idx_message_sender_created ON customer_messages(sender_type, sender_id, created_at DESC);

-- 2. 创建智能分析视图

-- 用户行为综合分析视图
CREATE OR REPLACE VIEW v_user_behavior_analysis AS
SELECT 
    u.id as user_id,
    u.nickName,
    u.last_active_at,
    -- 收藏数据
    COALESCE(f_stats.favorite_count, 0) as favorite_count,
    f_stats.last_favorite_time,
    f_stats.favorite_categories,
    -- 浏览数据
    COALESCE(b_stats.browse_count, 0) as browse_count,
    COALESCE(b_stats.unique_products_browsed, 0) as unique_products_browsed,
    b_stats.avg_browse_duration,
    b_stats.primary_browse_source,
    -- 分享数据
    COALESCE(s_stats.share_count, 0) as share_count,
    COALESCE(s_stats.total_share_clicks, 0) as total_share_clicks,
    s_stats.preferred_share_platform,
    -- 客服数据
    COALESCE(cs_stats.session_count, 0) as customer_service_sessions,
    cs_stats.avg_satisfaction,
    -- 活跃度评分
    (
        COALESCE(f_stats.favorite_count, 0) * 0.2 +
        COALESCE(b_stats.browse_count, 0) * 0.1 +
        COALESCE(s_stats.share_count, 0) * 0.3 +
        CASE WHEN u.last_active_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 20 ELSE 0 END
    ) as activity_score
FROM users u
LEFT JOIN (
    SELECT 
        user_id,
        COUNT(*) as favorite_count,
        MAX(created_at) as last_favorite_time,
        COUNT(DISTINCT p.category_id) as favorite_categories
    FROM favorites f
    JOIN products p ON f.product_id = p.id
    WHERE f.created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)
    GROUP BY user_id
) f_stats ON u.id = f_stats.user_id
LEFT JOIN (
    SELECT 
        user_id,
        COUNT(*) as browse_count,
        COUNT(DISTINCT product_id) as unique_products_browsed,
        AVG(browse_duration) as avg_browse_duration,
        (
            SELECT source 
            FROM browse_history bh2 
            WHERE bh2.user_id = bh1.user_id 
            GROUP BY source 
            ORDER BY COUNT(*) DESC 
            LIMIT 1
        ) as primary_browse_source
    FROM browse_history bh1
    WHERE browse_time >= DATE_SUB(NOW(), INTERVAL 90 DAY)
    GROUP BY user_id
) b_stats ON u.id = b_stats.user_id
LEFT JOIN (
    SELECT 
        user_id,
        COUNT(*) as share_count,
        SUM(click_count) as total_share_clicks,
        (
            SELECT share_platform 
            FROM share_records sr2 
            WHERE sr2.user_id = sr1.user_id 
            GROUP BY share_platform 
            ORDER BY COUNT(*) DESC 
            LIMIT 1
        ) as preferred_share_platform
    FROM share_records sr1
    WHERE share_time >= DATE_SUB(NOW(), INTERVAL 90 DAY)
    GROUP BY user_id
) s_stats ON u.id = s_stats.user_id
LEFT JOIN (
    SELECT 
        user_id,
        COUNT(*) as session_count,
        AVG(satisfaction) as avg_satisfaction
    FROM customer_sessions
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)
    GROUP BY user_id
) cs_stats ON u.id = cs_stats.user_id;

-- 商品热度综合分析视图
CREATE OR REPLACE VIEW v_product_popularity_analysis AS
SELECT 
    p.id,
    p.name,
    p.price,
    p.main_image,
    p.category_id,
    c.name as category_name,
    p.sales,
    p.rating,
    p.view_count,
    -- 收藏数据
    COALESCE(f_stats.favorite_count, 0) as favorite_count,
    f_stats.recent_favorite_count,
    -- 浏览数据
    COALESCE(b_stats.browse_count, 0) as browse_count,
    COALESCE(b_stats.unique_browsers, 0) as unique_browsers,
    b_stats.avg_browse_duration,
    -- 分享数据
    COALESCE(s_stats.share_count, 0) as share_count,
    COALESCE(s_stats.total_clicks, 0) as share_clicks,
    -- 对比数据
    COALESCE(comp_stats.compare_count, 0) as compare_count,
    -- 综合热度评分
    (
        p.sales * 0.3 +
        p.rating * 10 +
        p.view_count * 0.1 +
        COALESCE(f_stats.favorite_count, 0) * 2 +
        COALESCE(b_stats.browse_count, 0) * 0.5 +
        COALESCE(s_stats.share_count, 0) * 5 +
        COALESCE(comp_stats.compare_count, 0) * 3
    ) as popularity_score
FROM products p
JOIN categories c ON p.category_id = c.id
LEFT JOIN (
    SELECT 
        product_id,
        COUNT(*) as favorite_count,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as recent_favorite_count
    FROM favorites
    GROUP BY product_id
) f_stats ON p.id = f_stats.product_id
LEFT JOIN (
    SELECT 
        product_id,
        COUNT(*) as browse_count,
        COUNT(DISTINCT user_id) as unique_browsers,
        AVG(browse_duration) as avg_browse_duration
    FROM browse_history
    WHERE browse_time >= DATE_SUB(NOW(), INTERVAL 90 DAY)
    GROUP BY product_id
) b_stats ON p.id = b_stats.product_id
LEFT JOIN (
    SELECT 
        target_id as product_id,
        COUNT(*) as share_count,
        SUM(click_count) as total_clicks
    FROM share_records
    WHERE share_type = 'product' AND share_time >= DATE_SUB(NOW(), INTERVAL 90 DAY)
    GROUP BY target_id
) s_stats ON p.id = s_stats.product_id
LEFT JOIN (
    SELECT 
        product_id,
        COUNT(*) as compare_count
    FROM product_compare
    WHERE added_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)
    GROUP BY product_id
) comp_stats ON p.id = comp_stats.product_id
WHERE p.status = 1;

-- 3. 创建智能推荐相关表

-- 用户偏好标签表
CREATE TABLE IF NOT EXISTS user_preference_tags (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    tag_type ENUM('category', 'price_range', 'brand', 'feature', 'source') NOT NULL,
    tag_value VARCHAR(100) NOT NULL,
    weight DECIMAL(5,2) DEFAULT 1.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_tag (user_id, tag_type, tag_value),
    INDEX idx_user_weight (user_id, weight DESC),
    INDEX idx_tag_type (tag_type),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT '用户偏好标签表';

-- 商品相似度表
CREATE TABLE IF NOT EXISTS product_similarity (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    product_id BIGINT NOT NULL,
    similar_product_id BIGINT NOT NULL,
    similarity_score DECIMAL(5,4) NOT NULL,
    similarity_type ENUM('category', 'price', 'tags', 'behavior') NOT NULL,
    calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_product_score (product_id, similarity_score DESC),
    INDEX idx_similar_product (similar_product_id),
    INDEX idx_similarity_type (similarity_type),
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (similar_product_id) REFERENCES products(id) ON DELETE CASCADE
) COMMENT '商品相似度表';

-- 4. 创建智能存储过程

-- 更新用户偏好标签
DELIMITER $$
CREATE PROCEDURE IF NOT EXISTS sp_update_user_preferences(IN p_user_id INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    
    -- 基于收藏更新分类偏好
    INSERT INTO user_preference_tags (user_id, tag_type, tag_value, weight)
    SELECT 
        p_user_id,
        'category',
        c.name,
        COUNT(*) * 0.5
    FROM favorites f
    JOIN products p ON f.product_id = p.id
    JOIN categories c ON p.category_id = c.id
    WHERE f.user_id = p_user_id
    AND f.created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)
    GROUP BY c.id, c.name
    ON DUPLICATE KEY UPDATE 
        weight = weight + VALUES(weight),
        updated_at = NOW();
    
    -- 基于浏览更新价格偏好
    INSERT INTO user_preference_tags (user_id, tag_type, tag_value, weight)
    SELECT 
        p_user_id,
        'price_range',
        CASE 
            WHEN p.price <= 100 THEN 'low'
            WHEN p.price <= 300 THEN 'medium'
            ELSE 'high'
        END,
        COUNT(*) * 0.2
    FROM browse_history bh
    JOIN products p ON bh.product_id = p.id
    WHERE bh.user_id = p_user_id
    AND bh.browse_time >= DATE_SUB(NOW(), INTERVAL 90 DAY)
    GROUP BY CASE 
        WHEN p.price <= 100 THEN 'low'
        WHEN p.price <= 300 THEN 'medium'
        ELSE 'high'
    END
    ON DUPLICATE KEY UPDATE 
        weight = weight + VALUES(weight),
        updated_at = NOW();
        
    -- 清理过期的偏好标签
    DELETE FROM user_preference_tags 
    WHERE user_id = p_user_id 
    AND updated_at < DATE_SUB(NOW(), INTERVAL 180 DAY);
    
END$$
DELIMITER ;

-- 计算商品相似度
DELIMITER $$
CREATE PROCEDURE IF NOT EXISTS sp_calculate_product_similarity(IN p_product_id BIGINT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    
    -- 清理旧的相似度数据
    DELETE FROM product_similarity WHERE product_id = p_product_id;
    
    -- 基于分类的相似度
    INSERT INTO product_similarity (product_id, similar_product_id, similarity_score, similarity_type)
    SELECT 
        p_product_id,
        p2.id,
        0.8,
        'category'
    FROM products p1
    JOIN products p2 ON p1.category_id = p2.category_id AND p1.id != p2.id
    WHERE p1.id = p_product_id AND p2.status = 1
    LIMIT 20;
    
    -- 基于价格的相似度
    INSERT INTO product_similarity (product_id, similar_product_id, similarity_score, similarity_type)
    SELECT 
        p_product_id,
        p2.id,
        GREATEST(0.1, 1 - ABS(p1.price - p2.price) / GREATEST(p1.price, p2.price)),
        'price'
    FROM products p1
    JOIN products p2 ON p1.id != p2.id
    WHERE p1.id = p_product_id 
    AND p2.status = 1
    AND ABS(p1.price - p2.price) / GREATEST(p1.price, p2.price) <= 0.5
    ORDER BY ABS(p1.price - p2.price)
    LIMIT 15;
    
END$$
DELIMITER ;

-- 5. 创建定时任务相关存储过程

-- 数据清理和优化
DELIMITER $$
CREATE PROCEDURE IF NOT EXISTS sp_daily_maintenance()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    
    -- 清理90天前的浏览历史
    DELETE FROM browse_history 
    WHERE browse_time < DATE_SUB(NOW(), INTERVAL 90 DAY);
    
    -- 清理无点击的分享记录（30天前）
    DELETE FROM share_records 
    WHERE share_time < DATE_SUB(NOW(), INTERVAL 30 DAY) 
    AND click_count = 0;
    
    -- 清理已关闭的客服会话（30天前）
    DELETE FROM customer_sessions 
    WHERE session_status = 'closed' 
    AND closed_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- 更新商品收藏数统计
    UPDATE products p SET favorite_count = (
        SELECT COUNT(*) FROM favorites f WHERE f.product_id = p.id
    );
    
    -- 优化表
    OPTIMIZE TABLE favorites, browse_history, product_compare, share_records;
    
    SELECT 'Daily maintenance completed' as result;
END$$
DELIMITER ;

-- 6. 创建触发器优化

-- 浏览历史触发器：自动更新商品浏览量
DELIMITER $$
CREATE TRIGGER IF NOT EXISTS tr_browse_history_insert 
AFTER INSERT ON browse_history
FOR EACH ROW
BEGIN
    UPDATE products 
    SET view_count = view_count + 1 
    WHERE id = NEW.product_id;
END$$
DELIMITER ;

-- 分享记录触发器：自动更新用户活跃时间
DELIMITER $$
CREATE TRIGGER IF NOT EXISTS tr_share_record_insert 
AFTER INSERT ON share_records
FOR EACH ROW
BEGIN
    UPDATE users 
    SET last_active_at = NOW() 
    WHERE id = NEW.user_id;
END$$
DELIMITER ;

-- 7. 创建性能监控视图

-- 系统性能监控视图
CREATE OR REPLACE VIEW v_system_performance AS
SELECT 
    'favorites' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as daily_new,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as weekly_new
FROM favorites
UNION ALL
SELECT 
    'browse_history' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN browse_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as daily_new,
    COUNT(CASE WHEN browse_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as weekly_new
FROM browse_history
UNION ALL
SELECT 
    'share_records' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN share_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as daily_new,
    COUNT(CASE WHEN share_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as weekly_new
FROM share_records
UNION ALL
SELECT 
    'customer_sessions' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as daily_new,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as weekly_new
FROM customer_sessions;

COMMIT;

-- 执行完成提示
SELECT '用户功能数据库优化完成！包括：高级索引、智能视图、推荐算法、性能监控' as message;
