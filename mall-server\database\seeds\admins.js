const bcrypt = require('bcryptjs');

module.exports = {
  async run(pool) {
    console.log('创建管理员种子数据...');
    
    const hashedPassword = await bcrypt.hash('admin123', 10);
    
    const sql = `
      INSERT INTO admins (username, password, nickname, role) 
      VALUES (?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE 
        password = VALUES(password),
        nickname = VALUES(nickname),
        role = VALUES(role)
    `;
    
    await pool.execute(sql, ['admin', hashedPassword, '系统管理员', 'super']);
    
    console.log('管理员种子数据创建完成');
  }
}; 