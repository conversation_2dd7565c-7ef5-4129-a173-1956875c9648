'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.bulkInsert('banners', [
      {
        title: '心洁茶叶 - 优质茶叶专卖',
        image_url: '/uploads/banners/banner1.jpg',
        link_url: '/pages/product-list/product-list?categoryId=1',
        sort_order: 1,
        status: 1,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        title: '大红袍 - 岩茶之王',
        image_url: '/uploads/banners/banner2.jpg',
        link_url: '/pages/product-detail/product-detail?id=1',
        sort_order: 2,
        status: 1,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        title: '水仙茶 - 清香雅韵',
        image_url: '/uploads/banners/banner3.jpg',
        link_url: '/pages/product-detail/product-detail?id=2',
        sort_order: 3,
        status: 1,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        title: '肉桂茶 - 醇厚回甘',
        image_url: '/uploads/banners/banner4.jpg',
        link_url: '/pages/product-detail/product-detail?id=3',
        sort_order: 4,
        status: 1,
        created_at: new Date(),
        updated_at: new Date()
      }
    ], {});
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('banners', null, {});
  }
}; 