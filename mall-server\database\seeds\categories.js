module.exports = {
  async run(pool) {
    console.log('创建分类种子数据...');
    
    const categories = [
      { name: '大红袍', description: '武夷岩茶大红袍', sort_order: 1 },
      { name: '水仙', description: '武夷岩茶水仙', sort_order: 2 },
      { name: '肉桂', description: '武夷岩茶肉桂', sort_order: 3 },
      { name: '白茶', description: '白茶系列', sort_order: 4 },
      { name: '花茶', description: '花茶系列', sort_order: 5 }
    ];
    
    for (const category of categories) {
      const sql = `
        INSERT INTO categories (name, description, sort_order) 
        VALUES (?, ?, ?)
        ON DUPLICATE KEY UPDATE 
          description = VALUES(description),
          sort_order = VALUES(sort_order)
      `;
      
      await pool.execute(sql, [category.name, category.description, category.sort_order]);
    }
    
    console.log('分类种子数据创建完成');
  }
}; 