'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.bulkInsert('permissions', [
      // 系统管理
      {
        name: '系统管理',
        code: 'system',
        type: 'menu',
        parent_id: 0,
        path: '/system',
        component: 'Layout',
        icon: 'setting',
        sort_order: 1,
        status: 1,
        created_at: new Date()
      },
      {
        name: '管理员管理',
        code: 'system:admin',
        type: 'menu',
        parent_id: 1,
        path: '/system/admin',
        component: 'system/admin/index',
        icon: 'user',
        sort_order: 1,
        status: 1,
        created_at: new Date()
      },
      {
        name: '角色管理',
        code: 'system:role',
        type: 'menu',
        parent_id: 1,
        path: '/system/role',
        component: 'system/role/index',
        icon: 'team',
        sort_order: 2,
        status: 1,
        created_at: new Date()
      },
      {
        name: '权限管理',
        code: 'system:permission',
        type: 'menu',
        parent_id: 1,
        path: '/system/permission',
        component: 'system/permission/index',
        icon: 'safety',
        sort_order: 3,
        status: 1,
        created_at: new Date()
      },
      {
        name: '系统配置',
        code: 'system:config',
        type: 'menu',
        parent_id: 1,
        path: '/system/config',
        component: 'system/config/index',
        icon: 'tool',
        sort_order: 4,
        status: 1,
        created_at: new Date()
      },
      {
        name: '操作日志',
        code: 'system:log',
        type: 'menu',
        parent_id: 1,
        path: '/system/log',
        component: 'system/log/index',
        icon: 'file-text',
        sort_order: 5,
        status: 1,
        created_at: new Date()
      },

      // 管理员管理权限
      {
        name: '管理员列表',
        code: 'system:admin:list',
        type: 'page',
        parent_id: 2,
        path: '',
        component: '',
        icon: '',
        sort_order: 1,
        status: 1,
        created_at: new Date()
      },
      {
        name: '添加管理员',
        code: 'system:admin:add',
        type: 'button',
        parent_id: 2,
        path: '',
        component: '',
        icon: '',
        sort_order: 2,
        status: 1,
        created_at: new Date()
      },
      {
        name: '编辑管理员',
        code: 'system:admin:edit',
        type: 'button',
        parent_id: 2,
        path: '',
        component: '',
        icon: '',
        sort_order: 3,
        status: 1,
        created_at: new Date()
      },
      {
        name: '删除管理员',
        code: 'system:admin:delete',
        type: 'button',
        parent_id: 2,
        path: '',
        component: '',
        icon: '',
        sort_order: 4,
        status: 1,
        created_at: new Date()
      },
      {
        name: '重置密码',
        code: 'system:admin:reset-password',
        type: 'button',
        parent_id: 2,
        path: '',
        component: '',
        icon: '',
        sort_order: 5,
        status: 1,
        created_at: new Date()
      },
      {
        name: '启用/禁用',
        code: 'system:admin:status',
        type: 'button',
        parent_id: 2,
        path: '',
        component: '',
        icon: '',
        sort_order: 6,
        status: 1,
        created_at: new Date()
      },

      // 商品管理
      {
        name: '商品管理',
        code: 'product',
        type: 'menu',
        parent_id: 0,
        path: '/product',
        component: 'Layout',
        icon: 'shopping',
        sort_order: 2,
        status: 1,
        created_at: new Date()
      },
      {
        name: '商品列表',
        code: 'product:list',
        type: 'menu',
        parent_id: 14,
        path: '/product/list',
        component: 'product/list/index',
        icon: 'table',
        sort_order: 1,
        status: 1,
        created_at: new Date()
      },
      {
        name: '添加商品',
        code: 'product:add',
        type: 'menu',
        parent_id: 14,
        path: '/product/add',
        component: 'product/add/index',
        icon: 'plus',
        sort_order: 2,
        status: 1,
        created_at: new Date()
      },
      {
        name: '商品分类',
        code: 'product:category',
        type: 'menu',
        parent_id: 14,
        path: '/product/category',
        component: 'product/category/index',
        icon: 'folder',
        sort_order: 3,
        status: 1,
        created_at: new Date()
      },

      // 订单管理
      {
        name: '订单管理',
        code: 'order',
        type: 'menu',
        parent_id: 0,
        path: '/order',
        component: 'Layout',
        icon: 'shopping-cart',
        sort_order: 3,
        status: 1,
        created_at: new Date()
      },
      {
        name: '订单列表',
        code: 'order:list',
        type: 'menu',
        parent_id: 18,
        path: '/order/list',
        component: 'order/list/index',
        icon: 'table',
        sort_order: 1,
        status: 1,
        created_at: new Date()
      },
      {
        name: '订单详情',
        code: 'order:detail',
        type: 'page',
        parent_id: 18,
        path: '',
        component: '',
        icon: '',
        sort_order: 2,
        status: 1,
        created_at: new Date()
      },
      {
        name: '订单发货',
        code: 'order:ship',
        type: 'button',
        parent_id: 18,
        path: '',
        component: '',
        icon: '',
        sort_order: 3,
        status: 1,
        created_at: new Date()
      },

      // 用户管理
      {
        name: '用户管理',
        code: 'user',
        type: 'menu',
        parent_id: 0,
        path: '/user',
        component: 'Layout',
        icon: 'user',
        sort_order: 4,
        status: 1,
        created_at: new Date()
      },
      {
        name: '用户列表',
        code: 'user:list',
        type: 'menu',
        parent_id: 22,
        path: '/user/list',
        component: 'user/list/index',
        icon: 'table',
        sort_order: 1,
        status: 1,
        created_at: new Date()
      },
      {
        name: '用户详情',
        code: 'user:detail',
        type: 'page',
        parent_id: 22,
        path: '',
        component: '',
        icon: '',
        sort_order: 2,
        status: 1,
        created_at: new Date()
      }
    ], {});
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('permissions', null, {});
  }
}; 