'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.bulkInsert('roles', [
      {
        name: '超级管理员',
        code: 'super_admin',
        description: '拥有所有权限的超级管理员',
        level: 1,
        status: 1,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '系统管理员',
        code: 'system_admin',
        description: '系统管理权限',
        level: 2,
        status: 1,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '运营管理员',
        code: 'operation_admin',
        description: '运营相关权限',
        level: 2,
        status: 1,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '商品管理员',
        code: 'product_admin',
        description: '商品管理权限',
        level: 3,
        status: 1,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '订单管理员',
        code: 'order_admin',
        description: '订单管理权限',
        level: 3,
        status: 1,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '客服人员',
        code: 'customer_service',
        description: '客服相关权限',
        level: 3,
        status: 1,
        created_at: new Date(),
        updated_at: new Date()
      }
    ], {});
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('roles', null, {});
  }
}; 