'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.bulkInsert('system_configs', [
      {
        config_key: 'password_policy',
        config_value: JSON.stringify({
          min_length: 8,
          require_uppercase: true,
          require_lowercase: true,
          require_number: true,
          require_special: true,
          expire_days: 90
        }),
        config_type: 'json',
        description: '密码策略配置',
        is_system: 1,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        config_key: 'login_policy',
        config_value: JSON.stringify({
          max_attempts: 5,
          lock_duration: 30,
          session_timeout: 1440,
          enable_2fa: false
        }),
        config_type: 'json',
        description: '登录策略配置',
        is_system: 1,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        config_key: 'system_info',
        config_value: JSON.stringify({
          name: '心洁茶叶后台管理系统',
          version: '1.0.0',
          description: '专业的茶叶商城后台管理系统'
        }),
        config_type: 'json',
        description: '系统信息',
        is_system: 1,
        created_at: new Date(),
        updated_at: new Date()
      }
    ], {});
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('system_configs', null, {});
  }
}; 