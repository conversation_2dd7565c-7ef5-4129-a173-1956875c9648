# 心洁茶叶商城 API 文档

## 概述

心洁茶叶商城提供完整的RESTful API接口，支持微信小程序前端和管理后台。API采用JWT认证，支持缓存机制，提供完整的用户管理、商品管理、订单管理等功能。

## 基础信息

- **基础URL**: `https://api.xinjie-tea.com/api`
- **开发环境**: `http://localhost:4000/api`
- **API版本**: v1.0.0
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据
  }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误信息",
  "error": "详细错误信息（仅开发环境）"
}
```

## 通用状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

## 认证

### 获取Token
```http
POST /api/front/user/login
Content-Type: application/json

{
  "code": "微信登录code",
  "userInfo": {
    "nickName": "用户昵称",
    "avatarUrl": "头像URL"
  }
}
```

### 使用Token
```http
Authorization: Bearer <your-jwt-token>
```

## 前端API接口

### 用户相关

#### 用户登录
```http
POST /api/front/user/login
```

**请求参数:**
```json
{
  "code": "string", // 微信登录code
  "userInfo": {
    "nickName": "string",
    "avatarUrl": "string"
  }
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "jwt-token",
    "user": {
      "id": 1,
      "openid": "wx_openid",
      "nickName": "用户昵称",
      "avatarUrl": "头像URL",
      "phone": "手机号",
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

#### 用户注册
```http
POST /api/front/user/register
```

#### 获取用户信息
```http
GET /api/front/user/profile
Authorization: Bearer <token>
```

#### 更新用户信息
```http
PUT /api/front/user/profile
Authorization: Bearer <token>
```

### 商品相关

#### 获取商品列表
```http
GET /api/front/product/list?page=1&limit=10&categoryId=1&keyword=茶叶
```

**查询参数:**
- `page`: 页码（默认1）
- `limit`: 每页数量（默认10）
- `categoryId`: 分类ID（可选）
- `keyword`: 搜索关键词（可选）
- `sort`: 排序方式（price_asc, price_desc, sales_desc）

**响应示例:**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "龙井茶",
        "price": 99.00,
        "originalPrice": 128.00,
        "image": "商品图片URL",
        "sales": 100,
        "stock": 50,
        "categoryId": 1,
        "categoryName": "绿茶"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "totalPages": 10
    }
  }
}
```

#### 获取商品详情
```http
GET /api/front/product/detail/:id
```

#### 获取商品评价
```http
GET /api/front/product/reviews/:id?page=1&limit=10
```

### 分类相关

#### 获取分类列表
```http
GET /api/front/category/list
```

**响应示例:**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "绿茶",
      "image": "分类图片URL",
      "productCount": 50
    }
  ]
}
```

### 轮播图相关

#### 获取轮播图列表
```http
GET /api/front/banner/list
```

### 购物车相关

#### 添加到购物车
```http
POST /api/front/cart/add
Authorization: Bearer <token>
```

**请求参数:**
```json
{
  "productId": 1,
  "quantity": 2
}
```

#### 获取购物车列表
```http
GET /api/front/cart/list
Authorization: Bearer <token>
```

#### 更新购物车商品数量
```http
PUT /api/front/cart/update
Authorization: Bearer <token>
```

#### 删除购物车商品
```http
DELETE /api/front/cart/remove
Authorization: Bearer <token>
```

### 订单相关

#### 创建订单
```http
POST /api/front/order/create
Authorization: Bearer <token>
```

**请求参数:**
```json
{
  "items": [
    {
      "productId": 1,
      "quantity": 2
    }
  ],
  "addressId": 1,
  "remark": "订单备注"
}
```

#### 获取订单列表
```http
GET /api/front/order/list?page=1&limit=10&status=pending
Authorization: Bearer <token>
```

#### 获取订单详情
```http
GET /api/front/order/detail/:id
Authorization: Bearer <token>
```

#### 取消订单
```http
PUT /api/front/order/cancel/:id
Authorization: Bearer <token>
```

#### 确认收货
```http
PUT /api/front/order/confirm/:id
Authorization: Bearer <token>
```

### 地址相关

#### 获取地址列表
```http
GET /api/front/address/list
Authorization: Bearer <token>
```

#### 添加地址
```http
POST /api/front/address/add
Authorization: Bearer <token>
```

#### 更新地址
```http
PUT /api/front/address/update/:id
Authorization: Bearer <token>
```

#### 删除地址
```http
DELETE /api/front/address/delete/:id
Authorization: Bearer <token>
```

#### 设置默认地址
```http
PUT /api/front/address/default/:id
Authorization: Bearer <token>
```

### 支付相关

#### 创建支付订单
```http
POST /api/front/payment/create
Authorization: Bearer <token>
```

**请求参数:**
```json
{
  "orderId": 1,
  "paymentMethod": "wechat" // wechat, alipay
}
```

#### 查询支付状态
```http
GET /api/front/payment/status/:orderId
Authorization: Bearer <token>
```

#### 申请退款
```http
POST /api/front/payment/refund
Authorization: Bearer <token>
```

### 搜索相关

#### 搜索商品
```http
GET /api/front/search?keyword=茶叶&page=1&limit=10
```

## 管理后台API接口

### 认证相关

#### 管理员登录
```http
POST /api/admin/auth/login
```

**请求参数:**
```json
{
  "username": "admin",
  "password": "password"
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "jwt-token",
    "admin": {
      "id": 1,
      "username": "admin",
      "role": "super_admin",
      "lastLoginAt": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

### 用户管理

#### 获取用户列表
```http
GET /api/admin/user/list?page=1&limit=10&keyword=用户
Authorization: Bearer <admin-token>
```

#### 获取用户详情
```http
GET /api/admin/user/detail/:id
Authorization: Bearer <admin-token>
```

#### 更新用户状态
```http
PUT /api/admin/user/status/:id
Authorization: Bearer <admin-token>
```

### 商品管理

#### 获取商品列表
```http
GET /api/admin/product/list?page=1&limit=10&categoryId=1&status=active
Authorization: Bearer <admin-token>
```

#### 创建商品
```http
POST /api/admin/product/create
Authorization: Bearer <admin-token>
```

**请求参数:**
```json
{
  "name": "商品名称",
  "description": "商品描述",
  "price": 99.00,
  "originalPrice": 128.00,
  "categoryId": 1,
  "stock": 100,
  "images": ["图片URL1", "图片URL2"],
  "status": "active"
}
```

#### 更新商品
```http
PUT /api/admin/product/update/:id
Authorization: Bearer <admin-token>
```

#### 删除商品
```http
DELETE /api/admin/product/delete/:id
Authorization: Bearer <admin-token>
```

#### 更新商品状态
```http
PUT /api/admin/product/status/:id
Authorization: Bearer <admin-token>
```

### 订单管理

#### 获取订单列表
```http
GET /api/admin/order/list?page=1&limit=10&status=pending
Authorization: Bearer <admin-token>
```

#### 获取订单详情
```http
GET /api/admin/order/detail/:id
Authorization: Bearer <admin-token>
```

#### 更新订单状态
```http
PUT /api/admin/order/status/:id
Authorization: Bearer <admin-token>
```

#### 发货
```http
PUT /api/admin/order/ship/:id
Authorization: Bearer <admin-token>
```

### 分类管理

#### 获取分类列表
```http
GET /api/admin/category/list
Authorization: Bearer <admin-token>
```

#### 创建分类
```http
POST /api/admin/category/create
Authorization: Bearer <admin-token>
```

#### 更新分类
```http
PUT /api/admin/category/update/:id
Authorization: Bearer <admin-token>
```

#### 删除分类
```http
DELETE /api/admin/category/delete/:id
Authorization: Bearer <admin-token>
```

### 轮播图管理

#### 获取轮播图列表
```http
GET /api/admin/banner/list
Authorization: Bearer <admin-token>
```

#### 创建轮播图
```http
POST /api/admin/banner/create
Authorization: Bearer <admin-token>
```

#### 更新轮播图
```http
PUT /api/admin/banner/update/:id
Authorization: Bearer <admin-token>
```

#### 删除轮播图
```http
DELETE /api/admin/banner/delete/:id
Authorization: Bearer <admin-token>
```

### 统计相关

#### 获取总体统计
```http
GET /api/admin/statistics/overview
Authorization: Bearer <admin-token>
```

**响应示例:**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "totalUsers": 1000,
    "totalOrders": 500,
    "totalRevenue": 50000.00,
    "todayOrders": 10,
    "todayRevenue": 1000.00,
    "pendingOrders": 5
  }
}
```

#### 获取销售统计
```http
GET /api/admin/statistics/sales?period=7d
Authorization: Bearer <admin-token>
```

#### 获取用户统计
```http
GET /api/admin/statistics/users?period=30d
Authorization: Bearer <admin-token>
```

### 系统设置

#### 获取系统设置
```http
GET /api/admin/settings
Authorization: Bearer <admin-token>
```

#### 更新系统设置
```http
PUT /api/admin/settings
Authorization: Bearer <admin-token>
```

### 文件上传

#### 上传图片
```http
POST /api/admin/upload/image
Authorization: Bearer <admin-token>
Content-Type: multipart/form-data
```

**请求参数:**
- `file`: 图片文件

**响应示例:**
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "url": "https://cdn.xinjie-tea.com/images/filename.jpg",
    "filename": "filename.jpg",
    "size": 1024000
  }
}
```

### 缓存管理

#### 获取缓存统计
```http
GET /api/admin/cache/stats
Authorization: Bearer <admin-token>
```

#### 清除缓存
```http
POST /api/admin/cache/clear
Authorization: Bearer <admin-token>
```

**请求参数:**
```json
{
  "type": "all" // all, category, banner, product
}
```

## 限流规则

- **普通用户**: 每分钟100次请求
- **认证用户**: 每分钟200次请求
- **管理员**: 每分钟500次请求

## 缓存策略

- **分类列表**: 缓存1小时
- **轮播图**: 缓存1小时
- **商品列表**: 缓存5分钟
- **商品详情**: 缓存10分钟

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 10001 | 参数验证失败 |
| 10002 | 用户不存在 |
| 10003 | 密码错误 |
| 10004 | Token无效 |
| 10005 | Token过期 |
| 10006 | 权限不足 |
| 10007 | 商品不存在 |
| 10008 | 库存不足 |
| 10009 | 订单不存在 |
| 10010 | 订单状态错误 |
| 20001 | 微信登录失败 |
| 20002 | 支付创建失败 |
| 20003 | 支付回调验证失败 |
| 30001 | 文件上传失败 |
| 30002 | 文件格式不支持 |
| 30003 | 文件大小超限 |

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持用户管理、商品管理、订单管理
- 支持微信支付、支付宝支付
- 支持文件上传和CDN
- 支持缓存机制
- 支持限流保护 