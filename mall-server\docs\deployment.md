# 心洁茶叶商城部署文档

## 概述

本文档详细说明了心洁茶叶商城后端API服务的部署流程，包括环境配置、服务器部署、Nginx配置、SSL证书设置、监控告警等。

## 系统要求

### 服务器配置
- **操作系统**: Ubuntu 20.04+ / CentOS 7+ / Debian 10+
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 50GB以上SSD
- **网络**: 带宽10Mbps以上

### 软件要求
- **Node.js**: 16.0.0+
- **MySQL**: 8.0+
- **Redis**: 6.0+
- **Nginx**: 1.18+
- **PM2**: 5.0+

## 环境准备

### 1. 更新系统
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS
sudo yum update -y
```

### 2. 安装Node.js
```bash
# 使用NodeSource仓库
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

### 3. 安装MySQL
```bash
# Ubuntu/Debian
sudo apt install mysql-server -y

# CentOS
sudo yum install mysql-server -y

# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation
```

### 4. 安装Redis
```bash
# Ubuntu/Debian
sudo apt install redis-server -y

# CentOS
sudo yum install redis -y

# 启动Redis服务
sudo systemctl start redis
sudo systemctl enable redis
```

### 5. 安装Nginx
```bash
# Ubuntu/Debian
sudo apt install nginx -y

# CentOS
sudo yum install nginx -y

# 启动Nginx服务
sudo systemctl start nginx
sudo systemctl enable nginx
```

### 6. 安装PM2
```bash
sudo npm install -g pm2
```

## 项目部署

### 1. 克隆项目
```bash
# 创建项目目录
sudo mkdir -p /var/www/xinjie-mall
sudo chown $USER:$USER /var/www/xinjie-mall

# 克隆项目
cd /var/www/xinjie-mall
git clone <repository-url> .
```

### 2. 安装依赖
```bash
cd mall-server
npm install --production
```

### 3. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑环境配置
nano .env
```

**环境配置示例:**
```env
# 应用配置
NODE_ENV=production
PORT=4000
API_PREFIX=/api

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=xinjie_mall
DB_USER=xinjie_user
DB_PASSWORD=your_secure_password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=7d

# 微信小程序配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# 微信支付配置
WECHAT_MCH_ID=your_mch_id
WECHAT_API_KEY=your_api_key
WECHAT_NOTIFY_URL=https://api.xinjie-tea.com/api/front/payment/wechat/notify

# 支付宝配置
ALIPAY_APP_ID=your_alipay_app_id
ALIPAY_PRIVATE_KEY=your_private_key
ALIPAY_PUBLIC_KEY=your_public_key
ALIPAY_NOTIFY_URL=https://api.xinjie-tea.com/api/front/payment/alipay/notify

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760

# 日志配置
LOG_LEVEL=info
LOG_PATH=./logs

# CDN配置
CDN_ENABLED=true
CDN_DOMAIN=https://cdn.xinjie-tea.com
CDN_IMAGE_PATH=/images

# 监控配置
MONITOR_ENABLED=true
MONITOR_PORT=4001
MONITOR_USERNAME=admin
MONITOR_PASSWORD=your_monitor_password
```

### 4. 数据库初始化
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE xinjie_mall CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'xinjie_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON xinjie_mall.* TO 'xinjie_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# 运行数据库迁移
npm run migrate

# 初始化基础数据
npm run seed
```

### 5. 创建必要目录
```bash
# 创建上传目录
mkdir -p uploads
mkdir -p logs

# 设置权限
chmod 755 uploads
chmod 755 logs
```

## Nginx配置

### 1. 创建Nginx配置文件
```bash
sudo nano /etc/nginx/sites-available/xinjie-mall
```

**配置文件内容:**
```nginx
server {
    listen 80;
    server_name api.xinjie-tea.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.xinjie-tea.com;
    
    # SSL证书配置
    ssl_certificate /etc/letsencrypt/live/api.xinjie-tea.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.xinjie-tea.com/privkey.pem;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 客户端最大请求体大小
    client_max_body_size 10M;
    
    # 日志配置
    access_log /var/log/nginx/xinjie-mall-access.log;
    error_log /var/log/nginx/xinjie-mall-error.log;
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:4000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 静态文件服务
    location /uploads/ {
        alias /var/www/xinjie-mall/mall-server/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location /images/ {
        alias /var/www/xinjie-mall/xinjie-mall-miniprogram/images/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 监控面板
    location /monitor/ {
        proxy_pass http://127.0.0.1:4001/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:4000/api/health;
        access_log off;
    }
}
```

### 2. 启用站点配置
```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/xinjie-mall /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

## SSL证书配置

### 1. 安装Certbot
```bash
# Ubuntu/Debian
sudo apt install certbot python3-certbot-nginx -y

# CentOS
sudo yum install certbot python3-certbot-nginx -y
```

### 2. 获取SSL证书
```bash
sudo certbot --nginx -d api.xinjie-tea.com
```

### 3. 自动续期
```bash
# 测试自动续期
sudo certbot renew --dry-run

# 添加到crontab
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

## PM2部署配置

### 1. 创建PM2配置文件
```bash
nano ecosystem.config.js
```

**配置文件内容:**
```javascript
module.exports = {
  apps: [
    {
      name: 'xinjie-mall-api',
      script: 'app-https.js',
      cwd: '/var/www/xinjie-mall/mall-server',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 4000
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 4000
      },
      error_file: './logs/err.log',
      out_file: './logs/out.log',
      log_file: './logs/combined.log',
      time: true,
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=1024',
      watch: false,
      ignore_watch: ['node_modules', 'logs', 'uploads'],
      merge_logs: true,
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z'
    },
    {
      name: 'xinjie-mall-monitor',
      script: 'scripts/monitor-manager.js',
      cwd: '/var/www/xinjie-mall/mall-server',
      instances: 1,
      env: {
        NODE_ENV: 'production'
      },
      error_file: './logs/monitor-err.log',
      out_file: './logs/monitor-out.log',
      log_file: './logs/monitor-combined.log',
      time: true,
      watch: false,
      ignore_watch: ['node_modules', 'logs'],
      merge_logs: true,
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z'
    }
  ]
};
```

### 2. 启动应用
```bash
# 启动应用
pm2 start ecosystem.config.js --env production

# 保存PM2配置
pm2 save

# 设置开机自启
pm2 startup
```

### 3. PM2常用命令
```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs xinjie-mall-api

# 重启应用
pm2 restart xinjie-mall-api

# 停止应用
pm2 stop xinjie-mall-api

# 删除应用
pm2 delete xinjie-mall-api
```

## 监控告警系统

### 1. 启动监控系统
```bash
# 监控系统已通过PM2启动，也可以手动启动
cd /var/www/xinjie-mall/mall-server
node scripts/monitor-manager.js
```

### 2. 访问监控面板
- **URL**: https://api.xinjie-tea.com/monitor/
- **用户名**: admin
- **密码**: 在环境配置中设置的MONITOR_PASSWORD

### 3. 监控功能
- 系统资源监控（CPU、内存、磁盘、网络）
- 应用健康检查
- 数据库连接监控
- Redis连接监控
- 实时告警通知
- 日志查看

## 备份策略

### 1. 数据库备份
```bash
# 创建备份脚本
nano /var/www/xinjie-mall/backup-db.sh
```

**备份脚本内容:**
```bash
#!/bin/bash
BACKUP_DIR="/var/backups/xinjie-mall"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="xinjie_mall"
DB_USER="xinjie_user"
DB_PASS="your_secure_password"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/db_backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/db_backup_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +7 -delete

echo "Database backup completed: db_backup_$DATE.sql.gz"
```

```bash
# 设置执行权限
chmod +x /var/www/xinjie-mall/backup-db.sh

# 添加到crontab（每天凌晨2点备份）
crontab -e
# 添加以下行
0 2 * * * /var/www/xinjie-mall/backup-db.sh
```

### 2. 文件备份
```bash
# 创建文件备份脚本
nano /var/www/xinjie-mall/backup-files.sh
```

**文件备份脚本内容:**
```bash
#!/bin/bash
BACKUP_DIR="/var/backups/xinjie-mall"
DATE=$(date +%Y%m%d_%H%M%S)
SOURCE_DIR="/var/www/xinjie-mall"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份上传文件
tar -czf $BACKUP_DIR/uploads_backup_$DATE.tar.gz -C $SOURCE_DIR/mall-server uploads

# 删除7天前的备份
find $BACKUP_DIR -name "uploads_backup_*.tar.gz" -mtime +7 -delete

echo "Files backup completed: uploads_backup_$DATE.tar.gz"
```

```bash
# 设置执行权限
chmod +x /var/www/xinjie-mall/backup-files.sh

# 添加到crontab（每天凌晨3点备份）
crontab -e
# 添加以下行
0 3 * * * /var/www/xinjie-mall/backup-files.sh
```

## 性能优化

### 1. 数据库优化
```sql
-- 优化MySQL配置
SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB
SET GLOBAL innodb_log_file_size = 268435456; -- 256MB
SET GLOBAL max_connections = 200;
SET GLOBAL query_cache_size = 67108864; -- 64MB
```

### 2. Redis优化
```bash
# 编辑Redis配置
sudo nano /etc/redis/redis.conf
```

**优化配置:**
```conf
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 3. Nginx优化
```bash
# 编辑Nginx配置
sudo nano /etc/nginx/nginx.conf
```

**优化配置:**
```nginx
worker_processes auto;
worker_connections 1024;
keepalive_timeout 65;
gzip on;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
```

## 安全配置

### 1. 防火墙配置
```bash
# 安装UFW
sudo apt install ufw -y

# 配置防火墙规则
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### 2. 系统安全
```bash
# 禁用root登录
sudo nano /etc/ssh/sshd_config
# 设置 PermitRootLogin no

# 重启SSH服务
sudo systemctl restart ssh
```

### 3. 定期安全更新
```bash
# 添加到crontab
crontab -e
# 添加以下行（每周日凌晨2点更新）
0 2 * * 0 /usr/bin/apt update && /usr/bin/apt upgrade -y
```

## 故障排查

### 1. 查看应用日志
```bash
# PM2日志
pm2 logs xinjie-mall-api

# Nginx日志
sudo tail -f /var/log/nginx/xinjie-mall-error.log

# 系统日志
sudo journalctl -u nginx -f
```

### 2. 检查服务状态
```bash
# 检查PM2状态
pm2 status

# 检查Nginx状态
sudo systemctl status nginx

# 检查MySQL状态
sudo systemctl status mysql

# 检查Redis状态
sudo systemctl status redis
```

### 3. 性能监控
```bash
# 查看系统资源
htop

# 查看磁盘使用
df -h

# 查看内存使用
free -h

# 查看网络连接
netstat -tulpn
```

## 更新部署

### 1. 代码更新
```bash
# 进入项目目录
cd /var/www/xinjie-mall

# 拉取最新代码
git pull origin main

# 安装依赖
cd mall-server
npm install --production

# 运行数据库迁移
npm run migrate

# 重启应用
pm2 restart xinjie-mall-api
```

### 2. 回滚操作
```bash
# 查看PM2历史
pm2 logs xinjie-mall-api --lines 100

# 回滚到上一个版本
git reset --hard HEAD~1
pm2 restart xinjie-mall-api
```

## 联系支持

如遇到部署问题，请联系技术支持：
- **邮箱**: <EMAIL>
- **电话**: 400-xxx-xxxx
- **工作时间**: 周一至周五 9:00-18:00 