# 心洁茶叶商城开发文档

## 概述

本文档为心洁茶叶商城项目的开发指南，包含开发环境搭建、代码规范、测试流程等内容。

## 开发环境搭建

### 1. 环境要求

#### 1.1 基础环境
- **Node.js**: 18.x 或更高版本
- **npm**: 9.x 或更高版本
- **Git**: 2.x 或更高版本
- **MySQL**: 8.0 或更高版本
- **Redis**: 6.0 或更高版本

#### 1.2 开发工具推荐
- **IDE**: VS Code / WebStorm
- **数据库管理**: MySQL Workbench / Navicat
- **API测试**: Postman / Insomnia
- **版本控制**: Git

### 2. 项目结构

```
xinjie-tea/
├── mall-server/                 # 后端API服务
│   ├── src/                     # 源代码
│   │   ├── config/             # 配置文件
│   │   ├── controllers/        # 控制器
│   │   ├── middleware/         # 中间件
│   │   ├── models/             # 数据模型
│   │   ├── routes/             # 路由
│   │   ├── services/           # 服务层
│   │   └── utils/              # 工具函数
│   ├── docs/                   # 文档
│   ├── logs/                   # 日志文件
│   ├── uploads/                # 上传文件
│   └── tests/                  # 测试文件
├── xinjie.mall-admin/          # 管理后台
│   ├── src/                    # React源代码
│   ├── public/                 # 静态资源
│   └── dist/                   # 构建输出
└── xinjie-mall-miniprogram/    # 微信小程序
    ├── pages/                  # 页面文件
    ├── components/             # 组件
    ├── utils/                  # 工具函数
    └── config/                 # 配置文件
```

### 3. 环境配置

#### 3.1 克隆项目
```bash
git clone https://github.com/your-repo/xinjie-tea.git
cd xinjie-tea
```

#### 3.2 安装依赖
```bash
# 后端依赖
cd mall-server
npm install

# 管理后台依赖
cd ../xinjie.mall-admin
npm install

# 小程序依赖（如果需要）
cd ../xinjie-mall-miniprogram
# 小程序通常不需要npm install
```

#### 3.3 数据库配置
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE xinjie_mall CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入数据库结构
mysql -u root -p xinjie_mall < 数据库建表语句.sql
```

#### 3.4 环境变量配置
```bash
# 复制环境变量模板
cd mall-server
cp .env.example .env

# 编辑环境变量
vim .env
```

```env
# 开发环境配置
NODE_ENV=development
PORT=4000

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=xinjie_mall
DB_USER=root
DB_PASSWORD=

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT配置
JWT_SECRET=dev_jwt_secret_key
JWT_EXPIRES_IN=24h

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=5242880

# 日志配置
LOG_LEVEL=debug
LOG_PATH=./logs
```

### 4. 启动开发环境

#### 4.1 启动后端服务
```bash
cd mall-server
npm run dev
```

#### 4.2 启动管理后台
```bash
cd xinjie.mall-admin
npm run dev
```

#### 4.3 启动小程序
- 使用微信开发者工具打开 `xinjie-mall-miniprogram` 目录
- 配置AppID和开发环境

## 代码规范

### 1. JavaScript规范

#### 1.1 命名规范
```javascript
// 变量和函数使用驼峰命名
const userName = 'admin';
const getUserInfo = () => {};

// 常量使用大写
const API_BASE_URL = 'http://localhost:4000/api';

// 类名使用大驼峰
class UserService {}

// 文件名使用小写和连字符
// user-service.js
// product-controller.js
```

#### 1.2 代码格式
```javascript
// 使用2个空格缩进
function example() {
  const data = {
    name: 'test',
    value: 123
  };
  
  return data;
}

// 使用分号结尾
const result = example();

// 使用单引号
const message = 'Hello World';

// 对象属性简写
const name = 'admin';
const user = { name };
```

#### 1.3 注释规范
```javascript
/**
 * 用户服务类
 * @class UserService
 */
class UserService {
  /**
   * 根据ID获取用户信息
   * @param {number} id - 用户ID
   * @returns {Promise<Object>} 用户信息
   */
  async getUserById(id) {
    // 实现逻辑
  }
}
```

### 2. 数据库规范

#### 2.1 表命名
```sql
-- 使用下划线命名，全小写
-- 用户表
CREATE TABLE users (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 订单表
CREATE TABLE orders (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT NOT NULL,
  order_no VARCHAR(50) NOT NULL,
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### 2.2 字段命名
```sql
-- 主键统一使用id
-- 外键使用表名_id
-- 时间字段使用_at后缀
-- 状态字段使用status
CREATE TABLE products (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  category_id BIGINT NOT NULL,
  name VARCHAR(100) NOT NULL,
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 3. API规范

#### 3.1 RESTful设计
```javascript
// 资源使用名词，复数形式
GET    /api/products          // 获取商品列表
GET    /api/products/:id      // 获取单个商品
POST   /api/products          // 创建商品
PUT    /api/products/:id      // 更新商品
DELETE /api/products/:id      // 删除商品
```

#### 3.2 响应格式
```javascript
// 成功响应
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "name": "商品名称"
  }
}

// 错误响应
{
  "code": 400,
  "message": "参数错误",
  "errors": [
    {
      "field": "name",
      "message": "商品名称不能为空"
    }
  ]
}
```

### 4. 错误处理

#### 4.1 统一错误处理
```javascript
// 创建自定义错误类
class AppError extends Error {
  constructor(message, statusCode = 500) {
    super(message);
    this.statusCode = statusCode;
    this.name = this.constructor.name;
  }
}

// 在中间件中统一处理
app.use((err, req, res, next) => {
  const statusCode = err.statusCode || 500;
  const message = err.message || '服务器内部错误';
  
  res.status(statusCode).json({
    code: statusCode,
    message,
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
});
```

## 开发流程

### 1. 功能开发流程

#### 1.1 需求分析
- 明确功能需求
- 设计API接口
- 确定数据结构

#### 1.2 开发实现
```bash
# 1. 创建功能分支
git checkout -b feature/user-management

# 2. 开发功能
# - 创建数据模型
# - 实现服务层逻辑
# - 编写控制器
# - 配置路由

# 3. 编写测试
npm test

# 4. 提交代码
git add .
git commit -m "feat: 添加用户管理功能"

# 5. 推送分支
git push origin feature/user-management
```

#### 1.3 代码审查
- 创建Pull Request
- 代码审查
- 修改反馈
- 合并代码

### 2. 数据库变更流程

#### 2.1 创建迁移文件
```javascript
// migrations/001_add_user_table.js
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('users', {
      id: {
        type: Sequelize.BIGINT,
        primaryKey: true,
        autoIncrement: true
      },
      username: {
        type: Sequelize.STRING(50),
        allowNull: false,
        unique: true
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('users');
  }
};
```

#### 2.2 执行迁移
```bash
# 执行迁移
npm run migrate

# 回滚迁移
npm run migrate:undo
```

### 3. 测试流程

#### 3.1 单元测试
```javascript
// tests/unit/user-service.test.js
const UserService = require('../../src/services/user');

describe('UserService', () => {
  describe('getUserById', () => {
    it('should return user when user exists', async () => {
      const user = await UserService.getUserById(1);
      expect(user).toBeDefined();
      expect(user.id).toBe(1);
    });

    it('should return null when user not exists', async () => {
      const user = await UserService.getUserById(999);
      expect(user).toBeNull();
    });
  });
});
```

#### 3.2 集成测试
```javascript
// tests/integration/user-api.test.js
const request = require('supertest');
const app = require('../../src/app');

describe('User API', () => {
  describe('GET /api/users/:id', () => {
    it('should return user when user exists', async () => {
      const response = await request(app)
        .get('/api/users/1')
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.data.id).toBe(1);
    });
  });
});
```

#### 3.3 运行测试
```bash
# 运行所有测试
npm test

# 运行单元测试
npm run test:unit

# 运行集成测试
npm run test:integration

# 生成测试覆盖率报告
npm run test:coverage
```

## 调试技巧

### 1. 后端调试

#### 1.1 日志调试
```javascript
// 使用不同级别的日志
const logger = require('../utils/logger');

logger.debug('调试信息');
logger.info('一般信息');
logger.warn('警告信息');
logger.error('错误信息');
```

#### 1.2 断点调试
```javascript
// 使用debugger语句
function processData(data) {
  debugger; // 浏览器或Node.js调试器会在这里暂停
  return data.map(item => item.id);
}
```

#### 1.3 API调试
```bash
# 使用curl测试API
curl -X GET http://localhost:4000/api/users/1

# 使用Postman测试API
# 导入API文档到Postman进行测试
```

### 2. 前端调试

#### 2.1 小程序调试
- 使用微信开发者工具的调试面板
- 查看Console日志
- 使用Network面板查看请求
- 使用Storage面板查看存储

#### 2.2 React调试
- 使用React Developer Tools
- 使用浏览器开发者工具
- 使用Redux DevTools（如果使用Redux）

### 3. 数据库调试

#### 3.1 查询调试
```sql
-- 开启查询日志
SET GLOBAL general_log = 'ON';
SET GLOBAL log_output = 'TABLE';

-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';
```

#### 3.2 性能分析
```sql
-- 分析查询执行计划
EXPLAIN SELECT * FROM users WHERE username = 'admin';

-- 查看表状态
SHOW TABLE STATUS LIKE 'users';
```

## 性能优化

### 1. 数据库优化

#### 1.1 索引优化
```sql
-- 为常用查询字段添加索引
CREATE INDEX idx_username ON users(username);
CREATE INDEX idx_email ON users(email);
CREATE INDEX idx_created_at ON users(created_at);

-- 复合索引
CREATE INDEX idx_user_status ON users(user_id, status);
```

#### 1.2 查询优化
```javascript
// 避免N+1查询问题
// 错误示例
const users = await User.findAll();
for (const user of users) {
  const profile = await user.getProfile(); // N+1查询
}

// 正确示例
const users = await User.findAll({
  include: [Profile] // 预加载关联数据
});
```

### 2. 缓存优化

#### 2.1 Redis缓存
```javascript
// 缓存热点数据
const cacheKey = `user:${userId}`;
let user = await redis.get(cacheKey);

if (!user) {
  user = await User.findByPk(userId);
  await redis.setex(cacheKey, 3600, JSON.stringify(user));
}
```

#### 2.2 应用缓存
```javascript
// 使用内存缓存
const NodeCache = require('node-cache');
const cache = new NodeCache({ stdTTL: 600 });

function getCachedData(key) {
  let data = cache.get(key);
  if (!data) {
    data = fetchDataFromDatabase();
    cache.set(key, data);
  }
  return data;
}
```

### 3. 前端优化

#### 3.1 图片优化
```javascript
// 使用CDN和图片压缩
const imageUrl = `https://cdn.example.com/images/${imageId}?w=300&h=300&q=80`;
```

#### 3.2 代码分割
```javascript
// React懒加载
const UserList = React.lazy(() => import('./UserList'));

// 小程序分包
// app.json
{
  "subpackages": [
    {
      "root": "pages/user",
      "pages": ["list", "detail"]
    }
  ]
}
```

## 部署相关

### 1. 环境配置

#### 1.1 生产环境变量
```env
NODE_ENV=production
PORT=4000

# 数据库配置
DB_HOST=production-db-host
DB_PORT=3306
DB_NAME=xinjie_mall
DB_USER=prod_user
DB_PASSWORD=prod_password

# Redis配置
REDIS_HOST=production-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=prod_redis_password

# JWT配置
JWT_SECRET=production_jwt_secret_key
JWT_EXPIRES_IN=24h

# CDN配置
CDN_ENABLED=true
CDN_DOMAIN=https://cdn.xinjie-tea.com
```

#### 1.2 构建配置
```bash
# 后端构建
cd mall-server
npm run build

# 前端构建
cd ../xinjie.mall-admin
npm run build
```

### 2. 部署检查清单

- [ ] 环境变量配置正确
- [ ] 数据库连接正常
- [ ] Redis连接正常
- [ ] 文件上传目录权限正确
- [ ] 日志目录权限正确
- [ ] SSL证书配置正确
- [ ] 防火墙配置正确
- [ ] 监控和告警配置正确

## 常见问题

### 1. 开发环境问题

#### 1.1 端口占用
```bash
# 查看端口占用
netstat -tlnp | grep :4000

# 杀死占用进程
kill -9 <PID>
```

#### 1.2 数据库连接失败
```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 检查数据库配置
mysql -u root -p -h localhost
```

#### 1.3 依赖安装失败
```bash
# 清除npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install
```

### 2. 代码问题

#### 2.1 ESLint错误
```bash
# 自动修复
npm run lint:fix

# 查看具体错误
npm run lint
```

#### 2.2 测试失败
```bash
# 运行单个测试文件
npm test -- --testPathPattern=user-service.test.js

# 查看详细错误信息
npm test -- --verbose
```

## 联系信息

- **技术负责人**: <EMAIL>
- **开发团队**: <EMAIL>
- **技术支持**: <EMAIL>

---

## 更新日志

### v1.0.0 (2025-01-01)
- 初始开发文档
- 包含完整的开发环境搭建指南
- 添加代码规范和最佳实践
- 包含测试和调试指南 