# 心洁茶叶商城监控系统文档

## 概述

心洁茶叶商城监控系统是一个完整的监控告警解决方案，包含系统监控、应用监控、Web仪表板和告警通知功能。该系统不会对现有的功能和API产生任何影响，完全独立运行。

## 系统架构

### 监控组件

1. **系统监控** (`monitor-system.js`)
   - CPU、内存、磁盘使用率监控
   - 系统负载和进程监控
   - 网络状态监控

2. **应用监控** (`monitor-application.js`)
   - API服务健康检查
   - 数据库连接监控
   - Redis连接监控

3. **监控仪表板** (`monitor-dashboard.js`)
   - Web界面实时监控
   - 告警信息展示
   - 历史数据查看

4. **监控管理器** (`monitor-manager.js`)
   - 统一管理所有监控组件
   - 配置管理和状态控制
   - 告警通知管理

## 快速开始

### 1. 环境配置

#### 1.1 安装依赖
```bash
cd mall-server
npm install
```

#### 1.2 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑监控配置
vim .env
```

```env
# 监控系统配置
MONITOR_SYSTEM_ENABLED=true
MONITOR_APPLICATION_ENABLED=true
MONITOR_DASHBOARD_ENABLED=true
MONITOR_PORT=8082
MONITOR_HOST=0.0.0.0

# 告警配置
ALERT_WEBHOOK=https://your-webhook-url.com/alert
ALERT_EMAIL=<EMAIL>
ALERT_RETRY_COUNT=3
ALERT_RETRY_DELAY=60000

# 日志配置
MONITOR_LOG_LEVEL=info
MONITOR_LOG_RETENTION=7

# 认证配置（可选）
MONITOR_AUTH_ENABLED=true
MONITOR_USERNAME=admin
MONITOR_PASSWORD=admin123
```

### 2. 启动监控系统

#### 2.1 使用监控管理器（推荐）
```bash
# 启动完整监控系统
node scripts/monitor-manager.js
```

#### 2.2 单独启动组件
```bash
# 启动系统监控
node scripts/monitor-system.js

# 启动应用监控
node scripts/monitor-application.js

# 启动监控仪表板
node scripts/monitor-dashboard.js
```

#### 2.3 使用PM2管理
```bash
# 安装PM2
npm install -g pm2

# 启动监控系统
pm2 start scripts/monitor-manager.js --name "xinjie-monitor"

# 查看状态
pm2 status

# 查看日志
pm2 logs xinjie-monitor
```

### 3. 访问监控界面

- **监控仪表板**: http://localhost:8082
- **API健康检查**: http://localhost:8082/api/health
- **监控状态**: http://localhost:8082/api/status

## 功能特性

### 1. 系统监控

#### 1.1 监控指标
- **CPU使用率**: 实时监控CPU使用情况，阈值80%
- **内存使用率**: 监控内存使用情况，阈值85%
- **磁盘使用率**: 监控磁盘空间，阈值90%
- **系统负载**: 监控系统负载，阈值5.0
- **网络状态**: 监控网络接口状态

#### 1.2 告警规则
```javascript
// 告警阈值配置
thresholds: {
  cpu: 80,        // CPU使用率超过80%告警
  memory: 85,     // 内存使用率超过85%告警
  disk: 90,       // 磁盘使用率超过90%告警
  load: 5,        // 系统负载超过5告警
  network: 1000   // 网络延迟超过1000ms告警
}
```

### 2. 应用监控

#### 2.1 服务监控
- **API服务**: 检查API健康状态，响应时间监控
- **数据库**: MySQL连接状态和查询性能
- **Redis**: Redis连接状态和响应时间

#### 2.2 健康检查
```bash
# 检查API健康状态
curl http://localhost:8082/api/health

# 响应示例
{
  "status": "healthy",
  "services": {
    "api": { "status": "healthy", "responseTime": 45 },
    "database": { "status": "healthy", "responseTime": 12 },
    "redis": { "status": "healthy", "responseTime": 8 }
  },
  "timestamp": "2025-01-01T10:00:00.000Z"
}
```

### 3. 告警通知

#### 3.1 Webhook告警
```javascript
// 告警数据格式
{
  "timestamp": "2025-01-01T10:00:00.000Z",
  "server": "xinjie-server-01",
  "alert": {
    "service": "api",
    "message": "API服务响应超时",
    "count": 1,
    "recovery": false
  },
  "status": {
    "api": { "status": "error", "responseTime": 5000 },
    "database": { "status": "healthy", "responseTime": 12 },
    "redis": { "status": "healthy", "responseTime": 8 }
  }
}
```

#### 3.2 邮件告警
```javascript
// 邮件告警配置
const emailConfig = {
  host: 'smtp.example.com',
  port: 587,
  secure: false,
  auth: {
    user: '<EMAIL>',
    pass: 'your-password'
  }
};
```

### 4. 监控仪表板

#### 4.1 实时数据
- 系统资源使用率实时图表
- 服务健康状态实时更新
- 告警信息实时显示

#### 4.2 历史数据
- 监控数据历史趋势
- 告警历史记录
- 性能分析报告

## 配置说明

### 1. 监控配置

#### 1.1 系统监控配置
```javascript
// 监控间隔配置
config: {
  alert: {
    interval: 300000  // 5分钟检查一次
  },
  thresholds: {
    cpu: 80,          // CPU阈值
    memory: 85,       // 内存阈值
    disk: 90,         // 磁盘阈值
    load: 5           // 负载阈值
  }
}
```

#### 1.2 应用监控配置
```javascript
// 服务检查配置
services: {
  api: {
    url: 'http://localhost:4000/api/health',
    interval: 30000,  // 30秒检查一次
    timeout: 5000     // 5秒超时
  },
  database: {
    interval: 60000,  // 1分钟检查一次
    timeout: 3000     // 3秒超时
  },
  redis: {
    interval: 60000,  // 1分钟检查一次
    timeout: 3000     // 3秒超时
  }
}
```

### 2. 告警配置

#### 2.1 Webhook配置
```javascript
// Webhook告警配置
alert: {
  webhook: 'https://your-webhook-url.com/alert',
  retryCount: 3,      // 重试次数
  retryDelay: 60000   // 重试延迟
}
```

#### 2.2 邮件配置
```javascript
// 邮件告警配置
const nodemailer = require('nodemailer');

const transporter = nodemailer.createTransporter({
  host: 'smtp.example.com',
  port: 587,
  secure: false,
  auth: {
    user: '<EMAIL>',
    pass: 'your-password'
  }
});
```

### 3. 日志配置

#### 3.1 日志级别
```javascript
// 日志配置
log: {
  level: 'info',      // 日志级别: debug, info, warn, error
  retention: 7        // 日志保留天数
}
```

#### 3.2 日志轮转
```javascript
// 自动清理旧日志
cleanupLogs() {
  const files = fs.readdirSync(this.config.log.path);
  const now = new Date();
  const retentionDays = this.config.log.retention;

  files.forEach(file => {
    const filePath = path.join(this.config.log.path, file);
    const stats = fs.statSync(filePath);
    const daysOld = (now - stats.mtime) / (1000 * 60 * 60 * 24);

    if (daysOld > retentionDays) {
      fs.unlinkSync(filePath);
    }
  });
}
```

## API接口

### 1. 监控状态API

#### 1.1 获取整体状态
```http
GET /api/status
```

**响应示例**:
```json
{
  "system": {
    "current": {
      "cpu": { "usage": 45.2, "cores": 4 },
      "memory": { "usage": 67.8, "total": "8 GB" },
      "disk": { "usage": 23.4, "total": "100 GB" }
    },
    "stats": { /* 历史数据 */ }
  },
  "application": {
    "status": {
      "api": { "status": "healthy", "responseTime": 45 },
      "database": { "status": "healthy", "responseTime": 12 },
      "redis": { "status": "healthy", "responseTime": 8 }
    },
    "summary": {
      "healthy": 3,
      "warning": 0,
      "error": 0,
      "total": 3
    }
  },
  "timestamp": "2025-01-01T10:00:00.000Z"
}
```

#### 1.2 健康检查
```http
GET /api/health
```

**响应示例**:
```json
{
  "status": "healthy",
  "services": {
    "api": { "status": "healthy", "responseTime": 45 },
    "database": { "status": "healthy", "responseTime": 12 },
    "redis": { "status": "healthy", "responseTime": 8 }
  },
  "timestamp": "2025-01-01T10:00:00.000Z"
}
```

#### 1.3 获取告警
```http
GET /api/alerts
```

**响应示例**:
```json
[
  {
    "key": "api_alert",
    "service": "api",
    "error": "API服务响应超时",
    "timestamp": "2025-01-01T09:55:00.000Z",
    "count": 1
  }
]
```

### 2. 实时数据流

#### 2.1 Server-Sent Events
```http
GET /api/stream
```

**数据格式**:
```
data: {"system": {...}, "application": {...}, "timestamp": "..."}

data: {"system": {...}, "application": {...}, "timestamp": "..."}
```

### 3. 日志查看API

#### 3.1 查看系统日志
```http
GET /api/logs/system
```

#### 3.2 查看应用日志
```http
GET /api/logs/application
```

## 部署指南

### 1. 生产环境部署

#### 1.1 使用PM2部署
```bash
# 创建PM2配置文件
cat > ecosystem-monitor.config.js << EOF
module.exports = {
  apps: [{
    name: 'xinjie-monitor',
    script: './scripts/monitor-manager.js',
    instances: 1,
    env: {
      NODE_ENV: 'production',
      MONITOR_PORT: 8082,
      MONITOR_HOST: '0.0.0.0'
    },
    error_file: './logs/monitor-err.log',
    out_file: './logs/monitor-out.log',
    log_file: './logs/monitor-combined.log',
    time: true
  }]
};
EOF

# 启动监控
pm2 start ecosystem-monitor.config.js

# 保存配置
pm2 save

# 设置开机自启
pm2 startup
```

#### 1.2 使用Docker部署
```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm install --production

COPY . .

EXPOSE 8082

CMD ["node", "scripts/monitor-manager.js"]
```

```bash
# 构建镜像
docker build -t xinjie-monitor .

# 运行容器
docker run -d \
  --name xinjie-monitor \
  -p 8082:8082 \
  -e MONITOR_PORT=8082 \
  -e ALERT_WEBHOOK=https://your-webhook-url.com/alert \
  xinjie-monitor
```

### 2. Nginx配置

#### 2.1 反向代理配置
```nginx
# 监控仪表板代理
server {
    listen 80;
    server_name monitor.xinjie-tea.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name monitor.xinjie-tea.com;

    # SSL配置
    ssl_certificate /etc/letsencrypt/live/monitor.xinjie-tea.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/monitor.xinjie-tea.com/privkey.pem;

    # 监控仪表板代理
    location / {
        proxy_pass http://localhost:8082;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 故障排除

### 1. 常见问题

#### 1.1 监控系统无法启动
```bash
# 检查端口占用
netstat -tlnp | grep :8082

# 检查日志
tail -f logs/monitor-err.log

# 检查环境变量
echo $MONITOR_PORT
echo $ALERT_WEBHOOK
```

#### 1.2 告警无法发送
```bash
# 测试Webhook连接
curl -X POST https://your-webhook-url.com/alert \
  -H "Content-Type: application/json" \
  -d '{"test": true}'

# 检查网络连接
ping your-webhook-url.com
```

#### 1.3 监控数据不准确
```bash
# 检查系统权限
ls -la scripts/monitor-system.js

# 检查系统命令
which df
which ps

# 手动测试监控脚本
node scripts/monitor-system.js
```

### 2. 性能优化

#### 2.1 监控间隔调整
```javascript
// 根据系统负载调整监控间隔
const interval = systemLoad > 5 ? 60000 : 30000; // 高负载时降低频率
```

#### 2.2 数据存储优化
```javascript
// 限制历史数据量
const maxHistory = 100; // 只保留最近100个数据点
```

#### 2.3 日志优化
```javascript
// 异步写入日志
fs.appendFile(logFile, logEntry + '\n', (err) => {
  if (err) console.error('日志写入失败:', err);
});
```

## 扩展功能

### 1. 自定义监控

#### 1.1 添加自定义指标
```javascript
// 在SystemMonitor中添加自定义监控
async getCustomMetrics() {
  // 自定义监控逻辑
  return {
    customMetric: 'value'
  };
}
```

#### 1.2 自定义告警规则
```javascript
// 自定义告警检查
checkCustomAlerts(metrics) {
  if (metrics.customMetric > threshold) {
    this.sendAlert({
      type: 'Custom',
      message: '自定义指标异常'
    });
  }
}
```

### 2. 集成第三方服务

#### 2.1 集成Prometheus
```javascript
// Prometheus指标导出
const prometheusMetrics = {
  cpu_usage: metrics.cpu.usage,
  memory_usage: metrics.memory.usage,
  disk_usage: metrics.disk.usage
};
```

#### 2.2 集成Grafana
```javascript
// 数据源配置
const grafanaConfig = {
  url: 'http://grafana:3000',
  apiKey: 'your-api-key'
};
```

## 联系信息

- **技术支持**: <EMAIL>
- **监控系统维护**: <EMAIL>
- **紧急联系**: +86-138-0013-8000

---

## 更新日志

### v1.0.0 (2025-01-01)
- 初始监控系统发布
- 支持系统监控、应用监控、Web仪表板
- 支持Webhook和邮件告警
- 提供完整的API接口 