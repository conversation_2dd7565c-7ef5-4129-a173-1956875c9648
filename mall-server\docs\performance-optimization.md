# 性能优化功能使用说明

## 概述

本项目已实现以下性能优化功能：

1. **CDN支持** - 图片加速和优化
2. **数据库读写分离** - 提升数据库性能
3. **API响应缓存** - 减少数据库查询
4. **图片懒加载** - 优化前端加载体验
5. **性能监控** - 实时监控系统性能

## 1. CDN配置

### 启用CDN

在环境变量中设置：

```bash
CDN_ENABLED=true
CDN_DOMAIN=https://cdn.xinjie-tea.com
CDN_IMAGE_PATH=/images
```

### 使用CDN工具类

```javascript
const cdnUtils = require('./src/utils/cdn');

// 获取CDN图片URL
const cdnUrl = cdnUtils.getImageUrl(originalUrl, 'medium');

// 批量转换图片URL
const transformedData = cdnUtils.transformImages(data, 'medium');

// 获取响应式图片
const responsiveImages = cdnUtils.getResponsiveImages(originalUrl);
```

### 图片尺寸配置

- `thumbnail`: 150x150 (缩略图)
- `small`: 300x300 (小图)
- `medium`: 600x600 (中等图)
- `large`: 1200x1200 (大图)

## 2. 数据库读写分离

### 配置读写分离

在环境变量中设置：

```bash
# 读库配置
DB_READ_HOST=read-db.example.com
DB_READ_PORT=3306
DB_READ_USER=read_user
DB_READ_PASSWORD=read_password
DB_READ_NAME=xinjie_mall

# 写库配置
DB_WRITE_HOST=write-db.example.com
DB_WRITE_PORT=3306
DB_WRITE_USER=write_user
DB_WRITE_PASSWORD=write_password
DB_WRITE_NAME=xinjie_mall
```

### 自动读写分离

Sequelize会自动根据操作类型选择数据库：
- 查询操作（SELECT）使用读库
- 写操作（INSERT/UPDATE/DELETE）使用写库

## 3. API响应缓存

### 缓存中间件使用

```javascript
const { apiCache } = require('./src/middleware');

// 在路由中使用缓存
router.get('/list', apiCache.cache(300), controller.getList);
router.get('/detail/:id', apiCache.cache(600), controller.getDetail);
```

### 缓存管理API

```bash
# 获取缓存统计
GET /api/admin/cache/stats

# 清除指定类型缓存
POST /api/admin/cache/clear
{
  "type": "category" | "banner" | "product" | "all"
}

# 清除指定模块缓存
DELETE /api/admin/cache/module/:module
```

### 缓存键生成规则

- 格式：`api:{path}?{query}`
- 示例：`api:/front/product/list?page=1&limit=10`

## 4. 图片懒加载组件

### 在小程序中使用

```xml
<!-- 在页面WXML中 -->
<lazy-image 
  src="{{product.main_image}}" 
  size="medium"
  use-cdn="{{true}}"
  bind:load="onImageLoad"
  bind:error="onImageError"
/>
```

### 组件属性

- `src`: 图片URL
- `placeholder`: 占位图URL
- `mode`: 图片模式（aspectFill等）
- `size`: 图片尺寸（thumbnail/small/medium/large）
- `use-cdn`: 是否启用CDN
- `width`: 图片宽度
- `height`: 图片高度

### 组件事件

- `load`: 图片加载成功
- `error`: 图片加载失败
- `tap`: 图片点击
- `longtap`: 图片长按

## 5. 性能监控

### 运行监控脚本

```bash
# 单次监控
npm run monitor

# 持续监控
npm run monitor:watch
```

### 监控指标

- **系统资源**: CPU使用率、内存使用率、负载
- **缓存统计**: 缓存键数量、命中率、内存使用
- **优化建议**: 基于监控数据生成建议

### 监控报告

监控报告保存在 `logs/performance-YYYY-MM-DD.json` 文件中，包含：

```json
{
  "timestamp": "2024-01-01T00:00:00.000Z",
  "system": {
    "cpu": { "loadAverage": [1.2, 1.1, 1.0], "cores": 4 },
    "memory": { "usagePercent": "65.5" }
  },
  "cache": {
    "totalKeys": 150,
    "apiKeys": 45,
    "hitRate": 85.5
  },
  "recommendations": [
    {
      "type": "cache",
      "level": "info",
      "message": "缓存命中率较低，建议优化缓存策略"
    }
  ]
}
```

## 6. 性能优化最佳实践

### 缓存策略

1. **热点数据缓存**: 商品列表、轮播图等
2. **合理TTL**: 根据数据更新频率设置缓存时间
3. **缓存预热**: 系统启动时预加载热点数据

### 图片优化

1. **响应式图片**: 根据设备尺寸提供不同尺寸图片
2. **格式优化**: 使用WebP等现代图片格式
3. **懒加载**: 只加载可见区域的图片

### 数据库优化

1. **读写分离**: 查询和写操作分离到不同数据库
2. **索引优化**: 为常用查询字段添加索引
3. **连接池**: 合理配置数据库连接池大小

### 监控告警

1. **资源监控**: 监控CPU、内存、磁盘使用率
2. **性能指标**: 监控API响应时间、缓存命中率
3. **告警机制**: 设置阈值告警，及时发现问题

## 7. 故障排查

### 缓存问题

```bash
# 检查Redis连接
redis-cli ping

# 查看缓存键
redis-cli keys "api:*"

# 清除所有缓存
redis-cli flushall
```

### 数据库问题

```bash
# 检查数据库连接
mysql -h localhost -u root -p

# 查看慢查询日志
SHOW VARIABLES LIKE 'slow_query_log';
```

### 性能问题

```bash
# 查看系统资源
top
htop
free -h

# 查看网络连接
netstat -tulpn
```

## 8. 部署建议

### 生产环境配置

1. **CDN**: 使用专业的CDN服务商
2. **数据库**: 使用云数据库服务，配置读写分离
3. **缓存**: 使用Redis集群或云Redis服务
4. **监控**: 集成专业的APM监控工具

### 安全配置

1. **环境变量**: 敏感信息使用环境变量
2. **访问控制**: 限制数据库和Redis访问权限
3. **HTTPS**: 启用HTTPS加密传输
4. **防火墙**: 配置防火墙规则

## 9. 性能测试

### 压力测试

```bash
# 使用ab进行压力测试
ab -n 1000 -c 10 http://localhost:4000/api/front/product/list

# 使用wrk进行压力测试
wrk -t12 -c400 -d30s http://localhost:4000/api/front/product/list
```

### 性能基准

- **API响应时间**: < 200ms
- **缓存命中率**: > 80%
- **图片加载时间**: < 2s
- **数据库查询时间**: < 100ms

## 10. 更新日志

### v1.0.0 (2024-01-01)
- 实现CDN支持
- 实现数据库读写分离
- 实现API响应缓存
- 实现图片懒加载
- 实现性能监控

### 后续计划
- 添加更多缓存策略
- 优化图片压缩算法
- 增加更多监控指标
- 支持更多CDN服务商 