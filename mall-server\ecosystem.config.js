module.exports = {
  "apps": [
    {
      "name": "xinjie-mall",
      "script": "app.js",
      "instances": "max",
      "exec_mode": "cluster",
      "env": {
        "NODE_ENV": "production",
        "PORT": 4000
      },
      "error_file": "./logs/err.log",
      "out_file": "./logs/out.log",
      "log_file": "./logs/combined.log",
      "time": true,
      "max_memory_restart": "1G",
      "node_args": "--max-old-space-size=1024"
    }
  ]
};