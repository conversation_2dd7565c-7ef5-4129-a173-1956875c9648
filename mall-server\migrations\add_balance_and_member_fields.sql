-- 添加余额和会员相关字段到用户表
-- 执行时间：2025-07-27

-- 1. 添加余额字段
ALTER TABLE users 
ADD COLUMN balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '用户余额' AFTER points;

-- 2. 添加会员等级字段
ALTER TABLE users 
ADD COLUMN user_level INT DEFAULT 1 COMMENT '会员等级(1:普通 2:白银 3:黄金)' AFTER balance;

-- 3. 添加余额字段索引
ALTER TABLE users ADD INDEX idx_balance (balance);

-- 4. 添加会员等级字段索引
ALTER TABLE users ADD INDEX idx_user_level (user_level);

-- 5. 创建余额变动记录表
CREATE TABLE IF NOT EXISTS balance_records (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL COMMENT '用户ID',
  type TINYINT NOT NULL COMMENT '变动类型(1:增加 2:减少)',
  amount DECIMAL(10,2) NOT NULL COMMENT '变动金额',
  balance_before DECIMAL(10,2) NOT NULL COMMENT '变动前余额',
  balance_after DECIMAL(10,2) NOT NULL COMMENT '变动后余额',
  source TINYINT NOT NULL COMMENT '来源(1:充值 2:消费 3:退款 4:调整)',
  source_id INT NULL COMMENT '来源ID(订单ID等)',
  remark VARCHAR(255) NULL COMMENT '备注',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_user_id (user_id),
  INDEX idx_type (type),
  INDEX idx_source (source),
  INDEX idx_created_at (created_at),
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='余额变动记录表';

-- 6. 创建充值记录表
CREATE TABLE IF NOT EXISTS recharge_records (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL COMMENT '用户ID',
  order_no VARCHAR(50) NOT NULL COMMENT '充值订单号',
  total_amount DECIMAL(10,2) NOT NULL COMMENT '充值金额',
  payment_method TINYINT NOT NULL COMMENT '支付方式(1:微信 2:支付宝 3:其他)',
  payment_status TINYINT DEFAULT 0 COMMENT '支付状态(0:待支付 1:已支付 2:已取消)',
  transaction_id VARCHAR(100) NULL COMMENT '第三方交易号',
  remark VARCHAR(255) NULL COMMENT '备注',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  UNIQUE KEY uk_order_no (order_no),
  INDEX idx_user_id (user_id),
  INDEX idx_payment_status (payment_status),
  INDEX idx_created_at (created_at),
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值记录表';

-- 7. 创建积分记录表
CREATE TABLE IF NOT EXISTS points_records (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL COMMENT '用户ID',
  type TINYINT NOT NULL COMMENT '变动类型(1:增加 2:减少)',
  points INT NOT NULL COMMENT '变动积分',
  points_before INT NOT NULL COMMENT '变动前积分',
  points_after INT NOT NULL COMMENT '变动后积分',
  source TINYINT NOT NULL COMMENT '来源(1:购物获得 2:签到获得 3:活动获得 4:消费抵扣 5:系统调整)',
  source_id INT NULL COMMENT '来源ID(订单ID等)',
  remark VARCHAR(255) NULL COMMENT '备注',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  INDEX idx_user_id (user_id),
  INDEX idx_type (type),
  INDEX idx_source (source),
  INDEX idx_created_at (created_at),
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分记录表';

-- 8. 创建会员等级表
CREATE TABLE IF NOT EXISTS member_levels (
  id INT PRIMARY KEY AUTO_INCREMENT,
  level_code VARCHAR(20) NOT NULL COMMENT '等级代码',
  level_name VARCHAR(50) NOT NULL COMMENT '等级名称',
  min_points INT NOT NULL COMMENT '最低积分要求',
  discount_rate DECIMAL(3,2) NOT NULL DEFAULT 1.00 COMMENT '折扣率(0.90表示9折)',
  description TEXT NULL COMMENT '等级描述',
  sort_order INT DEFAULT 0 COMMENT '排序',
  status TINYINT DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  UNIQUE KEY uk_level_code (level_code),
  INDEX idx_min_points (min_points),
  INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员等级表';

-- 9. 插入默认会员等级数据
INSERT INTO member_levels (level_code, level_name, min_points, discount_rate, description, sort_order) VALUES
('bronze', '普通会员', 0, 1.00, '享受基础服务', 1),
('silver', '白银会员', 1000, 0.95, '享受95折优惠和优先服务', 2),
('gold', '黄金会员', 5000, 0.90, '享受90折优惠和专属服务', 3)
ON DUPLICATE KEY UPDATE 
level_name = VALUES(level_name),
min_points = VALUES(min_points),
discount_rate = VALUES(discount_rate),
description = VALUES(description);

-- 10. 创建会员权益表
CREATE TABLE IF NOT EXISTS member_benefits (
  id INT PRIMARY KEY AUTO_INCREMENT,
  level_id INT NOT NULL COMMENT '会员等级ID',
  benefit_type VARCHAR(20) NOT NULL COMMENT '权益类型',
  benefit_name VARCHAR(50) NOT NULL COMMENT '权益名称',
  benefit_value VARCHAR(100) NULL COMMENT '权益值',
  description TEXT NULL COMMENT '权益描述',
  sort_order INT DEFAULT 0 COMMENT '排序',
  status TINYINT DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_level_id (level_id),
  INDEX idx_benefit_type (benefit_type),
  INDEX idx_status (status),
  
  FOREIGN KEY (level_id) REFERENCES member_levels(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员权益表';

-- 执行完成提示
SELECT '余额和会员功能数据库迁移完成！' as message;
