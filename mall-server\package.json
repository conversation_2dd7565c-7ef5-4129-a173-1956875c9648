{"name": "xinjie-mall-server", "version": "1.0.0", "description": "心洁茶叶商城后端API服务", "main": "app.js", "scripts": {"start": "node app-https.js", "start:http": "node start-http.js", "start:unified": "node start-unified.js", "dev": "nodemon app-https.js", "dev:http": "nodemon start-http.js", "dev:unified": "nodemon start-unified.js", "test": "jest", "test:watch": "jest --watch", "test:unit": "jest tests/unit/", "test:integration": "jest tests/integration/", "test:performance": "jest tests/performance/", "test:security": "jest tests/security/", "test:coverage": "jest --coverage", "test:all": "bash scripts/run-tests.sh", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js", "build": "echo 'No build step required for Node.js'", "init-db": "node scripts/init-database.js", "monitor": "node scripts/performance-monitor.js", "monitor:watch": "node scripts/performance-monitor.js --watch", "cache:stats": "curl -X GET http://localhost:4000/api/admin/cache/stats", "cache:clear": "curl -X POST http://localhost:4000/api/admin/cache/clear -H 'Content-Type: application/json' -d '{\"type\":\"all\"}'", "start:secure": "node src/app-secure-fixed.js", "start:original": "node src/app-original-backup.js", "dev:secure": "nodemon src/app-secure-fixed.js"}, "keywords": ["mall", "api", "koa", "mysql", "wechat-miniprogram"], "author": "心洁茶叶", "license": "MIT", "dependencies": {"@koa/multer": "^3.0.2", "@koa/router": "^12.0.1", "alipay-sdk": "^3.6.1", "aliyun-sdk": "^1.12.5", "axios": "^1.6.7", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "dotenv": "^16.4.1", "express-rate-limit": "^7.1.5", "formidable": "^3.5.4", "fs-extra": "^11.2.0", "helmet": "^7.1.0", "ioredis": "^5.6.1", "joi": "^17.12.1", "jsonwebtoken": "^9.0.2", "koa": "^2.15.0", "koa-bodyparser": "^4.4.1", "koa-compress": "^5.1.1", "koa-cors": "^0.0.16", "koa-helmet": "^7.0.2", "koa-jwt": "^4.0.4", "koa-logger": "^3.2.1", "koa-ratelimit": "^5.1.0", "koa-static": "^5.0.0", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.9.1", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "nodemailer": "^6.9.8", "path": "^0.12.7", "qiniu": "^7.12.1", "redis": "^4.6.13", "sequelize": "^6.37.0", "sequelize-cli": "^6.6.2", "uuid": "^9.0.1", "validator": "^13.15.15", "wechatpay-node-v3": "^2.1.5", "winston": "^3.11.0"}, "devDependencies": {"eslint": "^9.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "nodemon": "^3.0.3", "prettier": "^3.2.5", "supertest": "^7.1.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}