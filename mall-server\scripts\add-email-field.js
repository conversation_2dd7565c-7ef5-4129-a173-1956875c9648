const { sequelize } = require('../src/models');

async function addEmailField() {
  try {
    console.log('🔄 添加 email 字段到 users 表...');
    
    // 添加 email 字段
    await sequelize.query('ALTER TABLE users ADD COLUMN email VARCHAR(100) COMMENT "邮箱"');
    
    console.log('✅ 成功添加 email 字段');
    
  } catch (error) {
    if (error.message.includes('Duplicate column name')) {
      console.log('ℹ️  email 字段已存在，跳过添加');
    } else {
      console.error('❌ 添加 email 字段失败:', error.message);
    }
  } finally {
    await sequelize.close();
  }
}

// 运行添加
if (require.main === module) {
  addEmailField();
}

module.exports = addEmailField;
