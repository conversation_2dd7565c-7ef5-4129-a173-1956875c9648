const { sequelize } = require('../src/models');

async function checkTableStructure() {
  try {
    console.log('🔍 检查 users 表结构...');
    
    // 查询表结构
    const [results] = await sequelize.query('DESCRIBE users');
    
    console.log('📋 当前 users 表字段:');
    console.table(results);
    
    // 检查缺失的字段
    const existingFields = results.map(row => row.Field);
    const requiredFields = [
      'id', 'openid', 'unionid', 'user_info', 'status', 
      'last_login_at', 'phone', 'email', 'level', 'points', 
      'remark', 'created_at', 'updated_at'
    ];
    
    const missingFields = requiredFields.filter(field => !existingFields.includes(field));
    
    if (missingFields.length > 0) {
      console.log('\n❌ 缺失的字段:');
      missingFields.forEach(field => console.log(`   - ${field}`));
    } else {
      console.log('\n✅ 所有必需字段都存在');
    }
    
    console.log('\n📝 建议的SQL语句来添加缺失字段:');
    if (missingFields.includes('openid')) {
      console.log('ALTER TABLE users ADD COLUMN openid VARCHAR(100) UNIQUE COMMENT "微信用户唯一标识";');
    }
    if (missingFields.includes('unionid')) {
      console.log('ALTER TABLE users ADD COLUMN unionid VARCHAR(100) COMMENT "微信开放平台唯一标识";');
    }
    if (missingFields.includes('user_info')) {
      console.log('ALTER TABLE users ADD COLUMN user_info TEXT COMMENT "用户信息JSON格式";');
    }
    if (missingFields.includes('last_login_at')) {
      console.log('ALTER TABLE users ADD COLUMN last_login_at DATETIME COMMENT "最后登录时间";');
    }
    if (missingFields.includes('level')) {
      console.log('ALTER TABLE users ADD COLUMN level INT DEFAULT 1 COMMENT "用户等级";');
    }
    if (missingFields.includes('points')) {
      console.log('ALTER TABLE users ADD COLUMN points INT DEFAULT 0 COMMENT "用户积分";');
    }
    if (missingFields.includes('remark')) {
      console.log('ALTER TABLE users ADD COLUMN remark TEXT COMMENT "备注信息";');
    }
    if (missingFields.includes('email')) {
      console.log('ALTER TABLE users ADD COLUMN email VARCHAR(100) COMMENT "邮箱";');
    }
    
  } catch (error) {
    console.error('❌ 检查表结构失败:', error.message);
  } finally {
    await sequelize.close();
  }
}

// 运行检查
if (require.main === module) {
  checkTableStructure();
}

module.exports = checkTableStructure;
