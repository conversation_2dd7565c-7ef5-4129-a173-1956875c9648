-- 评论功能数据库优化脚本
-- 执行前请备份数据库

-- 1. 为reviews表添加复合索引，优化查询性能
-- 用于查询用户对特定商品的评论
CREATE INDEX idx_reviews_user_product ON reviews(user_id, product_id);

-- 用于查询订单的评论状态
CREATE INDEX idx_reviews_order_product ON reviews(order_id, product_id);

-- 用于按时间排序的评论查询
CREATE INDEX idx_reviews_product_time ON reviews(product_id, created_at DESC);

-- 用于评分筛选查询
CREATE INDEX idx_reviews_product_rating ON reviews(product_id, rating);

-- 用于管理后台统计查询
CREATE INDEX idx_reviews_created_at ON reviews(created_at);

-- 用于查询待回复的评论
CREATE INDEX idx_reviews_reply_status ON reviews(reply_content);

-- 2. 为orders表添加索引，优化评论权限验证查询
-- 用于查询用户的已完成订单
CREATE INDEX idx_orders_user_status ON orders(user_id, order_status);

-- 3. 为order_items表添加索引，优化订单商品查询
-- 用于验证用户是否购买过某商品
CREATE INDEX idx_order_items_product ON order_items(product_id);

-- 4. 为products表添加评论相关字段的索引
-- 用于按评分排序商品
CREATE INDEX idx_products_rating ON products(rating DESC);

-- 用于按评论数排序商品
CREATE INDEX idx_products_review_count ON products(review_count DESC);

-- 5. 添加评论内容全文索引（MySQL 5.7+）
-- 用于评论内容搜索
ALTER TABLE reviews ADD FULLTEXT(content);

-- 6. 优化表结构，添加评论状态字段（可选）
-- ALTER TABLE reviews ADD COLUMN status TINYINT DEFAULT 1 COMMENT '评论状态(0:隐藏 1:显示 2:待审核)';
-- CREATE INDEX idx_reviews_status ON reviews(status);

-- 7. 添加评论有用性统计字段（可选扩展功能）
-- ALTER TABLE reviews ADD COLUMN helpful_count INT DEFAULT 0 COMMENT '有用数';
-- ALTER TABLE reviews ADD COLUMN unhelpful_count INT DEFAULT 0 COMMENT '无用数';

-- 8. 创建评论点赞表（可选扩展功能）
/*
CREATE TABLE review_likes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '点赞ID',
    review_id BIGINT NOT NULL COMMENT '评论ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    type TINYINT NOT NULL COMMENT '类型(1:有用 0:无用)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_review_user (review_id, user_id),
    INDEX idx_review_likes_review (review_id),
    INDEX idx_review_likes_user (user_id),
    FOREIGN KEY (review_id) REFERENCES reviews(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT '评论点赞表';
*/

-- 9. 创建评论举报表（可选扩展功能）
/*
CREATE TABLE review_reports (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '举报ID',
    review_id BIGINT NOT NULL COMMENT '评论ID',
    user_id BIGINT NOT NULL COMMENT '举报用户ID',
    reason VARCHAR(200) NOT NULL COMMENT '举报原因',
    status TINYINT DEFAULT 0 COMMENT '处理状态(0:待处理 1:已处理 2:已忽略)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '举报时间',
    processed_at TIMESTAMP NULL COMMENT '处理时间',
    INDEX idx_review_reports_review (review_id),
    INDEX idx_review_reports_user (user_id),
    INDEX idx_review_reports_status (status),
    FOREIGN KEY (review_id) REFERENCES reviews(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT '评论举报表';
*/

-- 10. 查看索引创建结果
SHOW INDEX FROM reviews;
SHOW INDEX FROM orders;
SHOW INDEX FROM order_items;
SHOW INDEX FROM products;

-- 11. 分析表统计信息，优化查询计划
ANALYZE TABLE reviews;
ANALYZE TABLE orders;
ANALYZE TABLE order_items;
ANALYZE TABLE products;

-- 12. 查看表大小和索引大小
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'DB Size in MB',
    ROUND((data_length / 1024 / 1024), 2) AS 'Data Size in MB',
    ROUND((index_length / 1024 / 1024), 2) AS 'Index Size in MB'
FROM information_schema.tables 
WHERE table_schema = DATABASE()
AND table_name IN ('reviews', 'orders', 'order_items', 'products');

-- 13. 优化建议查询
-- 查看慢查询相关的评论查询
/*
SELECT 
    DIGEST_TEXT,
    COUNT_STAR,
    AVG_TIMER_WAIT/1000000000 as avg_time_seconds,
    SUM_TIMER_WAIT/1000000000 as total_time_seconds
FROM performance_schema.events_statements_summary_by_digest 
WHERE DIGEST_TEXT LIKE '%reviews%' 
ORDER BY AVG_TIMER_WAIT DESC 
LIMIT 10;
*/
