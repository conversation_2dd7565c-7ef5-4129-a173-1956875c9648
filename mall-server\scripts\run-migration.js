const { sequelize } = require('../src/models');
const path = require('path');
const fs = require('fs');

async function runMigration() {
  try {
    console.log('🔄 开始运行数据库迁移...');
    
    // 获取迁移文件
    const migrationPath = path.join(__dirname, '../database/migrations/002_add_user_info_to_users.js');
    
    if (!fs.existsSync(migrationPath)) {
      console.error('❌ 迁移文件不存在:', migrationPath);
      return;
    }
    
    // 导入迁移文件
    const migration = require(migrationPath);
    
    // 运行迁移
    await migration.up(sequelize.getQueryInterface(), sequelize.Sequelize);
    
    console.log('✅ 数据库迁移完成');
    console.log('📝 已添加以下字段到 users 表:');
    console.log('   - user_info (TEXT): 用户信息JSON');
    console.log('   - openid (VARCHAR): 微信用户标识');
    console.log('   - unionid (VARCHAR): 微信开放平台标识');
    console.log('   - last_login_at (DATETIME): 最后登录时间');
    console.log('   - level (INT): 用户等级');
    console.log('   - points (INT): 用户积分');
    console.log('   - remark (TEXT): 备注信息');
    
  } catch (error) {
    console.error('❌ 数据库迁移失败:', error.message);
    console.log('💡 可能的原因:');
    console.log('   1. 数据库连接失败');
    console.log('   2. 字段已存在');
    console.log('   3. 权限不足');
    console.log('\n🔍 详细错误信息:');
    console.error(error);
  } finally {
    await sequelize.close();
  }
}

// 运行迁移
if (require.main === module) {
  runMigration();
}

module.exports = runMigration;
