const Koa = require('koa');
const helmet = require('koa-helmet');
const compress = require('koa-compress');
const logger = require('koa-logger');
const static = require('koa-static');
const bodyParser = require('koa-bodyparser');
const cors = require('koa-cors');
const path = require('path');

const config = require('./config');
const middleware = require('./middleware');
const routes = require('./routes');

const app = new Koa();

// 请求体解析中间件（必须放在最前面）
app.use(bodyParser({
  enableTypes: ['json', 'form', 'text'],
  jsonLimit: '10mb',
  formLimit: '10mb',
  textLimit: '10mb',
  strict: false,
  onerror: function (err, ctx) {
    console.error('Body parser error:', err);
    ctx.throw(422, 'body parse error');
  }
}));

// CORS中间件
app.use(cors({
  origin: '*',
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization', 'Accept'],
  credentials: true
}));

// 安全中间件
app.use(helmet({
  contentSecurityPolicy: false
}));

// 压缩中间件
app.use(compress());

// 日志中间件
if (config.env !== 'test') {
  app.use(logger());
}

// 静态文件服务
app.use(static(path.join(__dirname, '../uploads')));

// 应用自定义中间件
middleware.forEach(m => app.use(m));

// 应用路由
app.use(routes);

// 错误处理
app.on('error', (err, ctx) => {
  console.error('Server Error:', err);
  ctx.status = err.status || 500;
  ctx.body = {
    code: 500,
    message: '服务器内部错误',
    error: config.env === 'development' ? err.message : undefined
  };
});

module.exports = app; 