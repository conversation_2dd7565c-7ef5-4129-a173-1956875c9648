// 修复版安全加固应用启动文件
const Koa = require('koa');
const helmet = require('koa-helmet');
const compress = require('koa-compress');
const logger = require('koa-logger');
const static = require('koa-static');
const bodyParser = require('koa-bodyparser');
const cors = require('koa-cors');
const path = require('path');
require('dotenv').config();
const mount = require('koa-mount');

const config = require('./config');
const routes = require('./routes');
const authMiddleware = require('./middleware/auth');
const SecurityMiddleware = require('./middleware/security-fixed');

const app = new Koa();

// 1. 安全头设置（简化配置）
app.use(helmet({
  contentSecurityPolicy: false, // 避免影响小程序
  crossOriginEmbedderPolicy: false,
  hsts: process.env.NODE_ENV === 'production' ? {
    maxAge: 31536000,
    includeSubDomains: true
  } : false
}));

// 2. 请求体解析中间件
app.use(bodyParser({
  enableTypes: ['json', 'form', 'text'],
  jsonLimit: '10mb',
  formLimit: '10mb', 
  textLimit: '10mb',
  strict: false,
  onerror: (err, ctx) => {
    console.error('请求体解析错误:', err);
    ctx.throw(422, '请求体格式错误');
  }
}));

// 3. CORS中间件（兼容开发环境）
app.use(cors({
  origin: (ctx) => {
    const origin = ctx.get('Origin');
    
    // 开发环境允许所有来源
    if (process.env.NODE_ENV === 'development') {
      return origin || '*';
    }
    
    // 生产环境限制来源
    const allowedOrigins = [
      'https://your-domain.com',
      'https://admin.your-domain.com',
      'https://localhost:3000',
      'http://localhost:3000'
    ];
    
    if (allowedOrigins.includes(origin)) {
      return origin;
    }
    
    return false;
  },
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization', 'Accept', 'X-Requested-With'],
  credentials: true,
  maxAge: 86400
}));

// 4. 压缩中间件
app.use(compress({
  threshold: 2048,
  gzip: {
    flush: require('zlib').constants.Z_SYNC_FLUSH
  },
  deflate: {
    flush: require('zlib').constants.Z_SYNC_FLUSH,
  },
  br: false
}));

// 5. 日志中间件（过滤敏感信息）
if (config.env !== 'test') {
  app.use(logger((str, args) => {
    // 过滤密码等敏感信息
    const filteredStr = str
      .replace(/password[^&\s]*/gi, 'password=***')
      .replace(/token[^&\s]*/gi, 'token=***');
    console.log(filteredStr);
  }));
}

// 6. 静态文件服务
const uploadsPath = path.join(__dirname, '../uploads');
console.log('📁 静态资源目录:', uploadsPath);

app.use(mount('/uploads', static(uploadsPath, {
  maxAge: 30 * 24 * 60 * 60 * 1000, // 30天缓存
  hidden: false,
  index: false,
  defer: false,
  gzip: true
})));

// 小程序图片资源
const miniprogramImagesPath = path.join(__dirname, '../../xinjie-mall-miniprogram/images');
app.use(mount('/images', static(miniprogramImagesPath, {
  maxAge: 30 * 24 * 60 * 60 * 1000,
  hidden: false,
  index: false
})));

// 7. 安全中间件（按顺序应用）
app.use(SecurityMiddleware.securityHeaders());
app.use(SecurityMiddleware.requestSizeLimit(10)); // 10MB限制
app.use(SecurityMiddleware.xssProtection());
app.use(SecurityMiddleware.sqlInjectionProtection());
app.use(SecurityMiddleware.inputValidation());

// 8. 全局限流（宽松设置）
app.use(SecurityMiddleware.rateLimiting());

// 9. CSRF防护（开发环境跳过）
if (process.env.NODE_ENV === 'production') {
  app.use(SecurityMiddleware.csrfProtection());
}

// 10. 认证中间件
app.use(authMiddleware);

// 11. 特殊路由限流
const Router = require('@koa/router');
const specialRouter = new Router();

// 登录接口特殊限流
specialRouter.use('/api/front/auth/login', SecurityMiddleware.loginRateLimiting());
specialRouter.use('/api/front/auth/silent-login', SecurityMiddleware.loginRateLimiting());

// 支付接口严格限流
specialRouter.use('/api/front/payment', SecurityMiddleware.paymentRateLimiting());

// 应用特殊限流路由
app.use(specialRouter.routes());

// 12. 主路由
app.use(routes.routes());
app.use(routes.allowedMethods());

// 13. 全局错误处理
app.on('error', (err, ctx) => {
  // 记录详细错误信息
  const errorInfo = {
    error: err.message,
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined,
    url: ctx?.url,
    method: ctx?.method,
    ip: ctx?.ip,
    userAgent: ctx?.get('User-Agent'),
    timestamp: new Date().toISOString()
  };
  
  console.error('Server Error:', errorInfo);
  
  // 不向客户端泄露敏感错误信息
  if (ctx && !ctx.headerSent) {
    ctx.status = err.status || 500;
    ctx.body = {
      code: ctx.status,
      message: ctx.status === 500 ? '服务器内部错误' : err.message,
      timestamp: Date.now()
    };
  }
});

// 14. 未捕获异常处理
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
  // 给进程一些时间来清理
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// 15. 优雅关闭
const gracefulShutdown = () => {
  console.log('正在优雅关闭服务器...');
  process.exit(0);
};

process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

// 启动服务器
const PORT = config.port || 4001;
const server = app.listen(PORT, () => {
  console.log('🚀 心洁茶叶商城后端服务启动成功 (安全加固版)');
  console.log('📍 服务地址: http://localhost:' + PORT);
  console.log('🌍 环境: ' + config.env);
  console.log('⏰ 时间: ' + new Date().toLocaleString());
  console.log('');
  console.log('🛡️ 安全防护状态:');
  console.log('  ✅ SQL注入防护 (智能检测)');
  console.log('  ✅ XSS防护 (脚本过滤)');
  console.log('  ✅ 接口限流 (内存存储)');
  console.log('  ✅ 输入验证 (格式检查)');
  console.log('  ✅ 请求大小限制 (10MB)');
  console.log('  ✅ 安全头设置');
  console.log('  ✅ 错误处理');
  console.log('');
  console.log('📋 限流配置:');
  console.log('  • 全局限流: 100次/分钟');
  console.log('  • 登录限流: 5次/5分钟');
  console.log('  • 支付限流: 10次/分钟');
  console.log('');
  console.log('⚠️  注意: 开发环境已跳过部分安全检查');
});

// 设置服务器超时
server.timeout = 30000; // 30秒

// 导出应用实例
module.exports = app;
