const config = require('./index');

module.exports = {
  development: {
    host: config.database.host,
    port: config.database.port,
    database: config.database.database,
    username: config.database.username,
    password: config.database.password,
    dialect: config.database.dialect,
    timezone: config.database.timezone,
    logging: config.database.logging,
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  },
  test: {
    host: config.database.host,
    port: config.database.port,
    database: config.database.database + '_test',
    username: config.database.username,
    password: config.database.password,
    dialect: config.database.dialect,
    timezone: config.database.timezone,
    logging: false,
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  },
  production: {
    host: config.database.host,
    port: config.database.port,
    database: config.database.database + '_prod',
    username: config.database.username,
    password: config.database.password,
    dialect: config.database.dialect,
    timezone: config.database.timezone,
    logging: false,
    pool: {
      max: 20,
      min: 5,
      acquire: 30000,
      idle: 10000
    }
  }
}; 