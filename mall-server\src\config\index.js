require('dotenv').config();

const config = {
  // 应用配置
  env: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT) || 4000,
  apiPrefix: process.env.API_PREFIX || '/api',

  // 数据库配置
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 3306,
    database: process.env.DB_NAME || 'xinjie_mall',
    username: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD !== undefined ? process.env.DB_PASSWORD : '',
    dialect: 'mysql',
    timezone: '+08:00',
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    pool: {
      max: 20,
      min: 5,
      acquire: 30000,
      idle: 10000
    },
    // 读写分离配置
    replication: {
      read: [
        {
          host: process.env.DB_READ_HOST || process.env.DB_HOST || 'localhost',
          port: parseInt(process.env.DB_READ_PORT) || parseInt(process.env.DB_PORT) || 3306,
          username: process.env.DB_READ_USER || process.env.DB_USER || 'root',
          password: process.env.DB_READ_PASSWORD || process.env.DB_PASSWORD || '',
          database: process.env.DB_READ_NAME || process.env.DB_NAME || 'xinjie_mall'
        }
      ],
      write: {
        host: process.env.DB_WRITE_HOST || process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_WRITE_PORT) || parseInt(process.env.DB_PORT) || 3306,
        username: process.env.DB_WRITE_USER || process.env.DB_USER || 'root',
        password: process.env.DB_WRITE_PASSWORD || process.env.DB_PASSWORD || '',
        database: process.env.DB_WRITE_NAME || process.env.DB_NAME || 'xinjie_mall'
      }
    }
  },

  // Redis配置
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD || null,
    db: parseInt(process.env.REDIS_DB) || 0
  },

  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'xinjie_mall_jwt_secret_key_2024',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
    algorithm: 'HS256',
    issuer: 'xinjie-mall',
    audience: 'xinjie-mall-users'
  },

  // 文件上传配置
  upload: {
    path: process.env.UPLOAD_PATH || './uploads',
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 5242880, // 5MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    tempDir: './uploads/temp',
    productDir: './uploads/products',
    bannerDir: './uploads/banners',
    categoryDir: './uploads/categories',
    avatarDir: './uploads/avatars'
  },

  // 邮件配置
  email: {
    host: process.env.MAIL_HOST || 'smtp.qq.com',
    port: parseInt(process.env.MAIL_PORT) || 587,
    user: process.env.MAIL_USER || '',
    pass: process.env.MAIL_PASS || '',
    secure: false,
    from: process.env.MAIL_USER || '',
    templates: {
      welcome: {
        subject: '欢迎注册心洁茶叶商城',
        template: 'welcome.html'
      },
      resetPassword: {
        subject: '重置密码 - 心洁茶叶商城',
        template: 'reset-password.html'
      },
      orderConfirmation: {
        subject: '订单确认 - 心洁茶叶商城',
        template: 'order-confirmation.html'
      }
    }
  },

  // 短信配置
  sms: {
    accessKey: process.env.SMS_ACCESS_KEY || '',
    secretKey: process.env.SMS_SECRET_KEY || '',
    signName: process.env.SMS_SIGN_NAME || '心洁茶叶',
    templateCode: process.env.SMS_TEMPLATE_CODE || ''
  },

  // 支付配置
  payment: {
    wechat: {
      appId: process.env.WECHAT_APP_ID || '',
      mchId: process.env.WECHAT_MCH_ID || '',
      apiKey: process.env.WECHAT_API_KEY || '',
      notifyUrl: process.env.WECHAT_NOTIFY_URL || ''
    },
    alipay: {
      appId: process.env.ALIPAY_APP_ID || '',
      privateKey: process.env.ALIPAY_PRIVATE_KEY || '',
      publicKey: process.env.ALIPAY_PUBLIC_KEY || '',
      notifyUrl: process.env.ALIPAY_NOTIFY_URL || '',
      returnUrl: process.env.ALIPAY_RETURN_URL || ''
    }
  },

  // 微信小程序配置
  wxAppId: process.env.WX_APP_ID || 'wx8792033d9e7052f1',
  wxAppSecret: process.env.WX_APP_SECRET || '5623b74c771d82f6184ec72d319688d4',
  jwtSecret: process.env.JWT_SECRET || 'xinjie_mall_jwt_secret_key_2024',

  // 日志配置
  log: {
    level: process.env.LOG_LEVEL || 'info',
    path: process.env.LOG_PATH || './logs'
  },

  // 安全配置
  security: {
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 100, // 限制每个IP 15分钟内最多100个请求
      message: '请求过于频繁，请稍后再试'
    },
    cors: {
      origin: '*',
      allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowHeaders: ['Content-Type', 'Authorization', 'Accept'],
      credentials: true
    }
  },

  // 轮播图缓存配置
  bannerCache: {
    expireSeconds: parseInt(process.env.BANNER_CACHE_EXPIRE) || 3600 // 默认1小时
  },

  // CDN配置
  cdn: {
    enabled: process.env.CDN_ENABLED === 'true' || false,
    domain: process.env.NODE_ENV === 'development'
      ? 'http://localhost:4000'
      : (process.env.CDN_DOMAIN || 'https://api.xinjie-tea.com'),
    imagePath: process.env.CDN_IMAGE_PATH || '/images',
    fallbackUrl: process.env.NODE_ENV === 'development'
      ? 'http://localhost:4000'
      : (process.env.CDN_FALLBACK_URL || 'https://api.xinjie-tea.com'),
    cacheControl: process.env.CDN_CACHE_CONTROL || 'public, max-age=31536000',
    imageFormats: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
    imageSizes: {
      thumbnail: '150x150',
      small: '300x300',
      medium: '600x600',
      large: '1200x1200'
    }
  }
};

module.exports = config; 