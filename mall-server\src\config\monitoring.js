// 监控配置文件
module.exports = {
  // 应用监控
  application: {
    // 健康检查
    healthCheck: {
      enabled: true,
      interval: 30000, // 30秒检查一次
      timeout: 5000,   // 5秒超时
      endpoints: [
        '/api/health',
        '/api/health/db',
        '/api/health/redis'
      ]
    },
    
    // 性能监控
    performance: {
      enabled: true,
      // 响应时间阈值（毫秒）
      responseTimeThreshold: {
        warning: 1000,  // 1秒警告
        critical: 3000  // 3秒严重
      },
      // 内存使用阈值
      memoryThreshold: {
        warning: 0.8,   // 80%警告
        critical: 0.9   // 90%严重
      },
      // CPU使用阈值
      cpuThreshold: {
        warning: 0.7,   // 70%警告
        critical: 0.9   // 90%严重
      }
    },
    
    // 错误监控
    errorTracking: {
      enabled: true,
      // 错误率阈值
      errorRateThreshold: {
        warning: 0.05,  // 5%警告
        critical: 0.1   // 10%严重
      },
      // 忽略的错误类型
      ignoreErrors: [
        'ValidationError',
        'CastError'
      ]
    }
  },

  // 数据库监控
  database: {
    enabled: true,
    // 连接池监控
    connectionPool: {
      maxConnections: 20,
      warningThreshold: 15,
      criticalThreshold: 18
    },
    // 查询性能监控
    queryPerformance: {
      slowQueryThreshold: 1000, // 1秒
      logSlowQueries: true
    },
    // 数据库大小监控
    storage: {
      warningThreshold: 0.8,  // 80%
      criticalThreshold: 0.9  // 90%
    }
  },

  // Redis监控
  redis: {
    enabled: true,
    // 连接监控
    connection: {
      timeout: 5000,
      retryAttempts: 3
    },
    // 内存使用监控
    memory: {
      warningThreshold: 0.8,
      criticalThreshold: 0.9
    },
    // 命令执行监控
    commands: {
      slowCommandThreshold: 100, // 100ms
      logSlowCommands: true
    }
  },

  // 业务监控
  business: {
    // 订单监控
    orders: {
      // 订单创建监控
      creation: {
        enabled: true,
        alertThreshold: 100 // 每分钟超过100个订单告警
      },
      // 支付成功率监控
      paymentSuccess: {
        enabled: true,
        warningThreshold: 0.9,  // 90%以下警告
        criticalThreshold: 0.8  // 80%以下严重
      },
      // 订单异常监控
      anomaly: {
        enabled: true,
        checkInterval: 300000, // 5分钟检查一次
        thresholds: {
          cancelRate: 0.3,      // 取消率30%以上
          refundRate: 0.1       // 退款率10%以上
        }
      }
    },
    
    // 用户行为监控
    user: {
      // 登录监控
      login: {
        enabled: true,
        failureRateThreshold: 0.1, // 10%失败率
        suspiciousLoginDetection: true
      },
      // 注册监控
      registration: {
        enabled: true,
        dailyThreshold: 1000 // 每日注册超过1000告警
      }
    }
  },

  // 告警配置
  alerts: {
    // 告警渠道
    channels: {
      email: {
        enabled: true,
        recipients: [
          process.env.ALERT_EMAIL || '<EMAIL>'
        ],
        smtp: {
          host: process.env.SMTP_HOST || 'smtp.qq.com',
          port: process.env.SMTP_PORT || 587,
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS
        }
      },
      webhook: {
        enabled: false,
        url: process.env.WEBHOOK_URL,
        timeout: 5000
      },
      sms: {
        enabled: false,
        provider: 'aliyun',
        accessKey: process.env.SMS_ACCESS_KEY,
        secretKey: process.env.SMS_SECRET_KEY,
        signName: '心洁茶叶',
        templateCode: process.env.SMS_ALERT_TEMPLATE
      }
    },
    
    // 告警规则
    rules: {
      // 告警级别
      levels: {
        info: {
          color: '#36a3f7',
          notify: false
        },
        warning: {
          color: '#ffb800',
          notify: true,
          cooldown: 300000 // 5分钟冷却
        },
        critical: {
          color: '#f5222d',
          notify: true,
          cooldown: 60000 // 1分钟冷却
        }
      },
      
      // 告警抑制
      suppression: {
        enabled: true,
        maxAlertsPerHour: 10,
        duplicateWindow: 300000 // 5分钟内相同告警只发送一次
      }
    }
  },

  // 日志配置
  logging: {
    // 监控日志
    monitor: {
      enabled: true,
      level: 'info',
      file: './logs/monitor.log',
      maxSize: '10m',
      maxFiles: 5
    },
    
    // 访问日志
    access: {
      enabled: true,
      format: 'combined',
      file: './logs/access.log',
      maxSize: '50m',
      maxFiles: 10
    },
    
    // 错误日志
    error: {
      enabled: true,
      level: 'error',
      file: './logs/error.log',
      maxSize: '20m',
      maxFiles: 5
    }
  },

  // 报表配置
  reporting: {
    // 日报
    daily: {
      enabled: true,
      time: '09:00',
      recipients: [process.env.REPORT_EMAIL || '<EMAIL>'],
      metrics: [
        'requests',
        'errors',
        'responseTime',
        'orders',
        'users'
      ]
    },
    
    // 周报
    weekly: {
      enabled: true,
      day: 1, // 周一
      time: '09:00',
      recipients: [process.env.REPORT_EMAIL || '<EMAIL>']
    }
  }
};
