const config = require('./index');

module.exports = {
  wechat: {
    appId: config.payment.wechat.appId,
    mchId: config.payment.wechat.mchId,
    apiKey: config.payment.wechat.apiKey,
    notifyUrl: config.payment.wechat.notifyUrl,
    certPath: './certs/wechat/apiclient_cert.p12',
    keyPath: './certs/wechat/apiclient_key.pem'
  },
  alipay: {
    appId: process.env.ALIPAY_APP_ID || '',
    privateKey: process.env.ALIPAY_PRIVATE_KEY || '',
    publicKey: process.env.ALIPAY_PUBLIC_KEY || '',
    notifyUrl: process.env.ALIPAY_NOTIFY_URL || '',
    returnUrl: process.env.ALIPAY_RETURN_URL || ''
  }
}; 