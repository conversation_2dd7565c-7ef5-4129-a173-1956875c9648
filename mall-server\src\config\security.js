// 安全配置文件
module.exports = {
  // 跨域配置
  cors: {
    origin: process.env.NODE_ENV === 'production' 
      ? ['https://xinjie-tea.com', 'https://api.xinjie-tea.com']
      : ['http://localhost:3000', 'http://localhost:4000', 'http://localhost:8080'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
  },

  // 请求频率限制
  rateLimit: {
    // 通用限制
    general: {
      windowMs: 15 * 60 * 1000, // 15分钟
      max: process.env.NODE_ENV === 'production' ? 100 : 1000, // 生产环境更严格
      message: '请求过于频繁，请稍后再试',
      standardHeaders: true,
      legacyHeaders: false
    },
    
    // 登录限制
    auth: {
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 5, // 最多5次登录尝试
      message: '登录尝试次数过多，请15分钟后再试',
      skipSuccessfulRequests: true
    },
    
    // 支付限制
    payment: {
      windowMs: 60 * 1000, // 1分钟
      max: 3, // 最多3次支付请求
      message: '支付请求过于频繁，请稍后再试'
    },
    
    // 上传限制
    upload: {
      windowMs: 60 * 1000, // 1分钟
      max: 10, // 最多10次上传
      message: '上传过于频繁，请稍后再试'
    }
  },

  // 输入验证
  validation: {
    // 密码强度
    password: {
      minLength: 6,
      maxLength: 20,
      requireUppercase: false,
      requireLowercase: false,
      requireNumbers: true,
      requireSpecialChars: false
    },
    
    // 手机号验证
    phone: {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号'
    },
    
    // 邮箱验证
    email: {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: '请输入正确的邮箱地址'
    }
  },

  // 数据加密
  encryption: {
    // 敏感数据加密密钥
    secretKey: process.env.ENCRYPTION_SECRET || 'xinjie_mall_encryption_key_2024',
    
    // 加密算法
    algorithm: 'aes-256-cbc',
    
    // 需要加密的字段
    encryptFields: ['phone', 'email', 'address', 'realName'],
    
    // 密码哈希
    bcrypt: {
      saltRounds: 12
    }
  },

  // 会话安全
  session: {
    // JWT配置
    jwt: {
      secret: process.env.JWT_SECRET || 'xinjie_mall_jwt_secret_2024',
      expiresIn: process.env.NODE_ENV === 'production' ? '7d' : '30d',
      algorithm: 'HS256',
      issuer: 'xinjie-tea.com',
      audience: 'xinjie-mall-users'
    },
    
    // 刷新令牌
    refreshToken: {
      expiresIn: '30d',
      length: 32
    }
  },

  // 文件上传安全
  upload: {
    // 允许的文件类型
    allowedTypes: [
      'image/jpeg',
      'image/png', 
      'image/gif',
      'image/webp'
    ],
    
    // 文件大小限制（字节）
    maxFileSize: 5 * 1024 * 1024, // 5MB
    
    // 文件名安全
    sanitizeFileName: true,
    
    // 病毒扫描（生产环境建议启用）
    virusScan: process.env.NODE_ENV === 'production',
    
    // 图片处理
    imageProcessing: {
      maxWidth: 1920,
      maxHeight: 1080,
      quality: 85,
      format: 'jpeg'
    }
  },

  // SQL注入防护
  sqlInjection: {
    // 参数化查询
    useParameterizedQueries: true,
    
    // 输入过滤
    filterInput: true,
    
    // 危险关键词
    dangerousKeywords: [
      'DROP', 'DELETE', 'TRUNCATE', 'ALTER', 'CREATE',
      'EXEC', 'EXECUTE', 'SCRIPT', 'UNION', 'SELECT'
    ]
  },

  // XSS防护
  xss: {
    // 输出编码
    encodeOutput: true,
    
    // 内容安全策略
    csp: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https://api.xinjie-tea.com"]
    }
  },

  // 日志安全
  logging: {
    // 敏感信息过滤
    filterSensitive: true,
    
    // 不记录的敏感字段
    sensitiveFields: [
      'password', 'token', 'secret', 'key', 
      'phone', 'email', 'idCard', 'bankCard'
    ],
    
    // 日志级别
    level: process.env.NODE_ENV === 'production' ? 'warn' : 'debug',
    
    // 日志保留时间（天）
    retention: 30
  }
};
