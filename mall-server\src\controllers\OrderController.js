// 订单控制器 - 处理订单相关请求
const OrderService = require('../services/OrderService');
const { validationResult } = require('express-validator');

class OrderController {
  
  /**
   * 获取订单列表
   * GET /api/front/order/list
   */
  async getOrderList(req, res) {
    try {
      // 参数验证
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        });
      }
      
      // 获取用户ID（从JWT token中获取）
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '用户未登录'
        });
      }
      
      // 获取查询参数
      const {
        page = 1,
        size = 10,
        status = 'all',
        keyword = ''
      } = req.query;
      
      // 调用服务层获取订单列表
      const result = await OrderService.getOrderList({
        userId,
        page: parseInt(page),
        size: parseInt(size),
        status,
        keyword
      });
      
      if (result.success) {
        res.json({
          success: true,
          data: result.data,
          message: '获取订单列表成功'
        });
      } else {
        res.status(500).json({
          success: false,
          message: result.message || '获取订单列表失败'
        });
      }
      
    } catch (error) {
      console.error('获取订单列表异常:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 获取订单详情
   * GET /api/front/order/:id
   */
  async getOrderDetail(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '用户未登录'
        });
      }
      
      if (!id || isNaN(id)) {
        return res.status(400).json({
          success: false,
          message: '订单ID无效'
        });
      }
      
      const result = await OrderService.getOrderDetail(parseInt(id), userId);
      
      if (result.success) {
        res.json({
          success: true,
          data: result.data,
          message: '获取订单详情成功'
        });
      } else {
        const statusCode = result.message === '订单不存在' ? 404 : 500;
        res.status(statusCode).json({
          success: false,
          message: result.message || '获取订单详情失败'
        });
      }
      
    } catch (error) {
      console.error('获取订单详情异常:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 创建订单
   * POST /api/front/order/create
   */
  async createOrder(req, res) {
    try {
      // 参数验证
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        });
      }
      
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '用户未登录'
        });
      }
      
      const orderData = {
        userId,
        ...req.body
      };
      
      const result = await OrderService.createOrder(orderData);
      
      if (result.success) {
        res.status(201).json({
          success: true,
          data: result.data,
          message: result.message || '订单创建成功'
        });
      } else {
        res.status(400).json({
          success: false,
          message: result.message || '订单创建失败'
        });
      }
      
    } catch (error) {
      console.error('创建订单异常:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 取消订单
   * PUT /api/front/order/:id/cancel
   */
  async cancelOrder(req, res) {
    try {
      const { id } = req.params;
      const { reason } = req.body;
      const userId = req.user?.id;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '用户未登录'
        });
      }
      
      if (!id || isNaN(id)) {
        return res.status(400).json({
          success: false,
          message: '订单ID无效'
        });
      }
      
      const result = await OrderService.cancelOrder(parseInt(id), userId, reason);
      
      if (result.success) {
        res.json({
          success: true,
          message: result.message || '订单取消成功'
        });
      } else {
        const statusCode = result.message.includes('不存在') ? 404 : 400;
        res.status(statusCode).json({
          success: false,
          message: result.message || '订单取消失败'
        });
      }
      
    } catch (error) {
      console.error('取消订单异常:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 确认收货
   * PUT /api/front/order/:id/confirm
   */
  async confirmDelivery(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '用户未登录'
        });
      }
      
      if (!id || isNaN(id)) {
        return res.status(400).json({
          success: false,
          message: '订单ID无效'
        });
      }
      
      const result = await OrderService.confirmDelivery(parseInt(id), userId);
      
      if (result.success) {
        res.json({
          success: true,
          message: result.message || '确认收货成功'
        });
      } else {
        const statusCode = result.message.includes('不存在') ? 404 : 400;
        res.status(statusCode).json({
          success: false,
          message: result.message || '确认收货失败'
        });
      }
      
    } catch (error) {
      console.error('确认收货异常:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 获取订单统计
   * GET /api/front/order/statistics
   */
  async getOrderStatistics(req, res) {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '用户未登录'
        });
      }
      
      // 这里可以调用服务层获取订单统计数据
      // 简化处理，返回模拟数据
      const statistics = {
        pending: 0,    // 待付款
        paid: 0,       // 待发货
        shipped: 0,    // 待收货
        delivered: 0,  // 待评价
        completed: 0,  // 已完成
        cancelled: 0,  // 已取消
        refunded: 0    // 已退款
      };
      
      res.json({
        success: true,
        data: statistics,
        message: '获取订单统计成功'
      });
      
    } catch (error) {
      console.error('获取订单统计异常:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 申请退款
   * POST /api/front/order/:id/refund
   */
  async applyRefund(req, res) {
    try {
      const { id } = req.params;
      const { reason, amount, items } = req.body;
      const userId = req.user?.id;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '用户未登录'
        });
      }
      
      if (!id || isNaN(id)) {
        return res.status(400).json({
          success: false,
          message: '订单ID无效'
        });
      }
      
      // 这里应该调用服务层处理退款申请
      // 简化处理，直接返回成功
      res.json({
        success: true,
        message: '退款申请提交成功，请等待审核'
      });
      
    } catch (error) {
      console.error('申请退款异常:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 删除订单
   * DELETE /api/front/order/:id
   */
  async deleteOrder(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '用户未登录'
        });
      }
      
      if (!id || isNaN(id)) {
        return res.status(400).json({
          success: false,
          message: '订单ID无效'
        });
      }
      
      // 这里应该调用服务层删除订单
      // 注意：只有已完成或已取消的订单才能删除
      res.json({
        success: true,
        message: '订单删除成功'
      });
      
    } catch (error) {
      console.error('删除订单异常:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
}

module.exports = new OrderController();
