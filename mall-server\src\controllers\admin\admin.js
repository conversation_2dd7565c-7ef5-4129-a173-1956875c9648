const AdminUser = require('../../models/adminUser');
const Role = require('../../models/role');
const bcrypt = require('bcrypt');
const { Op } = require('sequelize');

class AdminController {
  // 获取管理员列表
  async getAdminList(ctx) {
    try {
      const { page = 1, limit = 10, username, role_id, status } = ctx.query;
      const offset = (page - 1) * limit;
      
      const where = {};
      if (username) {
        where.username = { [Op.like]: `%${username}%` };
      }
      if (role_id) {
        where.role_id = role_id;
      }
      if (status !== undefined && status !== '') {
        where.status = parseInt(status);
      }

      const { count, rows } = await AdminUser.findAndCountAll({
        where,
        include: [
          {
            model: Role,
            as: 'Role',
            attributes: ['id', 'name', 'code']
          }
        ],
        attributes: { exclude: ['password'] },
        order: [['created_at', 'DESC']],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: {
          list: rows,
          total: count,
          page: parseInt(page),
          limit: parseInt(limit)
        }
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error.message
      };
    }
  }

  // 创建管理员
  async createAdmin(ctx) {
    try {
      const {
        username,
        password,
        real_name,
        email,
        phone,
        role_id,
        department,
        position
      } = ctx.request.body;

      // 检查用户名是否已存在
      const existingAdmin = await AdminUser.findOne({ where: { username } });
      if (existingAdmin) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '用户名已存在'
        };
        return;
      }

      // 检查邮箱是否已存在
      if (email) {
        const existingEmail = await AdminUser.findOne({ where: { email } });
        if (existingEmail) {
          ctx.status = 400;
          ctx.body = {
            code: 400,
            message: '邮箱已存在'
          };
          return;
        }
      }

      // 验证密码强度
      const passwordErrors = this.validatePassword(password);
      if (passwordErrors.length > 0) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: passwordErrors.join(', ')
        };
        return;
      }

      // 加密密码
      const hashedPassword = await bcrypt.hash(password, 10);

      const admin = await AdminUser.create({
        username,
        password: hashedPassword,
        real_name,
        email,
        phone,
        role_id,
        department,
        position,
        created_by: ctx.state.user.id,
        password_changed_at: new Date(),
        password_expires_at: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000) // 90天后过期
      });

      ctx.body = {
        code: 200,
        message: '创建成功',
        data: {
          id: admin.id,
          username: admin.username,
          real_name: admin.real_name,
          email: admin.email,
          phone: admin.phone,
          role_id: admin.role_id,
          department: admin.department,
          position: admin.position
        }
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error.message
      };
    }
  }

  // 更新管理员
  async updateAdmin(ctx) {
    try {
      const { id } = ctx.params;
      const {
        real_name,
        email,
        phone,
        role_id,
        department,
        position
      } = ctx.request.body;

      const admin = await AdminUser.findByPk(id);
      if (!admin) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '管理员不存在'
        };
        return;
      }

      // 检查邮箱是否被其他用户使用
      if (email && email !== admin.email) {
        const existingEmail = await AdminUser.findOne({
          where: { email, id: { [Op.ne]: id } }
        });
        if (existingEmail) {
          ctx.status = 400;
          ctx.body = {
            code: 400,
            message: '邮箱已被其他用户使用'
          };
          return;
        }
      }

      await admin.update({
        real_name,
        email,
        phone,
        role_id,
        department,
        position
      });

      ctx.body = {
        code: 200,
        message: '更新成功'
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error.message
      };
    }
  }

  // 删除管理员
  async deleteAdmin(ctx) {
    try {
      const { id } = ctx.params;

      const admin = await AdminUser.findByPk(id);
      if (!admin) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '管理员不存在'
        };
        return;
      }

      // 不能删除自己
      if (parseInt(id) === ctx.state.user.id) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '不能删除自己'
        };
        return;
      }

      await admin.destroy();

      ctx.body = {
        code: 200,
        message: '删除成功'
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error.message
      };
    }
  }

  // 更新管理员状态
  async updateAdminStatus(ctx) {
    try {
      const { id } = ctx.params;
      const { status } = ctx.request.body;

      const admin = await AdminUser.findByPk(id);
      if (!admin) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '管理员不存在'
        };
        return;
      }

      // 不能禁用自己
      if (parseInt(id) === ctx.state.user.id && status === 0) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '不能禁用自己'
        };
        return;
      }

      await admin.update({ status });

      ctx.body = {
        code: 200,
        message: '状态更新成功'
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error.message
      };
    }
  }

  // 重置管理员密码
  async resetAdminPassword(ctx) {
    try {
      const { id } = ctx.params;
      const { new_password } = ctx.request.body;

      const admin = await AdminUser.findByPk(id);
      if (!admin) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '管理员不存在'
        };
        return;
      }

      // 验证密码强度
      const passwordErrors = this.validatePassword(new_password);
      if (passwordErrors.length > 0) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: passwordErrors.join(', ')
        };
        return;
      }

      // 加密密码
      const hashedPassword = await bcrypt.hash(new_password, 10);

      await admin.update({
        password: hashedPassword,
        password_changed_at: new Date(),
        password_expires_at: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
        login_attempts: 0
      });

      ctx.body = {
        code: 200,
        message: '密码重置成功'
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error.message
      };
    }
  }

  // 获取管理员详情
  async getAdminDetail(ctx) {
    try {
      const { id } = ctx.params;

      const admin = await AdminUser.findByPk(id, {
        include: [
          {
            model: Role,
            as: 'Role',
            attributes: ['id', 'name', 'code']
          }
        ],
        attributes: { exclude: ['password'] }
      });

      if (!admin) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '管理员不存在'
        };
        return;
      }

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: admin
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error.message
      };
    }
  }

  // 验证密码强度
  validatePassword(password) {
    const errors = [];
    const policy = {
      min_length: 8,
      require_uppercase: true,
      require_lowercase: true,
      require_number: true,
      require_special: true
    };

    if (password.length < policy.min_length) {
      errors.push(`密码长度不能少于${policy.min_length}位`);
    }
    if (policy.require_uppercase && !/[A-Z]/.test(password)) {
      errors.push('密码必须包含大写字母');
    }
    if (policy.require_lowercase && !/[a-z]/.test(password)) {
      errors.push('密码必须包含小写字母');
    }
    if (policy.require_number && !/\d/.test(password)) {
      errors.push('密码必须包含数字');
    }
    if (policy.require_special && !/[!@#$%^&*]/.test(password)) {
      errors.push('密码必须包含特殊字符');
    }

    return errors;
  }
}

module.exports = new AdminController(); 