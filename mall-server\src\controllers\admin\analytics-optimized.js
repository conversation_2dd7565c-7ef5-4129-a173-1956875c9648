// 优化版数据分析控制器 - 高效简洁版
const analyticsService = require('../../services/analytics-optimized');
const stockAlertService = require('../../services/stockAlert-optimized');
const autoShippingService = require('../../services/autoShipping-optimized');

class OptimizedAnalyticsController {

  // 统一响应格式
  sendResponse(ctx, data, message = '操作成功', code = 200) {
    ctx.status = code >= 400 ? code : 200;
    ctx.body = { code, message, data, timestamp: Date.now() };
  }

  // 统一错误处理
  handleError(ctx, error, message = '操作失败') {
    console.error(`${message}:`, error);
    this.sendResponse(ctx, null, `${message}: ${error.message}`, 500);
  }

  // 参数验证
  validateDateRange(ctx) {
    const { start_date, end_date } = ctx.query;
    if (!start_date || !end_date) {
      this.sendResponse(ctx, null, '请提供开始和结束日期', 400);
      return null;
    }
    return { startDate: new Date(start_date), endDate: new Date(end_date) };
  }

  // 综合仪表板 - 新增
  async getDashboard(ctx) {
    try {
      const data = await analyticsService.getDashboardData();
      this.sendResponse(ctx, data);
    } catch (error) {
      this.handleError(ctx, error, '获取仪表板数据失败');
    }
  }

  // 实时统计
  async getRealTimeStats(ctx) {
    try {
      const data = await analyticsService.getRealTimeStats();
      this.sendResponse(ctx, data);
    } catch (error) {
      this.handleError(ctx, error, '获取实时统计失败');
    }
  }

  // 销售趋势
  async getSalesTrend(ctx) {
    try {
      const { days = 7 } = ctx.query;
      const data = await analyticsService.getSalesTrend(parseInt(days));
      this.sendResponse(ctx, data);
    } catch (error) {
      this.handleError(ctx, error, '获取销售趋势失败');
    }
  }

  // 热销商品
  async getTopProducts(ctx) {
    try {
      const { limit = 10 } = ctx.query;
      const data = await analyticsService.getTopProducts(parseInt(limit));
      this.sendResponse(ctx, data);
    } catch (error) {
      this.handleError(ctx, error, '获取热销商品失败');
    }
  }

  // 转化漏斗
  async getConversionFunnel(ctx) {
    try {
      const { days = 7 } = ctx.query;
      const data = await analyticsService.getConversionFunnel(parseInt(days));
      this.sendResponse(ctx, data);
    } catch (error) {
      this.handleError(ctx, error, '获取转化漏斗失败');
    }
  }

  // 用户行为热力图 - 新增
  async getBehaviorHeatmap(ctx) {
    try {
      const { days = 7 } = ctx.query;
      const data = await analyticsService.getUserBehaviorHeatmap(parseInt(days));
      this.sendResponse(ctx, data);
    } catch (error) {
      this.handleError(ctx, error, '获取行为热力图失败');
    }
  }

  // 搜索关键词
  async getSearchKeywords(ctx) {
    try {
      const { limit = 20 } = ctx.query;
      const data = await analyticsService.getSearchKeywords(parseInt(limit));
      this.sendResponse(ctx, data);
    } catch (error) {
      this.handleError(ctx, error, '获取搜索关键词失败');
    }
  }

  // 批量分析 - 新增
  async getBatchAnalytics(ctx) {
    try {
      const { queries } = ctx.request.body;
      if (!queries || typeof queries !== 'object') {
        return this.sendResponse(ctx, null, '请提供有效的查询配置', 400);
      }
      
      const data = await analyticsService.getBatchAnalytics(queries);
      this.sendResponse(ctx, data);
    } catch (error) {
      this.handleError(ctx, error, '批量分析失败');
    }
  }

  // === 库存预警相关 ===

  // 库存预警统计
  async getStockAlertStats(ctx) {
    try {
      const data = await stockAlertService.getAlertStats();
      this.sendResponse(ctx, data);
    } catch (error) {
      this.handleError(ctx, error, '获取库存预警统计失败');
    }
  }

  // 检查库存
  async checkStock(ctx) {
    try {
      const alertCount = await stockAlertService.checkAllProductsStock();
      this.sendResponse(ctx, { alert_count: alertCount });
    } catch (error) {
      this.handleError(ctx, error, '检查库存失败');
    }
  }

  // 智能补货建议 - 新增
  async getStockRecommendations(ctx) {
    try {
      const data = await stockAlertService.getSmartRecommendations();
      this.sendResponse(ctx, data);
    } catch (error) {
      this.handleError(ctx, error, '获取补货建议失败');
    }
  }

  // 批量处理预警 - 新增
  async batchProcessAlerts(ctx) {
    try {
      const { alert_ids, action, note } = ctx.request.body;
      const adminUserId = ctx.state.user?.id;

      if (!alert_ids || !Array.isArray(alert_ids) || !action) {
        return this.sendResponse(ctx, null, '请提供有效的预警ID列表和操作类型', 400);
      }

      let result;
      if (action === 'resolve') {
        result = await stockAlertService.batchResolveAlerts(alert_ids, adminUserId, note);
      } else {
        return this.sendResponse(ctx, null, '不支持的操作类型', 400);
      }

      this.sendResponse(ctx, { processed_count: result });
    } catch (error) {
      this.handleError(ctx, error, '批量处理预警失败');
    }
  }

  // 预警趋势 - 新增
  async getAlertTrends(ctx) {
    try {
      const { days = 30 } = ctx.query;
      const data = await stockAlertService.getAlertTrends(parseInt(days));
      this.sendResponse(ctx, data);
    } catch (error) {
      this.handleError(ctx, error, '获取预警趋势失败');
    }
  }

  // === 自动发货相关 ===

  // 发货统计
  async getShippingStats(ctx) {
    try {
      const { days = 7 } = ctx.query;
      const data = await autoShippingService.getShippingStats(parseInt(days));
      this.sendResponse(ctx, data);
    } catch (error) {
      this.handleError(ctx, error, '获取发货统计失败');
    }
  }

  // 处理自动发货
  async processAutoShipping(ctx) {
    try {
      const processedCount = await autoShippingService.processBatchShipping();
      this.sendResponse(ctx, { processed_count: processedCount });
    } catch (error) {
      this.handleError(ctx, error, '处理自动发货失败');
    }
  }

  // 重试失败发货
  async retryFailedShipping(ctx) {
    try {
      const retryCount = await autoShippingService.retryFailedShipping();
      this.sendResponse(ctx, { retry_count: retryCount });
    } catch (error) {
      this.handleError(ctx, error, '重试失败发货失败');
    }
  }

  // 发货性能分析 - 新增
  async getShippingPerformance(ctx) {
    try {
      const data = await autoShippingService.getPerformanceAnalysis();
      this.sendResponse(ctx, data);
    } catch (error) {
      this.handleError(ctx, error, '获取发货性能分析失败');
    }
  }

  // 更新发货配置
  async updateShippingConfig(ctx) {
    try {
      const config = ctx.request.body;
      autoShippingService.updateConfig(config);
      this.sendResponse(ctx, autoShippingService.getConfig());
    } catch (error) {
      this.handleError(ctx, error, '更新发货配置失败');
    }
  }

  // === 系统相关 ===

  // 清理缓存
  async clearCache(ctx) {
    try {
      const { pattern } = ctx.query;
      analyticsService.clearCache(pattern);
      this.sendResponse(ctx, { message: '缓存已清理' });
    } catch (error) {
      this.handleError(ctx, error, '清理缓存失败');
    }
  }

  // 系统性能监控
  async getSystemPerformance(ctx) {
    try {
      const data = await analyticsService.getPerformanceMetrics();
      this.sendResponse(ctx, data);
    } catch (error) {
      this.handleError(ctx, error, '获取系统性能失败');
    }
  }

  // 配置管理
  async getConfigs(ctx) {
    try {
      const data = {
        stockAlert: stockAlertService.getConfig(),
        autoShipping: autoShippingService.getConfig()
      };
      this.sendResponse(ctx, data);
    } catch (error) {
      this.handleError(ctx, error, '获取配置失败');
    }
  }

  // 更新配置
  async updateConfigs(ctx) {
    try {
      const { stockAlert, autoShipping } = ctx.request.body;
      
      if (stockAlert) {
        stockAlertService.updateConfig(stockAlert);
      }
      
      if (autoShipping) {
        autoShippingService.updateConfig(autoShipping);
      }
      
      this.sendResponse(ctx, { message: '配置更新成功' });
    } catch (error) {
      this.handleError(ctx, error, '更新配置失败');
    }
  }
}

module.exports = new OptimizedAnalyticsController();
