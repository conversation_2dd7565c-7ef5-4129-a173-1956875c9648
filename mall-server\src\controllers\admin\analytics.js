// 数据分析控制器
const analyticsService = require('../../services/analytics');
const salesReportService = require('../../services/salesReport');
const stockAlertService = require('../../services/stockAlert');
const autoShippingService = require('../../services/autoShipping');
const userBehaviorService = require('../../services/userBehavior');

class AnalyticsController {

  // 获取销售数据分析
  async getSalesAnalytics(ctx) {
    try {
      const { 
        start_date, 
        end_date, 
        type = 'daily' 
      } = ctx.query;

      if (!start_date || !end_date) {
        ctx.body = {
          code: 400,
          message: '请提供开始和结束日期'
        };
        return;
      }

      const startDate = new Date(start_date);
      const endDate = new Date(end_date);

      const analytics = await analyticsService.getSalesAnalytics(startDate, endDate, type);

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: analytics
      };
    } catch (error) {
      console.error('获取销售分析失败:', error);
      ctx.body = {
        code: 500,
        message: '获取销售分析失败',
        error: error.message
      };
    }
  }

  // 获取用户行为分析
  async getUserBehaviorAnalytics(ctx) {
    try {
      const { 
        start_date, 
        end_date,
        user_id,
        behavior_type
      } = ctx.query;

      if (!start_date || !end_date) {
        ctx.body = {
          code: 400,
          message: '请提供开始和结束日期'
        };
        return;
      }

      const startDate = new Date(start_date);
      const endDate = new Date(end_date);
      const filters = {};
      
      if (user_id) filters.userId = user_id;
      if (behavior_type) filters.behaviorType = behavior_type;

      const analytics = await userBehaviorService.getUserBehaviorAnalysis(startDate, endDate, filters);

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: analytics
      };
    } catch (error) {
      console.error('获取用户行为分析失败:', error);
      ctx.body = {
        code: 500,
        message: '获取用户行为分析失败',
        error: error.message
      };
    }
  }

  // 获取实时数据统计
  async getRealTimeStats(ctx) {
    try {
      const stats = await analyticsService.getRealTimeStats();

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: stats
      };
    } catch (error) {
      console.error('获取实时统计失败:', error);
      ctx.body = {
        code: 500,
        message: '获取实时统计失败',
        error: error.message
      };
    }
  }

  // 获取转化漏斗分析
  async getConversionFunnel(ctx) {
    try {
      const { start_date, end_date } = ctx.query;

      if (!start_date || !end_date) {
        ctx.body = {
          code: 400,
          message: '请提供开始和结束日期'
        };
        return;
      }

      const startDate = new Date(start_date);
      const endDate = new Date(end_date);

      const funnelData = await userBehaviorService.getConversionFunnelAnalysis(startDate, endDate);

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: funnelData
      };
    } catch (error) {
      console.error('获取转化漏斗分析失败:', error);
      ctx.body = {
        code: 500,
        message: '获取转化漏斗分析失败',
        error: error.message
      };
    }
  }

  // 获取商品浏览分析
  async getProductViewAnalysis(ctx) {
    try {
      const { start_date, end_date } = ctx.query;

      if (!start_date || !end_date) {
        ctx.body = {
          code: 400,
          message: '请提供开始和结束日期'
        };
        return;
      }

      const startDate = new Date(start_date);
      const endDate = new Date(end_date);

      const analysis = await userBehaviorService.getProductViewAnalysis(startDate, endDate);

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: analysis
      };
    } catch (error) {
      console.error('获取商品浏览分析失败:', error);
      ctx.body = {
        code: 500,
        message: '获取商品浏览分析失败',
        error: error.message
      };
    }
  }

  // 获取用户路径分析
  async getUserPathAnalysis(ctx) {
    try {
      const { start_date, end_date } = ctx.query;

      if (!start_date || !end_date) {
        ctx.body = {
          code: 400,
          message: '请提供开始和结束日期'
        };
        return;
      }

      const startDate = new Date(start_date);
      const endDate = new Date(end_date);

      const pathAnalysis = await userBehaviorService.getUserPathAnalysis(startDate, endDate);

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: pathAnalysis
      };
    } catch (error) {
      console.error('获取用户路径分析失败:', error);
      ctx.body = {
        code: 500,
        message: '获取用户路径分析失败',
        error: error.message
      };
    }
  }

  // 获取用户留存分析
  async getUserRetentionAnalysis(ctx) {
    try {
      const { start_date, end_date } = ctx.query;

      if (!start_date || !end_date) {
        ctx.body = {
          code: 400,
          message: '请提供开始和结束日期'
        };
        return;
      }

      const startDate = new Date(start_date);
      const endDate = new Date(end_date);

      const retentionAnalysis = await userBehaviorService.getUserRetentionAnalysis(startDate, endDate);

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: retentionAnalysis
      };
    } catch (error) {
      console.error('获取用户留存分析失败:', error);
      ctx.body = {
        code: 500,
        message: '获取用户留存分析失败',
        error: error.message
      };
    }
  }

  // 生成销售报表
  async generateSalesReport(ctx) {
    try {
      const { type, date } = ctx.request.body;

      if (!type || !['daily', 'weekly', 'monthly'].includes(type)) {
        ctx.body = {
          code: 400,
          message: '请提供有效的报表类型 (daily/weekly/monthly)'
        };
        return;
      }

      const reportDate = date ? new Date(date) : new Date();
      let report;

      switch (type) {
        case 'daily':
          report = await salesReportService.generateDailyReport(reportDate);
          break;
        case 'weekly':
          report = await salesReportService.generateWeeklyReport(reportDate);
          break;
        case 'monthly':
          report = await salesReportService.generateMonthlyReport(reportDate);
          break;
      }

      ctx.body = {
        code: 200,
        message: '报表生成成功',
        data: report
      };
    } catch (error) {
      console.error('生成销售报表失败:', error);
      ctx.body = {
        code: 500,
        message: '生成销售报表失败',
        error: error.message
      };
    }
  }

  // 获取销售报表列表
  async getSalesReports(ctx) {
    try {
      const { 
        type, 
        start_date, 
        end_date, 
        page = 1, 
        limit = 20 
      } = ctx.query;

      if (!type) {
        ctx.body = {
          code: 400,
          message: '请提供报表类型'
        };
        return;
      }

      const reports = await salesReportService.getReports(
        type, 
        start_date, 
        end_date, 
        page, 
        limit
      );

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: reports
      };
    } catch (error) {
      console.error('获取销售报表失败:', error);
      ctx.body = {
        code: 500,
        message: '获取销售报表失败',
        error: error.message
      };
    }
  }
}

module.exports = new AnalyticsController();
