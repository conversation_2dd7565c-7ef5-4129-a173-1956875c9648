const authService = require('../../services/auth');

class AuthController {
  // 管理员登录
  async login(ctx) {
    try {
      const { username, password } = ctx.request.body;
      
      const result = await authService.login(username, password);
      
      ctx.body = {
        code: 200,
        message: '登录成功',
        data: result
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 管理员登出
  async logout(ctx) {
    try {
      await authService.logout(ctx.state.user.id);
      
      ctx.body = {
        code: 200,
        message: '登出成功'
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 获取管理员信息
  async getAdminInfo(ctx) {
    try {
      const adminId = ctx.state.user.id;
      const adminInfo = await authService.getAdminInfo(adminId);
      
      ctx.body = {
        code: 200,
        message: '获取成功',
        data: adminInfo
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 修改管理员密码
  async changePassword(ctx) {
    try {
      const adminId = ctx.state.user.id;
      const { oldPassword, newPassword } = ctx.request.body;
      
      await authService.changePassword(adminId, oldPassword, newPassword);
      
      ctx.body = {
        code: 200,
        message: '密码修改成功'
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }
}

module.exports = new AuthController(); 