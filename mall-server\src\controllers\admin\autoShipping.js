// 自动发货控制器
const autoShippingService = require('../../services/autoShipping');

class AutoShippingController {

  // 获取发货记录
  async getShippingRecords(ctx) {
    try {
      const {
        order_id,
        status,
        start_date,
        end_date,
        page = 1,
        limit = 20
      } = ctx.query;

      const filters = {
        orderId: order_id,
        status,
        startDate: start_date,
        endDate: end_date,
        page: parseInt(page),
        limit: parseInt(limit)
      };

      const records = await autoShippingService.getShippingRecords(filters);

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: records
      };
    } catch (error) {
      console.error('获取发货记录失败:', error);
      ctx.body = {
        code: 500,
        message: '获取发货记录失败',
        error: error.message
      };
    }
  }

  // 手动触发自动发货
  async processAutoShipping(ctx) {
    try {
      const processedCount = await autoShippingService.processAutoShipping();

      ctx.body = {
        code: 200,
        message: '自动发货处理完成',
        data: {
          processed_count: processedCount
        }
      };
    } catch (error) {
      console.error('自动发货处理失败:', error);
      ctx.body = {
        code: 500,
        message: '自动发货处理失败',
        error: error.message
      };
    }
  }

  // 重试失败的发货
  async retryShipping(ctx) {
    try {
      const { id } = ctx.params;

      const shipping = await autoShippingService.retryFailedShipping(id);

      ctx.body = {
        code: 200,
        message: '重试发货成功',
        data: shipping
      };
    } catch (error) {
      console.error('重试发货失败:', error);
      ctx.body = {
        code: 500,
        message: '重试发货失败',
        error: error.message
      };
    }
  }

  // 获取发货统计
  async getShippingStats(ctx) {
    try {
      const stats = await autoShippingService.getShippingStats();

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: stats
      };
    } catch (error) {
      console.error('获取发货统计失败:', error);
      ctx.body = {
        code: 500,
        message: '获取发货统计失败',
        error: error.message
      };
    }
  }

  // 更新自动发货规则
  async updateShippingRules(ctx) {
    try {
      const {
        enable_auto_shipping,
        auto_shipping_delay,
        working_hours_only,
        exclude_weekends
      } = ctx.request.body;

      const rules = {};
      if (enable_auto_shipping !== undefined) rules.enableAutoShipping = Boolean(enable_auto_shipping);
      if (auto_shipping_delay !== undefined) rules.autoShippingDelay = parseInt(auto_shipping_delay);
      if (working_hours_only !== undefined) rules.workingHoursOnly = Boolean(working_hours_only);
      if (exclude_weekends !== undefined) rules.excludeWeekends = Boolean(exclude_weekends);

      autoShippingService.updateShippingRules(rules);

      ctx.body = {
        code: 200,
        message: '自动发货规则更新成功',
        data: rules
      };
    } catch (error) {
      console.error('更新自动发货规则失败:', error);
      ctx.body = {
        code: 500,
        message: '更新自动发货规则失败',
        error: error.message
      };
    }
  }
}

module.exports = new AutoShippingController();
