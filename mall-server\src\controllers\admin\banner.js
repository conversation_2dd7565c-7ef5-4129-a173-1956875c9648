const { Banner } = require('../../models');
const bannerService = require('../../services/banner');

// 获取轮播图列表
const getBannerList = async (ctx) => {
  try {
    const { page = 1, limit = 10, status } = ctx.query;
    
    const where = {};
    if (status !== undefined) {
      where.status = parseInt(status);
    }
    
    const offset = (page - 1) * limit;
    
    const { count, rows } = await Banner.findAndCountAll({
      where,
      order: [['sort_order', 'ASC'], ['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
    
    ctx.body = {
      success: true,
      data: {
        list: rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    console.error('获取轮播图列表失败:', error);
    ctx.body = {
      success: false,
      message: '获取轮播图列表失败'
    };
  }
};

// 获取轮播图详情
const getBannerDetail = async (ctx) => {
  try {
    const { id } = ctx.params;
    
    const banner = await Banner.findByPk(id);
    
    if (!banner) {
      ctx.body = {
        success: false,
        message: '轮播图不存在'
      };
      return;
    }
    
    ctx.body = {
      success: true,
      data: banner
    };
  } catch (error) {
    console.error('获取轮播图详情失败:', error);
    ctx.body = {
      success: false,
      message: '获取轮播图详情失败'
    };
  }
};

// 添加轮播图
const addBanner = async (ctx) => {
  try {
    const bannerData = ctx.request.body;
    
    const banner = await bannerService.createBanner(bannerData);
    
    ctx.body = {
      success: true,
      data: banner,
      message: '轮播图添加成功'
    };
  } catch (error) {
    console.error('添加轮播图失败:', error);
    ctx.body = {
      success: false,
      message: '添加轮播图失败'
    };
  }
};

// 更新轮播图
const updateBanner = async (ctx) => {
  try {
    const { id } = ctx.params;
    const updateData = ctx.request.body;
    
    const banner = await bannerService.updateBanner(id, updateData);
    
    ctx.body = {
      success: true,
      data: banner,
      message: '轮播图更新成功'
    };
  } catch (error) {
    console.error('更新轮播图失败:', error);
    ctx.body = {
      success: false,
      message: error.message || '更新轮播图失败'
    };
  }
};

// 删除轮播图
const deleteBanner = async (ctx) => {
  try {
    const { id } = ctx.params;
    
    await bannerService.deleteBanner(id);
    
    ctx.body = {
      success: true,
      message: '轮播图删除成功'
    };
  } catch (error) {
    console.error('删除轮播图失败:', error);
    ctx.body = {
      success: false,
      message: error.message || '删除轮播图失败'
    };
  }
};

// 更新轮播图状态
const updateBannerStatus = async (ctx) => {
  try {
    const { id } = ctx.params;
    const { status } = ctx.request.body;
    
    await bannerService.updateBanner(id, { status });
    
    ctx.body = {
      success: true,
      message: '轮播图状态更新成功'
    };
  } catch (error) {
    console.error('更新轮播图状态失败:', error);
    ctx.body = {
      success: false,
      message: error.message || '更新轮播图状态失败'
    };
  }
};

// 手动清理轮播图缓存
const clearBannerCache = async (ctx) => {
  try {
    const result = await bannerService.clearCache();
    
    if (result) {
      ctx.body = {
        success: true,
        message: '轮播图缓存清理成功'
      };
    } else {
      ctx.body = {
        success: false,
        message: '轮播图缓存清理失败'
      };
    }
  } catch (error) {
    console.error('清理轮播图缓存失败:', error);
    ctx.body = {
      success: false,
      message: '清理轮播图缓存失败'
    };
  }
};

module.exports = {
  getBannerList,
  getBannerDetail,
  addBanner,
  updateBanner,
  deleteBanner,
  updateBannerStatus,
  clearBannerCache
};  
