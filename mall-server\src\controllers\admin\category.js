const { Category } = require('../../models');
const categoryService = require('../../services/category');
const apiCache = require('../../middleware/apiCache');

// 获取分类列表
const getCategoryList = async (ctx) => {
  try {
    const { page = 1, limit = 10, status } = ctx.query;
    
    const where = {};
    if (status !== undefined) {
      where.status = parseInt(status);
    }
    
    const offset = (page - 1) * limit;
    
    const { count, rows } = await Category.findAndCountAll({
      where,
      order: [['sort_order', 'ASC'], ['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
    
    ctx.body = {
      success: true,
      data: {
        list: rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    console.error('获取分类列表失败:', error);
    ctx.body = {
      success: false,
      message: '获取分类列表失败'
    };
  }
};

// 获取分类详情
const getCategoryDetail = async (ctx) => {
  try {
    const { id } = ctx.params;
    
    const category = await Category.findByPk(id);
    
    if (!category) {
      ctx.body = {
        success: false,
        message: '分类不存在'
      };
      return;
    }
    
    ctx.body = {
      success: true,
      data: category
    };
  } catch (error) {
    console.error('获取分类详情失败:', error);
    ctx.body = {
      success: false,
      message: '获取分类详情失败'
    };
  }
};

// 添加分类
const addCategory = async (ctx) => {
  try {
    const categoryData = ctx.request.body;
    
    const category = await categoryService.createCategory(categoryData);
    await apiCache.clearModuleCache('category');
    
    ctx.body = {
      success: true,
      data: category,
      message: '分类添加成功'
    };
  } catch (error) {
    console.error('添加分类失败:', error);
    ctx.body = {
      success: false,
      message: '添加分类失败'
    };
  }
};

// 更新分类
const updateCategory = async (ctx) => {
  try {
    const { id } = ctx.params;
    const updateData = ctx.request.body;
    
    const category = await categoryService.updateCategory(id, updateData);
    await apiCache.clearModuleCache('category');
    
    ctx.body = {
      success: true,
      data: category,
      message: '分类更新成功'
    };
  } catch (error) {
    console.error('更新分类失败:', error);
    ctx.body = {
      success: false,
      message: error.message || '更新分类失败'
    };
  }
};

// 删除分类
const deleteCategory = async (ctx) => {
  try {
    const { id } = ctx.params;
    
    await categoryService.deleteCategory(id);
    await apiCache.clearModuleCache('category');
    
    ctx.body = {
      success: true,
      message: '分类删除成功'
    };
  } catch (error) {
    console.error('删除分类失败:', error);
    ctx.body = {
      success: false,
      message: error.message || '删除分类失败'
    };
  }
};

// 更新分类状态
const updateCategoryStatus = async (ctx) => {
  try {
    const { id } = ctx.params;
    const { status } = ctx.request.body;
    
    await categoryService.updateCategory(id, { status });
    await apiCache.clearModuleCache('category');
    
    ctx.body = {
      success: true,
      message: '分类状态更新成功'
    };
  } catch (error) {
    console.error('更新分类状态失败:', error);
    ctx.body = {
      success: false,
      message: error.message || '更新分类状态失败'
    };
  }
};

// 手动清理分类缓存
const clearCategoryCache = async (ctx) => {
  try {
    const result = await categoryService.clearCache();
    
    if (result) {
      ctx.body = {
        success: true,
        message: '分类缓存清理成功'
      };
    } else {
      ctx.body = {
        success: false,
        message: '分类缓存清理失败'
      };
    }
  } catch (error) {
    console.error('清理分类缓存失败:', error);
    ctx.body = {
      success: false,
      message: '清理分类缓存失败'
    };
  }
};

module.exports = {
  getCategoryList,
  getCategoryDetail,
  addCategory,
  updateCategory,
  deleteCategory,
  updateCategoryStatus,
  clearCategoryCache
};  
