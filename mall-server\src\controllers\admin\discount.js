const { Discount, Product, Category, ProductDiscount, CategoryDiscount, UserDiscountUsage, sequelize } = require('../../models');
const { Op } = require('sequelize');
const { apiCache } = require('../../middleware');

// 获取折扣列表
const getDiscountList = async (ctx) => {
  try {
    const {
      page = 1,
      pageSize = 10,
      name = '',
      type = '',
      status = '',
      applicable_to = ''
    } = ctx.query;

    const offset = (page - 1) * pageSize;
    const where = {};

    if (name) {
      where.name = { [Op.like]: `%${name}%` };
    }

    if (type) {
      where.type = type;
    }

    if (status !== '') {
      where.status = status;
    }

    if (applicable_to) {
      where.applicableTo = applicable_to;
    }

    const { count, rows } = await Discount.findAndCountAll({
      where,
      order: [['priority', 'DESC'], ['createdAt', 'DESC']],
      limit: parseInt(pageSize),
      offset: parseInt(offset),
      attributes: {
        include: [
          // 添加计算字段：折扣状态
          [
            sequelize.literal(`
              CASE
                WHEN end_time < NOW() THEN 'expired'
                WHEN start_time > NOW() THEN 'pending'
                WHEN status = 0 THEN 'disabled'
                WHEN usage_limit IS NOT NULL AND used_count >= usage_limit THEN 'exhausted'
                ELSE 'active'
              END
            `),
            'discountStatus'
          ]
        ]
      }
    });

    ctx.body = {
      success: true,
      data: {
        list: rows,
        total: count,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPages: Math.ceil(count / pageSize)
      }
    };
  } catch (error) {
    console.error('获取折扣列表失败:', error);
    ctx.body = {
      success: false,
      message: '获取折扣列表失败'
    };
  }
};

// 获取折扣详情
const getDiscountDetail = async (ctx) => {
  try {
    const { id } = ctx.params;

    const discount = await Discount.findByPk(id, {
      include: [
        {
          model: Product,
          as: 'products',
          attributes: ['id', 'name', 'price', 'mainImage'],
          through: { attributes: [] }
        },
        {
          model: Category,
          as: 'categories',
          attributes: ['id', 'name'],
          through: { attributes: [] }
        }
      ]
    });

    if (!discount) {
      ctx.body = {
        success: false,
        message: '折扣不存在'
      };
      return;
    }

    ctx.body = {
      success: true,
      data: discount
    };
  } catch (error) {
    console.error('获取折扣详情失败:', error);
    ctx.body = {
      success: false,
      message: '获取折扣详情失败'
    };
  }
};

// 创建折扣
const createDiscount = async (ctx) => {
  try {
    const discountData = ctx.request.body;
    const { productIds = [], categoryIds = [] } = discountData;

    // 验证必填字段
    if (!discountData.name || !discountData.type || !discountData.value || 
        !discountData.startTime || !discountData.endTime) {
      ctx.body = {
        success: false,
        message: '请填写完整的折扣信息'
      };
      return;
    }

    // 验证时间
    if (new Date(discountData.startTime) >= new Date(discountData.endTime)) {
      ctx.body = {
        success: false,
        message: '开始时间必须早于结束时间'
      };
      return;
    }

    // 验证折扣值
    if (discountData.type === 1 && (discountData.value <= 0 || discountData.value >= 100)) {
      ctx.body = {
        success: false,
        message: '百分比折扣值必须在0-100之间'
      };
      return;
    }

    if (discountData.type === 2 && discountData.value <= 0) {
      ctx.body = {
        success: false,
        message: '固定金额折扣值必须大于0'
      };
      return;
    }

    // 创建折扣
    const discount = await Discount.create({
      name: discountData.name,
      description: discountData.description,
      type: discountData.type,
      value: discountData.value,
      minAmount: discountData.minAmount || 0,
      maxDiscount: discountData.maxDiscount,
      startTime: discountData.startTime,
      endTime: discountData.endTime,
      usageLimit: discountData.usageLimit,
      userLimit: discountData.userLimit,
      status: discountData.status || 1,
      priority: discountData.priority || 0,
      applicableTo: discountData.applicableTo || 1,
      createdBy: ctx.state.admin?.id
    });

    // 设置关联商品
    if (discountData.applicableTo === 2 && productIds.length > 0) {
      const productDiscounts = productIds.map(productId => ({
        discountId: discount.id,
        productId: productId
      }));
      await ProductDiscount.bulkCreate(productDiscounts);
    }

    // 设置关联分类
    if (discountData.applicableTo === 3 && categoryIds.length > 0) {
      const categoryDiscounts = categoryIds.map(categoryId => ({
        discountId: discount.id,
        categoryId: categoryId
      }));
      await CategoryDiscount.bulkCreate(categoryDiscounts);
    }

    // 清除相关缓存
    await apiCache.clearModuleCache('product');
    await apiCache.clearModuleCache('discount');

    ctx.body = {
      success: true,
      data: discount,
      message: '折扣创建成功'
    };
  } catch (error) {
    console.error('创建折扣失败:', error);
    ctx.body = {
      success: false,
      message: '创建折扣失败'
    };
  }
};

// 更新折扣
const updateDiscount = async (ctx) => {
  try {
    const { id } = ctx.params;
    const discountData = ctx.request.body;
    const { productIds = [], categoryIds = [] } = discountData;

    const discount = await Discount.findByPk(id);
    if (!discount) {
      ctx.body = {
        success: false,
        message: '折扣不存在'
      };
      return;
    }

    // 验证时间
    if (new Date(discountData.startTime) >= new Date(discountData.endTime)) {
      ctx.body = {
        success: false,
        message: '开始时间必须早于结束时间'
      };
      return;
    }

    // 更新折扣基本信息
    await discount.update({
      name: discountData.name,
      description: discountData.description,
      type: discountData.type,
      value: discountData.value,
      minAmount: discountData.minAmount || 0,
      maxDiscount: discountData.maxDiscount,
      startTime: discountData.startTime,
      endTime: discountData.endTime,
      usageLimit: discountData.usageLimit,
      userLimit: discountData.userLimit,
      status: discountData.status,
      priority: discountData.priority || 0,
      applicableTo: discountData.applicableTo
    });

    // 更新关联商品
    if (discountData.applicableTo === 2) {
      await ProductDiscount.destroy({ where: { discountId: id } });
      if (productIds.length > 0) {
        const productDiscounts = productIds.map(productId => ({
          discountId: id,
          productId: productId
        }));
        await ProductDiscount.bulkCreate(productDiscounts);
      }
    }

    // 更新关联分类
    if (discountData.applicableTo === 3) {
      await CategoryDiscount.destroy({ where: { discountId: id } });
      if (categoryIds.length > 0) {
        const categoryDiscounts = categoryIds.map(categoryId => ({
          discountId: id,
          categoryId: categoryId
        }));
        await CategoryDiscount.bulkCreate(categoryDiscounts);
      }
    }

    // 清除相关缓存
    await apiCache.clearModuleCache('product');
    await apiCache.clearModuleCache('discount');

    ctx.body = {
      success: true,
      data: discount,
      message: '折扣更新成功'
    };
  } catch (error) {
    console.error('更新折扣失败:', error);
    ctx.body = {
      success: false,
      message: '更新折扣失败'
    };
  }
};

// 删除折扣
const deleteDiscount = async (ctx) => {
  try {
    const { id } = ctx.params;

    const discount = await Discount.findByPk(id);
    if (!discount) {
      ctx.body = {
        success: false,
        message: '折扣不存在'
      };
      return;
    }

    // 检查是否有使用记录
    const usageCount = await UserDiscountUsage.count({
      where: { discountId: id }
    });

    if (usageCount > 0) {
      ctx.body = {
        success: false,
        message: '该折扣已有使用记录，无法删除'
      };
      return;
    }

    await discount.destroy();

    // 清除相关缓存
    await apiCache.clearModuleCache('product');
    await apiCache.clearModuleCache('discount');

    ctx.body = {
      success: true,
      message: '折扣删除成功'
    };
  } catch (error) {
    console.error('删除折扣失败:', error);
    ctx.body = {
      success: false,
      message: '删除折扣失败'
    };
  }
};

// 更新折扣状态
const updateDiscountStatus = async (ctx) => {
  try {
    const { id } = ctx.params;
    const { status } = ctx.request.body;

    const discount = await Discount.findByPk(id);
    if (!discount) {
      ctx.body = {
        success: false,
        message: '折扣不存在'
      };
      return;
    }

    await discount.update({ status });

    // 清除相关缓存
    await apiCache.clearModuleCache('product');
    await apiCache.clearModuleCache('discount');

    ctx.body = {
      success: true,
      message: '状态更新成功'
    };
  } catch (error) {
    console.error('更新折扣状态失败:', error);
    ctx.body = {
      success: false,
      message: '更新状态失败'
    };
  }
};

// 获取折扣统计信息
const getDiscountStats = async (ctx) => {
  try {
    const totalCount = await Discount.count();
    const activeCount = await Discount.count({
      where: {
        status: 1,
        startTime: { [Op.lte]: new Date() },
        endTime: { [Op.gte]: new Date() }
      }
    });
    const expiredCount = await Discount.count({
      where: {
        endTime: { [Op.lt]: new Date() }
      }
    });

    ctx.body = {
      success: true,
      data: {
        total: totalCount,
        active: activeCount,
        expired: expiredCount,
        disabled: totalCount - activeCount - expiredCount
      }
    };
  } catch (error) {
    console.error('获取折扣统计失败:', error);
    ctx.body = {
      success: false,
      message: '获取统计信息失败'
    };
  }
};

module.exports = {
  getDiscountList,
  getDiscountDetail,
  createDiscount,
  updateDiscount,
  deleteDiscount,
  updateDiscountStatus,
  getDiscountStats
};
