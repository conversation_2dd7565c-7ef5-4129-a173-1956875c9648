const orderService = require('../../services/order');
const { Order, OrderItem, User, Product } = require('../../models');
const { Op } = require('sequelize');

// 订单状态映射
const statusMap = {
  0: { text: '待付款', color: 'orange' },
  1: { text: '待发货', color: 'blue' },
  2: { text: '待收货', color: 'cyan' },
  3: { text: '已完成', color: 'green' },
  4: { text: '已取消', color: 'red' },
};

class AdminOrderController {
  // 获取订单列表
  async getOrderList(ctx) {
    try {
      const {
        page = 1,
        limit = 10,
        orderNo = '',
        receiverName = '',
        orderStatus,
        startDate = '',
        endDate = ''
      } = ctx.query;

      const where = {};
      
      if (orderNo) {
        where.order_no = { [Op.like]: `%${orderNo}%` };
      }
      
      if (receiverName) {
        where.receiver_name = { [Op.like]: `%${receiverName}%` };
      }
      
      if (orderStatus !== undefined && orderStatus !== '') {
        where.order_status = parseInt(orderStatus);
      }
      
      if (startDate) {
        where.created_at = { [Op.gte]: new Date(startDate) };
      }
      
      if (endDate) {
        where.created_at = { 
          ...where.created_at,
          [Op.lte]: new Date(endDate + ' 23:59:59')
        };
      }

      const offset = (page - 1) * limit;
      
      const { count, rows } = await Order.findAndCountAll({
        where,
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'username', 'nickname', 'phone']
          },
          {
            model: OrderItem,
            as: 'orderItems',
            include: [
              {
                model: Product,
                as: 'product',
                attributes: ['id', 'name', 'main_image']
              }
            ]
          }
        ],
        order: [['created_at', 'DESC']],
        limit: parseInt(limit),
        offset
      });

      // 获取订单状态统计
      const statusStats = await Order.findAll({
        attributes: [
          'order_status',
          [Order.sequelize.fn('COUNT', Order.sequelize.col('id')), 'count'],
          [Order.sequelize.fn('SUM', Order.sequelize.col('pay_amount')), 'total_amount']
        ],
        group: ['order_status'],
        raw: true
      });

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: {
          list: rows,
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit),
          statusStats
        }
      };
    } catch (error) {
      console.error('获取订单列表失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '获取订单列表失败',
        error: error.message
      };
    }
  }

  // 获取订单详情
  async getOrderDetail(ctx) {
    try {
      const { id } = ctx.params;

      const order = await Order.findByPk(id, {
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'username', 'nickname', 'phone', 'avatar']
          },
          {
            model: OrderItem,
            as: 'orderItems',
            include: [
              {
                model: Product,
                as: 'product',
                attributes: ['id', 'name', 'main_image', 'price']
              }
            ]
          }
        ]
      });

      if (!order) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '订单不存在'
        };
        return;
      }

      // 添加订单状态变更记录
      const logs = [
        {
          time: order.created_at,
          action: '下单',
          user: order.receiver_name,
          description: '用户提交订单'
        }
      ];

      if (order.pay_time) {
        logs.push({
          time: order.pay_time,
          action: '支付',
          user: order.receiver_name,
          description: '用户完成支付'
        });
      }

      if (order.delivery_time) {
        logs.push({
          time: order.delivery_time,
          action: '发货',
          user: '系统',
          description: `发货信息：${order.delivery_company} ${order.delivery_no}`
        });
      }

      if (order.receive_time) {
        logs.push({
          time: order.receive_time,
          action: '收货',
          user: order.receiver_name,
          description: '用户确认收货'
        });
      }

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: {
          ...order.toJSON(),
          logs
        }
      };
    } catch (error) {
      console.error('获取订单详情失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '获取订单详情失败',
        error: error.message
      };
    }
  }

  // 更新订单状态
  async updateOrderStatus(ctx) {
    try {
      const { id } = ctx.params;
      const { orderStatus, remark } = ctx.request.body;

      const order = await Order.findByPk(id, {
        include: [
          {
            model: require('../../models').ReturnRequest,
            as: 'returnRequests',
            where: {
              status: { [require('sequelize').Op.notIn]: [2, 9] } // 排除已拒绝和已取消
            },
            required: false
          }
        ]
      });

      if (!order) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '订单不存在'
        };
        return;
      }

      const newStatus = parseInt(orderStatus);
      const currentStatus = order.order_status;

      // 业务规则验证
      if (currentStatus === 4) { // 已取消订单不能修改状态
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '已取消的订单不能修改状态'
        };
        return;
      }

      // 检查是否有进行中的退货申请
      if (order.returnRequests && order.returnRequests.length > 0) {
        const activeReturns = order.returnRequests.filter(r =>
          ![2, 8, 9].includes(r.status) // 排除已拒绝、已完成、已取消
        );

        if (activeReturns.length > 0 && newStatus !== 4) {
          ctx.status = 400;
          ctx.body = {
            code: 400,
            message: '该订单有进行中的退货申请，无法修改状态'
          };
          return;
        }
      }

      // 更新订单状态
      const updateData = { order_status: newStatus };

      // 根据状态设置相应时间和退货相关字段
      if (newStatus === 1) { // 待发货
        updateData.pay_time = new Date();
        updateData.pay_status = 1;
        updateData.can_return = 1; // 允许退货
      } else if (newStatus === 2) { // 待收货（已发货）
        updateData.delivery_time = new Date();
        updateData.can_return = 1; // 允许退货
        // 设置退货截止时间（发货后7天）
        const returnDeadline = new Date();
        returnDeadline.setDate(returnDeadline.getDate() + 7);
        updateData.return_deadline = returnDeadline;
      } else if (newStatus === 3) { // 已完成
        updateData.receive_time = new Date();
        updateData.can_return = 0; // 完成后不允许退货（可根据业务需求调整）
      } else if (newStatus === 4) { // 已取消
        updateData.pay_status = 2; // 已退款
        updateData.can_return = 0; // 取消后不允许退货
      }

      if (remark) {
        updateData.remark = remark;
      }

      await order.update(updateData);

      // 记录状态变更日志（可选）
      console.log(`订单 ${order.order_no} 状态从 ${currentStatus} 更新为 ${newStatus}`);

      ctx.body = {
        code: 200,
        message: '订单状态更新成功',
        data: order
      };
    } catch (error) {
      console.error('更新订单状态失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '更新订单状态失败',
        error: error.message
      };
    }
  }

  // 发货
  async shipOrder(ctx) {
    try {
      const { id } = ctx.params;
      const { deliveryCompany, deliveryNo, remark } = ctx.request.body;

      if (!deliveryCompany || !deliveryNo) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '物流公司和快递单号不能为空'
        };
        return;
      }

      const order = await Order.findByPk(id);
      if (!order) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '订单不存在'
        };
        return;
      }

      // 检查订单状态是否允许发货
      if (order.order_status !== 1) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '只有待发货状态的订单才能发货'
        };
        return;
      }

      // 设置退货截止时间（发货后7天）
      const returnDeadline = new Date();
      returnDeadline.setDate(returnDeadline.getDate() + 7);

      // 更新发货信息
      await order.update({
        delivery_company: deliveryCompany,
        delivery_no: deliveryNo,
        delivery_time: new Date(),
        order_status: 2, // 待收货
        can_return: 1, // 允许退货
        return_deadline: returnDeadline, // 退货截止时间
        remark: remark || order.remark
      });

      ctx.body = {
        code: 200,
        message: '发货成功',
        data: order
      };
    } catch (error) {
      console.error('发货失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '发货失败',
        error: error.message
      };
    }
  }

  // 处理退款
  async handleRefund(ctx) {
    try {
      const { id } = ctx.params;
      const { refundAmount, refundReason, remark } = ctx.request.body;

      if (!refundAmount || !refundReason) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '退款金额和退款原因不能为空'
        };
        return;
      }

      const order = await Order.findByPk(id);
      if (!order) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '订单不存在'
        };
        return;
      }

      // 更新退款信息
      await order.update({
        pay_status: 2, // 已退款
        order_status: 4, // 已取消
        remark: `${remark || ''}\n退款原因：${refundReason}\n退款金额：${refundAmount}`.trim()
      });

      ctx.body = {
        code: 200,
        message: '退款处理成功',
        data: order
      };
    } catch (error) {
      console.error('处理退款失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '处理退款失败',
        error: error.message
      };
    }
  }

  // 获取订单统计
  async getOrderStats(ctx) {
    try {
      const { startDate, endDate } = ctx.query;
      
      const where = {};
      if (startDate && endDate) {
        where.created_at = {
          [Op.between]: [new Date(startDate), new Date(endDate + ' 23:59:59')]
        };
      }

      // 订单状态统计
      const statusStats = await Order.findAll({
        where,
        attributes: [
          'order_status',
          [Order.sequelize.fn('COUNT', Order.sequelize.col('id')), 'count'],
          [Order.sequelize.fn('SUM', Order.sequelize.col('pay_amount')), 'total_amount']
        ],
        group: ['order_status'],
        raw: true
      });

      // 总订单数和总金额
      const totalStats = await Order.findOne({
        where,
        attributes: [
          [Order.sequelize.fn('COUNT', Order.sequelize.col('id')), 'total_orders'],
          [Order.sequelize.fn('SUM', Order.sequelize.col('pay_amount')), 'total_amount']
        ],
        raw: true
      });

      // 最近7天订单趋势
      const trendStats = await Order.findAll({
        where: {
          created_at: {
            [Op.gte]: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        },
        attributes: [
          [Order.sequelize.fn('DATE', Order.sequelize.col('created_at')), 'date'],
          [Order.sequelize.fn('COUNT', Order.sequelize.col('id')), 'count'],
          [Order.sequelize.fn('SUM', Order.sequelize.col('pay_amount')), 'amount']
        ],
        group: [Order.sequelize.fn('DATE', Order.sequelize.col('created_at'))],
        order: [[Order.sequelize.fn('DATE', Order.sequelize.col('created_at')), 'ASC']],
        raw: true
      });

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: {
          statusStats,
          totalStats,
          trendStats
        }
      };
    } catch (error) {
      console.error('获取订单统计失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '获取订单统计失败',
        error: error.message
      };
    }
  }

  // 批量更新订单状态
  async batchUpdateStatus(ctx) {
    try {
      const { orderIds, status, remark } = ctx.request.body;

      if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '订单ID列表不能为空'
        };
        return;
      }

      if (status === undefined || status === '') {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '订单状态不能为空'
        };
        return;
      }

      const result = await orderService.batchUpdateStatus(orderIds, parseInt(status), remark);

      ctx.body = {
        code: 200,
        message: '批量状态更新成功',
        data: {
          updatedCount: result.length
        }
      };
    } catch (error) {
      console.error('批量更新订单状态失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '批量更新订单状态失败',
        error: error.message
      };
    }
  }

  // 批量发货
  async batchShip(ctx) {
    try {
      const { orderIds, deliveryCompany, deliveryNo } = ctx.request.body;

      if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '订单ID列表不能为空'
        };
        return;
      }

      if (!deliveryCompany || !deliveryNo) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '物流公司和快递单号不能为空'
        };
        return;
      }

      const result = await orderService.batchShip(orderIds, deliveryCompany, deliveryNo);

      ctx.body = {
        code: 200,
        message: '批量发货成功',
        data: {
          shippedCount: result.length
        }
      };
    } catch (error) {
      console.error('批量发货失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '批量发货失败',
        error: error.message
      };
    }
  }

  // 导出订单数据
  async exportOrders(ctx) {
    try {
      const {
        orderNo = '',
        receiverName = '',
        orderStatus,
        startDate = '',
        endDate = ''
      } = ctx.query;

      const filters = {};
      
      if (orderNo) {
        filters.orderNo = orderNo;
      }
      
      if (receiverName) {
        filters.receiverName = receiverName;
      }
      
      if (orderStatus !== undefined && orderStatus !== '') {
        filters.orderStatus = parseInt(orderStatus);
      }
      
      if (startDate) {
        filters.startDate = startDate;
      }
      
      if (endDate) {
        filters.endDate = endDate;
      }

      const orders = await orderService.exportOrders(filters);

      // 生成Excel数据
      const excelData = orders.map(order => ({
        '订单号': order.order_no,
        '收货人': order.receiver_name,
        '手机号': order.receiver_phone,
        '收货地址': order.receiver_address,
        '订单金额': order.pay_amount,
        '实付金额': order.pay_amount,
        '运费': order.freight_amount,
        '优惠金额': order.discount_amount,
        '订单状态': statusMap[order.order_status]?.text || '未知',
        '支付状态': order.pay_status === 1 ? '已支付' : order.pay_status === 2 ? '已退款' : '未支付',
        '物流公司': order.delivery_company || '',
        '快递单号': order.delivery_no || '',
        '下单时间': order.created_at ? new Date(order.created_at).toLocaleString() : '',
        '支付时间': order.pay_time ? new Date(order.pay_time).toLocaleString() : '',
        '发货时间': order.delivery_time ? new Date(order.delivery_time).toLocaleString() : '',
        '收货时间': order.receive_time ? new Date(order.receive_time).toLocaleString() : '',
        '备注': order.remark || '',
        '用户名': order.user?.username || '',
        '用户昵称': order.user?.nickname || '',
        '用户手机': order.user?.phone || '',
        '商品信息': order.orderItems?.map(item => 
          `${item.product_name} x${item.quantity} ¥${item.price}`
        ).join('; ') || ''
      }));

      // 设置响应头
      ctx.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      ctx.set('Content-Disposition', `attachment; filename=订单数据_${new Date().toISOString().split('T')[0]}.xlsx`);

      // 这里需要安装并引入xlsx库来生成Excel文件
      // 暂时返回JSON格式，前端可以处理
      ctx.body = {
        code: 200,
        message: '导出成功',
        data: excelData
      };
    } catch (error) {
      console.error('导出订单数据失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '导出订单数据失败',
        error: error.message
      };
    }
  }
}

module.exports = new AdminOrderController();  
