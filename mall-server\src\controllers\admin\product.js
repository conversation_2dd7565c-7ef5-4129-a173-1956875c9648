const { Product } = require('../../models');
const productService = require('../../services/product');
const apiCache = require('../../middleware/apiCache');

// 获取商品列表
const getProductList = async (ctx) => {
  try {
    const { page = 1, limit = 10, status, category_id } = ctx.query;
    
    const where = {};
    if (status !== undefined) {
      where.status = parseInt(status);
    }
    if (category_id !== undefined) {
      where.category_id = parseInt(category_id);
    }
    
    const offset = (page - 1) * limit;
    
    const { count, rows } = await Product.findAndCountAll({
      where,
      order: [['sort_order', 'ASC'], ['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
    
    ctx.body = {
      success: true,
      data: {
        list: rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    console.error('获取商品列表失败:', error);
    ctx.body = {
      success: false,
      message: '获取商品列表失败'
    };
  }
};

// 获取热门商品列表
const getHotProductList = async (ctx) => {
  try {
    const { limit = 10 } = ctx.query;
    
    const products = await Product.findAll({
      where: { status: 1 },
      order: [['sort_order', 'ASC'], ['created_at', 'DESC']],
      limit: parseInt(limit)
    });
    
    ctx.body = {
      success: true,
      data: products
    };
  } catch (error) {
    console.error('获取热门商品列表失败:', error);
    ctx.body = {
      success: false,
      message: '获取热门商品列表失败'
    };
  }
};

// 获取推荐商品列表
const getRecommendProductList = async (ctx) => {
  try {
    const { limit = 10 } = ctx.query;
    
    const products = await Product.findAll({
      where: { status: 1 },
      order: [['sort_order', 'ASC'], ['created_at', 'DESC']],
      limit: parseInt(limit)
    });
    
    ctx.body = {
      success: true,
      data: products
    };
  } catch (error) {
    console.error('获取推荐商品列表失败:', error);
    ctx.body = {
      success: false,
      message: '获取推荐商品列表失败'
    };
  }
};

// 获取商品详情
const getProductDetail = async (ctx) => {
  try {
    const { id } = ctx.params;
    
    const product = await Product.findByPk(id);
    
    if (!product) {
      ctx.body = {
        success: false,
        message: '商品不存在'
      };
      return;
    }
    
    ctx.body = {
      success: true,
      data: product
    };
  } catch (error) {
    console.error('获取商品详情失败:', error);
    ctx.body = {
      success: false,
      message: '获取商品详情失败'
    };
  }
};

// 添加商品
const addProduct = async (ctx) => {
  try {
    const productData = ctx.request.body;
    
    const product = await productService.createProduct(productData);
    await apiCache.clearModuleCache('product');
    
    ctx.body = {
      success: true,
      data: product,
      message: '商品添加成功'
    };
  } catch (error) {
    console.error('添加商品失败:', error);
    ctx.body = {
      success: false,
      message: '添加商品失败'
    };
  }
};

// 更新商品
const updateProduct = async (ctx) => {
  try {
    const { id } = ctx.params;
    const updateData = ctx.request.body;
    
    const product = await productService.updateProduct(id, updateData);
    await apiCache.clearModuleCache('product');
    
    ctx.body = {
      success: true,
      data: product,
      message: '商品更新成功'
    };
  } catch (error) {
    console.error('更新商品失败:', error);
    ctx.body = {
      success: false,
      message: error.message || '更新商品失败'
    };
  }
};

// 删除商品
const deleteProduct = async (ctx) => {
  try {
    const { id } = ctx.params;
    
    await productService.deleteProduct(id);
    await apiCache.clearModuleCache('product');
    
    ctx.body = {
      success: true,
      message: '商品删除成功'
    };
  } catch (error) {
    console.error('删除商品失败:', error);
    ctx.body = {
      success: false,
      message: error.message || '删除商品失败'
    };
  }
};

// 更新商品状态
const updateProductStatus = async (ctx) => {
  try {
    const { id } = ctx.params;
    const { status } = ctx.request.body;
    
    await productService.updateProduct(id, { status });
    await apiCache.clearModuleCache('product');
    
    ctx.body = {
      success: true,
      message: '商品状态更新成功'
    };
  } catch (error) {
    console.error('更新商品状态失败:', error);
    ctx.body = {
      success: false,
      message: error.message || '更新商品状态失败'
    };
  }
};

// 手动清理商品缓存
const clearProductCache = async (ctx) => {
  try {
    const result = await productService.clearCache();
    
    if (result) {
      ctx.body = {
        success: true,
        message: '商品缓存清理成功'
      };
    } else {
      ctx.body = {
        success: false,
        message: '商品缓存清理失败'
      };
    }
  } catch (error) {
    console.error('清理商品缓存失败:', error);
    ctx.body = {
      success: false,
      message: '清理商品缓存失败'
    };
  }
};

module.exports = {
  getProductList,
  getHotProductList,
  getRecommendProductList,
  getProductDetail,
  addProduct,
  updateProduct,
  deleteProduct,
  updateProductStatus,
  clearProductCache
}; 
