const returnService = require('../../services/returnService');
const { ReturnRequest, RefundRecord } = require('../../models');
const { Op } = require('sequelize');

// 退货状态映射
const RETURN_STATUS = {
  PENDING: 0,        // 待审核
  APPROVED: 1,       // 审核通过
  REJECTED: 2,       // 审核拒绝
  WAIT_RETURN: 3,    // 待寄回
  RETURNED: 4,       // 已寄回
  INSPECTING: 5,     // 验收中
  INSPECT_PASS: 6,   // 验收通过
  INSPECT_FAIL: 7,   // 验收不通过
  REFUNDED: 8,       // 退款完成
  CANCELLED: 9       // 已取消
};

class AdminReturnController {
  // 获取退货申请列表
  async getReturnRequestList(ctx) {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        returnNo = '',
        orderNo = '',
        userName = '',
        startDate = '',
        endDate = ''
      } = ctx.query;

      const params = {
        page,
        limit,
        status,
        returnNo,
        orderNo,
        startDate,
        endDate
      };

      const result = await returnService.getReturnRequestList(params);

      // 获取状态统计
      const statusStats = await ReturnRequest.findAll({
        attributes: [
          'status',
          [ReturnRequest.sequelize.fn('COUNT', ReturnRequest.sequelize.col('id')), 'count'],
          [ReturnRequest.sequelize.fn('SUM', ReturnRequest.sequelize.col('return_amount')), 'total_amount']
        ],
        group: ['status'],
        raw: true
      });

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: {
          ...result,
          statusStats
        }
      };
    } catch (error) {
      console.error('获取退货申请列表失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '获取退货申请列表失败',
        error: error.message
      };
    }
  }

  // 获取退货申请详情
  async getReturnRequestDetail(ctx) {
    try {
      const { id } = ctx.params;

      const returnRequest = await returnService.getReturnRequestDetail(id);

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: returnRequest
      };
    } catch (error) {
      console.error('获取退货申请详情失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '获取退货申请详情失败',
        error: error.message
      };
    }
  }

  // 审核退货申请
  async approveReturnRequest(ctx) {
    try {
      const { id } = ctx.params;
      const { approved, refuseReason, adminRemark } = ctx.request.body;
      const adminId = ctx.state.admin.id;
      const adminName = ctx.state.admin.username;

      const returnRequest = await ReturnRequest.findByPk(id);
      if (!returnRequest) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '退货申请不存在'
        };
        return;
      }

      if (returnRequest.status !== RETURN_STATUS.PENDING) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '该申请已处理，无法重复操作'
        };
        return;
      }

      const newStatus = approved ? RETURN_STATUS.APPROVED : RETURN_STATUS.REJECTED;
      const updateData = {
        status: newStatus,
        admin_remark: adminRemark
      };

      if (!approved && refuseReason) {
        updateData.refuse_reason = refuseReason;
      }

      await returnRequest.update(updateData);

      // 记录状态日志
      await returnService.addStatusLog(id, newStatus, {
        operatorType: 2, // 管理员
        operatorId: adminId,
        operatorName: adminName,
        remark: approved ? '审核通过' : `审核拒绝：${refuseReason}`
      });

      ctx.body = {
        code: 200,
        message: approved ? '审核通过' : '审核拒绝',
        data: returnRequest
      };
    } catch (error) {
      console.error('审核退货申请失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '审核退货申请失败',
        error: error.message
      };
    }
  }

  // 确认收货
  async confirmReceive(ctx) {
    try {
      const { id } = ctx.params;
      const { expressCompany, expressNo, remark } = ctx.request.body;
      const adminId = ctx.state.admin.id;
      const adminName = ctx.state.admin.username;

      const returnRequest = await ReturnRequest.findByPk(id);
      if (!returnRequest) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '退货申请不存在'
        };
        return;
      }

      if (returnRequest.status !== RETURN_STATUS.RETURNED) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '当前状态不允许确认收货'
        };
        return;
      }

      await returnRequest.update({
        status: RETURN_STATUS.INSPECTING,
        receive_time: new Date(),
        admin_remark: remark
      });

      // 记录状态日志
      await returnService.addStatusLog(id, RETURN_STATUS.INSPECTING, {
        operatorType: 2, // 管理员
        operatorId: adminId,
        operatorName: adminName,
        remark: '确认收货，开始验收'
      });

      ctx.body = {
        code: 200,
        message: '确认收货成功',
        data: returnRequest
      };
    } catch (error) {
      console.error('确认收货失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '确认收货失败',
        error: error.message
      };
    }
  }

  // 验收商品
  async inspectGoods(ctx) {
    try {
      const { id } = ctx.params;
      const { inspectResult, inspectRemark, returnItems } = ctx.request.body;
      const adminId = ctx.state.admin.id;
      const adminName = ctx.state.admin.username;

      const returnRequest = await ReturnRequest.findByPk(id, {
        include: [
          {
            model: require('../../models').ReturnItem,
            as: 'returnItems'
          }
        ]
      });

      if (!returnRequest) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '退货申请不存在'
        };
        return;
      }

      if (returnRequest.status !== RETURN_STATUS.INSPECTING) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '当前状态不允许验收'
        };
        return;
      }

      // 更新退货商品验收结果
      if (returnItems && returnItems.length > 0) {
        for (const item of returnItems) {
          const returnItem = returnRequest.returnItems.find(ri => ri.id === item.id);
          if (returnItem) {
            await returnItem.update({
              product_condition: item.condition,
              inspect_result: item.result,
              inspect_remark: item.remark
            });
          }
        }
      }

      const newStatus = inspectResult ? RETURN_STATUS.INSPECT_PASS : RETURN_STATUS.INSPECT_FAIL;
      
      await returnRequest.update({
        status: newStatus,
        inspect_time: new Date(),
        admin_remark: inspectRemark
      });

      // 记录状态日志
      await returnService.addStatusLog(id, newStatus, {
        operatorType: 2, // 管理员
        operatorId: adminId,
        operatorName: adminName,
        remark: inspectResult ? '验收通过' : `验收不通过：${inspectRemark}`
      });

      ctx.body = {
        code: 200,
        message: inspectResult ? '验收通过' : '验收不通过',
        data: returnRequest
      };
    } catch (error) {
      console.error('验收商品失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '验收商品失败',
        error: error.message
      };
    }
  }

  // 处理退款
  async processRefund(ctx) {
    try {
      const { id } = ctx.params;
      const { refundType, refundAmount, remark } = ctx.request.body;
      const adminId = ctx.state.admin.id;
      const adminName = ctx.state.admin.username;

      const returnRequest = await ReturnRequest.findByPk(id);
      if (!returnRequest) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '退货申请不存在'
        };
        return;
      }

      if (returnRequest.status !== RETURN_STATUS.INSPECT_PASS) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '当前状态不允许退款'
        };
        return;
      }

      // 创建退款记录
      const refundNo = returnService.generateRefundNo();
      const refundRecord = await RefundRecord.create({
        refund_no: refundNo,
        return_id: returnRequest.id,
        order_id: returnRequest.order_id,
        user_id: returnRequest.user_id,
        refund_amount: refundAmount || returnRequest.return_amount,
        refund_type: refundType || 2, // 默认余额退款
        refund_status: 2, // 退款成功（模拟）
        refund_time: new Date(),
        success_time: new Date(),
        operator_id: adminId,
        operator_name: adminName,
        remark: remark
      });

      // 更新退货申请状态
      await returnRequest.update({
        status: RETURN_STATUS.REFUNDED,
        refund_time: new Date(),
        admin_remark: remark
      });

      // 记录状态日志
      await returnService.addStatusLog(id, RETURN_STATUS.REFUNDED, {
        operatorType: 2, // 管理员
        operatorId: adminId,
        operatorName: adminName,
        remark: `退款完成，金额：${refundAmount || returnRequest.return_amount}元`
      });

      ctx.body = {
        code: 200,
        message: '退款处理成功',
        data: {
          returnRequest,
          refundRecord
        }
      };
    } catch (error) {
      console.error('处理退款失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '处理退款失败',
        error: error.message
      };
    }
  }

  // 获取退货统计数据
  async getReturnStatistics(ctx) {
    try {
      const { startDate, endDate } = ctx.query;

      const statistics = await returnService.getReturnStatistics({
        startDate,
        endDate
      });

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: statistics
      };
    } catch (error) {
      console.error('获取退货统计失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '获取退货统计失败',
        error: error.message
      };
    }
  }

  // 批量处理退货申请
  async batchProcessReturns(ctx) {
    try {
      const { returnIds, action, data } = ctx.request.body;
      const adminId = ctx.state.admin.id;
      const adminName = ctx.state.admin.username;

      if (!returnIds || !Array.isArray(returnIds) || returnIds.length === 0) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '请选择要处理的退货申请'
        };
        return;
      }

      const results = [];

      for (const returnId of returnIds) {
        try {
          let result;
          switch (action) {
            case 'approve':
              result = await returnService.approveReturnRequest(returnId, true, {
                adminRemark: data.remark,
                adminId,
                adminName
              });
              break;
            case 'reject':
              result = await returnService.approveReturnRequest(returnId, false, {
                refuseReason: data.refuseReason,
                adminRemark: data.remark,
                adminId,
                adminName
              });
              break;
            default:
              throw new Error('不支持的操作类型');
          }
          results.push({ returnId, success: true, data: result });
        } catch (error) {
          results.push({ returnId, success: false, error: error.message });
        }
      }

      ctx.body = {
        code: 200,
        message: '批量处理完成',
        data: results
      };
    } catch (error) {
      console.error('批量处理退货申请失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '批量处理退货申请失败',
        error: error.message
      };
    }
  }

  // 导出退货数据
  async exportReturnData(ctx) {
    try {
      const {
        status,
        startDate,
        endDate,
        format = 'json'
      } = ctx.query;

      const params = {
        page: 1,
        limit: 10000, // 导出大量数据
        status,
        startDate,
        endDate
      };

      const result = await returnService.getReturnRequestList(params);

      if (format === 'csv') {
        // 生成CSV格式
        const csvData = this.generateCSV(result.list);
        ctx.set('Content-Type', 'text/csv');
        ctx.set('Content-Disposition', 'attachment; filename=return_requests.csv');
        ctx.body = csvData;
      } else {
        // 返回JSON格式
        ctx.body = {
          code: 200,
          message: '导出成功',
          data: result
        };
      }
    } catch (error) {
      console.error('导出退货数据失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '导出退货数据失败',
        error: error.message
      };
    }
  }

  // 生成CSV数据
  generateCSV(data) {
    const headers = [
      '退货单号', '订单号', '用户', '退货原因', '退货金额',
      '状态', '申请时间', '处理时间'
    ];

    const rows = data.map(item => [
      item.return_no,
      item.order_no,
      item.user?.nickname || item.user?.username || '',
      item.return_reason,
      item.return_amount,
      this.getStatusText(item.status),
      item.created_at,
      item.updated_at
    ]);

    const csvContent = [headers, ...rows]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    return csvContent;
  }

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      0: '待审核',
      1: '审核通过',
      2: '审核拒绝',
      3: '待寄回',
      4: '已寄回',
      5: '验收中',
      6: '验收通过',
      7: '验收不通过',
      8: '退款完成',
      9: '已取消'
    };
    return statusMap[status] || '未知状态';
  }
}

module.exports = new AdminReturnController();
