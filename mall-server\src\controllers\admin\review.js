const { Review, User, Product, Order } = require('../../models');
const { Op } = require('sequelize');

// 获取评论列表
const getReviewList = async (ctx) => {
  try {
    const { page = 1, limit = 10, productId, userId, rating } = ctx.query;

    const where = {};

    if (productId) {
      where.product_id = productId;
    }

    if (userId) {
      where.user_id = userId;
    }

    if (rating) {
      where.rating = rating;
    }

    const reviews = await Review.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'nickname', 'avatar', 'phone']
        },
        {
          model: Product,
          as: 'product',
          attributes: ['id', 'name', 'main_image']
        },
        {
          model: Order,
          as: 'order',
          attributes: ['id', 'order_no']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit)
    });

    // 处理评价数据
    const processedReviews = reviews.rows.map(review => {
      const reviewData = review.toJSON();
      return {
        id: reviewData.id,
        rating: reviewData.rating,
        content: reviewData.content,
        images: reviewData.images ? JSON.parse(reviewData.images) : [],
        isAnonymous: reviewData.is_anonymous === 1,
        replyContent: reviewData.reply_content,
        replyTime: reviewData.reply_time,
        createTime: reviewData.created_at,
        user: reviewData.user,
        product: reviewData.product,
        order: reviewData.order
      };
    });

    ctx.body = {
      code: 200,
      message: '获取评论列表成功',
      data: {
        list: processedReviews,
        pagination: {
          total: reviews.count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(reviews.count / parseInt(limit))
        }
      }
    };
  } catch (error) {
    console.error('获取评论列表失败:', error);
    ctx.body = {
      code: 500,
      message: '获取评论列表失败'
    };
  }
};

// 获取用户评论列表
const getUserReviews = async (ctx) => {
  try {
    const { userId } = ctx.params;
    const { page = 1, limit = 10 } = ctx.query;

    const reviews = await Review.findAndCountAll({
      where: {
        user_id: userId
      },
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['id', 'name', 'main_image']
        },
        {
          model: Order,
          as: 'order',
          attributes: ['id', 'order_no']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit)
    });

    // 处理评价数据
    const processedReviews = reviews.rows.map(review => {
      const reviewData = review.toJSON();
      return {
        id: reviewData.id,
        rating: reviewData.rating,
        content: reviewData.content,
        images: reviewData.images ? JSON.parse(reviewData.images) : [],
        isAnonymous: reviewData.is_anonymous === 1,
        replyContent: reviewData.reply_content,
        replyTime: reviewData.reply_time,
        createTime: reviewData.created_at,
        product: reviewData.product,
        order: reviewData.order
      };
    });

    ctx.body = {
      code: 200,
      message: '获取用户评论成功',
      data: {
        list: processedReviews,
        pagination: {
          total: reviews.count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(reviews.count / parseInt(limit))
        }
      }
    };
  } catch (error) {
    console.error('获取用户评论失败:', error);
    ctx.body = {
      code: 500,
      message: '获取用户评论失败'
    };
  }
};

// 删除评论
const deleteReview = async (ctx) => {
  try {
    const { id } = ctx.params;

    const review = await Review.findByPk(id);
    if (!review) {
      return ctx.body = {
        code: 404,
        message: '评论不存在'
      };
    }

    await review.destroy();

    // 更新商品评分和评价数量
    const product = await Product.findByPk(review.product_id);
    if (product) {
      const allReviews = await Review.findAll({
        where: { product_id: review.product_id }
      });
      
      if (allReviews.length > 0) {
        const totalRating = allReviews.reduce((sum, r) => sum + r.rating, 0);
        const avgRating = totalRating / allReviews.length;
        
        await product.update({
          rating: parseFloat(avgRating.toFixed(2)),
          review_count: allReviews.length
        });
      } else {
        await product.update({
          rating: 0.00,
          review_count: 0
        });
      }
    }

    ctx.body = {
      code: 200,
      message: '评论删除成功'
    };
  } catch (error) {
    console.error('删除评论失败:', error);
    ctx.body = {
      code: 500,
      message: '删除评论失败'
    };
  }
};

// 回复评论
const replyReview = async (ctx) => {
  try {
    const { id } = ctx.params;
    const { replyContent } = ctx.request.body;

    if (!replyContent) {
      return ctx.body = {
        code: 400,
        message: '回复内容不能为空'
      };
    }

    const review = await Review.findByPk(id);
    if (!review) {
      return ctx.body = {
        code: 404,
        message: '评论不存在'
      };
    }

    await review.update({
      reply_content: replyContent,
      reply_time: new Date()
    });

    ctx.body = {
      code: 200,
      message: '回复成功',
      data: review
    };
  } catch (error) {
    console.error('回复评论失败:', error);
    ctx.body = {
      code: 500,
      message: '回复评论失败'
    };
  }
};

// 获取评论统计
const getReviewStats = async (ctx) => {
  try {
    const { Op } = require('sequelize');

    // 基础统计
    const totalReviews = await Review.count();
    const totalRating = await Review.sum('rating');
    const avgRating = totalReviews > 0 ? (totalRating / totalReviews).toFixed(2) : 0;

    // 按评分统计
    const ratingStats = await Review.findAll({
      attributes: [
        'rating',
        [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'count']
      ],
      group: ['rating'],
      order: [['rating', 'DESC']]
    });

    // 时间范围统计
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    const [todayReviews, yesterdayReviews, recentReviews, monthlyReviews] = await Promise.all([
      Review.count({ where: { created_at: { [Op.gte]: today } } }),
      Review.count({ where: { created_at: { [Op.gte]: yesterday, [Op.lt]: today } } }),
      Review.count({ where: { created_at: { [Op.gte]: sevenDaysAgo } } }),
      Review.count({ where: { created_at: { [Op.gte]: thirtyDaysAgo } } })
    ]);

    // 商品评论排行
    const productReviewStats = await Review.findAll({
      attributes: [
        'product_id',
        [require('sequelize').fn('COUNT', require('sequelize').col('Review.id')), 'review_count'],
        [require('sequelize').fn('AVG', require('sequelize').col('rating')), 'avg_rating']
      ],
      include: [{
        model: Product,
        as: 'product',
        attributes: ['id', 'name', 'main_image']
      }],
      group: ['product_id'],
      order: [[require('sequelize').fn('COUNT', require('sequelize').col('Review.id')), 'DESC']],
      limit: 10
    });

    // 用户评论活跃度
    const userReviewStats = await Review.findAll({
      attributes: [
        'user_id',
        [require('sequelize').fn('COUNT', require('sequelize').col('Review.id')), 'review_count'],
        [require('sequelize').fn('AVG', require('sequelize').col('rating')), 'avg_rating']
      ],
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'nickname', 'avatar']
      }],
      group: ['user_id'],
      order: [[require('sequelize').fn('COUNT', require('sequelize').col('Review.id')), 'DESC']],
      limit: 10
    });

    // 待回复评论数
    const pendingReplyCount = await Review.count({
      where: {
        reply_content: null
      }
    });

    // 匿名评论统计
    const anonymousCount = await Review.count({
      where: { is_anonymous: 1 }
    });

    // 有图片评论统计
    const withImagesCount = await Review.count({
      where: {
        images: { [Op.ne]: null }
      }
    });

    ctx.body = {
      code: 200,
      message: '获取评论统计成功',
      data: {
        // 基础统计
        totalReviews,
        avgRating: parseFloat(avgRating),
        pendingReplyCount,
        anonymousCount,
        withImagesCount,

        // 时间统计
        timeStats: {
          today: todayReviews,
          yesterday: yesterdayReviews,
          recent7Days: recentReviews,
          recent30Days: monthlyReviews
        },

        // 评分分布
        ratingStats: ratingStats.map(item => ({
          rating: item.rating,
          count: parseInt(item.dataValues.count),
          percentage: totalReviews > 0 ? ((parseInt(item.dataValues.count) / totalReviews) * 100).toFixed(1) : 0
        })),

        // 商品评论排行
        productStats: productReviewStats.map(item => ({
          productId: item.product_id,
          productName: item.product.name,
          productImage: item.product.main_image,
          reviewCount: parseInt(item.dataValues.review_count),
          avgRating: parseFloat(item.dataValues.avg_rating).toFixed(1)
        })),

        // 用户评论排行
        userStats: userReviewStats.map(item => ({
          userId: item.user_id,
          nickname: item.user.nickname,
          avatar: item.user.avatar,
          reviewCount: parseInt(item.dataValues.review_count),
          avgRating: parseFloat(item.dataValues.avg_rating).toFixed(1)
        }))
      }
    };
  } catch (error) {
    console.error('获取评论统计失败:', error);
    ctx.body = {
      code: 500,
      message: '获取评论统计失败'
    };
  }
};

module.exports = {
  getReviewList,
  getUserReviews,
  deleteReview,
  replyReview,
  getReviewStats
}; 