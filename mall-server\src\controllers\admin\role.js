const Role = require('../../models/role');
const Permission = require('../../models/permission');
const { Op } = require('sequelize');

class RoleController {
  // 获取角色列表
  async getRoleList(ctx) {
    try {
      const { page = 1, limit = 10, name, status } = ctx.query;
      const offset = (page - 1) * limit;
      
      const where = {};
      if (name) {
        where.name = { [Op.like]: `%${name}%` };
      }
      if (status !== undefined && status !== '') {
        where.status = parseInt(status);
      }

      const { count, rows } = await Role.findAndCountAll({
        where,
        include: [
          {
            model: Permission,
            as: 'Permissions',
            through: { attributes: [] },
            attributes: ['id', 'name', 'code']
          }
        ],
        order: [['created_at', 'DESC']],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: {
          list: rows,
          total: count,
          page: parseInt(page),
          limit: parseInt(limit)
        }
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error.message
      };
    }
  }

  // 获取所有角色（用于下拉选择）
  async getAllRoles(ctx) {
    try {
      const roles = await Role.findAll({
        where: { status: 1 },
        attributes: ['id', 'name', 'code', 'level'],
        order: [['level', 'ASC'], ['created_at', 'ASC']]
      });

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: roles
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error.message
      };
    }
  }

  // 创建角色
  async createRole(ctx) {
    try {
      const { name, code, description, level, permission_ids } = ctx.request.body;

      // 检查角色代码是否已存在
      const existingRole = await Role.findOne({ where: { code } });
      if (existingRole) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '角色代码已存在'
        };
        return;
      }

      const role = await Role.create({
        name,
        code,
        description,
        level: level || 3
      });

      // 分配权限
      if (permission_ids && permission_ids.length > 0) {
        const permissions = await Permission.findAll({
          where: { id: { [Op.in]: permission_ids } }
        });
        await role.setPermissions(permissions);
      }

      ctx.body = {
        code: 200,
        message: '创建成功',
        data: {
          id: role.id,
          name: role.name,
          code: role.code,
          description: role.description,
          level: role.level
        }
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error.message
      };
    }
  }

  // 更新角色
  async updateRole(ctx) {
    try {
      const { id } = ctx.params;
      const { name, code, description, level, permission_ids } = ctx.request.body;

      const role = await Role.findByPk(id);
      if (!role) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '角色不存在'
        };
        return;
      }

      // 检查角色代码是否被其他角色使用
      if (code && code !== role.code) {
        const existingRole = await Role.findOne({
          where: { code, id: { [Op.ne]: id } }
        });
        if (existingRole) {
          ctx.status = 400;
          ctx.body = {
            code: 400,
            message: '角色代码已被其他角色使用'
          };
          return;
        }
      }

      await role.update({
        name,
        code,
        description,
        level
      });

      // 更新权限
      if (permission_ids) {
        const permissions = await Permission.findAll({
          where: { id: { [Op.in]: permission_ids } }
        });
        await role.setPermissions(permissions);
      }

      ctx.body = {
        code: 200,
        message: '更新成功'
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error.message
      };
    }
  }

  // 删除角色
  async deleteRole(ctx) {
    try {
      const { id } = ctx.params;

      const role = await Role.findByPk(id);
      if (!role) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '角色不存在'
        };
        return;
      }

      // 检查是否有管理员使用此角色
      const AdminUser = require('../../models/adminUser');
      const adminCount = await AdminUser.count({ where: { role_id: id } });
      if (adminCount > 0) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '该角色下还有管理员，无法删除'
        };
        return;
      }

      await role.destroy();

      ctx.body = {
        code: 200,
        message: '删除成功'
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error.message
      };
    }
  }

  // 更新角色状态
  async updateRoleStatus(ctx) {
    try {
      const { id } = ctx.params;
      const { status } = ctx.request.body;

      const role = await Role.findByPk(id);
      if (!role) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '角色不存在'
        };
        return;
      }

      await role.update({ status });

      ctx.body = {
        code: 200,
        message: '状态更新成功'
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error.message
      };
    }
  }

  // 获取角色详情
  async getRoleDetail(ctx) {
    try {
      const { id } = ctx.params;

      const role = await Role.findByPk(id, {
        include: [
          {
            model: Permission,
            as: 'Permissions',
            through: { attributes: [] },
            attributes: ['id', 'name', 'code']
          }
        ]
      });

      if (!role) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '角色不存在'
        };
        return;
      }

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: role
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error.message
      };
    }
  }
}

module.exports = new RoleController(); 