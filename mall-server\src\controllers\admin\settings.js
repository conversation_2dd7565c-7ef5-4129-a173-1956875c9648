// 模拟数据存储（实际项目中应该使用数据库）
let settingsData = {
  basic: {
    siteName: '心洁茗茶管理系统',
    siteDescription: '专业的茗茶销售管理平台',
    logo: '',
    contactPhone: '************',
    contactEmail: '<EMAIL>',
    address: '福建省福州市茗茶批发市场'
  },
  payment: {
    alipayEnabled: true,
    wechatEnabled: true,
    alipayAppId: '',
    alipayPrivateKey: '',
    wechatAppId: '',
    wechatMchId: '',
    wechatApiKey: ''
  },
  shipping: {
    freeShippingAmount: 99,
    defaultShippingFee: 10,
    expressCompanies: ['顺丰速运', '圆通快递', '中通快递', '申通快递', '韵达快递']
  },
  sms: {
    provider: 'aliyun',
    accessKeyId: '',
    accessKeySecret: '',
    signName: '心洁茗茶',
    templateCode: ''
  },
  email: {
    smtpHost: 'smtp.qq.com',
    smtpPort: 587,
    smtpUser: '',
    smtpPassword: '',
    fromName: '心洁茗茶'
  }
};

// 获取基本设置
const getBasicSettings = async (ctx) => {
  try {
    ctx.body = {
      code: 200,
      message: '获取基本设置成功',
      data: settingsData.basic
    };
  } catch (error) {
    console.error('获取基本设置失败:', error);
    ctx.status = 500;
    ctx.body = {
      code: 500,
      message: '获取基本设置失败',
      error: error.message
    };
  }
};

// 更新基本设置
const updateBasicSettings = async (ctx) => {
  try {
    const data = ctx.request.body;
    settingsData.basic = { ...settingsData.basic, ...data };

    ctx.body = {
      code: 200,
      message: '更新基本设置成功',
      data: settingsData.basic
    };
  } catch (error) {
    console.error('更新基本设置失败:', error);
    ctx.status = 500;
    ctx.body = {
      code: 500,
      message: '更新基本设置失败',
      error: error.message
    };
  }
};

// 获取支付设置
const getPaymentSettings = async (ctx) => {
  try {
    ctx.body = {
      code: 200,
      message: '获取支付设置成功',
      data: settingsData.payment
    };
  } catch (error) {
    console.error('获取支付设置失败:', error);
    ctx.status = 500;
    ctx.body = {
      code: 500,
      message: '获取支付设置失败',
      error: error.message
    };
  }
};

// 更新支付设置
const updatePaymentSettings = async (ctx) => {
  try {
    const data = ctx.request.body;
    settingsData.payment = { ...settingsData.payment, ...data };

    ctx.body = {
      code: 200,
      message: '更新支付设置成功',
      data: settingsData.payment
    };
  } catch (error) {
    console.error('更新支付设置失败:', error);
    ctx.status = 500;
    ctx.body = {
      code: 500,
      message: '更新支付设置失败',
      error: error.message
    };
  }
};

// 获取物流设置
const getShippingSettings = async (ctx) => {
  try {
    ctx.body = {
      code: 200,
      message: '获取物流设置成功',
      data: settingsData.shipping
    };
  } catch (error) {
    console.error('获取物流设置失败:', error);
    ctx.status = 500;
    ctx.body = {
      code: 500,
      message: '获取物流设置失败',
      error: error.message
    };
  }
};

// 更新物流设置
const updateShippingSettings = async (ctx) => {
  try {
    const data = ctx.request.body;
    settingsData.shipping = { ...settingsData.shipping, ...data };

    ctx.body = {
      code: 200,
      message: '更新物流设置成功',
      data: settingsData.shipping
    };
  } catch (error) {
    console.error('更新物流设置失败:', error);
    ctx.status = 500;
    ctx.body = {
      code: 500,
      message: '更新物流设置失败',
      error: error.message
    };
  }
};

// 获取短信设置
const getSmsSettings = async (ctx) => {
  try {
    ctx.body = {
      code: 200,
      message: '获取短信设置成功',
      data: settingsData.sms
    };
  } catch (error) {
    console.error('获取短信设置失败:', error);
    ctx.status = 500;
    ctx.body = {
      code: 500,
      message: '获取短信设置失败',
      error: error.message
    };
  }
};

// 更新短信设置
const updateSmsSettings = async (ctx) => {
  try {
    const data = ctx.request.body;
    settingsData.sms = { ...settingsData.sms, ...data };

    ctx.body = {
      code: 200,
      message: '更新短信设置成功',
      data: settingsData.sms
    };
  } catch (error) {
    console.error('更新短信设置失败:', error);
    ctx.status = 500;
    ctx.body = {
      code: 500,
      message: '更新短信设置失败',
      error: error.message
    };
  }
};

// 获取邮件设置
const getEmailSettings = async (ctx) => {
  try {
    ctx.body = {
      code: 200,
      message: '获取邮件设置成功',
      data: settingsData.email
    };
  } catch (error) {
    console.error('获取邮件设置失败:', error);
    ctx.status = 500;
    ctx.body = {
      code: 500,
      message: '获取邮件设置失败',
      error: error.message
    };
  }
};

// 更新邮件设置
const updateEmailSettings = async (ctx) => {
  try {
    const data = ctx.request.body;
    settingsData.email = { ...settingsData.email, ...data };

    ctx.body = {
      code: 200,
      message: '更新邮件设置成功',
      data: settingsData.email
    };
  } catch (error) {
    console.error('更新邮件设置失败:', error);
    ctx.status = 500;
    ctx.body = {
      code: 500,
      message: '更新邮件设置失败',
      error: error.message
    };
  }
};

module.exports = {
  // 兼容旧的接口
  getSettings: getBasicSettings,
  updateSettings: updateBasicSettings,
  getPaymentSettings,
  updatePaymentSettings,

  // 新的详细接口
  getBasicSettings,
  updateBasicSettings,
  getShippingSettings,
  updateShippingSettings,
  getSmsSettings,
  updateSmsSettings,
  getEmailSettings,
  updateEmailSettings
};
