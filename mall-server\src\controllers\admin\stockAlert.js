// 库存预警控制器
const stockAlertService = require('../../services/stockAlert');

class StockAlertController {

  // 获取库存预警列表
  async getAlerts(ctx) {
    try {
      const {
        status = 'active',
        alert_type,
        alert_level,
        page = 1,
        limit = 20
      } = ctx.query;

      const filters = {
        status,
        alertType: alert_type,
        alertLevel: alert_level,
        page: parseInt(page),
        limit: parseInt(limit)
      };

      const alerts = await stockAlertService.getAlerts(filters);

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: alerts
      };
    } catch (error) {
      console.error('获取库存预警失败:', error);
      ctx.body = {
        code: 500,
        message: '获取库存预警失败',
        error: error.message
      };
    }
  }

  // 手动检查库存
  async checkStock(ctx) {
    try {
      const alertCount = await stockAlertService.checkAllProductsStock();

      ctx.body = {
        code: 200,
        message: '库存检查完成',
        data: {
          alert_count: alertCount
        }
      };
    } catch (error) {
      console.error('检查库存失败:', error);
      ctx.body = {
        code: 500,
        message: '检查库存失败',
        error: error.message
      };
    }
  }

  // 解决预警
  async resolveAlert(ctx) {
    try {
      const { id } = ctx.params;
      const { note } = ctx.request.body;
      const adminUserId = ctx.state.user?.id;

      if (!adminUserId) {
        ctx.body = {
          code: 401,
          message: '未授权操作'
        };
        return;
      }

      const alert = await stockAlertService.resolveAlert(id, adminUserId, note);

      ctx.body = {
        code: 200,
        message: '预警已解决',
        data: alert
      };
    } catch (error) {
      console.error('解决预警失败:', error);
      ctx.body = {
        code: 500,
        message: '解决预警失败',
        error: error.message
      };
    }
  }

  // 忽略预警
  async ignoreAlert(ctx) {
    try {
      const { id } = ctx.params;
      const { note } = ctx.request.body;
      const adminUserId = ctx.state.user?.id;

      if (!adminUserId) {
        ctx.body = {
          code: 401,
          message: '未授权操作'
        };
        return;
      }

      const alert = await stockAlertService.ignoreAlert(id, adminUserId, note);

      ctx.body = {
        code: 200,
        message: '预警已忽略',
        data: alert
      };
    } catch (error) {
      console.error('忽略预警失败:', error);
      ctx.body = {
        code: 500,
        message: '忽略预警失败',
        error: error.message
      };
    }
  }

  // 获取预警统计
  async getAlertStats(ctx) {
    try {
      const stats = await stockAlertService.getAlertStats();

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: stats
      };
    } catch (error) {
      console.error('获取预警统计失败:', error);
      ctx.body = {
        code: 500,
        message: '获取预警统计失败',
        error: error.message
      };
    }
  }

  // 设置预警阈值
  async setAlertThresholds(ctx) {
    try {
      const { low_stock, critical_stock, overstock } = ctx.request.body;

      const thresholds = {};
      if (low_stock !== undefined) thresholds.low_stock = parseInt(low_stock);
      if (critical_stock !== undefined) thresholds.critical_stock = parseInt(critical_stock);
      if (overstock !== undefined) thresholds.overstock = parseInt(overstock);

      stockAlertService.setAlertThresholds(thresholds);

      ctx.body = {
        code: 200,
        message: '预警阈值设置成功',
        data: thresholds
      };
    } catch (error) {
      console.error('设置预警阈值失败:', error);
      ctx.body = {
        code: 500,
        message: '设置预警阈值失败',
        error: error.message
      };
    }
  }

  // 清理旧预警
  async cleanupOldAlerts(ctx) {
    try {
      const { days = 30 } = ctx.request.body;

      const deletedCount = await stockAlertService.cleanupOldAlerts(parseInt(days));

      ctx.body = {
        code: 200,
        message: '清理完成',
        data: {
          deleted_count: deletedCount
        }
      };
    } catch (error) {
      console.error('清理旧预警失败:', error);
      ctx.body = {
        code: 500,
        message: '清理旧预警失败',
        error: error.message
      };
    }
  }
}

module.exports = new StockAlertController();
