const path = require('path');
const fs = require('fs');
const crypto = require('crypto');
const formidable = require('formidable');

// 上传图片
const uploadImage = async (ctx) => {
  try {
    const form = new formidable.IncomingForm();
    form.uploadDir = path.join(__dirname, '../../../uploads/temp');
    form.keepExtensions = true;
    form.maxFileSize = 5 * 1024 * 1024; // 5MB

    const [fields, files] = await new Promise((resolve, reject) => {
      form.parse(ctx.req, (err, fields, files) => {
        if (err) reject(err);
        else resolve([fields, files]);
      });
    });

    const file = files.file;
    
    if (!file) {
      ctx.body = {
        success: false,
        message: '未上传文件'
      };
      return;
    }

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    const fileType = file.mimetype || getMimeType(file.originalFilename);
    if (!allowedTypes.includes(fileType)) {
      // 删除临时文件
      fs.unlinkSync(file.filepath);
      ctx.body = {
        success: false,
        message: '不支持的文件类型，只支持 JPG、PNG、GIF、WebP 格式'
      };
      return;
    }

    // 生成文件名
    const ext = path.extname(file.originalFilename);
    const timestamp = Date.now();
    const random = crypto.randomBytes(8).toString('hex');
    const filename = `banners-${timestamp}${ext}`;

    // 确保上传目录存在
    const uploadDir = path.join(__dirname, '../../../uploads/banners');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // 移动文件到目标目录
    const targetPath = path.join(uploadDir, filename);
    fs.renameSync(file.filepath, targetPath);

    const imageUrl = `/uploads/banners/${filename}`;

    ctx.body = {
      success: true,
      data: {
        url: imageUrl,
        filename: filename,
        size: file.size,
        type: fileType
      },
      message: '图片上传成功'
    };

  } catch (error) {
    console.error('图片上传失败:', error);
    ctx.body = {
      success: false,
      message: '图片上传失败'
    };
  }
};

// 获取文件MIME类型
function getMimeType(filename) {
  const ext = path.extname(filename).toLowerCase();
  const mimeTypes = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.webp': 'image/webp'
  };
  return mimeTypes[ext] || 'application/octet-stream';
}

// 上传文件
const uploadFile = async (ctx) => {
  try {
    const form = new formidable.IncomingForm();
    form.uploadDir = path.join(__dirname, '../../../uploads/temp');
    form.keepExtensions = true;
    form.maxFileSize = 20 * 1024 * 1024; // 20MB

    const [fields, files] = await new Promise((resolve, reject) => {
      form.parse(ctx.req, (err, fields, files) => {
        if (err) reject(err);
        else resolve([fields, files]);
      });
    });

    const file = files.file;
    
    if (!file) {
      ctx.body = {
        success: false,
        message: '未上传文件'
      };
      return;
    }

    // 生成文件名
    const ext = path.extname(file.originalFilename);
    const timestamp = Date.now();
    const random = crypto.randomBytes(8).toString('hex');
    const filename = `file-${timestamp}-${random}${ext}`;

    // 确保上传目录存在
    const uploadDir = path.join(__dirname, '../../../uploads/files');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // 移动文件到目标目录
    const targetPath = path.join(uploadDir, filename);
    fs.renameSync(file.filepath, targetPath);

    const fileUrl = `/uploads/files/${filename}`;

    ctx.body = {
      success: true,
      data: {
        url: fileUrl,
        filename: filename,
        size: file.size,
        type: file.mimetype || getMimeType(file.originalFilename)
      },
      message: '文件上传成功'
    };

  } catch (error) {
    console.error('文件上传失败:', error);
    ctx.body = {
      success: false,
      message: '文件上传失败'
    };
  }
};

// 删除文件
const deleteFile = async (ctx) => {
  try {
    const { filePath } = ctx.request.body;
    
    if (!filePath) {
      ctx.body = {
        success: false,
        message: '文件路径不能为空'
      };
      return;
    }

    // 构建完整的文件路径
    const fullPath = path.join(__dirname, '../../../uploads', filePath.replace('/uploads/', ''));
    
    // 检查文件是否存在
    if (!fs.existsSync(fullPath)) {
      ctx.body = {
        success: false,
        message: '文件不存在'
      };
      return;
    }

    // 删除文件
    fs.unlinkSync(fullPath);

    ctx.body = {
      success: true,
      message: '文件删除成功'
    };

  } catch (error) {
    console.error('文件删除失败:', error);
    ctx.body = {
      success: false,
      message: '文件删除失败'
    };
  }
};

module.exports = {
  uploadImage,
  uploadFile,
  deleteFile
}; 
