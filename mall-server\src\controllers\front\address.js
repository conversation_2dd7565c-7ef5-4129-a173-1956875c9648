const addressService = require('../../services/address');

class AddressController {
  // 获取地址列表
  async getAddressList(ctx) {
    try {
      const userId = ctx.state.user.id;
      const addresses = await addressService.getAddressList(userId);

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: addresses
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 添加地址
  async addAddress(ctx) {
    try {
      const userId = ctx.state.user.id;
      const addressData = ctx.request.body;

      // 验证地址信息
      addressService.validateAddress(addressData);

      const address = await addressService.addAddress(userId, addressData);

      ctx.body = {
        code: 200,
        message: '添加成功',
        data: address
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 更新地址
  async updateAddress(ctx) {
    try {
      const userId = ctx.state.user.id;
      const { id } = ctx.params;
      const addressData = ctx.request.body;

      // 验证地址信息
      addressService.validateAddress(addressData);

      const address = await addressService.updateAddress(userId, parseInt(id), addressData);

      ctx.body = {
        code: 200,
        message: '更新成功',
        data: address
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 删除地址
  async deleteAddress(ctx) {
    try {
      const userId = ctx.state.user.id;
      const { id } = ctx.params;

      await addressService.deleteAddress(userId, parseInt(id));

      ctx.body = {
        code: 200,
        message: '删除成功'
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 设置默认地址
  async setDefaultAddress(ctx) {
    try {
      const userId = ctx.state.user.id;
      const { id } = ctx.params;

      const address = await addressService.setDefaultAddress(userId, parseInt(id));

      ctx.body = {
        code: 200,
        message: '设置成功',
        data: address
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 获取默认地址
  async getDefaultAddress(ctx) {
    try {
      const userId = ctx.state.user.id;
      const address = await addressService.getDefaultAddress(userId);

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: address
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 获取地址详情
  async getAddressDetail(ctx) {
    try {
      const userId = ctx.state.user.id;
      const { id } = ctx.params;

      const address = await addressService.getAddressDetail(userId, parseInt(id));

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: address
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }
}

module.exports = new AddressController(); 