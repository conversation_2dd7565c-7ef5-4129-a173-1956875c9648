const bannerService = require('../../services/banner');

class BannerController {
  // 获取轮播图列表
  async getBannerList(ctx) {
    try {
      console.log('🔍 开始获取轮播图列表...');
      const banners = await bannerService.getBannerList();
      console.log('📊 获取到轮播图数据:', banners);

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: banners
      };
      console.log('✅ 轮播图API响应成功');
    } catch (error) {
      console.error('❌ 获取轮播图列表失败:', error);
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message || '获取轮播图列表失败'
      };
    }
  }
}

module.exports = new BannerController(); 