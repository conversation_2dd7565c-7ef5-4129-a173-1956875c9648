const cartService = require('../../services/cart');
const DiscountService = require('../../services/discountService');

class CartController {
  // 获取购物车列表
  async getCartList(ctx) {
    try {
      // 检查用户认证
      if (!ctx.state.user || !ctx.state.user.id) {
        ctx.status = 401;
        ctx.body = {
          code: 401,
          message: '用户未登录'
        };
        return;
      }

      const userId = ctx.state.user.id;
      console.log('购物车请求 - 用户ID:', userId);

      const cartItems = await cartService.getCartList(userId);
      const stats = await cartService.getCartStats(userId);

      // 计算购物车折扣价格
      const cartTotal = await DiscountService.calculateCartTotal(cartItems, userId);

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: {
          items: cartTotal.items,
          stats: {
            ...stats,
            originalTotal: cartTotal.originalTotal,
            discountTotal: cartTotal.discountTotal,
            totalDiscount: cartTotal.totalDiscount
          }
        }
      };
    } catch (error) {
      console.error('购物车列表获取失败:', error);
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 添加商品到购物车
  async addToCart(ctx) {
    try {
      const userId = ctx.state.user.id;
      const { productId, quantity = 1 } = ctx.request.body;

      if (!productId) {
        throw new Error('商品ID不能为空');
      }

      const cartItem = await cartService.addToCart(userId, parseInt(productId), parseInt(quantity));

      ctx.body = {
        code: 200,
        message: '添加成功',
        data: cartItem
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 更新购物车商品数量
  async updateCartItem(ctx) {
    try {
      const userId = ctx.state.user.id;
      const { cartItemId, quantity } = ctx.request.body;

      if (!cartItemId || !quantity) {
        throw new Error('参数不完整');
      }

      const cartItem = await cartService.updateCartItem(userId, parseInt(cartItemId), parseInt(quantity));

      ctx.body = {
        code: 200,
        message: '更新成功',
        data: cartItem
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 删除购物车商品
  async removeFromCart(ctx) {
    try {
      const userId = ctx.state.user.id;
      const { cartItemId } = ctx.params;

      await cartService.removeFromCart(userId, parseInt(cartItemId));

      ctx.body = {
        code: 200,
        message: '删除成功'
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 选择/取消选择购物车商品
  async toggleCartItem(ctx) {
    try {
      const userId = ctx.state.user.id;
      const { cartItemId, selected } = ctx.request.body;

      if (cartItemId === undefined || selected === undefined) {
        throw new Error('参数不完整');
      }

      const cartItem = await cartService.toggleCartItem(userId, parseInt(cartItemId), parseInt(selected));

      ctx.body = {
        code: 200,
        message: '操作成功',
        data: cartItem
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 全选/取消全选购物车商品
  async toggleAllCartItems(ctx) {
    try {
      const userId = ctx.state.user.id;
      const { selected } = ctx.request.body;

      if (selected === undefined) {
        throw new Error('参数不完整');
      }

      await cartService.toggleAllCartItems(userId, parseInt(selected));

      ctx.body = {
        code: 200,
        message: '操作成功'
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 清空购物车
  async clearCart(ctx) {
    try {
      const userId = ctx.state.user.id;
      await cartService.clearCart(userId);

      ctx.body = {
        code: 200,
        message: '清空成功'
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 获取购物车统计
  async getCartStats(ctx) {
    try {
      const userId = ctx.state.user.id;
      const stats = await cartService.getCartStats(userId);

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: stats
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 获取购物车商品数量
  async getCartCount(ctx) {
    try {
      const userId = ctx.state.user.id;
      const count = await cartService.getCartCount(userId);

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: { count }
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 批量删除选中的购物车商品
  async removeSelectedItems(ctx) {
    try {
      const userId = ctx.state.user.id;
      await cartService.removeSelectedItems(userId);

      ctx.body = {
        code: 200,
        message: '删除成功'
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 检查购物车商品库存
  async checkCartStock(ctx) {
    try {
      const userId = ctx.state.user.id;
      const stockIssues = await cartService.checkCartStock(userId);

      ctx.body = {
        code: 200,
        message: '检查完成',
        data: stockIssues
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }
}

module.exports = new CartController(); 