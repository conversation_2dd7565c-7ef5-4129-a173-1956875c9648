// 购物车控制器 - 完整实现版
const cartService = require('../../services/cartService');

class CartController {

  // 统一响应格式
  sendResponse(ctx, data, message = '操作成功', code = 200) {
    ctx.status = code >= 400 ? code : 200;
    ctx.body = { 
      code, 
      message, 
      data, 
      timestamp: Date.now()
    };
  }

  // 统一错误处理
  handleError(ctx, error, message = '操作失败') {
    console.error(`${message}:`, error);
    const errorCode = error.code || 500;
    this.sendResponse(ctx, null, `${message}: ${error.message}`, errorCode);
  }

  // 获取用户ID
  getUserId(ctx) {
    const userId = ctx.state.user?.id;
    if (!userId) {
      this.sendResponse(ctx, null, '请先登录', 401);
      return null;
    }
    return userId;
  }

  // 添加商品到购物车
  async addToCart(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { product_id, sku_id, quantity, specifications } = ctx.request.body;

      if (!product_id) {
        return this.sendResponse(ctx, null, '请提供商品ID', 400);
      }

      if (quantity && (quantity <= 0 || quantity > 999)) {
        return this.sendResponse(ctx, null, '商品数量必须在1-999之间', 400);
      }

      const result = await cartService.addToCart(userId, {
        product_id: parseInt(product_id),
        sku_id: sku_id ? parseInt(sku_id) : null,
        quantity: quantity ? parseInt(quantity) : 1,
        specifications
      });

      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '添加购物车失败');
    }
  }

  // 获取购物车列表
  async getCartList(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const result = await cartService.getCartList(userId);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取购物车列表失败');
    }
  }

  // 更新购物车商品数量
  async updateQuantity(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { cart_id } = ctx.params;
      const { quantity } = ctx.request.body;

      if (!cart_id) {
        return this.sendResponse(ctx, null, '请提供购物车商品ID', 400);
      }

      if (!quantity || quantity <= 0 || quantity > 999) {
        return this.sendResponse(ctx, null, '商品数量必须在1-999之间', 400);
      }

      const result = await cartService.updateCartQuantity(
        userId, 
        parseInt(cart_id), 
        parseInt(quantity)
      );

      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '更新商品数量失败');
    }
  }

  // 删除购物车商品
  async removeFromCart(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { cart_ids } = ctx.request.body;

      if (!cart_ids || (Array.isArray(cart_ids) && cart_ids.length === 0)) {
        return this.sendResponse(ctx, null, '请提供要删除的商品ID', 400);
      }

      const ids = Array.isArray(cart_ids) 
        ? cart_ids.map(id => parseInt(id))
        : [parseInt(cart_ids)];

      const result = await cartService.removeFromCart(userId, ids);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '删除购物车商品失败');
    }
  }

  // 更新商品选中状态
  async updateSelection(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { cart_ids, selected } = ctx.request.body;

      if (!cart_ids || (Array.isArray(cart_ids) && cart_ids.length === 0)) {
        return this.sendResponse(ctx, null, '请提供要更新的商品ID', 400);
      }

      if (typeof selected !== 'boolean') {
        return this.sendResponse(ctx, null, '请提供有效的选中状态', 400);
      }

      const ids = Array.isArray(cart_ids) 
        ? cart_ids.map(id => parseInt(id))
        : [parseInt(cart_ids)];

      const result = await cartService.updateCartSelection(userId, ids, selected);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '更新选中状态失败');
    }
  }

  // 全选/取消全选
  async selectAll(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { selected } = ctx.request.body;

      if (typeof selected !== 'boolean') {
        return this.sendResponse(ctx, null, '请提供有效的选中状态', 400);
      }

      const result = await cartService.selectAll(userId, selected);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '全选操作失败');
    }
  }

  // 清空购物车
  async clearCart(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const result = await cartService.clearCart(userId);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '清空购物车失败');
    }
  }

  // 获取购物车商品数量
  async getCartCount(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const result = await cartService.getCartCount(userId);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取购物车数量失败');
    }
  }

  // 检查商品库存
  async checkStock(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { cart_ids } = ctx.query;
      let ids = null;

      if (cart_ids) {
        ids = Array.isArray(cart_ids) 
          ? cart_ids.map(id => parseInt(id))
          : cart_ids.split(',').map(id => parseInt(id));
      }

      const result = await cartService.checkStock(userId, ids);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '检查库存失败');
    }
  }

  // 获取选中商品信息（用于结算）
  async getSelectedItems(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const result = await cartService.getSelectedItems(userId);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取选中商品失败');
    }
  }

  // 批量操作
  async batchOperation(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { operation, cart_ids, data } = ctx.request.body;

      if (!operation || !cart_ids || cart_ids.length === 0) {
        return this.sendResponse(ctx, null, '请提供操作类型和商品ID', 400);
      }

      const ids = cart_ids.map(id => parseInt(id));
      let result;

      switch (operation) {
        case 'delete':
          result = await cartService.removeFromCart(userId, ids);
          break;
        case 'select':
          result = await cartService.updateCartSelection(userId, ids, true);
          break;
        case 'unselect':
          result = await cartService.updateCartSelection(userId, ids, false);
          break;
        default:
          return this.sendResponse(ctx, null, '不支持的操作类型', 400);
      }

      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '批量操作失败');
    }
  }

  // 购物车商品推荐
  async getRecommendations(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      // 这里可以基于购物车商品推荐相关商品
      // 暂时返回空数组，可以后续集成推荐算法
      const result = {
        recommendations: [],
        message: '暂无推荐商品'
      };

      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取推荐商品失败');
    }
  }
}

module.exports = new CartController();
