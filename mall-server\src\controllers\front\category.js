const categoryService = require('../../services/category');

class CategoryController {
  // 获取分类列表
  async getCategoryList(ctx) {
    try {
      const categories = await categoryService.getCategoryList();

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: categories
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 获取分类详情
  async getCategoryDetail(ctx) {
    try {
      const { id } = ctx.params;
      const category = await categoryService.getCategoryDetail(parseInt(id));

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: category
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }
}

module.exports = new CategoryController(); 