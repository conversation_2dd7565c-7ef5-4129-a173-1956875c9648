// 智能客服系统控制器 - 优化完善版
const customerService = require('../../services/customerService-enhanced');

class EnhancedCustomerServiceController {

  // 统一响应格式
  sendResponse(ctx, data, message = '操作成功', code = 200) {
    ctx.status = code >= 400 ? code : 200;
    ctx.body = { 
      code, 
      message, 
      data, 
      timestamp: Date.now(),
      requestId: ctx.state.requestId || this.generateRequestId()
    };
  }

  // 统一错误处理
  handleError(ctx, error, message = '操作失败') {
    console.error(`${message}:`, error);
    const errorCode = error.code || 500;
    this.sendResponse(ctx, null, `${message}: ${error.message}`, errorCode);
  }

  // 获取用户ID
  getUserId(ctx) {
    const userId = ctx.state.user?.id;
    if (!userId) {
      this.sendResponse(ctx, null, '请先登录', 401);
      return null;
    }
    return userId;
  }

  // 生成请求ID
  generateRequestId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // 智能创建客服会话
  async smartCreateSession(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { 
        category, 
        title, 
        priority, 
        tags, 
        initial_message 
      } = ctx.request.body;

      const result = await customerService.smartCreateSession(userId, {
        category, 
        title, 
        priority, 
        tags,
        initialMessage: initial_message
      });
      
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '智能创建客服会话失败');
    }
  }

  // 智能发送消息
  async smartSendMessage(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { session_id } = ctx.params;
      const { message_type, content, extra_data } = ctx.request.body;

      if (!content) {
        return this.sendResponse(ctx, null, '消息内容不能为空', 400);
      }

      const result = await customerService.smartSendMessage(
        parseInt(session_id), 
        userId, 
        'user', 
        { messageType: message_type, content, extraData: extra_data }
      );
      
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '智能发送消息失败');
    }
  }

  // 获取会话消息（带智能分析）
  async getSessionMessages(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { session_id } = ctx.params;
      const { page = 1, limit = 50, include_analysis = false } = ctx.query;

      // 验证会话权限
      const session = await CustomerSession.findByPk(parseInt(session_id));
      if (!session || session.user_id !== userId) {
        return this.sendResponse(ctx, null, '无权限访问该会话', 403);
      }

      const result = await customerService.getSessionMessages(
        parseInt(session_id), 
        page, 
        limit
      );

      // 如果需要分析数据
      if (include_analysis === 'true') {
        result.analysis = await this.analyzeSessionMessages(result.messages);
      }
      
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取会话消息失败');
    }
  }

  // 分析会话消息
  async analyzeSessionMessages(messages) {
    try {
      const analysis = {
        totalMessages: messages.length,
        userMessages: messages.filter(m => m.sender_type === 'user').length,
        systemMessages: messages.filter(m => m.sender_type === 'system').length,
        adminMessages: messages.filter(m => m.sender_type === 'admin').length,
        avgResponseTime: 0,
        sentiment: 'neutral'
      };

      // 计算平均响应时间
      const userMessages = messages.filter(m => m.sender_type === 'user');
      const adminMessages = messages.filter(m => m.sender_type === 'admin');
      
      if (userMessages.length > 0 && adminMessages.length > 0) {
        let totalResponseTime = 0;
        let responseCount = 0;

        userMessages.forEach(userMsg => {
          const nextAdminMsg = adminMessages.find(adminMsg => 
            new Date(adminMsg.created_at) > new Date(userMsg.created_at)
          );
          
          if (nextAdminMsg) {
            const responseTime = new Date(nextAdminMsg.created_at) - new Date(userMsg.created_at);
            totalResponseTime += responseTime;
            responseCount++;
          }
        });

        if (responseCount > 0) {
          analysis.avgResponseTime = Math.round(totalResponseTime / responseCount / 1000); // 秒
        }
      }

      // 简单情感分析
      const userTexts = userMessages.map(m => m.content).join(' ');
      analysis.sentiment = this.analyzeSentiment(userTexts);

      return analysis;
    } catch (error) {
      console.error('分析会话消息失败:', error);
      return null;
    }
  }

  // 简单情感分析
  analyzeSentiment(text) {
    const positiveWords = ['好', '满意', '不错', '谢谢', '感谢', '棒', '优秀'];
    const negativeWords = ['差', '不好', '问题', '投诉', '退款', '糟糕', '失望'];
    
    const lowerText = text.toLowerCase();
    let positiveCount = 0;
    let negativeCount = 0;

    positiveWords.forEach(word => {
      if (lowerText.includes(word)) positiveCount++;
    });

    negativeWords.forEach(word => {
      if (lowerText.includes(word)) negativeCount++;
    });

    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  // 获取用户会话列表（带统计）
  async getUserSessions(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { 
        page = 1, 
        limit = 20, 
        status = null,
        include_stats = false
      } = ctx.query;

      const result = await customerService.getUserSessions(userId, page, limit);

      // 如果需要统计数据
      if (include_stats === 'true') {
        result.stats = await customerService.getSmartSessionStats(null, 30);
      }

      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取会话列表失败');
    }
  }

  // 智能会话分配
  async smartAssignSession(ctx) {
    try {
      const { session_id } = ctx.params;
      
      const result = await customerService.smartAssignSession(parseInt(session_id));
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '智能会话分配失败');
    }
  }

  // 关闭会话（带智能总结）
  async closeSession(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { session_id } = ctx.params;
      const { feedback, auto_summary = false } = ctx.request.body;

      // 如果需要自动总结
      let sessionSummary = null;
      if (auto_summary) {
        sessionSummary = await this.generateSessionSummary(parseInt(session_id));
      }

      const result = await customerService.closeSession(
        parseInt(session_id), 
        userId, 
        feedback
      );

      if (sessionSummary) {
        result.summary = sessionSummary;
      }

      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '关闭会话失败');
    }
  }

  // 生成会话总结
  async generateSessionSummary(sessionId) {
    try {
      const messages = await customerService.getSessionMessages(sessionId, 1, 100);
      
      const summary = {
        messageCount: messages.messages.length,
        duration: this.calculateSessionDuration(messages.messages),
        mainTopics: this.extractMainTopics(messages.messages),
        resolution: this.analyzeResolution(messages.messages)
      };

      return summary;
    } catch (error) {
      console.error('生成会话总结失败:', error);
      return null;
    }
  }

  // 计算会话时长
  calculateSessionDuration(messages) {
    if (messages.length < 2) return 0;
    
    const firstMessage = messages[0];
    const lastMessage = messages[messages.length - 1];
    
    return Math.round((new Date(lastMessage.created_at) - new Date(firstMessage.created_at)) / 1000 / 60); // 分钟
  }

  // 提取主要话题
  extractMainTopics(messages) {
    const topics = [];
    const keywords = {
      '商品咨询': ['商品', '产品', '茶叶', '价格', '规格'],
      '订单问题': ['订单', '发货', '物流', '配送'],
      '支付问题': ['支付', '付款', '退款', '优惠'],
      '售后服务': ['退货', '换货', '质量', '问题']
    };

    const allText = messages.map(m => m.content).join(' ').toLowerCase();
    
    Object.keys(keywords).forEach(topic => {
      const hasKeyword = keywords[topic].some(keyword => allText.includes(keyword));
      if (hasKeyword) {
        topics.push(topic);
      }
    });

    return topics;
  }

  // 分析解决情况
  analyzeResolution(messages) {
    const lastMessages = messages.slice(-3); // 最后3条消息
    const lastUserMessage = lastMessages.reverse().find(m => m.sender_type === 'user');
    
    if (!lastUserMessage) return 'unknown';
    
    const content = lastUserMessage.content.toLowerCase();
    
    if (content.includes('谢谢') || content.includes('解决') || content.includes('好的')) {
      return 'resolved';
    }
    
    if (content.includes('还是') || content.includes('没有') || content.includes('不行')) {
      return 'unresolved';
    }
    
    return 'partial';
  }

  // 评价会话（带智能分析）
  async rateSession(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { session_id } = ctx.params;
      const { satisfaction, feedback, tags } = ctx.request.body;

      if (!satisfaction || satisfaction < 1 || satisfaction > 5) {
        return this.sendResponse(ctx, null, '请提供有效的满意度评分(1-5)', 400);
      }

      const result = await customerService.rateSession(
        parseInt(session_id), 
        userId, 
        satisfaction, 
        feedback
      );

      // 异步分析评价数据
      setImmediate(() => {
        this.analyzeRatingData(parseInt(session_id), satisfaction, feedback, tags);
      });

      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '评价会话失败');
    }
  }

  // 分析评价数据
  async analyzeRatingData(sessionId, satisfaction, feedback, tags) {
    try {
      // 这里可以实现评价数据分析逻辑
      // 例如：识别服务问题、改进建议等
      console.log(`会话 ${sessionId} 评价分析: 满意度 ${satisfaction}, 反馈: ${feedback}`);
    } catch (error) {
      console.error('分析评价数据失败:', error);
    }
  }

  // 获取智能FAQ
  async getSmartFAQ(ctx) {
    try {
      const { 
        category = null, 
        keyword = null,
        limit = 10
      } = ctx.query;

      // 基础FAQ数据
      let faqData = [
        {
          id: 1,
          category: '商品相关',
          question: '如何选择适合的茶叶？',
          answer: '您可以根据个人口味偏好选择，我们有绿茶、红茶、乌龙茶等多种类型。建议新手从清香型绿茶开始尝试。',
          keywords: ['选择', '茶叶', '口味', '新手'],
          popularity: 95
        },
        {
          id: 2,
          category: '订单相关',
          question: '如何查看订单状态？',
          answer: '您可以在"我的订单"页面查看所有订单的详细状态，包括待付款、待发货、待收货等。',
          keywords: ['订单', '状态', '查看'],
          popularity: 88
        },
        {
          id: 3,
          category: '配送相关',
          question: '一般多久能收到商品？',
          answer: '正常情况下，付款后1-2个工作日内发货，快递配送时间为2-5个工作日，具体以实际物流为准。',
          keywords: ['配送', '时间', '发货', '快递'],
          popularity: 92
        },
        {
          id: 4,
          category: '售后相关',
          question: '收到商品有问题怎么办？',
          answer: '如果收到的商品有质量问题，请在收货后7天内联系客服，我们将为您提供退换货服务。',
          keywords: ['质量', '问题', '退换货', '售后'],
          popularity: 85
        },
        {
          id: 5,
          category: '支付相关',
          question: '支持哪些支付方式？',
          answer: '我们支持微信支付、支付宝等多种支付方式，支付过程安全可靠。',
          keywords: ['支付', '方式', '微信', '支付宝'],
          popularity: 78
        }
      ];

      // 智能过滤
      if (category) {
        faqData = faqData.filter(item => item.category === category);
      }

      if (keyword) {
        const lowerKeyword = keyword.toLowerCase();
        faqData = faqData.filter(item => 
          item.question.toLowerCase().includes(lowerKeyword) ||
          item.answer.toLowerCase().includes(lowerKeyword) ||
          item.keywords.some(k => k.toLowerCase().includes(lowerKeyword))
        );
      }

      // 按热度排序并限制数量
      faqData = faqData
        .sort((a, b) => b.popularity - a.popularity)
        .slice(0, parseInt(limit));

      const result = {
        faq: faqData,
        categories: [...new Set(faqData.map(item => item.category))],
        total: faqData.length
      };

      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取智能FAQ失败');
    }
  }

  // 智能客服回复
  async getSmartAutoReply(ctx) {
    try {
      const { message, context = null } = ctx.query;
      if (!message) {
        return this.sendResponse(ctx, null, '请提供消息内容', 400);
      }

      // 生成AI回复
      const aiResponse = await customerService.generateAIResponse(message, context);
      
      // 获取相关FAQ
      const relatedFAQ = await this.findRelatedFAQ(message);

      const result = {
        reply: aiResponse || '很抱歉，我没有理解您的问题。请详细描述您遇到的问题，或联系人工客服为您服务。',
        isAuto: true,
        confidence: aiResponse ? 0.8 : 0.3,
        relatedFAQ,
        suggestions: this.generateReplySuggestions(message)
      };

      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取智能回复失败');
    }
  }

  // 查找相关FAQ
  async findRelatedFAQ(message) {
    try {
      // 简单的关键词匹配
      const keywords = message.toLowerCase().split(' ');
      const faqData = await this.getBasicFAQData();
      
      const relatedFAQ = faqData.filter(item => 
        keywords.some(keyword => 
          item.keywords.some(faqKeyword => 
            faqKeyword.toLowerCase().includes(keyword) || 
            keyword.includes(faqKeyword.toLowerCase())
          )
        )
      ).slice(0, 3);

      return relatedFAQ;
    } catch (error) {
      console.error('查找相关FAQ失败:', error);
      return [];
    }
  }

  // 生成回复建议
  generateReplySuggestions(message) {
    const suggestions = [];
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('商品') || lowerMessage.includes('茶叶')) {
      suggestions.push('查看商品详情', '联系商品顾问', '查看用户评价');
    }

    if (lowerMessage.includes('订单')) {
      suggestions.push('查询订单状态', '修改收货地址', '申请退款');
    }

    if (lowerMessage.includes('配送') || lowerMessage.includes('物流')) {
      suggestions.push('查看物流信息', '联系快递公司', '修改配送时间');
    }

    return suggestions.slice(0, 3);
  }

  // 获取基础FAQ数据
  async getBasicFAQData() {
    // 这里应该从数据库获取，暂时返回静态数据
    return [
      {
        id: 1,
        question: '如何选择适合的茶叶？',
        keywords: ['选择', '茶叶', '口味', '新手']
      },
      {
        id: 2,
        question: '如何查看订单状态？',
        keywords: ['订单', '状态', '查看']
      }
    ];
  }

  // 获取客服统计
  async getServiceStats(ctx) {
    try {
      const { days = 30 } = ctx.query;
      const result = await customerService.getSmartSessionStats(null, parseInt(days));
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取客服统计失败');
    }
  }

  // 上传客服文件（智能处理）
  async smartUploadFile(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { session_id } = ctx.params;
      const file = ctx.request.files?.file;
      
      if (!file) {
        return this.sendResponse(ctx, null, '请选择要上传的文件', 400);
      }

      // 智能文件类型检测和处理
      const fileInfo = await this.processUploadedFile(file);
      
      if (!fileInfo.isValid) {
        return this.sendResponse(ctx, null, fileInfo.error, 400);
      }

      // 发送文件消息
      const result = await customerService.smartSendMessage(
        parseInt(session_id),
        userId,
        'user',
        {
          messageType: fileInfo.messageType,
          content: fileInfo.description,
          extraData: fileInfo.data
        }
      );

      this.sendResponse(ctx, { 
        fileUrl: fileInfo.url, 
        fileInfo,
        ...result 
      });
    } catch (error) {
      this.handleError(ctx, error, '智能文件上传失败');
    }
  }

  // 处理上传的文件
  async processUploadedFile(file) {
    try {
      const allowedTypes = {
        'image/jpeg': { type: 'image', maxSize: 5 * 1024 * 1024 },
        'image/png': { type: 'image', maxSize: 5 * 1024 * 1024 },
        'image/gif': { type: 'image', maxSize: 2 * 1024 * 1024 },
        'application/pdf': { type: 'document', maxSize: 10 * 1024 * 1024 }
      };

      const fileConfig = allowedTypes[file.type];
      if (!fileConfig) {
        return {
          isValid: false,
          error: '不支持的文件类型'
        };
      }

      if (file.size > fileConfig.maxSize) {
        return {
          isValid: false,
          error: `文件大小超过限制 (${Math.round(fileConfig.maxSize / 1024 / 1024)}MB)`
        };
      }

      // 生成文件URL（这里应该实现实际的文件保存逻辑）
      const fileUrl = `/uploads/customer-service/${Date.now()}_${file.name}`;
      
      return {
        isValid: true,
        url: fileUrl,
        messageType: fileConfig.type,
        description: `发送了一个${fileConfig.type === 'image' ? '图片' : '文件'}`,
        data: {
          fileUrl,
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type,
          uploadTime: new Date().toISOString()
        }
      };

    } catch (error) {
      console.error('处理上传文件失败:', error);
      return {
        isValid: false,
        error: '文件处理失败'
      };
    }
  }
}

module.exports = new EnhancedCustomerServiceController();
