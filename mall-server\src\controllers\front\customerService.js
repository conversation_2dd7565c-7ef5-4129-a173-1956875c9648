// 客服系统控制器
const customerService = require('../../services/customerService');

class CustomerServiceController {

  // 统一响应格式
  sendResponse(ctx, data, message = '操作成功', code = 200) {
    ctx.status = code >= 400 ? code : 200;
    ctx.body = { code, message, data, timestamp: Date.now() };
  }

  // 统一错误处理
  handleError(ctx, error, message = '操作失败') {
    console.error(`${message}:`, error);
    this.sendResponse(ctx, null, `${message}: ${error.message}`, 500);
  }

  // 获取用户ID
  getUserId(ctx) {
    const userId = ctx.state.user?.id;
    if (!userId) {
      this.sendResponse(ctx, null, '请先登录', 401);
      return null;
    }
    return userId;
  }

  // 创建客服会话
  async createSession(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { category, title, priority, tags } = ctx.request.body;
      const result = await customerService.createSession(userId, {
        category, title, priority, tags
      });
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '创建客服会话失败');
    }
  }

  // 发送消息
  async sendMessage(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { session_id } = ctx.params;
      const { message_type, content, extra_data } = ctx.request.body;

      if (!content) {
        return this.sendResponse(ctx, null, '消息内容不能为空', 400);
      }

      const result = await customerService.sendMessage(
        parseInt(session_id), 
        userId, 
        'user', 
        { messageType: message_type, content, extraData: extra_data }
      );
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '发送消息失败');
    }
  }

  // 获取会话消息
  async getSessionMessages(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { session_id } = ctx.params;
      const { page = 1, limit = 50 } = ctx.query;

      // 验证会话权限
      const session = await customerService.getSessionById(parseInt(session_id));
      if (!session || session.user_id !== userId) {
        return this.sendResponse(ctx, null, '无权限访问该会话', 403);
      }

      const result = await customerService.getSessionMessages(parseInt(session_id), page, limit);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取会话消息失败');
    }
  }

  // 获取用户会话列表
  async getUserSessions(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { page = 1, limit = 20 } = ctx.query;
      const result = await customerService.getUserSessions(userId, page, limit);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取会话列表失败');
    }
  }

  // 关闭会话
  async closeSession(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { session_id } = ctx.params;
      const { feedback } = ctx.request.body;

      const result = await customerService.closeSession(parseInt(session_id), userId, feedback);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '关闭会话失败');
    }
  }

  // 评价会话
  async rateSession(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { session_id } = ctx.params;
      const { satisfaction, feedback } = ctx.request.body;

      if (!satisfaction || satisfaction < 1 || satisfaction > 5) {
        return this.sendResponse(ctx, null, '请提供有效的满意度评分(1-5)', 400);
      }

      const result = await customerService.rateSession(parseInt(session_id), userId, satisfaction, feedback);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '评价会话失败');
    }
  }

  // 获取常见问题
  async getFAQ(ctx) {
    try {
      // 这里可以从数据库或配置文件中获取常见问题
      const faqData = [
        {
          id: 1,
          category: '商品相关',
          question: '如何选择适合的茶叶？',
          answer: '您可以根据个人口味偏好选择，我们有绿茶、红茶、乌龙茶等多种类型。建议新手从清香型绿茶开始尝试。'
        },
        {
          id: 2,
          category: '订单相关',
          question: '如何查看订单状态？',
          answer: '您可以在"我的订单"页面查看所有订单的详细状态，包括待付款、待发货、待收货等。'
        },
        {
          id: 3,
          category: '配送相关',
          question: '一般多久能收到商品？',
          answer: '正常情况下，付款后1-2个工作日内发货，快递配送时间为2-5个工作日，具体以实际物流为准。'
        },
        {
          id: 4,
          category: '售后相关',
          question: '收到商品有问题怎么办？',
          answer: '如果收到的商品有质量问题，请在收货后7天内联系客服，我们将为您提供退换货服务。'
        },
        {
          id: 5,
          category: '支付相关',
          question: '支持哪些支付方式？',
          answer: '我们支持微信支付、支付宝等多种支付方式，支付过程安全可靠。'
        }
      ];

      const { category } = ctx.query;
      let filteredFAQ = faqData;
      
      if (category) {
        filteredFAQ = faqData.filter(item => item.category === category);
      }

      this.sendResponse(ctx, {
        faq: filteredFAQ,
        categories: [...new Set(faqData.map(item => item.category))]
      });
    } catch (error) {
      this.handleError(ctx, error, '获取常见问题失败');
    }
  }

  // 智能客服回复
  async getAutoReply(ctx) {
    try {
      const { message } = ctx.query;
      if (!message) {
        return this.sendResponse(ctx, null, '请提供消息内容', 400);
      }

      // 简单的关键词匹配自动回复
      const autoReplies = {
        '退货': '如需退货，请确保商品未开封且在7天内，联系客服提供订单号即可办理。',
        '换货': '如需换货，请确保商品未开封且在7天内，联系客服提供订单号和换货原因。',
        '发货': '订单支付成功后，我们会在1-2个工作日内安排发货，请耐心等待。',
        '物流': '您可以在订单详情页面查看物流信息，或联系快递公司查询配送进度。',
        '支付': '我们支持微信支付、支付宝等支付方式，支付过程安全可靠。',
        '价格': '商品价格以页面显示为准，如有疑问请联系客服确认。',
        '质量': '我们所有商品均经过严格质检，如收到商品有质量问题，请及时联系客服。',
        '保存': '茶叶请存放在干燥、阴凉、无异味的地方，避免阳光直射。',
        '泡茶': '不同茶叶有不同的冲泡方法，建议水温80-95度，冲泡时间2-5分钟。'
      };

      let reply = '很抱歉，我没有理解您的问题。请详细描述您遇到的问题，或联系人工客服为您服务。';
      
      for (const [keyword, response] of Object.entries(autoReplies)) {
        if (message.includes(keyword)) {
          reply = response;
          break;
        }
      }

      this.sendResponse(ctx, { reply, isAuto: true });
    } catch (error) {
      this.handleError(ctx, error, '获取自动回复失败');
    }
  }

  // 上传客服文件
  async uploadFile(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      // 这里应该实现文件上传逻辑
      // 可以使用 multer 或其他文件上传中间件
      
      const { session_id } = ctx.params;
      const file = ctx.request.files?.file;
      
      if (!file) {
        return this.sendResponse(ctx, null, '请选择要上传的文件', 400);
      }

      // 文件类型和大小验证
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
      const maxSize = 5 * 1024 * 1024; // 5MB

      if (!allowedTypes.includes(file.type)) {
        return this.sendResponse(ctx, null, '只支持上传图片文件', 400);
      }

      if (file.size > maxSize) {
        return this.sendResponse(ctx, null, '文件大小不能超过5MB', 400);
      }

      // 这里应该实现实际的文件保存逻辑
      const fileUrl = `/uploads/customer-service/${Date.now()}_${file.name}`;
      
      // 发送文件消息
      const result = await customerService.sendMessage(
        parseInt(session_id),
        userId,
        'user',
        {
          messageType: 'image',
          content: '发送了一张图片',
          extraData: {
            fileUrl,
            fileName: file.name,
            fileSize: file.size,
            fileType: file.type
          }
        }
      );

      this.sendResponse(ctx, { fileUrl, ...result });
    } catch (error) {
      this.handleError(ctx, error, '文件上传失败');
    }
  }
}

module.exports = new CustomerServiceController();
