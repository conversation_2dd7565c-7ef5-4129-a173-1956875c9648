const OrderService = require('../../services/OrderService');

class OrderController {
  // 创建订单
  async createOrder(ctx) {
    try {
      const userId = ctx.state.user?.id;
      if (!userId) {
        ctx.status = 401;
        ctx.body = {
          success: false,
          message: '用户未登录'
        };
        return;
      }

      const orderData = {
        userId,
        ...ctx.request.body
      };

      const result = await OrderService.createOrder(orderData);

      if (result.success) {
        ctx.status = 201;
        ctx.body = {
          success: true,
          data: result.data,
          message: result.message || '订单创建成功'
        };
      } else {
        ctx.status = 400;
        ctx.body = {
          success: false,
          message: result.message || '订单创建失败'
        };
      }

    } catch (error) {
      console.error('创建订单异常:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '服务器内部错误'
      };
    }
  }

  // 获取订单列表
  async getOrderList(ctx) {
    try {
      const userId = ctx.state.user?.id;
      if (!userId) {
        ctx.status = 401;
        ctx.body = {
          success: false,
          message: '用户未登录'
        };
        return;
      }

      const { page = 1, size = 10, status = 'all', keyword = '' } = ctx.query;

      const result = await OrderService.getOrderList({
        userId,
        page: parseInt(page),
        size: parseInt(size),
        status,
        keyword
      });

      if (result.success) {
        ctx.body = {
          success: true,
          data: result.data,
          message: '获取订单列表成功'
        };
      } else {
        ctx.status = 500;
        ctx.body = {
          success: false,
          message: result.message || '获取订单列表失败'
        };
      }
    } catch (error) {
      console.error('获取订单列表异常:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '服务器内部错误'
      };
    }
  }

  // 获取订单详情
  async getOrderDetail(ctx) {
    try {
      const { id } = ctx.params;
      const userId = ctx.state.user?.id;

      if (!userId) {
        ctx.status = 401;
        ctx.body = {
          success: false,
          message: '用户未登录'
        };
        return;
      }

      if (!id || isNaN(id)) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          message: '订单ID无效'
        };
        return;
      }

      const result = await OrderService.getOrderDetail(parseInt(id), userId);

      if (result.success) {
        ctx.body = {
          success: true,
          data: result.data,
          message: '获取订单详情成功'
        };
      } else {
        const statusCode = result.message === '订单不存在' ? 404 : 500;
        ctx.status = statusCode;
        ctx.body = {
          success: false,
          message: result.message || '获取订单详情失败'
        };
      }

    } catch (error) {
      console.error('获取订单详情异常:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '服务器内部错误'
      };
    }
  }

  // 取消订单
  async cancelOrder(ctx) {
    try {
      const { id } = ctx.params;
      const { reason } = ctx.request.body;
      const userId = ctx.state.user?.id;

      if (!userId) {
        ctx.status = 401;
        ctx.body = {
          success: false,
          message: '用户未登录'
        };
        return;
      }

      if (!id || isNaN(id)) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          message: '订单ID无效'
        };
        return;
      }

      const result = await OrderService.cancelOrder(parseInt(id), userId, reason);

      if (result.success) {
        ctx.body = {
          success: true,
          message: result.message || '订单取消成功'
        };
      } else {
        const statusCode = result.message.includes('不存在') ? 404 : 400;
        ctx.status = statusCode;
        ctx.body = {
          success: false,
          message: result.message || '订单取消失败'
        };
      }

    } catch (error) {
      console.error('取消订单异常:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '服务器内部错误'
      };
    }
  }

  // 确认收货
  async confirmOrder(ctx) {
    try {
      const { id } = ctx.params;
      const userId = ctx.state.user?.id;

      if (!userId) {
        ctx.status = 401;
        ctx.body = {
          success: false,
          message: '用户未登录'
        };
        return;
      }

      if (!id || isNaN(id)) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          message: '订单ID无效'
        };
        return;
      }

      const result = await OrderService.confirmDelivery(parseInt(id), userId);

      if (result.success) {
        ctx.body = {
          success: true,
          message: result.message || '确认收货成功'
        };
      } else {
        const statusCode = result.message.includes('不存在') ? 404 : 400;
        ctx.status = statusCode;
        ctx.body = {
          success: false,
          message: result.message || '确认收货失败'
        };
      }

    } catch (error) {
      console.error('确认收货异常:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '服务器内部错误'
      };
    }
  }

  // 申请退款
  async applyRefund(ctx) {
    try {
      const { id } = ctx.params;
      const { reason, amount, items } = ctx.request.body;
      const userId = ctx.state.user?.id;

      if (!userId) {
        ctx.status = 401;
        ctx.body = {
          success: false,
          message: '用户未登录'
        };
        return;
      }

      if (!id || isNaN(id)) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          message: '订单ID无效'
        };
        return;
      }

      // 这里应该调用服务层处理退款申请
      // 简化处理，直接返回成功
      ctx.body = {
        success: true,
        message: '退款申请提交成功，请等待审核'
      };

    } catch (error) {
      console.error('申请退款异常:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '服务器内部错误'
      };
    }
  }

  // 支付订单
  async payOrder(ctx) {
    try {
      const { id } = ctx.params;
      const { paymentMethod } = ctx.request.body;
      const userId = ctx.state.user?.id;

      if (!userId) {
        ctx.status = 401;
        ctx.body = {
          success: false,
          message: '用户未登录'
        };
        return;
      }

      if (!id || isNaN(id)) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          message: '订单ID无效'
        };
        return;
      }

      // 这里应该调用支付服务
      // 简化处理，直接返回支付成功
      ctx.body = {
        success: true,
        data: {
          paymentUrl: `https://pay.example.com/order/${id}`,
          paymentMethod: paymentMethod || 'wechat'
        },
        message: '支付发起成功'
      };

    } catch (error) {
      console.error('支付订单异常:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '服务器内部错误'
      };
    }
  }

  // 获取订单统计
  async getOrderStats(ctx) {
    try {
      const userId = ctx.state.user?.id;
      if (!userId) {
        ctx.status = 401;
        ctx.body = {
          success: false,
          message: '用户未登录'
        };
        return;
      }

      // 这里可以调用服务层获取订单统计数据
      // 简化处理，返回模拟数据
      const statistics = {
        pending: 0,    // 待付款
        paid: 0,       // 待发货
        shipped: 0,    // 待收货
        delivered: 0,  // 待评价
        completed: 0,  // 已完成
        cancelled: 0,  // 已取消
        refunded: 0    // 已退款
      };

      ctx.body = {
        success: true,
        data: statistics,
        message: '获取订单统计成功'
      };

    } catch (error) {
      console.error('获取订单统计异常:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '服务器内部错误'
      };
    }
  }

  // 获取物流信息
  async getLogistics(ctx) {
    try {
      const { id } = ctx.params;
      const userId = ctx.state.user?.id;

      if (!userId) {
        ctx.status = 401;
        ctx.body = {
          success: false,
          message: '用户未登录'
        };
        return;
      }

      if (!id || isNaN(id)) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          message: '订单ID无效'
        };
        return;
      }

      // 获取订单信息
      const orderResult = await OrderService.getOrderDetail(parseInt(id), userId);

      if (!orderResult.success) {
        ctx.status = 404;
        ctx.body = {
          success: false,
          message: '订单不存在'
        };
        return;
      }

      const order = orderResult.data;

      // 模拟物流信息
      const logistics = {
        company: order.shippingInfo?.company || '顺丰速运',
        trackingNo: order.shippingInfo?.trackingNo || 'SF1234567890',
        status: order.shippingStatus,
        traces: [
          {
            time: '2024-01-20 10:00:00',
            status: '已发货',
            description: '您的订单已从【深圳分拣中心】发出'
          },
          {
            time: '2024-01-20 14:30:00',
            status: '运输中',
            description: '快件到达【广州转运中心】'
          },
          {
            time: '2024-01-21 09:15:00',
            status: '派送中',
            description: '快件正在派送中，派送员：张师傅，电话：138****5678'
          }
        ]
      };

      ctx.body = {
        success: true,
        data: logistics,
        message: '获取物流信息成功'
      };

    } catch (error) {
      console.error('获取物流信息异常:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '服务器内部错误'
      };
    }
  }
}

module.exports = new OrderController(); 