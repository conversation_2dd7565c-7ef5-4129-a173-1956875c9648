const paymentService = require('../../services/payment');

class PaymentController {
  // 创建支付订单
  async createPayment(ctx) {
    try {
      const { orderId, paymentMethod = 'wechat' } = ctx.request.body;
      const userId = ctx.state.user?.id;

      if (!orderId) {
        ctx.body = {
          code: 400,
          message: '订单ID不能为空'
        };
        return;
      }

      let result;
      if (paymentMethod === 'wechat') {
        result = await paymentService.createWechatPayment(orderId, userId);
      } else if (paymentMethod === 'alipay') {
        result = await paymentService.createAlipayPayment(orderId, userId);
      } else {
        throw new Error('不支持的支付方式');
      }

      ctx.body = {
        code: 200,
        message: '支付订单创建成功',
        data: result,
        success: true
      };
    } catch (error) {
      console.error('创建支付订单失败:', error);
      ctx.body = {
        code: 400,
        message: error.message,
        success: false
      };
    }
  }

  // 支付回调
  async paymentCallback(ctx) {
    try {
      const result = await paymentService.handleCallback(ctx.request.body);
      ctx.body = result;
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 查询支付状态
  async getPaymentStatus(ctx) {
    try {
      const { orderId } = ctx.params;
      const userId = ctx.state.user?.id;

      if (!orderId) {
        ctx.body = {
          code: 400,
          message: '订单ID不能为空'
        };
        return;
      }

      const result = await paymentService.queryPaymentStatus(orderId, userId);

      ctx.body = {
        code: 200,
        message: '查询成功',
        data: result,
        success: true
      };
    } catch (error) {
      console.error('查询支付状态失败:', error);
      ctx.body = {
        code: 400,
        message: error.message,
        success: false
      };
    }
  }

  // 申请退款
  async applyRefund(ctx) {
    try {
      const { orderId, reason } = ctx.request.body;
      const result = await paymentService.applyRefund(orderId, reason);

      ctx.body = {
        code: 200,
        message: '退款申请成功',
        data: result
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }
}

module.exports = new PaymentController(); 