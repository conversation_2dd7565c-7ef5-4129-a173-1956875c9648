const productService = require('../../services/product');
const DiscountService = require('../../services/discountService');

class ProductController {
  // 获取商品列表
  async getProductList(ctx) {
    try {
      const {
        page = 1,
        limit,
        pageSize,
        categoryId,
        keyword,
        isHot,
        isRecommend,
        minPrice,
        maxPrice,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = ctx.query;

      // 兼容 limit 和 pageSize 两种分页参数
      const realLimit = limit ? parseInt(limit) : (pageSize ? parseInt(pageSize) : 10);
      
      // 参数校验
      if (realLimit <= 0 || realLimit > 100) {
        throw new Error('分页大小必须在1-100之间');
      }

      const result = await productService.getProductList({
        page: parseInt(page),
        limit: realLimit,
        categoryId: categoryId ? parseInt(categoryId) : undefined,
        keyword,
        isHot: isHot ? parseInt(isHot) : undefined,
        isRecommend: isRecommend ? parseInt(isRecommend) : undefined,
        minPrice: minPrice ? parseFloat(minPrice) : undefined,
        maxPrice: maxPrice ? parseFloat(maxPrice) : undefined,
        sortBy,
        sortOrder
      });

      // 添加折扣信息
      if (result.list && result.list.length > 0) {
        const userId = ctx.state.user ? ctx.state.user.id : null;
        result.list = await DiscountService.addDiscountToProducts(result.list, userId);
      }

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: result
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message || '获取商品列表失败'
      };
    }
  }

  // 获取商品详情
  async getProductDetail(ctx) {
    try {
      const { id } = ctx.params;
      const product = await productService.getProductDetail(parseInt(id));

      // 添加折扣信息
      if (product) {
        const userId = ctx.state.user ? ctx.state.user.id : null;
        const discount = await DiscountService.getProductDiscount(
          product.id,
          product.category_id,
          userId
        );

        const priceInfo = DiscountService.calculateDiscountPrice(
          product.price,
          discount,
          1
        );

        Object.assign(product, priceInfo);
      }

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: product
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 获取推荐商品
  async getRecommendProducts(ctx) {
    try {
      const { limit = 10 } = ctx.query;
      const products = await productService.getRecommendProducts(parseInt(limit));

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: products
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 获取热销商品
  async getHotProducts(ctx) {
    try {
      const { limit = 10 } = ctx.query;
      const products = await productService.getHotProducts(parseInt(limit));

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: products
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 获取新品商品
  async getNewProducts(ctx) {
    try {
      const { limit = 10 } = ctx.query;
      const products = await productService.getNewProducts(parseInt(limit));

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: products
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 搜索商品
  async searchProducts(ctx) {
    try {
      const { keyword, page = 1, limit = 10 } = ctx.query;

      if (!keyword) {
        throw new Error('搜索关键词不能为空');
      }

      const result = await productService.searchProducts(keyword, parseInt(page), parseInt(limit));

      ctx.body = {
        code: 200,
        message: '搜索成功',
        data: result
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }
}

module.exports = new ProductController(); 