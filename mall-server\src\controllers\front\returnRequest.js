const returnService = require('../../services/returnService');
const { ReturnRequest } = require('../../models');

// 退货状态映射
const RETURN_STATUS = {
  PENDING: 0,        // 待审核
  APPROVED: 1,       // 审核通过
  REJECTED: 2,       // 审核拒绝
  WAIT_RETURN: 3,    // 待寄回
  RETURNED: 4,       // 已寄回
  INSPECTING: 5,     // 验收中
  INSPECT_PASS: 6,   // 验收通过
  INSPECT_FAIL: 7,   // 验收不通过
  REFUNDED: 8,       // 退款完成
  CANCELLED: 9       // 已取消
};

class FrontReturnController {
  // 检查订单是否可以退货
  async checkOrderCanReturn(ctx) {
    try {
      const { orderId } = ctx.params;
      const userId = ctx.state.user.id;

      const order = await returnService.checkOrderCanReturn(orderId, userId);

      ctx.body = {
        code: 200,
        message: '订单可以退货',
        data: {
          canReturn: true,
          order: {
            id: order.id,
            order_no: order.order_no,
            total_amount: order.total_amount,
            pay_amount: order.pay_amount,
            delivery_time: order.delivery_time,
            orderItems: order.orderItems
          }
        }
      };
    } catch (error) {
      console.error('检查订单退货状态失败:', error);
      ctx.body = {
        code: 400,
        message: error.message,
        data: {
          canReturn: false
        }
      };
    }
  }

  // 提交退货申请
  async submitReturnRequest(ctx) {
    try {
      const userId = ctx.state.user.id;
      const {
        orderId,
        returnType = 2, // 默认退货退款
        returnReason,
        returnDescription,
        returnItems,
        contactPhone,
        returnImages = []
      } = ctx.request.body;

      // 验证必填字段
      if (!orderId || !returnReason || !returnItems || returnItems.length === 0) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '请填写完整的退货信息'
        };
        return;
      }

      // 验证退货商品
      for (const item of returnItems) {
        if (!item.orderItemId || !item.quantity || item.quantity <= 0) {
          ctx.status = 400;
          ctx.body = {
            code: 400,
            message: '退货商品信息不完整'
          };
          return;
        }
      }

      const data = {
        orderId,
        userId,
        returnType,
        returnReason,
        returnDescription,
        returnItems,
        contactPhone,
        returnImages
      };

      const returnRequest = await returnService.createReturnRequest(data);

      ctx.body = {
        code: 200,
        message: '退货申请提交成功',
        data: returnRequest
      };
    } catch (error) {
      console.error('提交退货申请失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error.message || '提交退货申请失败'
      };
    }
  }

  // 获取用户退货申请列表
  async getUserReturnRequestList(ctx) {
    try {
      const userId = ctx.state.user.id;
      const {
        page = 1,
        limit = 10,
        status
      } = ctx.query;

      const params = {
        page,
        limit,
        userId,
        status
      };

      const result = await returnService.getReturnRequestList(params);

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: result
      };
    } catch (error) {
      console.error('获取退货申请列表失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '获取退货申请列表失败',
        error: error.message
      };
    }
  }

  // 获取退货申请详情
  async getReturnRequestDetail(ctx) {
    try {
      const { id } = ctx.params;
      const userId = ctx.state.user.id;

      const returnRequest = await returnService.getReturnRequestDetail(id, userId);

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: returnRequest
      };
    } catch (error) {
      console.error('获取退货申请详情失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error.message || '获取退货申请详情失败'
      };
    }
  }

  // 填写退货物流信息
  async updateReturnExpress(ctx) {
    try {
      const { id } = ctx.params;
      const userId = ctx.state.user.id;
      const { expressCompany, expressNo } = ctx.request.body;

      if (!expressCompany || !expressNo) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '请填写完整的物流信息'
        };
        return;
      }

      const returnRequest = await returnService.updateReturnExpress(id, userId, {
        expressCompany,
        expressNo
      });

      ctx.body = {
        code: 200,
        message: '物流信息提交成功',
        data: returnRequest
      };
    } catch (error) {
      console.error('更新退货物流信息失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error.message || '更新退货物流信息失败'
      };
    }
  }

  // 取消退货申请
  async cancelReturnRequest(ctx) {
    try {
      const { id } = ctx.params;
      const userId = ctx.state.user.id;
      const { reason } = ctx.request.body;

      const returnRequest = await returnService.cancelReturnRequest(id, userId, reason);

      ctx.body = {
        code: 200,
        message: '退货申请已取消',
        data: returnRequest
      };
    } catch (error) {
      console.error('取消退货申请失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error.message || '取消退货申请失败'
      };
    }
  }

  // 获取退货配置信息
  async getReturnSettings(ctx) {
    try {
      // 这里可以从数据库获取退货配置，暂时返回默认配置
      const settings = {
        returnDeadlineDays: 7,
        returnReasons: [
          '质量问题',
          '商品描述不符',
          '收到商品破损',
          '商品缺件',
          '不喜欢/不合适',
          '其他原因'
        ],
        returnAddress: {
          name: '心洁茗茶客服',
          phone: '************',
          address: '福建省福州市仓山区心洁茗茶仓储中心',
          zipcode: '350000'
        },
        returnShippingFee: 10
      };

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: settings
      };
    } catch (error) {
      console.error('获取退货配置失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '获取退货配置失败',
        error: error.message
      };
    }
  }
}

module.exports = new FrontReturnController();
