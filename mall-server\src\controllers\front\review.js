const { Review, User, Product, Order } = require('../../models');
const { Op } = require('sequelize');

// 发表商品评价
const createReview = async (ctx) => {
  try {
    const { orderId, productId, rating, content, images, isAnonymous } = ctx.request.body;
    const userId = ctx.state.user.id;

    // 验证参数
    if (!orderId || !productId || !rating) {
      return ctx.body = {
        code: 400,
        message: '缺少必要参数'
      };
    }

    // 验证评分范围
    if (rating < 1 || rating > 5) {
      return ctx.body = {
        code: 400,
        message: '评分必须在1-5之间'
      };
    }

    // 检查用户是否已购买该商品
    const order = await Order.findOne({
      where: {
        id: orderId,
        user_id: userId,
        order_status: 3 // 已完成状态
      },
      include: [{
        model: require('../../models/orderItem')(ctx.app.context.sequelize),
        as: 'orderItems',
        where: {
          product_id: productId
        }
      }]
    });

    if (!order) {
      return ctx.body = {
        code: 400,
        message: '您还没有购买过该商品或订单未完成'
      };
    }

    // 检查是否已经评价过该商品
    const existingReview = await Review.findOne({
      where: {
        order_id: orderId,
        product_id: productId,
        user_id: userId
      }
    });

    if (existingReview) {
      return ctx.body = {
        code: 400,
        message: '您已经评价过该商品，不能重复评价'
      };
    }

    // 验证订单商品是否存在
    const orderItem = order.orderItems.find(item => item.product_id == productId);
    if (!orderItem) {
      return ctx.body = {
        code: 400,
        message: '该订单中不包含此商品'
      };
    }

    // 这里的重复检查已经在上面完成，删除重复代码

    // 处理评价图片
    let processedImages = null;
    if (images && Array.isArray(images) && images.length > 0) {
      // 验证图片数量限制
      if (images.length > 5) {
        return ctx.body = {
          code: 400,
          message: '评价图片最多只能上传5张'
        };
      }

      // 验证图片格式
      const validImageTypes = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
      for (const image of images) {
        if (typeof image !== 'string' || !validImageTypes.some(type => image.toLowerCase().includes(type))) {
          return ctx.body = {
            code: 400,
            message: '图片格式不支持，请上传jpg、png、gif或webp格式的图片'
          };
        }
      }
      processedImages = JSON.stringify(images);
    }

    // 验证评价内容长度
    if (content && content.length > 500) {
      return ctx.body = {
        code: 400,
        message: '评价内容不能超过500字'
      };
    }

    // 创建评价
    const review = await Review.create({
      order_id: orderId,
      product_id: productId,
      user_id: userId,
      rating,
      content: content || '',
      images: processedImages,
      is_anonymous: isAnonymous ? 1 : 0
    });

    // 更新商品评分和评价数量
    const product = await Product.findByPk(productId);
    if (product) {
      const allReviews = await Review.findAll({
        where: { product_id: productId }
      });
      
      const totalRating = allReviews.reduce((sum, r) => sum + r.rating, 0);
      const avgRating = totalRating / allReviews.length;
      
      await product.update({
        rating: parseFloat(avgRating.toFixed(2)),
        review_count: allReviews.length
      });
    }

    ctx.body = {
      code: 200,
      message: '评价发表成功',
      data: review
    };
  } catch (error) {
    console.error('发表评价失败:', error);
    ctx.body = {
      code: 500,
      message: '发表评价失败'
    };
  }
};

// 获取商品评价列表
const getProductReviews = async (ctx) => {
  try {
    const { productId } = ctx.params;
    const { page = 1, limit = 10, rating } = ctx.query;

    const where = {
      product_id: productId
    };

    if (rating) {
      where.rating = rating;
    }

    const reviews = await Review.findAndCountAll({
      where,
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'nickname', 'avatar'],
        where: {
          status: 1 // 只显示正常用户的评价
        }
      }],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit)
    });

    // 处理评价数据
    const processedReviews = reviews.rows.map(review => {
      const reviewData = review.toJSON();
      return {
        id: reviewData.id,
        rating: reviewData.rating,
        content: reviewData.content,
        images: reviewData.images ? JSON.parse(reviewData.images) : [],
        isAnonymous: reviewData.is_anonymous === 1,
        replyContent: reviewData.reply_content,
        replyTime: reviewData.reply_time,
        createTime: reviewData.created_at,
        user: reviewData.is_anonymous === 1 ? {
          id: 0,
          nickname: '匿名用户',
          avatar: '/images/common/default-avatar.png'
        } : reviewData.user
      };
    });

    ctx.body = {
      code: 200,
      message: '获取评价列表成功',
      data: {
        list: processedReviews,
        pagination: {
          total: reviews.count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(reviews.count / parseInt(limit))
        }
      }
    };
  } catch (error) {
    console.error('获取商品评价失败:', error);
    ctx.body = {
      code: 500,
      message: '获取商品评价失败'
    };
  }
};

// 获取用户评价列表
const getUserReviews = async (ctx) => {
  try {
    const userId = ctx.state.user.id;
    const { page = 1, limit = 10 } = ctx.query;

    const reviews = await Review.findAndCountAll({
      where: {
        user_id: userId
      },
      include: [{
        model: Product,
        as: 'product',
        attributes: ['id', 'name', 'main_image']
      }],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit)
    });

    // 处理评价数据
    const processedReviews = reviews.rows.map(review => {
      const reviewData = review.toJSON();
      return {
        id: reviewData.id,
        rating: reviewData.rating,
        content: reviewData.content,
        images: reviewData.images ? JSON.parse(reviewData.images) : [],
        isAnonymous: reviewData.is_anonymous === 1,
        replyContent: reviewData.reply_content,
        replyTime: reviewData.reply_time,
        createTime: reviewData.created_at,
        product: reviewData.product
      };
    });

    ctx.body = {
      code: 200,
      message: '获取用户评价成功',
      data: {
        list: processedReviews,
        pagination: {
          total: reviews.count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(reviews.count / parseInt(limit))
        }
      }
    };
  } catch (error) {
    console.error('获取用户评价失败:', error);
    ctx.body = {
      code: 500,
      message: '获取用户评价失败'
    };
  }
};

// 删除评价
const deleteReview = async (ctx) => {
  try {
    const { id } = ctx.params;
    const userId = ctx.state.user.id;

    const review = await Review.findOne({
      where: {
        id,
        user_id: userId
      }
    });

    if (!review) {
      return ctx.body = {
        code: 404,
        message: '评价不存在或无权删除'
      };
    }

    await review.destroy();

    // 更新商品评分和评价数量
    const product = await Product.findByPk(review.product_id);
    if (product) {
      const allReviews = await Review.findAll({
        where: { product_id: review.product_id }
      });
      
      if (allReviews.length > 0) {
        const totalRating = allReviews.reduce((sum, r) => sum + r.rating, 0);
        const avgRating = totalRating / allReviews.length;
        
        await product.update({
          rating: parseFloat(avgRating.toFixed(2)),
          review_count: allReviews.length
        });
      } else {
        await product.update({
          rating: 0.00,
          review_count: 0
        });
      }
    }

    ctx.body = {
      code: 200,
      message: '评价删除成功'
    };
  } catch (error) {
    console.error('删除评价失败:', error);
    ctx.body = {
      code: 500,
      message: '删除评价失败'
    };
  }
};

// 检查用户是否可以评价商品
const checkCanReview = async (ctx) => {
  try {
    const { productId } = ctx.params;
    const userId = ctx.state.user.id;

    // 查找用户已完成的订单中包含该商品的订单
    const order = await Order.findOne({
      where: {
        user_id: userId,
        order_status: 3 // 已完成状态
      },
      include: [{
        model: require('../../models/orderItem')(ctx.app.context.sequelize),
        as: 'orderItems',
        where: {
          product_id: productId
        }
      }]
    });

    if (!order) {
      return ctx.body = {
        code: 200,
        message: '您还没有购买过该商品或订单未完成',
        data: {
          canReview: false,
          reason: '未购买或订单未完成'
        }
      };
    }

    // 检查是否已经评价过
    const existingReview = await Review.findOne({
      where: {
        order_id: order.id,
        product_id: productId,
        user_id: userId
      }
    });

    if (existingReview) {
      return ctx.body = {
        code: 200,
        message: '您已经评价过该商品',
        data: {
          canReview: false,
          reason: '已评价',
          review: existingReview
        }
      };
    }

    ctx.body = {
      code: 200,
      message: '可以评价',
      data: {
        canReview: true,
        orderId: order.id
      }
    };
  } catch (error) {
    console.error('检查评价权限失败:', error);
    ctx.body = {
      code: 500,
      message: '检查评价权限失败'
    };
  }
};

// 获取用户可评价的商品列表
const getPendingReviews = async (ctx) => {
  try {
    const userId = ctx.state.user.id;
    const { page = 1, limit = 10 } = ctx.query;

    // 查找用户已完成但未评价的订单商品
    const orders = await Order.findAll({
      where: {
        user_id: userId,
        order_status: 3 // 已完成状态
      },
      include: [{
        model: require('../../models/orderItem')(ctx.app.context.sequelize),
        as: 'orderItems',
        include: [{
          model: Product,
          as: 'product',
          attributes: ['id', 'name', 'main_image', 'price']
        }]
      }],
      order: [['created_at', 'DESC']]
    });

    // 获取所有已评价的商品ID
    const reviewedProducts = await Review.findAll({
      where: { user_id: userId },
      attributes: ['order_id', 'product_id']
    });

    const reviewedMap = new Map();
    reviewedProducts.forEach(review => {
      const key = `${review.order_id}_${review.product_id}`;
      reviewedMap.set(key, true);
    });

    // 筛选出未评价的商品
    const pendingItems = [];
    orders.forEach(order => {
      order.orderItems.forEach(item => {
        const key = `${order.id}_${item.product_id}`;
        if (!reviewedMap.has(key)) {
          pendingItems.push({
            orderId: order.id,
            orderNo: order.order_no,
            orderTime: order.created_at,
            productId: item.product_id,
            productName: item.product.name,
            productImage: item.product.main_image,
            productPrice: item.price,
            quantity: item.quantity,
            specifications: item.specifications
          });
        }
      });
    });

    // 分页处理
    const total = pendingItems.length;
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const endIndex = startIndex + parseInt(limit);
    const paginatedItems = pendingItems.slice(startIndex, endIndex);

    ctx.body = {
      code: 200,
      message: '获取待评价商品成功',
      data: {
        list: paginatedItems,
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    };
  } catch (error) {
    console.error('获取待评价商品失败:', error);
    ctx.body = {
      code: 500,
      message: '获取待评价商品失败'
    };
  }
};

// 批量评价商品
const batchCreateReviews = async (ctx) => {
  try {
    const { reviews } = ctx.request.body;
    const userId = ctx.state.user.id;

    if (!reviews || !Array.isArray(reviews) || reviews.length === 0) {
      return ctx.body = {
        code: 400,
        message: '评价数据不能为空'
      };
    }

    if (reviews.length > 10) {
      return ctx.body = {
        code: 400,
        message: '一次最多只能评价10个商品'
      };
    }

    const createdReviews = [];
    const errors = [];

    // 使用事务处理批量评价
    const transaction = await ctx.app.context.sequelize.transaction();

    try {
      for (const reviewData of reviews) {
        const { orderId, productId, rating, content, images, isAnonymous } = reviewData;

        // 验证必要参数
        if (!orderId || !productId || !rating) {
          errors.push(`商品ID ${productId}: 缺少必要参数`);
          continue;
        }

        // 验证评分范围
        if (rating < 1 || rating > 5) {
          errors.push(`商品ID ${productId}: 评分必须在1-5之间`);
          continue;
        }

        // 检查购买权限
        const order = await Order.findOne({
          where: {
            id: orderId,
            user_id: userId,
            order_status: 3
          },
          include: [{
            model: require('../../models/orderItem')(ctx.app.context.sequelize),
            as: 'orderItems',
            where: { product_id: productId }
          }],
          transaction
        });

        if (!order) {
          errors.push(`商品ID ${productId}: 未购买或订单未完成`);
          continue;
        }

        // 检查是否已评价
        const existingReview = await Review.findOne({
          where: {
            order_id: orderId,
            product_id: productId,
            user_id: userId
          },
          transaction
        });

        if (existingReview) {
          errors.push(`商品ID ${productId}: 已评价过该商品`);
          continue;
        }

        // 创建评价
        const review = await Review.create({
          order_id: orderId,
          product_id: productId,
          user_id: userId,
          rating,
          content: content || '',
          images: images ? JSON.stringify(images) : null,
          is_anonymous: isAnonymous ? 1 : 0
        }, { transaction });

        createdReviews.push(review);
      }

      // 更新商品评分
      const productIds = [...new Set(createdReviews.map(r => r.product_id))];
      for (const productId of productIds) {
        const allReviews = await Review.findAll({
          where: { product_id: productId },
          transaction
        });

        const totalRating = allReviews.reduce((sum, r) => sum + r.rating, 0);
        const avgRating = totalRating / allReviews.length;

        await Product.update({
          rating: parseFloat(avgRating.toFixed(2)),
          review_count: allReviews.length
        }, {
          where: { id: productId },
          transaction
        });
      }

      await transaction.commit();

      ctx.body = {
        code: 200,
        message: '批量评价完成',
        data: {
          success: createdReviews.length,
          errors: errors.length,
          errorMessages: errors
        }
      };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('批量评价失败:', error);
    ctx.body = {
      code: 500,
      message: '批量评价失败'
    };
  }
};

module.exports = {
  createReview,
  getProductReviews,
  getUserReviews,
  deleteReview,
  checkCanReview,
  getPendingReviews,
  batchCreateReviews
};