const searchService = require('../../services/search');

class SearchController {
  // 搜索商品
  async searchProducts(ctx) {
    try {
      const { keyword, page = 1, limit = 10 } = ctx.query;
      const result = await searchService.searchProducts(keyword, parseInt(page), parseInt(limit));

      ctx.body = {
        code: 200,
        message: '搜索成功',
        data: result
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 搜索建议
  async getSearchSuggestions(ctx) {
    try {
      const { keyword } = ctx.query;
      const suggestions = await searchService.getSearchSuggestions(keyword);

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: suggestions
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }
}

module.exports = new SearchController(); 