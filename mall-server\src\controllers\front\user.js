const userService = require('../../services/user');
const { validateUserLogin, validateUserRegister, validateUserUpdate, validateChangePassword } = require('../../validators/user');

class UserController {
  // 用户登录
  async login(ctx) {
    try {
      const { username, password } = ctx.request.body;
      
      // 参数验证
      await validateUserLogin.validateAsync({ username, password });
      
      const result = await userService.login(username, password);
      
      ctx.body = {
        code: 200,
        message: '登录成功',
        data: result
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 用户注册
  async register(ctx) {
    try {
      const userData = ctx.request.body;
      
      // 参数验证
      await validateUserRegister.validateAsync(userData);
      
      const result = await userService.register(userData);
      
      ctx.body = {
        code: 200,
        message: '注册成功',
        data: result
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 用户登出
  async logout(ctx) {
    try {
      await userService.logout(ctx.state.user.id);
      
      ctx.body = {
        code: 200,
        message: '登出成功'
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 获取用户信息
  async getUserInfo(ctx) {
    try {
      const userId = ctx.state.user.id;
      const userInfo = await userService.getUserInfo(userId);
      
      ctx.body = {
        code: 200,
        message: '获取成功',
        data: userInfo
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 更新用户信息
  async updateUserInfo(ctx) {
    try {
      const userId = ctx.state.user.id;
      const userData = ctx.request.body;

      // 暂时跳过验证，直接更新
      // await validateUserUpdate.validateAsync(userData);

      const result = await userService.updateUserInfo(userId, userData);

      ctx.body = {
        code: 200,
        message: '更新成功',
        data: result
      };
    } catch (error) {
      console.error('更新用户信息失败:', error);
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 修改密码
  async changePassword(ctx) {
    try {
      const userId = ctx.state.user.id;
      const { oldPassword, newPassword } = ctx.request.body;
      
      // 参数验证
      await validateChangePassword.validateAsync({ oldPassword, newPassword });
      
      await userService.changePassword(userId, oldPassword, newPassword);
      
      ctx.body = {
        code: 200,
        message: '密码修改成功'
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 获取用户地址列表
  async getUserAddresses(ctx) {
    try {
      const userId = ctx.state.user.id;
      const addresses = await userService.getUserAddresses(userId);
      
      ctx.body = {
        code: 200,
        message: '获取成功',
        data: addresses
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 获取用户订单列表
  async getUserOrders(ctx) {
    try {
      const userId = ctx.state.user.id;
      const { page = 1, limit = 10, status } = ctx.query;
      
      const result = await userService.getUserOrders(userId, {
        page: parseInt(page),
        limit: parseInt(limit),
        status: status ? parseInt(status) : undefined
      });
      
      ctx.body = {
        code: 200,
        message: '获取成功',
        data: result
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }

  // 获取用户购物车
  async getUserCart(ctx) {
    try {
      const userId = ctx.state.user.id;
      const cartItems = await userService.getUserCart(userId);
      
      ctx.body = {
        code: 200,
        message: '获取成功',
        data: cartItems
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error.message
      };
    }
  }
}

module.exports = new UserController(); 