// 用户功能优化控制器 - 智能增强版
const favoriteService = require('../../services/favoriteService-enhanced');
const browseHistoryService = require('../../services/browseHistoryService-enhanced');
const productCompareService = require('../../services/productCompareService-enhanced');
const shareService = require('../../services/shareService-enhanced');
const customerService = require('../../services/customerService-enhanced');

class EnhancedUserFeaturesController {

  // 统一响应格式
  sendResponse(ctx, data, message = '操作成功', code = 200) {
    ctx.status = code >= 400 ? code : 200;
    ctx.body = { 
      code, 
      message, 
      data, 
      timestamp: Date.now(),
      requestId: ctx.state.requestId || this.generateRequestId()
    };
  }

  // 统一错误处理
  handleError(ctx, error, message = '操作失败') {
    console.error(`${message}:`, error);
    const errorCode = error.code || 500;
    this.sendResponse(ctx, null, `${message}: ${error.message}`, errorCode);
  }

  // 获取用户ID
  getUserId(ctx) {
    const userId = ctx.state.user?.id;
    if (!userId) {
      this.sendResponse(ctx, null, '请先登录', 401);
      return null;
    }
    return userId;
  }

  // 生成请求ID
  generateRequestId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // === 智能收藏功能 ===

  // 智能添加收藏
  async smartAddFavorite(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { product_id, source = 'manual' } = ctx.request.body;
      if (!product_id) {
        return this.sendResponse(ctx, null, '请提供商品ID', 400);
      }

      const result = await favoriteService.smartAddFavorite(userId, product_id, source);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '智能添加收藏失败');
    }
  }

  // 获取智能收藏列表
  async getSmartFavoriteList(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { 
        page = 1, 
        limit = 20, 
        sort_by = 'smart',
        category = null,
        price_min = null,
        price_max = null
      } = ctx.query;

      const options = {
        page: parseInt(page),
        limit: parseInt(limit),
        sortBy: sort_by,
        category: category ? parseInt(category) : null,
        priceRange: (price_min && price_max) ? {
          min: parseFloat(price_min),
          max: parseFloat(price_max)
        } : null
      };

      const result = await favoriteService.getSmartFavoriteList(userId, options);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取智能收藏列表失败');
    }
  }

  // 获取收藏趋势分析
  async getFavoriteTrends(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { days = 30 } = ctx.query;
      const result = await favoriteService.getFavoriteTrends(userId, parseInt(days));
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取收藏趋势失败');
    }
  }

  // 获取收藏价值分析
  async getFavoriteValueAnalysis(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const result = await favoriteService.getFavoriteValueAnalysis(userId);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取收藏价值分析失败');
    }
  }

  // === 智能浏览历史功能 ===

  // 智能记录浏览
  async smartRecordBrowse(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { 
        product_id, 
        source = 'direct', 
        duration = 0, 
        device_info,
        referrer,
        search_keyword,
        session_id
      } = ctx.request.body;

      if (!product_id) {
        return this.sendResponse(ctx, null, '请提供商品ID', 400);
      }

      const result = await browseHistoryService.smartRecordBrowse(userId, product_id, {
        source, 
        duration, 
        deviceInfo: device_info,
        referrer,
        searchKeyword: search_keyword,
        sessionId: session_id
      });
      
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '智能记录浏览失败');
    }
  }

  // 获取智能浏览历史
  async getSmartBrowseHistory(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { 
        page = 1, 
        limit = 20, 
        group_by = 'date',
        time_range = 30,
        include_analysis = true
      } = ctx.query;

      const options = {
        page: parseInt(page),
        limit: parseInt(limit),
        groupBy: group_by,
        timeRange: parseInt(time_range),
        includeAnalysis: include_analysis === 'true'
      };

      const result = await browseHistoryService.getSmartBrowseHistory(userId, options);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取智能浏览历史失败');
    }
  }

  // 获取智能推荐
  async getSmartRecommendations(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { 
        limit = 10, 
        algorithm = 'hybrid' 
      } = ctx.query;

      const result = await browseHistoryService.getSmartRecommendations(userId, {
        limit: parseInt(limit),
        algorithm
      });
      
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取智能推荐失败');
    }
  }

  // 获取浏览热力图
  async getBrowseHeatmap(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { days = 30 } = ctx.query;
      const result = await browseHistoryService.getBrowseHeatmap(userId, parseInt(days));
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取浏览热力图失败');
    }
  }

  // === 智能商品对比功能 ===

  // 智能添加到对比
  async smartAddToCompare(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { product_id, compare_group = 'default' } = ctx.request.body;
      if (!product_id) {
        return this.sendResponse(ctx, null, '请提供商品ID', 400);
      }

      const result = await productCompareService.smartAddToCompare(userId, product_id, compare_group);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '智能添加对比失败');
    }
  }

  // 获取智能对比详情
  async getSmartCompareDetails(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { compare_group = 'default' } = ctx.query;
      const result = await productCompareService.getSmartCompareDetails(userId, compare_group);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取智能对比详情失败');
    }
  }

  // 获取对比建议
  async getCompareSuggestions(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { 
        product_id, 
        compare_group = 'default',
        limit = 6
      } = ctx.query;

      if (!product_id) {
        return this.sendResponse(ctx, null, '请提供商品ID', 400);
      }

      const result = await productCompareService.getSmartCompareSuggestions(
        userId, 
        parseInt(product_id), 
        compare_group, 
        parseInt(limit)
      );
      
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取对比建议失败');
    }
  }

  // === 智能分享功能 ===

  // 智能生成分享内容
  async smartGenerateShareContent(ctx) {
    try {
      const { 
        share_type, 
        target_id, 
        platform = 'wechat' 
      } = ctx.query;

      if (!share_type) {
        return this.sendResponse(ctx, null, '请提供分享类型', 400);
      }

      const userId = ctx.state.user?.id;
      const result = await shareService.smartGenerateShareContent(
        share_type, 
        target_id ? parseInt(target_id) : null, 
        platform, 
        userId
      );
      
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '智能生成分享内容失败');
    }
  }

  // 智能记录分享
  async smartRecordShare(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const shareData = ctx.request.body;
      if (!shareData.shareType || !shareData.sharePlatform) {
        return this.sendResponse(ctx, null, '请提供分享类型和平台', 400);
      }

      const result = await shareService.smartRecordShare(userId, shareData);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '智能记录分享失败');
    }
  }

  // 获取分享效果分析
  async getShareEffectAnalysis(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { days = 30 } = ctx.query;
      const result = await shareService.getShareEffectAnalysis(userId, parseInt(days));
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取分享效果分析失败');
    }
  }

  // 生成分享海报
  async generateSharePoster(ctx) {
    try {
      const { 
        share_type, 
        target_id,
        template = 'default',
        custom_text = '',
        qr_code_size = 120
      } = ctx.query;

      if (!share_type) {
        return this.sendResponse(ctx, null, '请提供分享类型', 400);
      }

      const result = await shareService.generateSharePoster(
        share_type, 
        target_id ? parseInt(target_id) : null,
        {
          template,
          customText: custom_text,
          qrCodeSize: parseInt(qr_code_size)
        }
      );
      
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '生成分享海报失败');
    }
  }

  // 获取分享排行榜
  async getShareRanking(ctx) {
    try {
      const { 
        type = 'product', 
        limit = 10, 
        days = 30 
      } = ctx.query;

      const result = await shareService.getShareRanking(type, parseInt(limit), parseInt(days));
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取分享排行榜失败');
    }
  }

  // === 系统管理功能 ===

  // 清理缓存
  async clearCache(ctx) {
    try {
      const { pattern } = ctx.query;
      
      favoriteService.clearCache(pattern);
      browseHistoryService.clearCache(pattern);
      productCompareService.clearCache(pattern);
      shareService.clearCache(pattern);
      
      this.sendResponse(ctx, { message: '缓存已清理' });
    } catch (error) {
      this.handleError(ctx, error, '清理缓存失败');
    }
  }

  // 获取系统状态
  async getSystemStatus(ctx) {
    try {
      const status = {
        browseHistory: browseHistoryService.getQueueStatus(),
        customerService: customerService.getQueueStatus(),
        timestamp: Date.now(),
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage()
      };
      
      this.sendResponse(ctx, status);
    } catch (error) {
      this.handleError(ctx, error, '获取系统状态失败');
    }
  }
}

module.exports = new EnhancedUserFeaturesController();
