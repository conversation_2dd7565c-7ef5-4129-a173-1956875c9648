// 用户功能控制器 - 收藏、浏览历史、对比、分享、客服
const favoriteService = require('../../services/favoriteService');
const browseHistoryService = require('../../services/browseHistoryService');
const productCompareService = require('../../services/productCompareService');
const shareService = require('../../services/shareService');
const customerService = require('../../services/customerService');

class UserFeaturesController {

  // 统一响应格式
  sendResponse(ctx, data, message = '操作成功', code = 200) {
    ctx.status = code >= 400 ? code : 200;
    ctx.body = { code, message, data, timestamp: Date.now() };
  }

  // 统一错误处理
  handleError(ctx, error, message = '操作失败') {
    console.error(`${message}:`, error);
    this.sendResponse(ctx, null, `${message}: ${error.message}`, 500);
  }

  // 获取用户ID
  getUserId(ctx) {
    const userId = ctx.state.user?.id;
    if (!userId) {
      this.sendResponse(ctx, null, '请先登录', 401);
      return null;
    }
    return userId;
  }

  // === 商品收藏功能 ===

  // 添加收藏
  async addFavorite(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { product_id } = ctx.request.body;
      if (!product_id) {
        return this.sendResponse(ctx, null, '请提供商品ID', 400);
      }

      const result = await favoriteService.addFavorite(userId, product_id);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '添加收藏失败');
    }
  }

  // 取消收藏
  async removeFavorite(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { product_id } = ctx.params;
      const result = await favoriteService.removeFavorite(userId, parseInt(product_id));
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '取消收藏失败');
    }
  }

  // 获取收藏列表
  async getFavoriteList(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { page = 1, limit = 20 } = ctx.query;
      const result = await favoriteService.getFavoriteList(userId, page, limit);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取收藏列表失败');
    }
  }

  // 批量检查收藏状态
  async batchCheckFavorited(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { product_ids } = ctx.request.body;
      if (!Array.isArray(product_ids)) {
        return this.sendResponse(ctx, null, '商品ID列表格式错误', 400);
      }

      const result = await favoriteService.batchCheckFavorited(userId, product_ids);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '检查收藏状态失败');
    }
  }

  // 清空收藏夹
  async clearFavorites(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const result = await favoriteService.clearFavorites(userId);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '清空收藏夹失败');
    }
  }

  // === 浏览历史功能 ===

  // 记录浏览历史
  async recordBrowse(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { product_id, source, duration, device_info } = ctx.request.body;
      if (!product_id) {
        return this.sendResponse(ctx, null, '请提供商品ID', 400);
      }

      const result = await browseHistoryService.recordBrowse(userId, product_id, {
        source, duration, deviceInfo: device_info
      });
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '记录浏览历史失败');
    }
  }

  // 获取浏览历史
  async getBrowseHistory(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { page = 1, limit = 20 } = ctx.query;
      const result = await browseHistoryService.getBrowseHistory(userId, page, limit);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取浏览历史失败');
    }
  }

  // 获取最近浏览
  async getRecentBrowsed(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { limit = 10 } = ctx.query;
      const result = await browseHistoryService.getRecentBrowsed(userId, limit);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取最近浏览失败');
    }
  }

  // 清空浏览历史
  async clearBrowseHistory(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const result = await browseHistoryService.clearBrowseHistory(userId);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '清空浏览历史失败');
    }
  }

  // 基于浏览历史推荐
  async getRecommendedProducts(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { limit = 10 } = ctx.query;
      const result = await browseHistoryService.getRecommendedProducts(userId, limit);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取推荐商品失败');
    }
  }

  // === 商品对比功能 ===

  // 添加到对比
  async addToCompare(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { product_id, compare_group = 'default' } = ctx.request.body;
      if (!product_id) {
        return this.sendResponse(ctx, null, '请提供商品ID', 400);
      }

      const result = await productCompareService.addToCompare(userId, product_id, compare_group);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '添加对比失败');
    }
  }

  // 从对比中移除
  async removeFromCompare(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { product_id } = ctx.params;
      const { compare_group = 'default' } = ctx.query;
      const result = await productCompareService.removeFromCompare(userId, parseInt(product_id), compare_group);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '移除对比失败');
    }
  }

  // 获取对比列表
  async getCompareList(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { compare_group = 'default' } = ctx.query;
      const result = await productCompareService.getCompareList(userId, compare_group);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取对比列表失败');
    }
  }

  // 获取对比详情
  async getCompareDetails(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { compare_group = 'default' } = ctx.query;
      const result = await productCompareService.getCompareDetails(userId, compare_group);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取对比详情失败');
    }
  }

  // 清空对比列表
  async clearCompareList(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { compare_group = 'default' } = ctx.query;
      const result = await productCompareService.clearCompareList(userId, compare_group);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '清空对比列表失败');
    }
  }

  // === 分享功能 ===

  // 生成分享数据
  async generateShareData(ctx) {
    try {
      const { share_type, target_id } = ctx.query;
      if (!share_type) {
        return this.sendResponse(ctx, null, '请提供分享类型', 400);
      }

      let result;
      if (share_type === 'product' && target_id) {
        result = await shareService.generateProductShareData(target_id);
      } else {
        result = {
          shareType: 'page',
          shareTitle: '心洁茶叶商城',
          shareDesc: '优质茶叶，品味生活',
          shareImage: '/static/images/logo.png',
          shareUrl: process.env.FRONTEND_URL || 'https://your-domain.com'
        };
      }

      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '生成分享数据失败');
    }
  }

  // 记录分享
  async recordShare(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { share_type, target_id, share_platform, extra_data } = ctx.request.body;
      if (!share_type || !share_platform) {
        return this.sendResponse(ctx, null, '请提供分享类型和平台', 400);
      }

      const result = await shareService.recordShare(userId, share_type, target_id, share_platform, extra_data);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '记录分享失败');
    }
  }

  // 获取分享记录
  async getShareRecords(ctx) {
    try {
      const userId = this.getUserId(ctx);
      if (!userId) return;

      const { page = 1, limit = 20 } = ctx.query;
      const result = await shareService.getUserShareRecords(userId, page, limit);
      this.sendResponse(ctx, result);
    } catch (error) {
      this.handleError(ctx, error, '获取分享记录失败');
    }
  }
}

module.exports = new UserFeaturesController();
