const RedisUtils = require('../utils/redis');
const config = require('../config');

/**
 * API响应缓存中间件
 * 支持GET请求的缓存，提升API响应速度
 */
class APICacheMiddleware {
  constructor() {
    this.defaultTTL = 300; // 默认5分钟缓存
  }

  /**
   * 生成缓存键
   * @param {string} url - 请求URL
   * @param {Object} query - 查询参数
   * @returns {string} 缓存键
   */
  generateCacheKey(url, query = {}) {
    const queryString = Object.keys(query)
      .sort()
      .map(key => `${key}=${query[key]}`)
      .join('&');
    
    return `api:${url}${queryString ? `?${queryString}` : ''}`;
  }

  /**
   * 缓存中间件工厂函数
   * @param {number} ttl - 缓存时间（秒）
   * @param {Function} keyGenerator - 自定义缓存键生成函数
   * @returns {Function} 中间件函数
   */
  cache(ttl = this.defaultTTL, keyGenerator = null) {
    // 确保ttl是数字
    const cacheTTL = parseInt(ttl) || this.defaultTTL;
    return async (ctx, next) => {
      // 只缓存GET请求
      if (ctx.method !== 'GET') {
        return await next();
      }

      // 生成缓存键
      const cacheKey = keyGenerator 
        ? keyGenerator(ctx)
        : this.generateCacheKey(ctx.path, ctx.query);

      try {
        // 尝试从缓存获取数据
        const cachedData = await RedisUtils.get(cacheKey);
        
        if (cachedData) {
          // 检查缓存数据是否为有效JSON
          let parsedData;
          try {
            parsedData = JSON.parse(cachedData);
          } catch (parseError) {
            console.warn(`缓存数据格式错误，清除缓存键: ${cacheKey}`, parseError.message);
            await RedisUtils.del(cacheKey);
            // 继续正常处理请求
            await next();
            return;
          }
          
          ctx.body = parsedData;
          ctx.set('X-Cache', 'HIT');
          return;
        }

        // 缓存未命中，继续处理请求
        await next();

        // 缓存响应数据
        if (ctx.status === 200 && ctx.body) {
          const cacheData = JSON.stringify(ctx.body);
          await RedisUtils.set(cacheKey, cacheData, cacheTTL);
          ctx.set('X-Cache', 'MISS');
        }
      } catch (error) {
        console.error('API缓存中间件错误:', error);
        // 缓存出错时继续正常处理
        await next();
      }
    };
  }

  /**
   * 清除指定模式的缓存
   * @param {string} pattern - 缓存键模式
   * @returns {Promise<number>} 清除的缓存数量
   */
  async clearCache(pattern) {
    try {
      const keys = await RedisUtils.keys(pattern);
      if (keys.length > 0) {
        await RedisUtils.delPattern(pattern);
      }
      return keys.length;
    } catch (error) {
      console.error('清除缓存错误:', error);
      return 0;
    }
  }

  /**
   * 清除所有API缓存
   * @returns {Promise<number>} 清除的缓存数量
   */
  async clearAllCache() {
    return await this.clearCache('api:*');
  }

  /**
   * 清除特定模块的缓存
   * @param {string} module - 模块名称
   * @returns {Promise<number>} 清除的缓存数量
   */
  async clearModuleCache(module) {
    return await this.clearCache(`api:*/${module}*`);
  }

  /**
   * 获取缓存统计信息
   * @returns {Promise<Object>} 缓存统计信息
   */
  async getCacheStats() {
    try {
      const keys = await RedisUtils.keys('api:*');
      const stats = {
        totalKeys: keys.length,
        modules: {}
      };

      // 按模块统计
      keys.forEach(key => {
        const parts = key.split(':')[1].split('/');
        const module = parts[1] || 'other';
        stats.modules[module] = (stats.modules[module] || 0) + 1;
      });

      return stats;
    } catch (error) {
      console.error('获取缓存统计错误:', error);
      return { totalKeys: 0, modules: {} };
    }
  }
}

module.exports = new APICacheMiddleware(); 