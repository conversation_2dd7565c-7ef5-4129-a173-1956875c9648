const jwt = require('jsonwebtoken');
const config = require('../config');

module.exports = async (ctx, next) => {
  // 跳过不需要认证的路由
  const publicRoutes = [
    '/api/front/user/login',
    '/api/front/user/register',
    '/api/front/auth/silent-login',
    '/api/admin/auth/login',
    '/api/front/banner/list',
    '/api/front/product/list',
    '/api/front/product/hot',
    '/api/front/product/recommend',
    '/api/front/category/list',
    '/api/health'
  ];

  // 跳过静态资源路径
  if (ctx.path.startsWith('/uploads/') ||
      ctx.path.startsWith('/images/') ||
      ctx.path.startsWith('/static/')) {
    return await next();
  }

  if (publicRoutes.includes(ctx.path)) {
    return await next();
  }

  const token = ctx.headers.authorization?.replace('Bearer ', '');
  
  if (!token) {
    ctx.status = 401;
    ctx.body = {
      code: 401,
      message: '未提供认证令牌'
    };
    return;
  }

  try {
    const decoded = jwt.verify(token, config.jwt.secret);
    ctx.state.user = decoded;
    await next();
  } catch (error) {
    ctx.status = 401;
    ctx.body = {
      code: 401,
      message: '认证令牌无效或已过期'
    };
  }
}; 