// 优化版用户行为追踪中间件 - 高效简洁版
const { UserBehavior } = require('../models');

class OptimizedBehaviorTracker {
  
  constructor() {
    this.behaviorQueue = [];
    this.batchSize = 50;
    this.flushInterval = 30000; // 30秒
    this.isProcessing = false;
    
    // 启动批量处理
    this.startBatchProcessor();
  }

  // 启动批量处理器
  startBatchProcessor() {
    setInterval(() => {
      if (this.behaviorQueue.length > 0) {
        this.flushBehaviors();
      }
    }, this.flushInterval);
  }

  // 批量写入行为数据
  async flushBehaviors() {
    if (this.isProcessing || this.behaviorQueue.length === 0) return;
    
    this.isProcessing = true;
    const behaviors = this.behaviorQueue.splice(0, this.batchSize);
    
    try {
      await UserBehavior.bulkCreate(behaviors, { 
        ignoreDuplicates: true,
        validate: false // 跳过验证提高性能
      });
      console.log(`📊 批量写入 ${behaviors.length} 条行为数据`);
    } catch (error) {
      console.error('❌ 批量写入行为数据失败:', error);
      // 失败的数据重新加入队列
      this.behaviorQueue.unshift(...behaviors);
    } finally {
      this.isProcessing = false;
    }
  }

  // 添加行为到队列
  addBehavior(behaviorData) {
    // 数据清洗和标准化
    const cleanData = this.cleanBehaviorData(behaviorData);
    if (cleanData) {
      this.behaviorQueue.push(cleanData);
      
      // 队列满时立即处理
      if (this.behaviorQueue.length >= this.batchSize) {
        setImmediate(() => this.flushBehaviors());
      }
    }
  }

  // 数据清洗
  cleanBehaviorData(data) {
    try {
      return {
        user_id: data.userId || null,
        openid: data.openid || null,
        behavior_type: data.behaviorType,
        target_type: data.targetType,
        target_id: data.targetId || null,
        page_path: data.pagePath ? data.pagePath.substring(0, 255) : null,
        search_keyword: data.searchKeyword ? data.searchKeyword.substring(0, 100) : null,
        session_id: data.sessionId || null,
        ip_address: data.ipAddress || null,
        user_agent: data.userAgent ? data.userAgent.substring(0, 500) : null,
        extra_data: data.extraData ? JSON.stringify(data.extraData) : null,
        created_at: new Date()
      };
    } catch (error) {
      console.error('数据清洗失败:', error);
      return null;
    }
  }

  // 通用行为追踪中间件
  static track(options = {}) {
    const {
      behaviorType = 'view',
      targetType = 'page',
      extractTargetId = null,
      condition = () => true
    } = options;

    return async (ctx, next) => {
      await next();
      
      // 检查条件
      if (!condition(ctx) || ctx.status !== 200) return;
      
      try {
        const targetId = extractTargetId ? extractTargetId(ctx) : null;
        
        const behaviorData = {
          userId: ctx.state.user?.id,
          openid: ctx.state.user?.openid,
          behaviorType,
          targetType,
          targetId,
          pagePath: ctx.path,
          searchKeyword: ctx.query.keyword || ctx.query.q || null,
          sessionId: ctx.headers['x-session-id'] || ctx.headers['x-request-id'],
          ipAddress: ctx.ip,
          userAgent: ctx.headers['user-agent'],
          extraData: {
            method: ctx.method,
            query: Object.keys(ctx.query).length > 0 ? ctx.query : null,
            referer: ctx.headers.referer
          }
        };

        // 异步添加到队列
        setImmediate(() => {
          tracker.addBehavior(behaviorData);
        });

      } catch (error) {
        console.error('行为追踪失败:', error);
      }
    };
  }

  // 商品浏览追踪
  static trackProductView() {
    return this.track({
      behaviorType: 'view',
      targetType: 'product',
      extractTargetId: (ctx) => {
        const match = ctx.path.match(/\/product\/(\d+)/);
        return match ? parseInt(match[1]) : null;
      },
      condition: (ctx) => ctx.method === 'GET' && ctx.path.includes('/product/')
    });
  }

  // 搜索行为追踪
  static trackSearch() {
    return this.track({
      behaviorType: 'search',
      targetType: 'product',
      condition: (ctx) => ctx.method === 'GET' && 
        (ctx.path.includes('/search') || ctx.query.keyword || ctx.query.q)
    });
  }

  // 购物车行为追踪
  static trackCartAction() {
    return this.track({
      behaviorType: 'add_cart',
      targetType: 'product',
      extractTargetId: (ctx) => {
        const productId = ctx.request.body?.product_id || ctx.request.body?.productId;
        return productId ? parseInt(productId) : null;
      },
      condition: (ctx) => ctx.method === 'POST' && ctx.path.includes('/cart')
    });
  }

  // 订单行为追踪
  static trackOrderAction() {
    return this.track({
      behaviorType: 'order',
      targetType: 'order',
      extractTargetId: (ctx) => {
        const orderId = ctx.body?.data?.id || ctx.body?.data?.order_id;
        return orderId ? parseInt(orderId) : null;
      },
      condition: (ctx) => ctx.method === 'POST' && ctx.path.includes('/order')
    });
  }

  // 支付行为追踪
  static trackPaymentAction() {
    return this.track({
      behaviorType: 'pay',
      targetType: 'order',
      extractTargetId: (ctx) => {
        const orderId = ctx.request.body?.orderId || ctx.request.body?.order_id;
        return orderId ? parseInt(orderId) : null;
      },
      condition: (ctx) => ctx.method === 'POST' && ctx.path.includes('/payment')
    });
  }

  // 页面访问追踪（轻量级）
  static trackPageView() {
    return this.track({
      behaviorType: 'view',
      targetType: 'page',
      condition: (ctx) => ctx.method === 'GET' && 
        !ctx.path.includes('/static') && 
        !ctx.path.includes('/uploads') &&
        !ctx.path.includes('/api/health')
    });
  }

  // 获取队列状态
  getQueueStatus() {
    return {
      queueLength: this.behaviorQueue.length,
      isProcessing: this.isProcessing,
      batchSize: this.batchSize,
      flushInterval: this.flushInterval
    };
  }

  // 立即刷新队列
  async forceFlush() {
    await this.flushBehaviors();
  }

  // 清空队列
  clearQueue() {
    this.behaviorQueue.length = 0;
    console.log('🧹 行为追踪队列已清空');
  }
}

// 创建全局实例
const tracker = new OptimizedBehaviorTracker();

// 导出类和实例
module.exports = OptimizedBehaviorTracker;
module.exports.tracker = tracker;
