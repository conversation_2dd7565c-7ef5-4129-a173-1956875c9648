// 用户行为追踪中间件
const userBehaviorService = require('../services/userBehavior');

class BehaviorTracker {
  
  // 记录页面访问行为
  static trackPageView() {
    return async (ctx, next) => {
      await next();
      
      // 只记录GET请求的页面访问
      if (ctx.method === 'GET' && ctx.status === 200) {
        try {
          const behaviorData = {
            userId: ctx.state.user?.id || null,
            openid: ctx.state.user?.openid || null,
            behaviorType: 'view',
            targetType: 'page',
            targetId: null,
            pagePath: ctx.path,
            sessionId: ctx.headers['x-session-id'] || null,
            ipAddress: ctx.ip,
            userAgent: ctx.headers['user-agent'],
            extraData: {
              query: ctx.query,
              referer: ctx.headers.referer
            }
          };

          // 异步记录，不影响响应
          setImmediate(() => {
            userBehaviorService.recordBehavior(behaviorData);
          });
        } catch (error) {
          console.error('记录页面访问行为失败:', error);
        }
      }
    };
  }

  // 记录商品浏览行为
  static trackProductView() {
    return async (ctx, next) => {
      await next();
      
      if (ctx.method === 'GET' && ctx.status === 200 && ctx.path.includes('/product/')) {
        try {
          // 从路径中提取商品ID
          const productIdMatch = ctx.path.match(/\/product\/(\d+)/);
          const productId = productIdMatch ? parseInt(productIdMatch[1]) : null;

          if (productId) {
            const behaviorData = {
              userId: ctx.state.user?.id || null,
              openid: ctx.state.user?.openid || null,
              behaviorType: 'view',
              targetType: 'product',
              targetId: productId,
              pagePath: ctx.path,
              sessionId: ctx.headers['x-session-id'] || null,
              ipAddress: ctx.ip,
              userAgent: ctx.headers['user-agent'],
              extraData: {
                referer: ctx.headers.referer
              }
            };

            setImmediate(() => {
              userBehaviorService.recordBehavior(behaviorData);
            });
          }
        } catch (error) {
          console.error('记录商品浏览行为失败:', error);
        }
      }
    };
  }

  // 记录搜索行为
  static trackSearch() {
    return async (ctx, next) => {
      await next();
      
      if (ctx.method === 'GET' && ctx.status === 200 && ctx.path.includes('/search')) {
        try {
          const keyword = ctx.query.keyword || ctx.query.q;
          
          if (keyword) {
            const behaviorData = {
              userId: ctx.state.user?.id || null,
              openid: ctx.state.user?.openid || null,
              behaviorType: 'search',
              targetType: 'product',
              targetId: null,
              pagePath: ctx.path,
              searchKeyword: keyword,
              sessionId: ctx.headers['x-session-id'] || null,
              ipAddress: ctx.ip,
              userAgent: ctx.headers['user-agent'],
              extraData: {
                query: ctx.query
              }
            };

            setImmediate(() => {
              userBehaviorService.recordBehavior(behaviorData);
            });
          }
        } catch (error) {
          console.error('记录搜索行为失败:', error);
        }
      }
    };
  }

  // 记录购物车操作行为
  static trackCartAction() {
    return async (ctx, next) => {
      await next();
      
      if (ctx.method === 'POST' && ctx.status === 200 && ctx.path.includes('/cart')) {
        try {
          const productId = ctx.request.body?.product_id || ctx.request.body?.productId;
          
          if (productId) {
            const behaviorData = {
              userId: ctx.state.user?.id || null,
              openid: ctx.state.user?.openid || null,
              behaviorType: 'add_cart',
              targetType: 'product',
              targetId: parseInt(productId),
              pagePath: ctx.path,
              sessionId: ctx.headers['x-session-id'] || null,
              ipAddress: ctx.ip,
              userAgent: ctx.headers['user-agent'],
              extraData: {
                quantity: ctx.request.body?.quantity || 1
              }
            };

            setImmediate(() => {
              userBehaviorService.recordBehavior(behaviorData);
            });
          }
        } catch (error) {
          console.error('记录购物车行为失败:', error);
        }
      }
    };
  }

  // 记录订单行为
  static trackOrderAction() {
    return async (ctx, next) => {
      await next();
      
      if (ctx.method === 'POST' && ctx.status === 200 && ctx.path.includes('/order')) {
        try {
          const orderId = ctx.body?.data?.id || ctx.body?.data?.order_id;
          
          if (orderId) {
            const behaviorData = {
              userId: ctx.state.user?.id || null,
              openid: ctx.state.user?.openid || null,
              behaviorType: 'order',
              targetType: 'order',
              targetId: parseInt(orderId),
              pagePath: ctx.path,
              sessionId: ctx.headers['x-session-id'] || null,
              ipAddress: ctx.ip,
              userAgent: ctx.headers['user-agent'],
              extraData: {
                orderAmount: ctx.request.body?.total_amount
              }
            };

            setImmediate(() => {
              userBehaviorService.recordBehavior(behaviorData);
            });
          }
        } catch (error) {
          console.error('记录订单行为失败:', error);
        }
      }
    };
  }

  // 记录支付行为
  static trackPaymentAction() {
    return async (ctx, next) => {
      await next();
      
      if (ctx.method === 'POST' && ctx.status === 200 && ctx.path.includes('/payment')) {
        try {
          const orderId = ctx.request.body?.orderId || ctx.request.body?.order_id;
          
          if (orderId) {
            const behaviorData = {
              userId: ctx.state.user?.id || null,
              openid: ctx.state.user?.openid || null,
              behaviorType: 'pay',
              targetType: 'order',
              targetId: parseInt(orderId),
              pagePath: ctx.path,
              sessionId: ctx.headers['x-session-id'] || null,
              ipAddress: ctx.ip,
              userAgent: ctx.headers['user-agent'],
              extraData: {
                paymentMethod: ctx.request.body?.paymentMethod
              }
            };

            setImmediate(() => {
              userBehaviorService.recordBehavior(behaviorData);
            });
          }
        } catch (error) {
          console.error('记录支付行为失败:', error);
        }
      }
    };
  }

  // 通用行为记录方法
  static recordBehavior(behaviorType, targetType = 'page') {
    return async (ctx, next) => {
      await next();
      
      if (ctx.status === 200) {
        try {
          const behaviorData = {
            userId: ctx.state.user?.id || null,
            openid: ctx.state.user?.openid || null,
            behaviorType,
            targetType,
            targetId: null,
            pagePath: ctx.path,
            sessionId: ctx.headers['x-session-id'] || null,
            ipAddress: ctx.ip,
            userAgent: ctx.headers['user-agent'],
            extraData: {
              method: ctx.method,
              query: ctx.query,
              body: ctx.request.body
            }
          };

          setImmediate(() => {
            userBehaviorService.recordBehavior(behaviorData);
          });
        } catch (error) {
          console.error(`记录${behaviorType}行为失败:`, error);
        }
      }
    };
  }
}

module.exports = BehaviorTracker;
