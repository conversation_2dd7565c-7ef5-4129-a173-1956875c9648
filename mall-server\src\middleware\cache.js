const Redis = require('ioredis');
const config = require('../config');

const redis = new Redis(config.redis);

module.exports = (duration = 300) => {
  // 确保duration是数字
  const cacheDuration = parseInt(duration) || 300;
  return async (ctx, next) => {
    // 只对GET请求进行缓存
    if (ctx.method !== 'GET') {
      return await next();
    }

    const key = `cache:${ctx.url}`;
    
    try {
      const cached = await redis.get(key);
      
      if (cached) {
        ctx.body = JSON.parse(cached);
        return;
      }

      await next();

      // 缓存响应
      if (ctx.status === 200) {
        await redis.set(key, JSON.stringify(ctx.body), 'EX', cacheDuration);
      }
    } catch (error) {
      // Redis连接失败时跳过缓存
      await next();
    }
  };
}; 