const config = require('../config');

module.exports = async (ctx, next) => {
  try {
    await next();
  } catch (err) {
    ctx.status = err.status || 500;
    
    // 记录错误日志
    console.error('Error:', err);
    
    // 根据环境返回不同的错误信息
    const errorResponse = {
      code: ctx.status,
      message: err.message || '服务器内部错误'
    };

    if (config.env === 'development') {
      errorResponse.stack = err.stack;
      errorResponse.details = err;
    }

    ctx.body = errorResponse;
    
    // 触发错误事件
    ctx.app.emit('error', err, ctx);
  }
}; 