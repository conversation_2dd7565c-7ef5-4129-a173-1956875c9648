const auth = require('./auth');
const error = require('./error');
const logger = require('./logger');
const rateLimit = require('./rateLimit');
const validate = require('./validate');
const upload = require('./upload');
const cache = require('./cache');
const apiCache = require('./apiCache');

module.exports = [
  // 错误处理中间件（必须放在最前面）
  error,
  
  // 日志中间件
  logger,
  
  // 限流中间件
  rateLimit,
  
  // 缓存中间件
  cache,
  
  // 参数验证中间件
  validate,
  
  // 文件上传中间件
  upload,
  
  // 认证中间件（可选，根据路由需要）
  auth
]; 

// 导出单独的中间件供路由使用
module.exports.apiCache = apiCache; 