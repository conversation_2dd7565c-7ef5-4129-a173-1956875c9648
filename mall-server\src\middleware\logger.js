const winston = require('winston');
const config = require('../config');

// 创建日志记录器
const logger = winston.createLogger({
  level: config.log.level,
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ 
      filename: `${config.log.path}/error.log`, 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: `${config.log.path}/combined.log` 
    })
  ]
});

if (config.env !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}

module.exports = async (ctx, next) => {
  const start = Date.now();
  
  await next();
  
  const ms = Date.now() - start;
  const logLevel = ctx.status >= 400 ? 'warn' : 'info';
  
  logger.log(logLevel, {
    method: ctx.method,
    url: ctx.url,
    status: ctx.status,
    responseTime: `${ms}ms`,
    userAgent: ctx.headers['user-agent'],
    ip: ctx.ip
  });
}; 