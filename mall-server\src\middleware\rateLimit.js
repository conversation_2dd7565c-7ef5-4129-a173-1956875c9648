const Redis = require('ioredis');
const config = require('../config');

const redis = new Redis(config.redis);

module.exports = async (ctx, next) => {
  const key = `rate_limit:${ctx.ip}`;
  const limit = 100; // 每分钟100次请求
  const window = 60; // 60秒窗口

  try {
    const current = await redis.incr(key);
    
    if (current === 1) {
      await redis.expire(key, window);
    }

    if (current > limit) {
      ctx.status = 429;
      ctx.body = {
        code: 429,
        message: '请求过于频繁，请稍后再试'
      };
      return;
    }

    ctx.set('X-RateLimit-Limit', limit);
    ctx.set('X-RateLimit-Remaining', Math.max(0, limit - current));
    
    await next();
  } catch (error) {
    // Redis连接失败时跳过限流
    await next();
  }
}; 