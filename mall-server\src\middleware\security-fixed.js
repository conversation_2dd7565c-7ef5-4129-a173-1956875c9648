// 修复版安全防护中间件
const validator = require('validator');

class SecurityMiddleware {
  // 1. 改进的SQL注入防护（减少误报）
  static sqlInjectionProtection() {
    return async (ctx, next) => {
      const { body, query } = ctx.request;
      
      // 检查请求体和查询参数
      const checkData = { ...body, ...query };
      
      for (const [key, value] of Object.entries(checkData)) {
        if (typeof value === 'string' && value.length > 0) {
          // 更精确的SQL注入检测模式
          const dangerousPatterns = [
            // 明显的SQL注入模式
            /(\bUNION\b.*\bSELECT\b)/i,
            /(\bDROP\b.*\bTABLE\b)/i,
            /(\bDELETE\b.*\bFROM\b)/i,
            /(\bINSERT\b.*\bINTO\b)/i,
            /(\bUPDATE\b.*\bSET\b)/i,
            // SQL注释
            /(--\s|\/\*|\*\/)/,
            // 危险的函数调用
            /(\bEXEC\b|\bEVAL\b)/i,
            // 明显的注入尝试
            /('.*OR.*'.*=.*'|".*OR.*".*=.*")/i,
            /('.*AND.*'.*=.*'|".*AND.*".*=.*")/i
          ];
          
          for (const pattern of dangerousPatterns) {
            if (pattern.test(value)) {
              console.warn(`检测到可能的SQL注入攻击: ${key} = ${value.substring(0, 100)}`);
              ctx.status = 400;
              ctx.body = {
                code: 400,
                message: '请求参数包含非法字符'
              };
              return;
            }
          }
        }
      }
      
      await next();
    };
  }

  // 2. 改进的XSS防护
  static xssProtection() {
    return async (ctx, next) => {
      // 设置XSS防护头
      ctx.set('X-XSS-Protection', '1; mode=block');
      ctx.set('X-Content-Type-Options', 'nosniff');
      ctx.set('X-Frame-Options', 'DENY');
      
      // 只对特定字段进行XSS清理，避免影响正常数据
      if (ctx.request.body) {
        ctx.request.body = this.sanitizeXSS(ctx.request.body);
      }
      
      await next();
    };
  }

  // 改进的XSS清理（更智能）
  static sanitizeXSS(obj) {
    if (typeof obj === 'string') {
      // 只清理明显的XSS攻击模式
      return obj
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '');
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeXSS(item));
    }
    
    if (obj && typeof obj === 'object') {
      const sanitized = {};
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = this.sanitizeXSS(value);
      }
      return sanitized;
    }
    
    return obj;
  }

  // 3. 简化的限流（不依赖Redis）
  static createRateLimit(windowMs = 60000, max = 100, message = '请求过于频繁') {
    const requests = new Map();
    
    return async (ctx, next) => {
      const key = ctx.ip;
      const now = Date.now();
      
      // 清理过期记录
      if (requests.has(key)) {
        const userRequests = requests.get(key);
        const validRequests = userRequests.filter(time => now - time < windowMs);
        requests.set(key, validRequests);
      }
      
      // 检查请求频率
      const userRequests = requests.get(key) || [];
      if (userRequests.length >= max) {
        ctx.status = 429;
        ctx.body = {
          code: 429,
          message: message
        };
        return;
      }
      
      // 记录请求
      userRequests.push(now);
      requests.set(key, userRequests);
      
      await next();
    };
  }

  // 4. 基础限流
  static rateLimiting() {
    return this.createRateLimit(60000, 100, '请求过于频繁，请稍后再试');
  }

  // 5. 登录限流
  static loginRateLimiting() {
    return this.createRateLimit(300000, 5, '登录尝试过于频繁，请5分钟后再试');
  }

  // 6. 支付限流
  static paymentRateLimiting() {
    return this.createRateLimit(60000, 10, '支付请求过于频繁，请稍后再试');
  }

  // 7. 输入验证
  static inputValidation() {
    return async (ctx, next) => {
      const { body } = ctx.request;
      
      if (body) {
        // 验证邮箱格式
        if (body.email && !validator.isEmail(body.email)) {
          ctx.status = 400;
          ctx.body = {
            code: 400,
            message: '邮箱格式不正确'
          };
          return;
        }
        
        // 验证手机号格式
        if (body.phone && !validator.isMobilePhone(body.phone, 'zh-CN')) {
          ctx.status = 400;
          ctx.body = {
            code: 400,
            message: '手机号格式不正确'
          };
          return;
        }
        
        // 验证字符串长度
        for (const [key, value] of Object.entries(body)) {
          if (typeof value === 'string' && value.length > 2000) {
            ctx.status = 400;
            ctx.body = {
              code: 400,
              message: `${key}字段长度超出限制`
            };
            return;
          }
        }
      }
      
      await next();
    };
  }

  // 8. CSRF防护（简化版）
  static csrfProtection() {
    return async (ctx, next) => {
      // 对于非安全方法，检查来源
      if (!['GET', 'HEAD', 'OPTIONS'].includes(ctx.method)) {
        const referer = ctx.get('Referer');
        const origin = ctx.get('Origin');
        const host = ctx.get('Host');
        
        // 开发环境跳过检查
        if (process.env.NODE_ENV === 'development') {
          await next();
          return;
        }
        
        if (!referer && !origin) {
          ctx.status = 403;
          ctx.body = {
            code: 403,
            message: '请求来源验证失败'
          };
          return;
        }
      }
      
      await next();
    };
  }

  // 9. 请求大小限制
  static requestSizeLimit(maxSizeInMB = 10) {
    return async (ctx, next) => {
      const contentLength = ctx.get('Content-Length');
      
      if (contentLength) {
        const sizeInBytes = parseInt(contentLength);
        const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
        
        if (sizeInBytes > maxSizeInBytes) {
          ctx.status = 413;
          ctx.body = {
            code: 413,
            message: '请求体过大'
          };
          return;
        }
      }
      
      await next();
    };
  }

  // 10. 安全头设置
  static securityHeaders() {
    return async (ctx, next) => {
      // 设置安全响应头
      ctx.set('X-Content-Type-Options', 'nosniff');
      ctx.set('X-Frame-Options', 'DENY');
      ctx.set('X-XSS-Protection', '1; mode=block');
      
      // 只在HTTPS环境下设置HSTS
      if (ctx.secure) {
        ctx.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
      }
      
      await next();
    };
  }
}

module.exports = SecurityMiddleware;
