// 安全防护中间件
const rateLimit = require('koa-ratelimit');
const Redis = require('ioredis');
const validator = require('validator');

// Redis连接（用于限流存储）
const redis = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || '',
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true
});

class SecurityMiddleware {
  // 1. SQL注入防护
  static sqlInjectionProtection() {
    return async (ctx, next) => {
      const { body, query } = ctx.request;
      
      // 检查请求体和查询参数
      const checkData = { ...body, ...query };
      
      for (const [key, value] of Object.entries(checkData)) {
        if (typeof value === 'string') {
          // 检测常见的SQL注入模式
          const sqlPatterns = [
            /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
            /(--|#|\/\*|\*\/)/,
            /(\b(OR|AND)\b.*=.*)/i,
            /([\'\";])/
          ];
          
          for (const pattern of sqlPatterns) {
            if (pattern.test(value)) {
              console.warn(`检测到可能的SQL注入攻击: ${key} = ${value}`);
              ctx.status = 400;
              ctx.body = {
                code: 400,
                message: '请求参数包含非法字符'
              };
              return;
            }
          }
        }
      }
      
      await next();
    };
  }

  // 2. XSS防护
  static xssProtection() {
    return async (ctx, next) => {
      // 设置XSS防护头
      ctx.set('X-XSS-Protection', '1; mode=block');
      ctx.set('X-Content-Type-Options', 'nosniff');
      ctx.set('X-Frame-Options', 'DENY');
      
      // 清理输入数据
      if (ctx.request.body) {
        ctx.request.body = this.sanitizeObject(ctx.request.body);
      }
      
      if (ctx.query) {
        ctx.query = this.sanitizeObject(ctx.query);
      }
      
      await next();
    };
  }

  // HTML转义函数
  static escapeHtml(text) {
    if (typeof text !== 'string') return text;
    
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;')
      .replace(/\//g, '&#x2F;');
  }

  // 清理对象中的所有字符串
  static sanitizeObject(obj) {
    if (typeof obj === 'string') {
      return this.escapeHtml(obj);
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeObject(item));
    }
    
    if (obj && typeof obj === 'object') {
      const sanitized = {};
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = this.sanitizeObject(value);
      }
      return sanitized;
    }
    
    return obj;
  }

  // 3. 接口限流
  static rateLimiting() {
    return rateLimit({
      driver: 'redis',
      db: redis,
      duration: 60000, // 1分钟
      errorMessage: {
        code: 429,
        message: '请求过于频繁，请稍后再试'
      },
      id: (ctx) => ctx.ip,
      headers: {
        remaining: 'Rate-Limit-Remaining',
        reset: 'Rate-Limit-Reset',
        total: 'Rate-Limit-Total'
      },
      max: 100, // 每分钟最多100次请求
      disableHeader: false
    });
  }

  // 4. 严格限流（用于敏感接口）
  static strictRateLimiting() {
    return rateLimit({
      driver: 'redis',
      db: redis,
      duration: 60000, // 1分钟
      errorMessage: {
        code: 429,
        message: '操作过于频繁，请稍后再试'
      },
      id: (ctx) => ctx.ip,
      max: 10, // 每分钟最多10次请求
      disableHeader: false
    });
  }

  // 5. 登录限流
  static loginRateLimiting() {
    return rateLimit({
      driver: 'redis',
      db: redis,
      duration: 300000, // 5分钟
      errorMessage: {
        code: 429,
        message: '登录尝试过于频繁，请5分钟后再试'
      },
      id: (ctx) => ctx.ip,
      max: 5, // 5分钟内最多5次登录尝试
      disableHeader: false
    });
  }

  // 6. 输入验证
  static inputValidation() {
    return async (ctx, next) => {
      const { body } = ctx.request;
      
      if (body) {
        // 验证邮箱格式
        if (body.email && !validator.isEmail(body.email)) {
          ctx.status = 400;
          ctx.body = {
            code: 400,
            message: '邮箱格式不正确'
          };
          return;
        }
        
        // 验证手机号格式
        if (body.phone && !validator.isMobilePhone(body.phone, 'zh-CN')) {
          ctx.status = 400;
          ctx.body = {
            code: 400,
            message: '手机号格式不正确'
          };
          return;
        }
        
        // 验证URL格式
        if (body.url && !validator.isURL(body.url)) {
          ctx.status = 400;
          ctx.body = {
            code: 400,
            message: 'URL格式不正确'
          };
          return;
        }
        
        // 验证字符串长度
        for (const [key, value] of Object.entries(body)) {
          if (typeof value === 'string' && value.length > 1000) {
            ctx.status = 400;
            ctx.body = {
              code: 400,
              message: `${key}字段长度超出限制`
            };
            return;
          }
        }
      }
      
      await next();
    };
  }

  // 7. CSRF防护
  static csrfProtection() {
    return async (ctx, next) => {
      // 对于非GET请求，检查Referer头
      if (!['GET', 'HEAD', 'OPTIONS'].includes(ctx.method)) {
        const referer = ctx.get('Referer');
        const origin = ctx.get('Origin');
        const host = ctx.get('Host');
        
        if (!referer && !origin) {
          ctx.status = 403;
          ctx.body = {
            code: 403,
            message: '请求来源验证失败'
          };
          return;
        }
        
        // 验证请求来源
        const allowedOrigins = [
          `https://${host}`,
          `http://${host}`,
          'https://localhost:3000', // 开发环境
          'http://localhost:3000'
        ];
        
        const requestOrigin = origin || new URL(referer).origin;
        if (!allowedOrigins.includes(requestOrigin)) {
          console.warn(`可疑的跨站请求: ${requestOrigin}`);
          ctx.status = 403;
          ctx.body = {
            code: 403,
            message: '请求来源不被允许'
          };
          return;
        }
      }
      
      await next();
    };
  }

  // 8. IP白名单（用于管理接口）
  static ipWhitelist(allowedIPs = []) {
    return async (ctx, next) => {
      const clientIP = ctx.ip;
      
      // 开发环境跳过IP检查
      if (process.env.NODE_ENV === 'development') {
        await next();
        return;
      }
      
      if (allowedIPs.length > 0 && !allowedIPs.includes(clientIP)) {
        console.warn(`未授权的IP访问: ${clientIP}`);
        ctx.status = 403;
        ctx.body = {
          code: 403,
          message: '访问被拒绝'
        };
        return;
      }
      
      await next();
    };
  }

  // 9. 请求大小限制
  static requestSizeLimit(maxSize = '10mb') {
    return async (ctx, next) => {
      const contentLength = ctx.get('Content-Length');
      
      if (contentLength) {
        const sizeInBytes = parseInt(contentLength);
        const maxSizeInBytes = this.parseSize(maxSize);
        
        if (sizeInBytes > maxSizeInBytes) {
          ctx.status = 413;
          ctx.body = {
            code: 413,
            message: '请求体过大'
          };
          return;
        }
      }
      
      await next();
    };
  }

  // 解析大小字符串
  static parseSize(size) {
    const units = {
      'b': 1,
      'kb': 1024,
      'mb': 1024 * 1024,
      'gb': 1024 * 1024 * 1024
    };
    
    const match = size.toLowerCase().match(/^(\d+(?:\.\d+)?)\s*(b|kb|mb|gb)?$/);
    if (!match) return 0;
    
    const value = parseFloat(match[1]);
    const unit = match[2] || 'b';
    
    return Math.floor(value * units[unit]);
  }
}

module.exports = SecurityMiddleware;
