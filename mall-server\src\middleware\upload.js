const multer = require('@koa/multer');
const path = require('path');
const fs = require('fs-extra');
const config = require('../config');

// 确保上传目录存在
fs.ensureDirSync(config.upload.path);
fs.ensureDirSync(config.upload.tempDir);
fs.ensureDirSync(config.upload.productDir);
fs.ensureDirSync(config.upload.bannerDir);
fs.ensureDirSync(config.upload.categoryDir);
fs.ensureDirSync(config.upload.avatarDir);

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let uploadPath = config.upload.tempDir;
    
    // 根据文件类型选择上传目录
    if (file.fieldname === 'product') {
      uploadPath = config.upload.productDir;
    } else if (file.fieldname === 'banner') {
      uploadPath = config.upload.bannerDir;
    } else if (file.fieldname === 'category') {
      uploadPath = config.upload.categoryDir;
    } else if (file.fieldname === 'avatar') {
      uploadPath = config.upload.avatarDir;
    }
    
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

const fileFilter = (req, file, cb) => {
  if (config.upload.allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: config.upload.maxFileSize
  }
});

module.exports = upload; 