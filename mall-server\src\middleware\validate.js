const Joi = require('joi');

module.exports = (schema) => {
  return async (ctx, next) => {
    if (!schema) {
      return await next();
    }

    const data = {
      body: ctx.request.body,
      query: ctx.query,
      params: ctx.params
    };

    try {
      await schema.validateAsync(data);
      await next();
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: '参数验证失败',
        details: error.details
      };
    }
  };
}; 