// 地址管理模型
const { sequelize } = require('../config/sequelize');

class AddressModel {
  /**
   * 获取用户地址列表
   */
  static async getUserAddresses(userId) {
    try {
      const [addresses] = await sequelize.query(`
        SELECT 
          id,
          receiver_name,
          receiver_phone,
          province,
          city,
          district,
          detail_address,
          postal_code,
          address_tag,
          is_default,
          longitude,
          latitude,
          created_at,
          updated_at
        FROM user_addresses 
        WHERE user_id = ? AND status = 1 
        ORDER BY is_default DESC, created_at DESC
      `, [userId]);
      
      return addresses;
    } catch (error) {
      console.error('获取用户地址列表失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取用户默认地址
   */
  static async getDefaultAddress(userId) {
    try {
      const [addresses] = await sequelize.query(`
        SELECT 
          id,
          receiver_name,
          receiver_phone,
          province,
          city,
          district,
          detail_address,
          postal_code,
          address_tag,
          is_default,
          longitude,
          latitude
        FROM user_addresses 
        WHERE user_id = ? AND is_default = 1 AND status = 1 
        LIMIT 1
      `, [userId]);
      
      return addresses[0] || null;
    } catch (error) {
      console.error('获取默认地址失败:', error);
      throw error;
    }
  }
  
  /**
   * 根据ID获取地址详情
   */
  static async getAddressById(addressId, userId) {
    try {
      const [addresses] = await sequelize.query(`
        SELECT 
          id,
          receiver_name,
          receiver_phone,
          province,
          city,
          district,
          detail_address,
          postal_code,
          address_tag,
          is_default,
          longitude,
          latitude
        FROM user_addresses 
        WHERE id = ? AND user_id = ? AND status = 1
      `, [addressId, userId]);
      
      return addresses[0] || null;
    } catch (error) {
      console.error('获取地址详情失败:', error);
      throw error;
    }
  }
  
  /**
   * 创建新地址
   */
  static async createAddress(userId, addressData) {
    try {
      const {
        receiver_name,
        receiver_phone,
        province,
        city,
        district,
        detail_address,
        postal_code = '',
        address_tag = '家',
        is_default = 0,
        longitude = null,
        latitude = null
      } = addressData;
      
      // 如果设置为默认地址，先取消其他默认地址
      if (is_default) {
        await this.clearDefaultAddress(userId);
      }
      
      const [result] = await sequelize.query(`
        INSERT INTO user_addresses (
          user_id, receiver_name, receiver_phone, province, city, district,
          detail_address, postal_code, address_tag, is_default, longitude, latitude
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        userId, receiver_name, receiver_phone, province, city, district,
        detail_address, postal_code, address_tag, is_default, longitude, latitude
      ]);
      
      return result.insertId;
    } catch (error) {
      console.error('创建地址失败:', error);
      throw error;
    }
  }
  
  /**
   * 更新地址
   */
  static async updateAddress(addressId, userId, addressData) {
    try {
      const {
        receiver_name,
        receiver_phone,
        province,
        city,
        district,
        detail_address,
        postal_code = '',
        address_tag = '家',
        is_default = 0,
        longitude = null,
        latitude = null
      } = addressData;
      
      // 如果设置为默认地址，先取消其他默认地址
      if (is_default) {
        await this.clearDefaultAddress(userId);
      }
      
      const [result] = await sequelize.query(`
        UPDATE user_addresses SET
          receiver_name = ?,
          receiver_phone = ?,
          province = ?,
          city = ?,
          district = ?,
          detail_address = ?,
          postal_code = ?,
          address_tag = ?,
          is_default = ?,
          longitude = ?,
          latitude = ?,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ? AND user_id = ? AND status = 1
      `, [
        receiver_name, receiver_phone, province, city, district,
        detail_address, postal_code, address_tag, is_default, longitude, latitude,
        addressId, userId
      ]);
      
      return result.affectedRows > 0;
    } catch (error) {
      console.error('更新地址失败:', error);
      throw error;
    }
  }
  
  /**
   * 删除地址
   */
  static async deleteAddress(addressId, userId) {
    try {
      const [result] = await sequelize.query(`
        UPDATE user_addresses SET status = 0, updated_at = CURRENT_TIMESTAMP
        WHERE id = ? AND user_id = ?
      `, [addressId, userId]);
      
      return result.affectedRows > 0;
    } catch (error) {
      console.error('删除地址失败:', error);
      throw error;
    }
  }
  
  /**
   * 设置默认地址
   */
  static async setDefaultAddress(addressId, userId) {
    try {
      // 先取消所有默认地址
      await this.clearDefaultAddress(userId);
      
      // 设置新的默认地址
      const [result] = await sequelize.query(`
        UPDATE user_addresses SET is_default = 1, updated_at = CURRENT_TIMESTAMP
        WHERE id = ? AND user_id = ? AND status = 1
      `, [addressId, userId]);
      
      return result.affectedRows > 0;
    } catch (error) {
      console.error('设置默认地址失败:', error);
      throw error;
    }
  }
  
  /**
   * 清除用户的所有默认地址
   */
  static async clearDefaultAddress(userId) {
    try {
      await sequelize.query(`
        UPDATE user_addresses SET is_default = 0
        WHERE user_id = ? AND status = 1
      `, [userId]);
    } catch (error) {
      console.error('清除默认地址失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取省市区数据
   */
  static async getRegions(parentId = 0) {
    try {
      const [regions] = await sequelize.query(`
        SELECT id, name, code, level
        FROM regions 
        WHERE parent_id = ? AND status = 1 
        ORDER BY sort_order ASC, id ASC
      `, [parentId]);
      
      return regions;
    } catch (error) {
      console.error('获取省市区数据失败:', error);
      throw error;
    }
  }
  
  /**
   * 根据名称搜索地区
   */
  static async searchRegions(keyword) {
    try {
      const [regions] = await sequelize.query(`
        SELECT id, name, code, level, parent_id
        FROM regions 
        WHERE name LIKE ? AND status = 1 
        ORDER BY level ASC, sort_order ASC
        LIMIT 20
      `, [`%${keyword}%`]);
      
      return regions;
    } catch (error) {
      console.error('搜索地区失败:', error);
      throw error;
    }
  }
}

module.exports = AddressModel;
