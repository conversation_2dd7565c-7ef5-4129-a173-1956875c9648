// 购物车数据模型
const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Cart = sequelize.define('Cart', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true,
    comment: '购物车ID'
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '用户ID'
  },
  product_id: {
    type: DataTypes.BIGINT,
    allowNull: false,
    comment: '商品ID'
  },
  sku_id: {
    type: DataTypes.BIGINT,
    allowNull: true,
    comment: 'SKU ID'
  },
  quantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    comment: '商品数量'
  },
  selected: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '是否选中'
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: '加入购物车时的价格'
  },
  specifications: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '商品规格信息'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '更新时间'
  }
}, {
  tableName: 'cart',
  timestamps: false,
  indexes: [
    {
      unique: true,
      fields: ['user_id', 'product_id', 'sku_id']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['product_id']
    },
    {
      fields: ['created_at']
    }
  ]
});

// 定义关联关系
Cart.associate = function(models) {
  // 购物车属于用户
  Cart.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user'
  });
  
  // 购物车包含商品
  Cart.belongsTo(models.Product, {
    foreignKey: 'product_id',
    as: 'product'
  });
};

module.exports = Cart;
