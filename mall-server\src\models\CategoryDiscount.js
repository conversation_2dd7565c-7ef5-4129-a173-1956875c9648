const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const CategoryDiscount = sequelize.define('CategoryDiscount', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true,
    comment: '关联ID'
  },
  discountId: {
    type: DataTypes.BIGINT,
    allowNull: false,
    field: 'discount_id',
    comment: '折扣ID'
  },
  categoryId: {
    type: DataTypes.BIGINT,
    allowNull: false,
    field: 'category_id',
    comment: '分类ID'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'created_at',
    comment: '创建时间'
  }
}, {
  tableName: 'category_discounts',
  timestamps: false,
  indexes: [
    { 
      unique: true,
      fields: ['discount_id', 'category_id'],
      name: 'uk_discount_category'
    },
    { fields: ['discount_id'] },
    { fields: ['category_id'] }
  ]
});

  return CategoryDiscount;
};
