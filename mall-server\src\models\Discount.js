const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Discount = sequelize.define('Discount', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true,
    comment: '折扣ID'
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '折扣名称'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '折扣描述'
  },
  type: {
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 1,
    comment: '折扣类型(1:百分比折扣 2:固定金额折扣 3:满减折扣 4:买N送M)',
    validate: {
      isIn: [[1, 2, 3, 4]]
    }
  },
  value: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: '折扣值(百分比折扣为0-100，固定金额为具体金额)',
    validate: {
      min: 0
    }
  },
  minAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0.00,
    field: 'min_amount',
    comment: '最低消费金额(满减折扣使用)'
  },
  maxDiscount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    field: 'max_discount',
    comment: '最大折扣金额(百分比折扣时限制最大优惠)'
  },
  buyQuantity: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'buy_quantity',
    comment: '购买数量(买N送M使用)'
  },
  getQuantity: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'get_quantity',
    comment: '赠送数量(买N送M使用)'
  },
  startTime: {
    type: DataTypes.DATE,
    allowNull: false,
    field: 'start_time',
    comment: '开始时间'
  },
  endTime: {
    type: DataTypes.DATE,
    allowNull: false,
    field: 'end_time',
    comment: '结束时间'
  },
  usageLimit: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'usage_limit',
    comment: '使用次数限制(NULL为无限制)'
  },
  usedCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    field: 'used_count',
    comment: '已使用次数'
  },
  userLimit: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'user_limit',
    comment: '单用户使用次数限制(NULL为无限制)'
  },
  status: {
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 1,
    comment: '状态(0:禁用 1:启用 2:已过期)',
    validate: {
      isIn: [[0, 1, 2]]
    }
  },
  priority: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '优先级(数字越大优先级越高)'
  },
  stackable: {
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 0,
    comment: '是否可叠加(0:不可叠加 1:可叠加)',
    validate: {
      isIn: [[0, 1]]
    }
  },
  applicableTo: {
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 1,
    field: 'applicable_to',
    comment: '适用范围(1:全部商品 2:指定商品 3:指定分类)',
    validate: {
      isIn: [[1, 2, 3]]
    }
  },
  createdBy: {
    type: DataTypes.BIGINT,
    allowNull: true,
    field: 'created_by',
    comment: '创建人ID'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'created_at',
    comment: '创建时间'
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'updated_at',
    comment: '更新时间'
  }
}, {
  tableName: 'discounts',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    { fields: ['type'] },
    { fields: ['status'] },
    { fields: ['start_time'] },
    { fields: ['end_time'] },
    { fields: ['priority'] },
    { fields: ['applicable_to'] }
  ]
});

// 实例方法
Discount.prototype.isActive = function() {
  const now = new Date();
  return this.status === 1 && 
         this.startTime <= now && 
         this.endTime >= now &&
         (this.usageLimit === null || this.usedCount < this.usageLimit);
};

Discount.prototype.isExpired = function() {
  return new Date() > this.endTime;
};

Discount.prototype.canUse = function(userId = null) {
  if (!this.isActive()) {
    return false;
  }
  
  // 如果有用户限制，需要额外检查用户使用次数
  if (userId && this.userLimit !== null) {
    // 这里需要查询用户使用记录，暂时返回true
    // 实际实现时需要查询 user_discount_usage 表
    return true;
  }
  
  return true;
};

// 计算折扣价格
Discount.prototype.calculateDiscountPrice = function(originalPrice, quantity = 1) {
  if (!this.isActive()) {
    return originalPrice;
  }
  
  let discountPrice = originalPrice;
  
  switch (this.type) {
    case 1: // 百分比折扣
      discountPrice = originalPrice * (100 - this.value) / 100;
      if (this.maxDiscount && (originalPrice - discountPrice) > this.maxDiscount) {
        discountPrice = originalPrice - this.maxDiscount;
      }
      break;
      
    case 2: // 固定金额折扣
      discountPrice = Math.max(originalPrice - this.value, 0.01);
      break;
      
    case 3: // 满减折扣
      const totalAmount = originalPrice * quantity;
      if (totalAmount >= this.minAmount) {
        const discountAmount = this.value;
        discountPrice = Math.max((totalAmount - discountAmount) / quantity, 0.01);
      }
      break;
      
    case 4: // 买N送M (暂时不实现复杂逻辑)
      // 这种类型需要特殊处理，暂时返回原价
      break;
  }
  
  return Math.round(discountPrice * 100) / 100; // 保留两位小数
};

  // 静态方法
  Discount.getActiveDiscounts = function() {
    const { Op } = require('sequelize');
    const now = new Date();
    return this.findAll({
      where: {
        status: 1,
        startTime: { [Op.lte]: now },
        endTime: { [Op.gte]: now }
      },
      order: [['priority', 'DESC'], ['created_at', 'ASC']]
    });
  };

  Discount.getDiscountTypes = function() {
    return {
      1: '百分比折扣',
      2: '固定金额折扣',
      3: '满减折扣',
      4: '买N送M'
    };
  };

  Discount.getApplicableToTypes = function() {
    return {
      1: '全部商品',
      2: '指定商品',
      3: '指定分类'
    };
  };

  return Discount;
};
