// 订单数据模型 - 完整版
const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/sequelize');

const OrderModel = sequelize.define('OrderModel', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true,
    comment: '订单ID'
  },
  order_no: {
    type: DataTypes.STRING(32),
    allowNull: false,
    unique: true,
    comment: '订单号'
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '用户ID'
  },
  order_status: {
    type: DataTypes.ENUM('pending', 'paid', 'shipped', 'delivered', 'completed', 'cancelled', 'refunded'),
    defaultValue: 'pending',
    comment: '订单状态'
  },
  payment_status: {
    type: DataTypes.ENUM('unpaid', 'paid', 'refunded', 'partial_refund'),
    defaultValue: 'unpaid',
    comment: '支付状态'
  },
  shipping_status: {
    type: DataTypes.ENUM('unshipped', 'shipped', 'delivered'),
    defaultValue: 'unshipped',
    comment: '物流状态'
  },
  total_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: '订单总金额'
  },
  product_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: '商品总金额'
  },
  shipping_fee: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0.00,
    comment: '运费'
  },
  discount_amount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0.00,
    comment: '优惠金额'
  },
  coupon_amount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0.00,
    comment: '优惠券金额'
  },
  payment_method: {
    type: DataTypes.ENUM('wechat', 'alipay', 'balance', 'cod'),
    allowNull: true,
    comment: '支付方式'
  },
  payment_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '支付时间'
  },
  shipping_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '发货时间'
  },
  delivery_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '收货时间'
  },
  receiver_name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '收货人姓名'
  },
  receiver_phone: {
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: '收货人电话'
  },
  receiver_address: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '收货地址'
  },
  receiver_province: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '收货省份'
  },
  receiver_city: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '收货城市'
  },
  receiver_district: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '收货区县'
  },
  shipping_company: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '物流公司'
  },
  shipping_no: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '物流单号'
  },
  remark: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '订单备注'
  },
  cancel_reason: {
    type: DataTypes.STRING(200),
    allowNull: true,
    comment: '取消原因'
  },
  refund_reason: {
    type: DataTypes.STRING(200),
    allowNull: true,
    comment: '退款原因'
  },
  extra_data: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '扩展数据'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '更新时间'
  }
}, {
  tableName: 'orders',
  timestamps: false,
  indexes: [
    {
      unique: true,
      fields: ['order_no']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['order_status']
    },
    {
      fields: ['payment_status']
    },
    {
      fields: ['created_at']
    },
    {
      fields: ['user_id', 'order_status']
    },
    {
      fields: ['user_id', 'created_at']
    }
  ]
});

module.exports = OrderModel;
