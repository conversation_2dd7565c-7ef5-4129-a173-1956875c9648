const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const ProductDiscount = sequelize.define('ProductDiscount', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true,
    comment: '关联ID'
  },
  discountId: {
    type: DataTypes.BIGINT,
    allowNull: false,
    field: 'discount_id',
    comment: '折扣ID'
  },
  productId: {
    type: DataTypes.BIGINT,
    allowNull: false,
    field: 'product_id',
    comment: '商品ID'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'created_at',
    comment: '创建时间'
  }
}, {
  tableName: 'product_discounts',
  timestamps: false,
  indexes: [
    { 
      unique: true,
      fields: ['discount_id', 'product_id'],
      name: 'uk_discount_product'
    },
    { fields: ['discount_id'] },
    { fields: ['product_id'] }
  ]
});

  return ProductDiscount;
};
