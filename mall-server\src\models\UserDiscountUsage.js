const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const UserDiscountUsage = sequelize.define('UserDiscountUsage', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true,
    comment: '使用记录ID'
  },
  userId: {
    type: DataTypes.BIGINT,
    allowNull: false,
    field: 'user_id',
    comment: '用户ID'
  },
  discountId: {
    type: DataTypes.BIGINT,
    allowNull: false,
    field: 'discount_id',
    comment: '折扣ID'
  },
  orderId: {
    type: DataTypes.BIGINT,
    allowNull: true,
    field: 'order_id',
    comment: '订单ID'
  },
  discountAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    field: 'discount_amount',
    comment: '折扣金额'
  },
  usedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'used_at',
    comment: '使用时间'
  }
}, {
  tableName: 'user_discount_usage',
  timestamps: false,
  indexes: [
    { fields: ['user_id'] },
    { fields: ['discount_id'] },
    { fields: ['order_id'] },
    { fields: ['used_at'] }
  ]
});

  return UserDiscountUsage;
};
