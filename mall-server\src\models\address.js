const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Address = sequelize.define('Address', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '地址ID'
    },
    user_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '用户ID'
    },
    receiver: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '收货人姓名'
    },
    phone: {
      type: DataTypes.STRING(20),
      allowNull: false,
      comment: '收货人手机号'
    },
    province: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '省份'
    },
    city: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '城市'
    },
    district: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '区县'
    },
    detail_address: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '详细地址'
    },
    is_default: {
      type: DataTypes.TINYINT,
      defaultValue: 0,
      comment: '是否默认地址(0:否 1:是)'
    }
  }, {
    tableName: 'addresses',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['is_default']
      }
    ]
  });

  return Address;
}; 