const { query } = require('./index');

class AdminModel {
  // 根据用户名查找管理员
  async findByUsername(username) {
    const [rows] = await query(
      'SELECT * FROM admins WHERE username = ? AND status = 1',
      [username]
    );
    return rows[0];
  }

  // 根据ID查找管理员
  async findById(id) {
    const [rows] = await query(
      'SELECT * FROM admins WHERE id = ? AND status = 1',
      [id]
    );
    return rows[0];
  }

  // 创建管理员
  async create(adminData) {
    const { username, password, nickname, role } = adminData;
    const [result] = await query(
      'INSERT INTO admins (username, password, nickname, role, created_at) VALUES (?, ?, ?, ?, NOW())',
      [username, password, nickname, role]
    );
    
    return this.findById(result.insertId);
  }

  // 更新管理员
  async update(id, adminData) {
    const fields = [];
    const values = [];
    
    Object.keys(adminData).forEach(key => {
      if (adminData[key] !== undefined) {
        fields.push(`${key} = ?`);
        values.push(adminData[key]);
      }
    });
    
    if (fields.length === 0) {
      return this.findById(id);
    }
    
    values.push(id);
    
    await query(
      `UPDATE admins SET ${fields.join(', ')}, updated_at = NOW() WHERE id = ?`,
      values
    );
    
    return this.findById(id);
  }

  // 删除管理员（软删除）
  async delete(id) {
    await query(
      'UPDATE admins SET status = 0, deleted_at = NOW() WHERE id = ?',
      [id]
    );
    return true;
  }

  // 获取管理员列表
  async findAll(options = {}) {
    const { page = 1, limit = 10, search = '' } = options;
    const offset = (page - 1) * limit;
    
    let sql = 'SELECT id, username, nickname, role, created_at FROM admins WHERE status = 1';
    const params = [];
    
    if (search) {
      sql += ' AND (username LIKE ? OR nickname LIKE ?)';
      params.push(`%${search}%`, `%${search}%`);
    }
    
    sql += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
    params.push(limit, offset);
    
    const [rows] = await query(sql, params);
    return rows;
  }

  // 统计管理员总数
  async count(options = {}) {
    const { search = '' } = options;
    
    let sql = 'SELECT COUNT(*) as total FROM admins WHERE status = 1';
    const params = [];
    
    if (search) {
      sql += ' AND (username LIKE ? OR nickname LIKE ?)';
      params.push(`%${search}%`, `%${search}%`);
    }
    
    const [rows] = await query(sql, params);
    return rows[0].total;
  }
}

module.exports = new AdminModel(); 