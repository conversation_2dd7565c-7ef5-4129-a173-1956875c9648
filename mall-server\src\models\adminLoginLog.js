const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const AdminLoginLog = sequelize.define('AdminLoginLog', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '日志ID'
    },
    admin_id: {
      type: DataTypes.BIGINT,
      comment: '管理员ID'
    },
    username: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '用户名'
    },
    ip_address: {
      type: DataTypes.STRING(50),
      comment: 'IP地址'
    },
    user_agent: {
      type: DataTypes.TEXT,
      comment: '用户代理'
    },
    login_type: {
      type: DataTypes.STRING(20),
      defaultValue: 'password',
      comment: '登录类型(password:密码 2fa:双因素)'
    },
    status: {
      type: DataTypes.TINYINT,
      defaultValue: 1,
      comment: '状态(1:成功 0:失败)'
    },
    failure_reason: {
      type: DataTypes.STRING(200),
      comment: '失败原因'
    }
  }, {
    tableName: 'admin_login_logs',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: false,
    indexes: [
      {
        fields: ['admin_id']
      },
      {
        fields: ['username']
      },
      {
        fields: ['status']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  AdminLoginLog.associate = (models) => {
    AdminLoginLog.belongsTo(models.AdminUser, { foreignKey: 'admin_id' });
  };

  return AdminLoginLog;
}; 