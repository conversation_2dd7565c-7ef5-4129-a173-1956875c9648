const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const AdminOperationLog = sequelize.define('AdminOperationLog', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '日志ID'
    },
    admin_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '管理员ID'
    },
    username: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '用户名'
    },
    action: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '操作类型'
    },
    resource: {
      type: DataTypes.STRING(100),
      comment: '操作资源'
    },
    resource_id: {
      type: DataTypes.STRING(50),
      comment: '资源ID'
    },
    description: {
      type: DataTypes.TEXT,
      comment: '操作描述'
    },
    ip_address: {
      type: DataTypes.STRING(50),
      comment: 'IP地址'
    },
    user_agent: {
      type: DataTypes.TEXT,
      comment: '用户代理'
    },
    request_data: {
      type: DataTypes.TEXT,
      comment: '请求数据'
    },
    response_data: {
      type: DataTypes.TEXT,
      comment: '响应数据'
    },
    status: {
      type: DataTypes.TINYINT,
      defaultValue: 1,
      comment: '状态(1:成功 0:失败)'
    },
    error_message: {
      type: DataTypes.TEXT,
      comment: '错误信息'
    },
    execution_time: {
      type: DataTypes.INTEGER,
      comment: '执行时间(毫秒)'
    }
  }, {
    tableName: 'admin_operation_logs',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: false,
    indexes: [
      {
        fields: ['admin_id']
      },
      {
        fields: ['action']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  AdminOperationLog.associate = (models) => {
    AdminOperationLog.belongsTo(models.AdminUser, { foreignKey: 'admin_id' });
  };

  return AdminOperationLog;
}; 