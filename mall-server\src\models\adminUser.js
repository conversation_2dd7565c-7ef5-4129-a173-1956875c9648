const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const AdminUser = sequelize.define('AdminUser', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '管理员ID'
    },
    username: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      comment: '用户名'
    },
    password: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '密码(加密)'
    },
    real_name: {
      type: DataTypes.STRING(50),
      comment: '真实姓名'
    },
    phone: {
      type: DataTypes.STRING(20),
      comment: '手机号'
    },
    email: {
      type: DataTypes.STRING(100),
      comment: '邮箱'
    },
    avatar: {
      type: DataTypes.STRING(255),
      comment: '头像URL'
    },
    role_id: {
      type: DataTypes.BIGINT,
      comment: '角色ID'
    },
    status: {
      type: DataTypes.TINYINT,
      defaultValue: 1,
      comment: '状态(0:禁用 1:正常)'
    },
    last_login_time: {
      type: DataTypes.DATE,
      comment: '最后登录时间'
    },
    last_login_ip: {
      type: DataTypes.STRING(50),
      comment: '最后登录IP'
    },
    department: {
      type: DataTypes.STRING(50),
      comment: '部门'
    },
    position: {
      type: DataTypes.STRING(50),
      comment: '职位'
    },
    login_attempts: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '登录失败次数'
    },
    password_changed_at: {
      type: DataTypes.DATE,
      comment: '密码修改时间'
    },
    password_expires_at: {
      type: DataTypes.DATE,
      comment: '密码过期时间'
    },
    two_factor_enabled: {
      type: DataTypes.TINYINT,
      defaultValue: 0,
      comment: '是否启用双因素认证'
    },
    two_factor_secret: {
      type: DataTypes.STRING(100),
      comment: '双因素认证密钥'
    },
    created_by: {
      type: DataTypes.BIGINT,
      comment: '创建人ID'
    }
  }, {
    tableName: 'admin_users',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['username']
      },
      {
        fields: ['status']
      },
      {
        fields: ['role_id']
      },
      {
        fields: ['email']
      }
    ]
  });

  AdminUser.associate = (models) => {
    AdminUser.belongsTo(models.Role, { foreignKey: 'role_id' });
    AdminUser.hasMany(models.AdminOperationLog, { foreignKey: 'admin_id' });
    AdminUser.hasMany(models.AdminLoginLog, { foreignKey: 'admin_id' });
  };

  return AdminUser;
}; 