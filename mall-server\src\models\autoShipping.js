const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const AutoShipping = sequelize.define('AutoShipping', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '自动发货记录ID'
    },
    order_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '订单ID'
    },
    shipping_rule_id: {
      type: DataTypes.BIGINT,
      allowNull: true,
      comment: '发货规则ID'
    },
    shipping_company: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '快递公司'
    },
    tracking_number: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '快递单号'
    },
    shipping_status: {
      type: DataTypes.ENUM('pending', 'processing', 'shipped', 'delivered', 'failed'),
      defaultValue: 'pending',
      comment: '发货状态'
    },
    auto_generated: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: '是否自动生成'
    },
    shipping_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '发货时间'
    },
    estimated_delivery: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '预计送达时间'
    },
    actual_delivery: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '实际送达时间'
    },
    shipping_cost: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0.00,
      comment: '运费成本'
    },
    error_message: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '错误信息'
    },
    retry_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '重试次数'
    },
    last_retry_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '最后重试时间'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间'
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '更新时间'
    }
  }, {
    tableName: 'auto_shipping',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['order_id']
      },
      {
        fields: ['shipping_status']
      },
      {
        fields: ['tracking_number']
      },
      {
        fields: ['shipping_time']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  // 关联关系
  AutoShipping.associate = function(models) {
    AutoShipping.belongsTo(models.Order, {
      foreignKey: 'order_id',
      as: 'order'
    });
  };

  return AutoShipping;
};
