const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Banner = sequelize.define('Banner', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '轮播图ID'
    },
    title: {
      type: DataTypes.STRING(100),
      comment: '标题'
    },
    image_url: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '图片URL'
    },
    link_url: {
      type: DataTypes.STRING(255),
      comment: '跳转链接'
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '排序序号'
    },
    start_time: {
      type: DataTypes.DATE,
      comment: '开始时间'
    },
    end_time: {
      type: DataTypes.DATE,
      comment: '结束时间'
    },
    status: {
      type: DataTypes.TINYINT,
      defaultValue: 1,
      comment: '状态(0:禁用 1:启用)'
    }
  }, {
    tableName: 'banners',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['status']
      },
      {
        fields: ['sort_order']
      },
      {
        fields: ['start_time']
      },
      {
        fields: ['end_time']
      }
    ]
  });

  return Banner;
}; 