// 浏览历史模型
const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const BrowseHistory = sequelize.define('BrowseHistory', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '浏览记录ID'
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '用户ID'
    },
    product_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '商品ID'
    },
    browse_time: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '浏览时间'
    },
    browse_duration: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '浏览时长(秒)'
    },
    source: {
      type: DataTypes.ENUM('search', 'category', 'recommend', 'share', 'direct'),
      defaultValue: 'direct',
      comment: '浏览来源'
    },
    device_info: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '设备信息'
    }
  }, {
    tableName: 'browse_history',
    timestamps: false,
    indexes: [
      {
        fields: ['user_id', 'product_id']
      },
      {
        fields: ['user_id', 'browse_time']
      },
      {
        fields: ['product_id']
      },
      {
        fields: ['browse_time']
      },
      {
        fields: ['source']
      }
    ]
  });

  // 关联关系
  BrowseHistory.associate = function(models) {
    BrowseHistory.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
    
    BrowseHistory.belongsTo(models.Product, {
      foreignKey: 'product_id',
      as: 'product'
    });
  };

  return BrowseHistory;
};
