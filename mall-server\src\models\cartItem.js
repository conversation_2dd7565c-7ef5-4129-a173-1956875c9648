const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const CartItem = sequelize.define('CartItem', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '购物车项ID'
    },
    user_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '用户ID'
    },
    product_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '商品ID'
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: '商品数量'
    },
    selected: {
      type: DataTypes.TINYINT,
      defaultValue: 1,
      comment: '是否选中(0:否 1:是)'
    }
  }, {
    tableName: 'cart_items',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['product_id']
      },
      {
        unique: true,
        fields: ['user_id', 'product_id']
      }
    ]
  });

  return CartItem;
}; 