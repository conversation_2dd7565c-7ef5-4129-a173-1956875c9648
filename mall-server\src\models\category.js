const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Category = sequelize.define('Category', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '分类ID'
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '分类名称'
    },
    parent_id: {
      type: DataTypes.BIGINT,
      defaultValue: 0,
      comment: '父级分类ID(0为顶级分类)'
    },
    level: {
      type: DataTypes.TINYINT,
      defaultValue: 1,
      comment: '分类层级'
    },
    icon: {
      type: DataTypes.STRING(255),
      comment: '分类图标'
    },
    image: {
      type: DataTypes.STRING(255),
      comment: '分类图片'
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '排序序号'
    },
    status: {
      type: DataTypes.TINYINT,
      defaultValue: 1,
      comment: '状态(0:禁用 1:正常)'
    },
    description: {
      type: DataTypes.TEXT,
      comment: '分类描述'
    }
  }, {
    tableName: 'categories',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['parent_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['sort_order']
      }
    ]
  });

  return Category;
}; 