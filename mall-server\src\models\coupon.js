// 优惠券模型
module.exports = (sequelize, DataTypes) => {
  const Coupon = sequelize.define('Coupon', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '优惠券名称'
    },
    code: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      comment: '优惠券代码'
    },
    type: {
      type: DataTypes.TINYINT,
      allowNull: false,
      defaultValue: 1,
      comment: '优惠券类型：1-满减券，2-折扣券，3-免邮券'
    },
    discount_type: {
      type: DataTypes.TINYINT,
      allowNull: false,
      defaultValue: 1,
      comment: '折扣类型：1-固定金额，2-百分比'
    },
    discount_value: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      comment: '折扣值'
    },
    min_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0,
      comment: '最低消费金额'
    },
    max_discount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      comment: '最大折扣金额（折扣券用）'
    },
    total_quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '发放总数量'
    },
    used_quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '已使用数量'
    },
    per_user_limit: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: '每用户限领数量'
    },
    start_time: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '开始时间'
    },
    end_time: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '结束时间'
    },
    status: {
      type: DataTypes.TINYINT,
      allowNull: false,
      defaultValue: 1,
      comment: '状态：0-禁用，1-启用'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '优惠券描述'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'coupons',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['code']
      },
      {
        fields: ['status']
      },
      {
        fields: ['start_time', 'end_time']
      }
    ]
  });

  // 关联关系
  Coupon.associate = function(models) {
    // 优惠券与用户优惠券的关系
    Coupon.hasMany(models.UserCoupon, {
      foreignKey: 'coupon_id',
      as: 'userCoupons'
    });
  };

  return Coupon;
};
