// 客服对话模型
const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  // 客服会话模型
  const CustomerSession = sequelize.define('CustomerSession', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '会话ID'
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '用户ID'
    },
    admin_id: {
      type: DataTypes.BIGINT,
      allowNull: true,
      comment: '客服ID'
    },
    session_status: {
      type: DataTypes.ENUM('waiting', 'active', 'closed', 'timeout'),
      defaultValue: 'waiting',
      comment: '会话状态'
    },
    priority: {
      type: DataTypes.ENUM('low', 'normal', 'high', 'urgent'),
      defaultValue: 'normal',
      comment: '优先级'
    },
    category: {
      type: DataTypes.ENUM('product', 'order', 'payment', 'delivery', 'refund', 'other'),
      defaultValue: 'other',
      comment: '问题分类'
    },
    title: {
      type: DataTypes.STRING(200),
      allowNull: true,
      comment: '会话标题'
    },
    tags: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '标签'
    },
    satisfaction: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '满意度评分(1-5)'
    },
    feedback: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '用户反馈'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间'
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '更新时间'
    },
    closed_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '关闭时间'
    }
  }, {
    tableName: 'customer_sessions',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['admin_id']
      },
      {
        fields: ['session_status']
      },
      {
        fields: ['priority']
      },
      {
        fields: ['category']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  // 客服消息模型
  const CustomerMessage = sequelize.define('CustomerMessage', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '消息ID'
    },
    session_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '会话ID'
    },
    sender_type: {
      type: DataTypes.ENUM('user', 'admin', 'system'),
      allowNull: false,
      comment: '发送者类型'
    },
    sender_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '发送者ID'
    },
    message_type: {
      type: DataTypes.ENUM('text', 'image', 'file', 'product', 'order', 'system'),
      defaultValue: 'text',
      comment: '消息类型'
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '消息内容'
    },
    extra_data: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '额外数据(图片URL、文件信息等)'
    },
    is_read: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: '是否已读'
    },
    read_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '阅读时间'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '发送时间'
    }
  }, {
    tableName: 'customer_messages',
    timestamps: false,
    indexes: [
      {
        fields: ['session_id']
      },
      {
        fields: ['sender_type', 'sender_id']
      },
      {
        fields: ['message_type']
      },
      {
        fields: ['is_read']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  // 关联关系
  CustomerSession.associate = function(models) {
    CustomerSession.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
    
    CustomerSession.belongsTo(models.AdminUser, {
      foreignKey: 'admin_id',
      as: 'admin'
    });
    
    CustomerSession.hasMany(models.CustomerMessage, {
      foreignKey: 'session_id',
      as: 'messages'
    });
  };

  CustomerMessage.associate = function(models) {
    CustomerMessage.belongsTo(models.CustomerSession, {
      foreignKey: 'session_id',
      as: 'session'
    });
  };

  return { CustomerSession, CustomerMessage };
};
