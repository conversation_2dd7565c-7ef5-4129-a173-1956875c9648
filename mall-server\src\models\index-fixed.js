const { sequelize } = require('../config/sequelize');

console.log('📊 初始化数据库模型...');

// 兼容的模型加载函数
function loadModel(modelName) {
  try {
    const modelModule = require('./' + modelName);
    
    // 检查是否是函数导出
    if (typeof modelModule === 'function') {
      return modelModule(sequelize);
    } else {
      // 直接导出的模型
      return modelModule;
    }
  } catch (error) {
    console.warn('⚠️ 模型加载失败:', modelName, error.message);
    return null;
  }
}

// 加载所有模型
const User = loadModel('user');
const Category = loadModel('category');
const Product = loadModel('product');
const CartItem = loadModel('cartItem');
const Address = loadModel('address');
const Order = loadModel('order');
const OrderItem = loadModel('orderItem');
const Review = loadModel('review');
const Banner = loadModel('banner');

// 只导出成功加载的模型
const models = {
  sequelize
};

if (User) models.User = User;
if (Category) models.Category = Category;
if (Product) models.Product = Product;
if (CartItem) models.CartItem = CartItem;
if (Address) models.Address = Address;
if (Order) models.Order = Order;
if (OrderItem) models.OrderItem = OrderItem;
if (Review) models.Review = Review;
if (Banner) models.Banner = Banner;

console.log('✅ 模型加载完成，成功加载:', Object.keys(models).filter(k => k !== 'sequelize').join(', '));

module.exports = models;