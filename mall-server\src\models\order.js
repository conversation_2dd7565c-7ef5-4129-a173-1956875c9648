const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Order = sequelize.define('Order', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '订单ID'
    },
    order_no: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      comment: '订单号'
    },
    user_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '用户ID'
    },
    total_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      comment: '订单总金额'
    },
    pay_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      comment: '实付金额'
    },
    freight_amount: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0.00,
      comment: '运费'
    },
    discount_amount: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0.00,
      comment: '优惠金额'
    },
    receiver_name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '收货人姓名'
    },
    receiver_phone: {
      type: DataTypes.STRING(20),
      allowNull: false,
      comment: '收货人手机号'
    },
    receiver_address: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '收货地址'
    },
    delivery_company: {
      type: DataTypes.STRING(50),
      comment: '快递公司'
    },
    delivery_no: {
      type: DataTypes.STRING(50),
      comment: '快递单号'
    },
    pay_type: {
      type: DataTypes.TINYINT,
      comment: '支付方式(1:微信 2:支付宝 3:银行卡)'
    },
    pay_status: {
      type: DataTypes.TINYINT,
      defaultValue: 0,
      comment: '支付状态(0:未支付 1:已支付 2:已退款)'
    },
    order_status: {
      type: DataTypes.TINYINT,
      defaultValue: 0,
      comment: '订单状态(0:待付款 1:待发货 2:待收货 3:已完成 4:已取消)'
    },
    pay_time: {
      type: DataTypes.DATE,
      comment: '支付时间'
    },
    delivery_time: {
      type: DataTypes.DATE,
      comment: '发货时间'
    },
    receive_time: {
      type: DataTypes.DATE,
      comment: '收货时间'
    },
    remark: {
      type: DataTypes.TEXT,
      comment: '订单备注'
    }
  }, {
    tableName: 'orders',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['order_no']
      },
      {
        fields: ['pay_status']
      },
      {
        fields: ['order_status']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  return Order;
}; 