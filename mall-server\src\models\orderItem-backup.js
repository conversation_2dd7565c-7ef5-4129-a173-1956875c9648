// 订单项数据模型 - 修复版本
const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const OrderItem = sequelize.define('OrderItem', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '订单商品ID'
    },
    order_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '订单ID'
    },
    product_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '商品ID'
    },
    product_name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '商品名称'
    },
    product_image: {
      type: DataTypes.STRING(255),
      comment: '商品图片'
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      comment: '商品单价'
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: '购买数量'
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      comment: '创建时间'
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      comment: '更新时间'
    }
  }, {
    tableName: 'order_items',
    timestamps: false,
    comment: '订单商品表'
  });

  // 定义关联关系
  OrderItem.associate = (models) => {
    // 订单项属于订单
    OrderItem.belongsTo(models.Order, {
      foreignKey: 'order_id',
      as: 'order'
    });

    // 订单项属于商品
    OrderItem.belongsTo(models.Product, {
      foreignKey: 'product_id',
      as: 'product'
    });
  };

  return OrderItem;
};
