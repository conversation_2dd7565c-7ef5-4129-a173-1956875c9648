const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Permission = sequelize.define('Permission', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '权限ID'
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '权限名称'
    },
    code: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      comment: '权限代码'
    },
    type: {
      type: DataTypes.STRING(20),
      defaultValue: 'menu',
      comment: '权限类型(menu:菜单 page:页面 button:按钮 api:接口)'
    },
    parent_id: {
      type: DataTypes.BIGINT,
      defaultValue: 0,
      comment: '父权限ID'
    },
    path: {
      type: DataTypes.STRING(200),
      comment: '路径'
    },
    component: {
      type: DataTypes.STRING(200),
      comment: '组件'
    },
    icon: {
      type: DataTypes.STRING(50),
      comment: '图标'
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '排序'
    },
    status: {
      type: DataTypes.TINYINT,
      defaultValue: 1,
      comment: '状态(0:禁用 1:正常)'
    }
  }, {
    tableName: 'permissions',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: false,
    indexes: [
      {
        fields: ['code']
      },
      {
        fields: ['parent_id']
      },
      {
        fields: ['status']
      }
    ]
  });

  Permission.associate = (models) => {
    Permission.belongsToMany(models.Role, {
      through: 'role_permissions',
      foreignKey: 'permission_id',
      otherKey: 'role_id'
    });
  };

  return Permission;
}; 