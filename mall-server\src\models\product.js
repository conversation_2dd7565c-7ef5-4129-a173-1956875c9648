const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Product = sequelize.define('Product', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '商品ID'
    },
    name: {
      type: DataTypes.STRING(200),
      allowNull: false,
      comment: '商品名称'
    },
    category_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '分类ID'
    },
    brand: {
      type: DataTypes.STRING(100),
      comment: '品牌'
    },
    description: {
      type: DataTypes.TEXT,
      comment: '商品描述'
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      comment: '商品价格'
    },
    original_price: {
      type: DataTypes.DECIMAL(10, 2),
      comment: '原价'
    },
    stock: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '库存数量'
    },
    sales: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '销量'
    },
    rating: {
      type: DataTypes.DECIMAL(3, 2),
      defaultValue: 0.00,
      comment: '评分'
    },
    review_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '评价数量'
    },
    main_image: {
      type: DataTypes.STRING(255),
      comment: '主图URL'
    },
    images: {
      type: DataTypes.TEXT,
      comment: '商品图片(JSON格式)'
    },
    weight: {
      type: DataTypes.DECIMAL(8, 2),
      comment: '重量(克)'
    },
    unit: {
      type: DataTypes.STRING(20),
      comment: '单位(包/盒/斤等)'
    },
    is_hot: {
      type: DataTypes.TINYINT,
      defaultValue: 0,
      comment: '是否热销(0:否 1:是)'
    },
    is_recommend: {
      type: DataTypes.TINYINT,
      defaultValue: 0,
      comment: '是否推荐(0:否 1:是)'
    },
    status: {
      type: DataTypes.TINYINT,
      defaultValue: 1,
      comment: '状态(0:下架 1:上架)'
    }
  }, {
    tableName: 'products',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['category_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['is_hot']
      },
      {
        fields: ['is_recommend']
      }
    ]
  });

  return Product;
}; 