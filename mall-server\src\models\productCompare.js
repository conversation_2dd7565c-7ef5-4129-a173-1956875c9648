// 商品对比模型
const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const ProductCompare = sequelize.define('ProductCompare', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '对比ID'
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '用户ID'
    },
    product_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '商品ID'
    },
    compare_group: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '对比组标识'
    },
    added_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '添加时间'
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '排序序号'
    }
  }, {
    tableName: 'product_compare',
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ['user_id', 'product_id', 'compare_group'],
        name: 'uk_user_product_group'
      },
      {
        fields: ['user_id', 'compare_group']
      },
      {
        fields: ['product_id']
      },
      {
        fields: ['added_at']
      }
    ]
  });

  // 关联关系
  ProductCompare.associate = function(models) {
    ProductCompare.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
    
    ProductCompare.belongsTo(models.Product, {
      foreignKey: 'product_id',
      as: 'product'
    });
  };

  return ProductCompare;
};
