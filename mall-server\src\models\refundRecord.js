const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const RefundRecord = sequelize.define('RefundRecord', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '退款记录ID'
    },
    refund_no: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      comment: '退款单号'
    },
    return_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '退货申请ID'
    },
    order_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '原订单ID'
    },
    user_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '用户ID'
    },
    refund_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      comment: '退款金额'
    },
    refund_type: {
      type: DataTypes.TINYINT,
      allowNull: false,
      comment: '退款方式(1:原路退回 2:余额退款 3:线下退款)'
    },
    refund_status: {
      type: DataTypes.TINYINT,
      allowNull: false,
      defaultValue: 0,
      comment: '退款状态(0:待退款 1:退款中 2:退款成功 3:退款失败)'
    },
    refund_channel: {
      type: DataTypes.STRING(50),
      comment: '退款渠道'
    },
    transaction_id: {
      type: DataTypes.STRING(100),
      comment: '第三方交易号'
    },
    refund_time: {
      type: DataTypes.DATE,
      comment: '退款时间'
    },
    success_time: {
      type: DataTypes.DATE,
      comment: '退款成功时间'
    },
    fail_reason: {
      type: DataTypes.STRING(500),
      comment: '退款失败原因'
    },
    operator_id: {
      type: DataTypes.BIGINT,
      comment: '操作员ID'
    },
    operator_name: {
      type: DataTypes.STRING(50),
      comment: '操作员姓名'
    },
    remark: {
      type: DataTypes.TEXT,
      comment: '备注'
    }
  }, {
    tableName: 'refund_records',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['refund_no']
      },
      {
        fields: ['return_id']
      },
      {
        fields: ['order_id']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['refund_status']
      }
    ]
  });

  return RefundRecord;
};
