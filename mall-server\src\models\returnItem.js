const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const ReturnItem = sequelize.define('ReturnItem', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '退货商品ID'
    },
    return_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '退货申请ID'
    },
    order_item_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '原订单商品ID'
    },
    product_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '商品ID'
    },
    product_name: {
      type: DataTypes.STRING(200),
      allowNull: false,
      comment: '商品名称'
    },
    product_image: {
      type: DataTypes.STRING(500),
      comment: '商品图片'
    },
    product_price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      comment: '商品单价'
    },
    return_quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '退货数量'
    },
    return_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      comment: '退货金额'
    },
    return_reason: {
      type: DataTypes.STRING(200),
      comment: '退货原因'
    },
    product_condition: {
      type: DataTypes.TINYINT,
      comment: '商品状态(1:完好 2:轻微瑕疵 3:严重损坏)'
    },
    inspect_result: {
      type: DataTypes.TINYINT,
      comment: '验收结果(1:通过 2:不通过)'
    },
    inspect_remark: {
      type: DataTypes.TEXT,
      comment: '验收备注'
    }
  }, {
    tableName: 'return_items',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['return_id']
      },
      {
        fields: ['product_id']
      },
      {
        fields: ['order_item_id']
      }
    ]
  });

  return ReturnItem;
};
