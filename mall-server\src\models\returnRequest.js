const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const ReturnRequest = sequelize.define('ReturnRequest', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '退货申请ID'
    },
    return_no: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      comment: '退货单号'
    },
    order_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '原订单ID'
    },
    order_no: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '原订单号'
    },
    user_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '用户ID'
    },
    return_type: {
      type: DataTypes.TINYINT,
      allowNull: false,
      defaultValue: 1,
      comment: '退货类型(1:仅退款 2:退货退款 3:换货)'
    },
    return_reason: {
      type: DataTypes.STRING(200),
      allowNull: false,
      comment: '退货原因'
    },
    return_description: {
      type: DataTypes.TEXT,
      comment: '退货详细说明'
    },
    return_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      comment: '申请退款金额'
    },
    return_quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: '退货数量'
    },
    return_images: {
      type: DataTypes.JSON,
      comment: '退货凭证图片'
    },
    contact_phone: {
      type: DataTypes.STRING(20),
      comment: '联系电话'
    },
    return_address: {
      type: DataTypes.TEXT,
      comment: '退货地址'
    },
    status: {
      type: DataTypes.TINYINT,
      allowNull: false,
      defaultValue: 0,
      comment: '退货状态(0:待审核 1:审核通过 2:审核拒绝 3:待寄回 4:已寄回 5:验收中 6:验收通过 7:验收不通过 8:退款完成 9:已取消)'
    },
    admin_remark: {
      type: DataTypes.TEXT,
      comment: '管理员备注'
    },
    refuse_reason: {
      type: DataTypes.STRING(500),
      comment: '拒绝原因'
    },
    return_express_company: {
      type: DataTypes.STRING(50),
      comment: '退货快递公司'
    },
    return_express_no: {
      type: DataTypes.STRING(50),
      comment: '退货快递单号'
    },
    return_express_time: {
      type: DataTypes.DATE,
      comment: '退货寄出时间'
    },
    receive_time: {
      type: DataTypes.DATE,
      comment: '商家收货时间'
    },
    inspect_time: {
      type: DataTypes.DATE,
      comment: '验收时间'
    },
    refund_time: {
      type: DataTypes.DATE,
      comment: '退款时间'
    }
  }, {
    tableName: 'return_requests',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['return_no']
      },
      {
        fields: ['order_id']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  return ReturnRequest;
};
