const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const ReturnStatusLog = sequelize.define('ReturnStatusLog', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '日志ID'
    },
    return_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '退货申请ID'
    },
    status: {
      type: DataTypes.TINYINT,
      allowNull: false,
      comment: '状态'
    },
    status_text: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '状态描述'
    },
    operator_type: {
      type: DataTypes.TINYINT,
      allowNull: false,
      comment: '操作者类型(1:用户 2:管理员 3:系统)'
    },
    operator_id: {
      type: DataTypes.BIGINT,
      comment: '操作者ID'
    },
    operator_name: {
      type: DataTypes.STRING(50),
      comment: '操作者姓名'
    },
    remark: {
      type: DataTypes.TEXT,
      comment: '备注'
    }
  }, {
    tableName: 'return_status_logs',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: false,
    indexes: [
      {
        fields: ['return_id']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  return ReturnStatusLog;
};
