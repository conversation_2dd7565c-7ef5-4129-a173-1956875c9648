const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Review = sequelize.define('Review', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '评价ID'
    },
    order_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '订单ID'
    },
    product_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '商品ID'
    },
    user_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '用户ID'
    },
    rating: {
      type: DataTypes.TINYINT,
      allowNull: false,
      comment: '评分(1-5星)'
    },
    content: {
      type: DataTypes.TEXT,
      comment: '评价内容'
    },
    images: {
      type: DataTypes.TEXT,
      comment: '评价图片(JSON格式)'
    },
    is_anonymous: {
      type: DataTypes.TINYINT,
      defaultValue: 0,
      comment: '是否匿名(0:否 1:是)'
    },
    reply_content: {
      type: DataTypes.TEXT,
      comment: '商家回复内容'
    },
    reply_time: {
      type: DataTypes.DATE,
      comment: '回复时间'
    }
  }, {
    tableName: 'reviews',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['order_id']
      },
      {
        fields: ['product_id']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['rating']
      }
    ]
  });

  return Review;
}; 