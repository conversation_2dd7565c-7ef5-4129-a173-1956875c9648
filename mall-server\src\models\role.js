const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Role = sequelize.define('Role', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '角色ID'
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '角色名称'
    },
    code: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      comment: '角色代码'
    },
    description: {
      type: DataTypes.TEXT,
      comment: '角色描述'
    },
    level: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
      comment: '角色级别(1:超级管理员 2:管理员 3:操作员)'
    },
    status: {
      type: DataTypes.TINYINT,
      defaultValue: 1,
      comment: '状态(0:禁用 1:正常)'
    }
  }, {
    tableName: 'roles',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['code']
      },
      {
        fields: ['status']
      }
    ]
  });

  Role.associate = (models) => {
    Role.hasMany(models.AdminUser, { foreignKey: 'role_id' });
    Role.belongsToMany(models.Permission, {
      through: 'role_permissions',
      foreignKey: 'role_id',
      otherKey: 'permission_id'
    });
  };

  return Role;
}; 