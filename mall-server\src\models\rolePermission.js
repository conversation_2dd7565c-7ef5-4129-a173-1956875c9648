const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const RolePermission = sequelize.define('RolePermission', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '主键'
    },
    role_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '角色ID'
    },
    permission_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '权限ID'
    }
  }, {
    tableName: 'role_permissions',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['role_id', 'permission_id']
      }
    ]
  });

  return RolePermission;
}; 