const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const SalesReport = sequelize.define('SalesReport', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '报表ID'
    },
    report_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: '报表日期'
    },
    report_type: {
      type: DataTypes.ENUM('daily', 'weekly', 'monthly', 'yearly'),
      allowNull: false,
      comment: '报表类型'
    },
    total_orders: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '总订单数'
    },
    total_amount: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0.00,
      comment: '总销售额'
    },
    paid_orders: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '已支付订单数'
    },
    paid_amount: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0.00,
      comment: '已支付金额'
    },
    refund_orders: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '退款订单数'
    },
    refund_amount: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0.00,
      comment: '退款金额'
    },
    new_users: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '新增用户数'
    },
    active_users: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '活跃用户数'
    },
    conversion_rate: {
      type: DataTypes.DECIMAL(5, 4),
      defaultValue: 0.0000,
      comment: '转化率'
    },
    avg_order_amount: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0.00,
      comment: '平均订单金额'
    },
    top_products: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '热销商品TOP10'
    },
    top_categories: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '热销分类TOP10'
    },
    user_analysis: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '用户分析数据'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间'
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '更新时间'
    }
  }, {
    tableName: 'sales_reports',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['report_date', 'report_type']
      },
      {
        fields: ['report_type']
      },
      {
        fields: ['report_date']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  return SalesReport;
};
