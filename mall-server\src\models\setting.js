const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Setting = sequelize.define('Setting', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '设置ID'
    },
    key: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      comment: '设置键'
    },
    value: {
      type: DataTypes.TEXT,
      comment: '设置值'
    },
    description: {
      type: DataTypes.STRING(255),
      comment: '设置描述'
    }
  }, {
    tableName: 'settings',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['key']
      }
    ]
  });

  return Setting;
}; 