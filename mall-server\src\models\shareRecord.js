// 分享记录模型
const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const ShareRecord = sequelize.define('ShareRecord', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '分享记录ID'
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '分享用户ID'
    },
    share_type: {
      type: DataTypes.ENUM('product', 'category', 'page', 'order'),
      allowNull: false,
      comment: '分享类型'
    },
    target_id: {
      type: DataTypes.BIGINT,
      allowNull: true,
      comment: '分享目标ID'
    },
    share_platform: {
      type: DataTypes.ENUM('wechat', 'moments', 'qq', 'weibo', 'copy_link'),
      allowNull: false,
      comment: '分享平台'
    },
    share_title: {
      type: DataTypes.STRING(200),
      allowNull: true,
      comment: '分享标题'
    },
    share_desc: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '分享描述'
    },
    share_image: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '分享图片'
    },
    share_url: {
      type: DataTypes.STRING(500),
      allowNull: true,
      comment: '分享链接'
    },
    click_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '点击次数'
    },
    conversion_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '转化次数'
    },
    share_time: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '分享时间'
    },
    extra_data: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '额外数据'
    }
  }, {
    tableName: 'share_records',
    timestamps: false,
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['share_type', 'target_id']
      },
      {
        fields: ['share_platform']
      },
      {
        fields: ['share_time']
      },
      {
        fields: ['click_count']
      }
    ]
  });

  // 关联关系
  ShareRecord.associate = function(models) {
    ShareRecord.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
    
    ShareRecord.belongsTo(models.Product, {
      foreignKey: 'target_id',
      as: 'product',
      constraints: false,
      scope: {
        share_type: 'product'
      }
    });
  };

  return ShareRecord;
};
