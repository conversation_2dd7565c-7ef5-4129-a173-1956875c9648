const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const StockAlert = sequelize.define('StockAlert', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '库存预警ID'
    },
    product_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '商品ID'
    },
    alert_type: {
      type: DataTypes.ENUM('low_stock', 'out_of_stock', 'overstock'),
      allowNull: false,
      comment: '预警类型'
    },
    current_stock: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '当前库存'
    },
    threshold_value: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '阈值'
    },
    alert_level: {
      type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
      allowNull: false,
      comment: '预警级别'
    },
    status: {
      type: DataTypes.ENUM('active', 'resolved', 'ignored'),
      defaultValue: 'active',
      comment: '预警状态'
    },
    message: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '预警消息'
    },
    resolved_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '解决时间'
    },
    resolved_by: {
      type: DataTypes.BIGINT,
      allowNull: true,
      comment: '解决人ID'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间'
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '更新时间'
    }
  }, {
    tableName: 'stock_alerts',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['product_id']
      },
      {
        fields: ['alert_type']
      },
      {
        fields: ['alert_level']
      },
      {
        fields: ['status']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  // 关联关系
  StockAlert.associate = function(models) {
    StockAlert.belongsTo(models.Product, {
      foreignKey: 'product_id',
      as: 'product'
    });
    
    StockAlert.belongsTo(models.AdminUser, {
      foreignKey: 'resolved_by',
      as: 'resolver'
    });
  };

  return StockAlert;
};
