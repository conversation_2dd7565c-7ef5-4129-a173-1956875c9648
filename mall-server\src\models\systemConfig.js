const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const SystemConfig = sequelize.define('SystemConfig', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '配置ID'
    },
    config_key: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      comment: '配置键'
    },
    config_value: {
      type: DataTypes.TEXT,
      comment: '配置值'
    },
    config_type: {
      type: DataTypes.STRING(20),
      defaultValue: 'string',
      comment: '配置类型(string:字符串 json:JSON number:数字 boolean:布尔)'
    },
    description: {
      type: DataTypes.TEXT,
      comment: '配置描述'
    },
    is_system: {
      type: DataTypes.TINYINT,
      defaultValue: 0,
      comment: '是否系统配置(0:否 1:是)'
    }
  }, {
    tableName: 'system_configs',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['config_key']
      },
      {
        fields: ['is_system']
      }
    ]
  });

  return SystemConfig;
}; 