const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  
  // 微信相关字段
  openid: {
    type: DataTypes.STRING(100),
    unique: true,
    allowNull: false,
    comment: '微信用户唯一标识'
  },
  
  unionid: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '微信开放平台唯一标识'
  },
  
  // 用户昵称
  nickname: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '用户昵称'
  },

  // 用户头像
  avatar: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '用户头像URL'
  },

  // 扩展字段
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '手机号'
  },

  // 性别
  gender: {
    type: DataTypes.TINYINT,
    defaultValue: 0,
    comment: '性别(0:未知 1:男 2:女)'
  },

  // 生日
  birthday: {
    type: DataTypes.DATEONLY,
    allowNull: true,
    comment: '生日'
  },

  // 用户状态
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'banned'),
    defaultValue: 'active',
    comment: '用户状态'
  },

  // 登录相关
  lastLoginAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'last_login_at',
    comment: '最后登录时间'
  },

  // 用户信息（JSON格式存储）
  userInfo: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'user_info', // 指定数据库字段名
    comment: '用户信息（头像、昵称等）'
  },

  // 用户等级
  level: {
    type: DataTypes.INTEGER,
    defaultValue: 1,
    comment: '用户等级'
  },

  // 积分
  points: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '用户积分'
  },

  // 余额
  balance: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0.00,
    comment: '用户余额'
  },

  // 会员等级
  user_level: {
    type: DataTypes.INTEGER,
    defaultValue: 1,
    comment: '会员等级(1:普通 2:白银 3:黄金)'
  },

  // 备注
  remark: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '备注信息'
  },

  email: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '邮箱'
  }
}, {
  tableName: 'users',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  
  // 索引
  indexes: [
    {
      unique: true,
      fields: ['openid']
    },
    {
      fields: ['unionid']
    },
    {
      fields: ['status']
    },
    {
      fields: ['lastLoginAt']
    }
  ],
  
  // 钩子函数
  hooks: {
    beforeCreate: (user) => {
      if (user.userInfo && typeof user.userInfo === 'object') {
        user.userInfo = JSON.stringify(user.userInfo);
      }
    },
    
    beforeUpdate: (user) => {
      if (user.userInfo && typeof user.userInfo === 'object') {
        user.userInfo = JSON.stringify(user.userInfo);
      }
    },
    
    afterFind: (users) => {
      if (Array.isArray(users)) {
        users.forEach(user => {
          if (user.userInfo) {
            try {
              user.userInfo = JSON.parse(user.userInfo);
            } catch (e) {
              console.error('解析用户信息失败:', e);
            }
          }
        });
      } else if (users && users.userInfo) {
        try {
          users.userInfo = JSON.parse(users.userInfo);
        } catch (e) {
          console.error('解析用户信息失败:', e);
        }
      }
    }
  }
  });

  return User;
}; 