const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const UserBehavior = sequelize.define('UserBehavior', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '行为记录ID'
    },
    user_id: {
      type: DataTypes.BIGINT,
      allowNull: true,
      comment: '用户ID（可为空，支持匿名用户）'
    },
    openid: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '微信openid'
    },
    behavior_type: {
      type: DataTypes.ENUM('view', 'search', 'add_cart', 'order', 'pay', 'share', 'favorite'),
      allowNull: false,
      comment: '行为类型'
    },
    target_type: {
      type: DataTypes.ENUM('product', 'category', 'order', 'page'),
      allowNull: false,
      comment: '目标类型'
    },
    target_id: {
      type: DataTypes.BIGINT,
      allowNull: true,
      comment: '目标ID'
    },
    page_path: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '页面路径'
    },
    search_keyword: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '搜索关键词'
    },
    session_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '会话ID'
    },
    ip_address: {
      type: DataTypes.STRING(45),
      allowNull: true,
      comment: 'IP地址'
    },
    user_agent: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '用户代理'
    },
    extra_data: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '额外数据'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间'
    }
  }, {
    tableName: 'user_behaviors',
    timestamps: false,
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['openid']
      },
      {
        fields: ['behavior_type']
      },
      {
        fields: ['target_type', 'target_id']
      },
      {
        fields: ['created_at']
      },
      {
        fields: ['search_keyword']
      }
    ]
  });

  // 关联关系
  UserBehavior.associate = function(models) {
    UserBehavior.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
    
    UserBehavior.belongsTo(models.Product, {
      foreignKey: 'target_id',
      as: 'product',
      constraints: false,
      scope: {
        target_type: 'product'
      }
    });
  };

  return UserBehavior;
};
