// 用户优惠券模型
module.exports = (sequelize, DataTypes) => {
  const UserCoupon = sequelize.define('UserCoupon', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '用户ID'
    },
    coupon_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '优惠券ID'
    },
    order_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '使用的订单ID'
    },
    status: {
      type: DataTypes.TINYINT,
      allowNull: false,
      defaultValue: 0,
      comment: '状态：0-未使用，1-已使用，2-已过期'
    },
    received_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '领取时间'
    },
    used_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '使用时间'
    },
    expired_at: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '过期时间'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'user_coupons',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['coupon_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['expired_at']
      }
    ]
  });

  // 关联关系
  UserCoupon.associate = function(models) {
    // 用户优惠券与用户的关系
    UserCoupon.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });

    // 用户优惠券与优惠券的关系
    UserCoupon.belongsTo(models.Coupon, {
      foreignKey: 'coupon_id',
      as: 'coupon'
    });

    // 用户优惠券与订单的关系
    UserCoupon.belongsTo(models.Order, {
      foreignKey: 'order_id',
      as: 'order'
    });
  };

  return UserCoupon;
};
