const Router = require('@koa/router');
const adminController = require('../../controllers/admin/admin');
const auth = require('../../middleware/auth');

const router = new Router();

// 获取管理员列表
router.get('/list', auth, adminController.getAdminList);

// 获取管理员详情
router.get('/detail/:id', auth, adminController.getAdminDetail);

// 创建管理员
router.post('/create', auth, adminController.createAdmin);

// 更新管理员
router.put('/update/:id', auth, adminController.updateAdmin);

// 删除管理员
router.delete('/delete/:id', auth, adminController.deleteAdmin);

// 更新管理员状态
router.put('/status/:id', auth, adminController.updateAdminStatus);

// 重置管理员密码
router.put('/reset-password/:id', auth, adminController.resetAdminPassword);

module.exports = router; 