// 优化版数据分析路由 - 高效简洁版
const Router = require('@koa/router');
const controller = require('../../controllers/admin/analytics-optimized');

const router = new Router();

// === 核心分析接口 ===
router.get('/dashboard', controller.getDashboard);              // 综合仪表板
router.get('/realtime', controller.getRealTimeStats);           // 实时统计
router.get('/sales-trend', controller.getSalesTrend);           // 销售趋势
router.get('/top-products', controller.getTopProducts);         // 热销商品
router.get('/conversion-funnel', controller.getConversionFunnel); // 转化漏斗
router.get('/behavior-heatmap', controller.getBehaviorHeatmap); // 行为热力图
router.get('/search-keywords', controller.getSearchKeywords);   // 搜索关键词
router.post('/batch-analytics', controller.getBatchAnalytics);  // 批量分析

// === 库存预警接口 ===
router.get('/stock/stats', controller.getStockAlertStats);      // 预警统计
router.post('/stock/check', controller.checkStock);             // 检查库存
router.get('/stock/recommendations', controller.getStockRecommendations); // 补货建议
router.post('/stock/batch-process', controller.batchProcessAlerts); // 批量处理
router.get('/stock/trends', controller.getAlertTrends);         // 预警趋势

// === 自动发货接口 ===
router.get('/shipping/stats', controller.getShippingStats);     // 发货统计
router.post('/shipping/process', controller.processAutoShipping); // 处理发货
router.post('/shipping/retry', controller.retryFailedShipping); // 重试发货
router.get('/shipping/performance', controller.getShippingPerformance); // 性能分析
router.post('/shipping/config', controller.updateShippingConfig); // 更新配置

// === 系统管理接口 ===
router.delete('/cache', controller.clearCache);                 // 清理缓存
router.get('/performance', controller.getSystemPerformance);    // 系统性能
router.get('/configs', controller.getConfigs);                  // 获取配置
router.post('/configs', controller.updateConfigs);              // 更新配置

module.exports = router;
