// 数据分析路由
const Router = require('@koa/router');
const analyticsController = require('../../controllers/admin/analytics');
const stockAlertController = require('../../controllers/admin/stockAlert');
const autoShippingController = require('../../controllers/admin/autoShipping');

const router = new Router();

// 数据分析相关路由
router.get('/sales', analyticsController.getSalesAnalytics);
router.get('/user-behavior', analyticsController.getUserBehaviorAnalytics);
router.get('/real-time', analyticsController.getRealTimeStats);
router.get('/conversion-funnel', analyticsController.getConversionFunnel);
router.get('/product-views', analyticsController.getProductViewAnalysis);
router.get('/user-paths', analyticsController.getUserPathAnalysis);
router.get('/user-retention', analyticsController.getUserRetentionAnalysis);

// 销售报表相关路由
router.post('/reports/generate', analyticsController.generateSalesReport);
router.get('/reports', analyticsController.getSalesReports);

// 库存预警相关路由
router.get('/stock-alerts', stockAlertController.getAlerts);
router.post('/stock-alerts/check', stockAlertController.checkStock);
router.put('/stock-alerts/:id/resolve', stockAlertController.resolveAlert);
router.put('/stock-alerts/:id/ignore', stockAlertController.ignoreAlert);
router.get('/stock-alerts/stats', stockAlertController.getAlertStats);
router.post('/stock-alerts/thresholds', stockAlertController.setAlertThresholds);
router.post('/stock-alerts/cleanup', stockAlertController.cleanupOldAlerts);

// 自动发货相关路由
router.get('/auto-shipping', autoShippingController.getShippingRecords);
router.post('/auto-shipping/process', autoShippingController.processAutoShipping);
router.post('/auto-shipping/:id/retry', autoShippingController.retryShipping);
router.get('/auto-shipping/stats', autoShippingController.getShippingStats);
router.post('/auto-shipping/rules', autoShippingController.updateShippingRules);

module.exports = router;
