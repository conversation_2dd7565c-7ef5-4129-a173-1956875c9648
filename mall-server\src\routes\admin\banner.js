const Router = require('@koa/router');
const bannerController = require('../../controllers/admin/banner');

console.log('【路由挂载】加载 banner 路由');

const router = new Router();

// 获取轮播图列表 (根路径)
router.get('/', bannerController.getBannerList);

// 获取轮播图列表
router.get('/list', bannerController.getBannerList);

// 获取轮播图详情
router.get('/detail/:id', bannerController.getBannerDetail);

// 添加轮播图
router.post('/add', bannerController.addBanner);

// 更新轮播图
router.put('/update/:id', bannerController.updateBanner);

// 删除轮播图
router.delete('/delete/:id', bannerController.deleteBanner);

// 更新轮播图状态
router.put('/status/:id', bannerController.updateBannerStatus);

// 清理轮播图缓存
router.post('/clear-cache', bannerController.clearBannerCache);

module.exports = router; 