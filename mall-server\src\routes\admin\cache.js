const Router = require('@koa/router');
const { apiCache } = require('../../middleware');

const router = new Router();

// 清除指定类型的缓存
router.post('/clear', async (ctx) => {
  try {
    const { type } = ctx.request.body?.type || ctx.query?.type || {};
    
    console.log('清除缓存请求:', { type, body: ctx.request.body, query: ctx.query });
    
    let clearedCount = 0;
    
    switch (type) {
      case 'category':
        clearedCount = await apiCache.clearModuleCache('category');
        break;
      case 'banner':
        clearedCount = await apiCache.clearModuleCache('banner');
        break;
      case 'product':
        clearedCount = await apiCache.clearModuleCache('product');
        break;
      case 'all':
        clearedCount = await apiCache.clearAllCache();
        break;
      default:
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '无效的缓存类型',
          data: null
        };
        return;
    }
    
    console.log(`清除缓存成功，类型: ${type}，数量: ${clearedCount}`);
    
    ctx.body = {
      code: 200,
      message: '缓存清除成功',
      data: {
        type,
        clearedCount
      }
    };
  } catch (error) {
    console.error('清除缓存失败:', error);
    ctx.status = 500;
    ctx.body = {
      code: 500,
      message: '缓存清除失败',
      error: error.message
    };
  }
});

// 获取缓存统计信息
router.get('/stats', async (ctx) => {
  try {
    const stats = await apiCache.getCacheStats();
    
    ctx.body = {
      code: 200,
      message: '获取缓存统计成功',
      data: stats
    };
  } catch (error) {
    console.error('获取缓存统计失败:', error);
    ctx.status = 500;
    ctx.body = {
      code: 500,
      message: '获取缓存统计失败',
      error: error.message
    };
  }
});

// 清除指定模块的缓存
router.delete('/module/:module', async (ctx) => {
  try {
    const { module } = ctx.params;
    const clearedCount = await apiCache.clearModuleCache(module);
    
    ctx.body = {
      code: 200,
      message: '模块缓存清除成功',
      data: {
        module,
        clearedCount
      }
    };
  } catch (error) {
    console.error('清除模块缓存失败:', error);
    ctx.status = 500;
    ctx.body = {
      code: 500,
      message: '清除模块缓存失败',
      error: error.message
    };
  }
});

module.exports = router; 