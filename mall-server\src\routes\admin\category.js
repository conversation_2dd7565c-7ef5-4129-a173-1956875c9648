const Router = require('@koa/router');
const categoryController = require('../../controllers/admin/category');

const router = new Router();

// 获取分类列表 (根路径)
router.get('/', categoryController.getCategoryList);

// 获取分类列表
router.get('/list', categoryController.getCategoryList);

// 获取分类详情
router.get('/detail/:id', categoryController.getCategoryDetail);

// 添加分类
router.post('/add', categoryController.addCategory);

// 更新分类
router.put('/update/:id', categoryController.updateCategory);

// 删除分类
router.delete('/delete/:id', categoryController.deleteCategory);

// 更新分类状态
router.put('/status/:id', categoryController.updateCategoryStatus);

// 清理分类缓存
router.post('/clear-cache', categoryController.clearCategoryCache);

module.exports = router; 