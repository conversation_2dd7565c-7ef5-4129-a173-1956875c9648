const Router = require('@koa/router');
const discountController = require('../../controllers/admin/discount');

const router = new Router();

// 获取折扣列表
router.get('/list', discountController.getDiscountList);

// 获取折扣详情
router.get('/detail/:id', discountController.getDiscountDetail);

// 创建折扣
router.post('/create', discountController.createDiscount);

// 更新折扣
router.put('/update/:id', discountController.updateDiscount);

// 删除折扣
router.delete('/delete/:id', discountController.deleteDiscount);

// 更新折扣状态
router.put('/status/:id', discountController.updateDiscountStatus);

// 获取折扣统计信息
router.get('/stats', discountController.getDiscountStats);

module.exports = router;
