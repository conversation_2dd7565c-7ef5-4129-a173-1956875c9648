const Router = require('@koa/router');
const orderController = require('../../controllers/admin/order');

const router = new Router();

// 获取订单列表
router.get('/list', orderController.getOrderList);

// 获取订单详情
router.get('/detail/:id', orderController.getOrderDetail);

// 更新订单状态
router.put('/status/:id', orderController.updateOrderStatus);

// 发货
router.post('/ship/:id', orderController.shipOrder);

// 处理退款
router.post('/refund/:id', orderController.handleRefund);

// 获取订单统计
router.get('/stats', orderController.getOrderStats);

// 批量更新订单状态
router.post('/batch-status', orderController.batchUpdateStatus);

// 批量发货
router.post('/batch-ship', orderController.batchShip);

// 导出订单数据
router.get('/export', orderController.exportOrders);

module.exports = router; 