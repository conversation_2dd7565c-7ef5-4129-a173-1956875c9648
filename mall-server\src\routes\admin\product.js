const Router = require('@koa/router');
const productController = require('../../controllers/admin/product');

const router = new Router();

// 获取商品列表
router.get('/list', productController.getProductList);

// 获取热门商品列表
router.get('/hot/list', productController.getHotProductList);

// 获取推荐商品列表
router.get('/recommend/list', productController.getRecommendProductList);

// 获取商品详情
router.get('/detail/:id', productController.getProductDetail);

// 添加商品
router.post('/add', productController.addProduct);

// 更新商品
router.put('/update/:id', productController.updateProduct);

// 删除商品
router.delete('/delete/:id', productController.deleteProduct);

// 更新商品状态
router.put('/status/:id', productController.updateProductStatus);

// 清理商品缓存
router.post('/clear-cache', productController.clearProductCache);

module.exports = router; 