const Router = require('@koa/router');
const returnRequestController = require('../../controllers/admin/returnRequest');

console.log('【路由挂载】加载 admin returnRequest 路由');

const router = new Router();

// 获取退货申请列表
router.get('/', returnRequestController.getReturnRequestList);

// 获取退货申请详情
router.get('/detail/:id', returnRequestController.getReturnRequestDetail);

// 审核退货申请
router.put('/approve/:id', returnRequestController.approveReturnRequest);

// 确认收货
router.put('/receive/:id', returnRequestController.confirmReceive);

// 验收商品
router.put('/inspect/:id', returnRequestController.inspectGoods);

// 处理退款
router.put('/refund/:id', returnRequestController.processRefund);

// 获取退货统计数据
router.get('/statistics', returnRequestController.getReturnStatistics);

// 批量处理退货申请
router.post('/batch', returnRequestController.batchProcessReturns);

// 导出退货数据
router.get('/export', returnRequestController.exportReturnData);

module.exports = router;
