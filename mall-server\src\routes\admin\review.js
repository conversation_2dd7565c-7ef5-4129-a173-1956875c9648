const Router = require('@koa/router');
const { getReviewList, getUserReviews, deleteReview, replyReview, getReviewStats } = require('../../controllers/admin/review');
const auth = require('../../middleware/auth');

const router = new Router({
  prefix: '/api/admin/review'
});

// 获取评论列表
router.get('/list', auth, getReviewList);

// 获取用户评论列表
router.get('/user/:userId', auth, getUserReviews);

// 删除评论
router.delete('/:id', auth, deleteReview);

// 回复评论
router.post('/:id/reply', auth, replyReview);

// 获取评论统计
router.get('/stats', auth, getReviewStats);

module.exports = router; 