const Router = require('@koa/router');
const roleController = require('../../controllers/admin/role');
const auth = require('../../middleware/auth');

const router = new Router();

// 获取角色列表
router.get('/list', auth, roleController.getRoleList);

// 获取所有角色（用于下拉选择）
router.get('/all', auth, roleController.getAllRoles);

// 获取角色详情
router.get('/detail/:id', auth, roleController.getRoleDetail);

// 创建角色
router.post('/create', auth, roleController.createRole);

// 更新角色
router.put('/update/:id', auth, roleController.updateRole);

// 删除角色
router.delete('/delete/:id', auth, roleController.deleteRole);

// 更新角色状态
router.put('/status/:id', auth, roleController.updateRoleStatus);

module.exports = router; 