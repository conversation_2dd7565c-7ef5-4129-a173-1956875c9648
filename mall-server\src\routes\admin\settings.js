const Router = require('@koa/router');
const settingsController = require('../../controllers/admin/settings');

const router = new Router();

// 基本设置
router.get('/basic', settingsController.getBasicSettings);
router.put('/basic', settingsController.updateBasicSettings);

// 支付设置
router.get('/payment', settingsController.getPaymentSettings);
router.put('/payment', settingsController.updatePaymentSettings);

// 物流设置
router.get('/shipping', settingsController.getShippingSettings);
router.put('/shipping', settingsController.updateShippingSettings);

// 短信设置
router.get('/sms', settingsController.getSmsSettings);
router.put('/sms', settingsController.updateSmsSettings);

// 邮件设置
router.get('/email', settingsController.getEmailSettings);
router.put('/email', settingsController.updateEmailSettings);

// 兼容旧接口
router.get('/', settingsController.getSettings);
router.put('/', settingsController.updateSettings);

module.exports = router;