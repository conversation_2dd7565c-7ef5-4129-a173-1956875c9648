const Router = require('@koa/router');
const userController = require('../../controllers/admin/user');

const router = new Router();

// 获取用户列表
router.get('/list', userController.getUserList);

// 获取用户详情
router.get('/detail/:id', userController.getUserDetail);

// 更新用户状态
router.put('/status/:id', userController.updateUserStatus);

// 删除用户
router.delete('/delete/:id', userController.deleteUser);

module.exports = router; 