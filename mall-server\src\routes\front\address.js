const Router = require('@koa/router');
const addressController = require('../../controllers/front/address');
const jwt = require('jsonwebtoken');
const config = require('../../config');

const router = new Router();

// 认证中间件
const authMiddleware = async (ctx, next) => {
  const token = ctx.headers.authorization?.replace('Bearer ', '');

  if (!token) {
    ctx.status = 401;
    ctx.body = {
      code: 401,
      message: '未提供认证令牌'
    };
    return;
  }

  try {
    const decoded = jwt.verify(token, config.jwtSecret);
    ctx.state.user = {
      id: decoded.userId,
      userId: decoded.userId,
      openid: decoded.openid
    };
    await next();
  } catch (error) {
    ctx.status = 401;
    ctx.body = {
      code: 401,
      message: '认证令牌无效或已过期'
    };
  }
};

// 获取地址列表（需要认证）
router.get('/list', authMiddleware, addressController.getAddressList);

// 添加地址（需要认证）
router.post('/add', authMiddleware, addressController.addAddress);

// 更新地址（需要认证）
router.put('/update/:id', authMiddleware, addressController.updateAddress);

// 删除地址（需要认证）
router.delete('/delete/:id', authMiddleware, addressController.deleteAddress);

// 设置默认地址（需要认证）
router.put('/default/:id', authMiddleware, addressController.setDefaultAddress);

// 获取地址详情（需要认证）
router.get('/detail/:id', authMiddleware, addressController.getAddressDetail);

module.exports = router; 