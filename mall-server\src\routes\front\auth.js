const Router = require('@koa/router');
const axios = require('axios');
const jwt = require('jsonwebtoken');
const { User } = require('../../models');
const config = require('../../config');

const router = new Router();

/**
 * 测试请求体解析
 */
router.post('/test-body', async (ctx) => {
  console.log('测试请求体解析');
  console.log('请求体:', ctx.request.body);
  console.log('请求头:', ctx.request.headers);

  ctx.body = {
    code: 200,
    message: '测试成功',
    data: {
      body: ctx.request.body,
      hasBody: !!ctx.request.body,
      bodyType: typeof ctx.request.body
    }
  };
});

/**
 * 开发环境模拟登录
 */
router.post('/dev-login', async (ctx) => {
  try {
    console.log('收到开发环境模拟登录请求');

    const { User } = require('../../models');
    const jwt = require('jsonwebtoken');
    const config = require('../../config');

    // 创建或获取测试用户
    let user = await User.findOne({
      where: { openid: 'dev_test_openid_123' }
    });

    if (!user) {
      user = await User.create({
        openid: 'dev_test_openid_123',
        nickname: '测试用户',
        avatar: 'https://example.com/avatar.jpg',
        userInfo: JSON.stringify({
          nickName: '测试用户',
          avatarUrl: 'https://example.com/avatar.jpg',
          gender: 1
        })
      });
    }

    // 生成JWT token
    const token = jwt.sign(
      {
        userId: user.id,
        openid: user.openid
      },
      config.jwtSecret,
      { expiresIn: '7d' }
    );

    // 安全解析用户信息
    let parsedUserInfo = null;
    try {
      if (user.userInfo && typeof user.userInfo === 'string') {
        parsedUserInfo = JSON.parse(user.userInfo);
      } else if (user.userInfo && typeof user.userInfo === 'object') {
        parsedUserInfo = user.userInfo;
      }
    } catch (e) {
      console.warn('解析用户信息失败:', e.message);
      parsedUserInfo = null;
    }

    ctx.body = {
      code: 200,
      message: '登录成功',
      data: {
        token,
        openid: user.openid,
        isNewUser: false,
        userInfo: parsedUserInfo
      }
    };

  } catch (error) {
    console.error('开发环境登录失败:', error);
    ctx.status = 500;
    ctx.body = {
      code: 500,
      message: '登录失败',
      error: error.message
    };
  }
});

/**
 * 静默登录
 * 通过微信 code 获取 openid，无需用户授权
 */
router.post('/silent-login', async (ctx) => {
  try {
    console.log('静默登录请求开始');
    console.log('请求头:', ctx.request.headers);
    console.log('请求体:', ctx.request.body);
    console.log('请求方法:', ctx.request.method);
    console.log('请求URL:', ctx.request.url);
    
    // 检查请求体是否存在
    if (!ctx.request.body) {
      console.error('请求体为空');
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: '请求体为空，请检查Content-Type设置'
      };
      return;
    }
    
    const { code } = ctx.request.body;
    
    if (!code) {
      console.error('缺少code参数');
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: '缺少登录凭证'
      };
      return;
    }

    console.log('静默登录开始，code:', code);

    // 检查微信配置
    console.log('微信配置检查:', {
      wxAppId: config.wxAppId,
      wxAppSecretLength: config.wxAppSecret ? config.wxAppSecret.length : 0,
      wxAppSecretPrefix: config.wxAppSecret ? config.wxAppSecret.substring(0, 8) + '...' : 'undefined'
    });

    if (!config.wxAppId || !config.wxAppSecret) {
      console.error('微信配置缺失');
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '微信配置错误'
      };
      return;
    }

    // 调用微信接口获取 openid 和 session_key
    const response = await axios.get('https://api.weixin.qq.com/sns/jscode2session', {
      params: {
        appid: config.wxAppId,
        secret: config.wxAppSecret,
        js_code: code,
        grant_type: 'authorization_code'
      },
      timeout: 10000
    });

    console.log('微信API响应:', response.data);

    const { openid, session_key, unionid, errcode, errmsg } = response.data;

    if (errcode) {
      console.error('微信API错误:', errcode, errmsg);
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: '获取用户身份失败',
        error: errmsg
      };
      return;
    }

    if (!openid) {
      console.error('未获取到openid');
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: '获取用户身份失败',
        error: response.data.errmsg || '未获取到openid'
      };
      return;
    }

    console.log('微信返回数据:', { openid, unionid });

    // 查找或创建用户
    let user = await User.findOne({ where: { openid } });
    let isNewUser = false;

    if (!user) {
      // 新用户，创建用户记录
      user = await User.create({
        openid,
        unionid: unionid || null,
        status: 'active',
        lastLoginAt: new Date()
      });
      isNewUser = true;
      console.log('创建新用户:', user.id);
    } else {
      // 老用户，更新登录时间
      await user.update({
        lastLoginAt: new Date()
      });
      console.log('老用户登录:', user.id);
    }

    // 生成 JWT token
    const token = jwt.sign(
      { 
        userId: user.id, 
        openid: user.openid,
        type: 'user'
      }, 
      config.jwtSecret, 
      { expiresIn: '7d' }
    );

    console.log('登录成功，用户ID:', user.id);

    ctx.body = {
      code: 200,
      message: '登录成功',
      data: {
        token,
        openid: user.openid,
        isNewUser,
        userInfo: user.userInfo ? JSON.parse(user.userInfo) : null
      }
    };

  } catch (error) {
    console.error('静默登录失败:', error);
    
    if (error.code === 'ECONNABORTED') {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '微信服务超时，请稍后重试'
      };
    } else if (error.response) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '微信服务异常',
        error: error.response.data
      };
    } else {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '登录失败',
        error: error.message
      };
    }
  }
});

/**
 * 更新用户信息
 * 需要用户主动授权，获取头像、昵称等信息
 */
router.post('/update-user-info', async (ctx) => {
  try {
    // 验证 token
    const token = ctx.headers.authorization?.replace('Bearer ', '');
    if (!token) {
      ctx.status = 401;
      ctx.body = {
        code: 401,
        message: '未授权访问'
      };
      return;
    }

    const decoded = jwt.verify(token, config.jwtSecret);
    const { userId } = decoded;

    console.log('请求体内容:', ctx.request.body);
    console.log('请求体类型:', typeof ctx.request.body);

    const requestBody = ctx.request.body || {};

    // 支持两种格式：{userInfo: {...}} 或直接传递用户信息
    let userInfo = requestBody.userInfo || requestBody;

    // 如果直接传递了用户信息字段，构造userInfo对象
    if (!requestBody.userInfo && (requestBody.nickName || requestBody.avatarUrl || requestBody.gender !== undefined)) {
      userInfo = {
        nickName: requestBody.nickName,
        avatarUrl: requestBody.avatarUrl,
        gender: requestBody.gender
      };
    }

    if (!userInfo || (typeof userInfo === 'object' && Object.keys(userInfo).length === 0)) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: '缺少用户信息'
      };
      return;
    }

    console.log('更新用户信息:', { userId, userInfo });

    // 查找用户
    const user = await User.findByPk(userId);
    if (!user) {
      ctx.status = 404;
      ctx.body = {
        code: 404,
        message: '用户不存在'
      };
      return;
    }

    // 更新用户信息
    const updatedUser = await user.update({
      userInfo: JSON.stringify(userInfo),
      nickname: userInfo.nickName || userInfo.nickname,
      avatar: userInfo.avatarUrl || userInfo.avatar,
      gender: userInfo.gender || 0
    });

    ctx.body = {
      code: 200,
      message: '用户信息更新成功',
      data: {
        userInfo: userInfo
      }
    };

  } catch (error) {
    console.error('更新用户信息失败:', error);
    ctx.status = 500;
    ctx.body = {
      code: 500,
      message: '更新失败',
      error: error.message
    };
  }
});

/**
 * 退出登录
 */
router.post('/logout', async (ctx) => {
  try {
    // 验证 token
    const token = ctx.headers.authorization?.replace('Bearer ', '');
    if (!token) {
      ctx.status = 401;
      ctx.body = {
        code: 401,
        message: '未授权访问'
      };
      return;
    }

    const decoded = jwt.verify(token, config.jwtSecret);
    const { userId } = decoded;

    console.log('用户退出登录:', userId);

    // 这里可以添加一些退出登录的逻辑
    // 比如记录退出时间、清除某些缓存等

    ctx.body = {
      code: 200,
      message: '退出登录成功'
    };

  } catch (error) {
    console.error('退出登录失败:', error);
    ctx.status = 500;
    ctx.body = {
      code: 500,
      message: '退出失败',
      error: error.message
    };
  }
});

/**
 * 获取用户资料
 */
router.get('/profile', async (ctx) => {
  try {
    // 验证 token
    const token = ctx.headers.authorization?.replace('Bearer ', '');
    if (!token) {
      ctx.status = 401;
      ctx.body = {
        code: 401,
        message: '未授权访问'
      };
      return;
    }

    const decoded = jwt.verify(token, config.jwtSecret);
    const { userId } = decoded;

    // 查找用户
    const user = await User.findByPk(userId);
    if (!user) {
      ctx.status = 404;
      ctx.body = {
        code: 404,
        message: '用户不存在'
      };
      return;
    }

    // 安全解析用户信息
    let parsedUserInfo = null;
    try {
      if (user.userInfo && typeof user.userInfo === 'string') {
        parsedUserInfo = JSON.parse(user.userInfo);
      } else if (user.userInfo && typeof user.userInfo === 'object') {
        parsedUserInfo = user.userInfo;
      }
    } catch (e) {
      console.warn('解析用户信息失败:', e.message);
      parsedUserInfo = null;
    }

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: {
        id: user.id,
        openid: user.openid,
        nickname: user.nickname,
        avatar: user.avatar,
        userInfo: parsedUserInfo,
        status: user.status,
        created_at: user.created_at,
        updated_at: user.updated_at
      }
    };

  } catch (error) {
    console.error('获取用户资料失败:', error);
    ctx.status = 500;
    ctx.body = {
      code: 500,
      message: '获取失败',
      error: error.message
    };
  }
});

/**
 * 开发环境测试登录接口
 * 仅在开发环境下可用，用于测试小程序功能
 */
router.post('/test-login', async (ctx) => {
  try {
    // 只在开发环境提供此接口
    if (config.env !== 'development') {
      ctx.status = 404;
      ctx.body = {
        code: 404,
        message: '接口不存在'
      };
      return;
    }

    console.log('测试登录请求:', ctx.request.body);
    
    const { code } = ctx.request.body;
    
    if (!code) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: '缺少登录凭证'
      };
      return;
    }

    // 生成测试用的openid
    const testOpenid = `test_openid_${code.slice(-8)}`;
    
    // 查找或创建测试用户
    let user = await User.findOne({
      where: { openid: testOpenid }
    });

    let isNewUser = false;
    if (!user) {
      isNewUser = true;
      user = await User.create({
        openid: testOpenid,
        status: 'active',
        lastLoginAt: new Date()
      });
      console.log('创建测试用户:', testOpenid);
    } else {
      // 更新最后登录时间
      await user.update({
        lastLoginAt: new Date()
      });
      console.log('测试用户登录:', testOpenid);
    }

    // 生成 JWT token
    const token = jwt.sign(
      { 
        userId: user.id, 
        openid: user.openid,
        type: 'user'
      }, 
      config.jwtSecret, 
      { expiresIn: '7d' }
    );

    ctx.body = {
      code: 200,
      message: '测试登录成功',
      data: {
        token,
        openid: user.openid,
        isNewUser,
        userId: user.id
      }
    };

    console.log('测试登录成功:', testOpenid);

  } catch (error) {
    console.error('测试登录失败:', error);
    ctx.status = 500;
    ctx.body = {
      code: 500,
      message: '测试登录失败',
      error: error.message
    };
  }
});

module.exports = router; 