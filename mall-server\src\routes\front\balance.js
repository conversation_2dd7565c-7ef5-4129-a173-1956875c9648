const Router = require('@koa/router');
const jwt = require('jsonwebtoken');
const config = require('../../config');
const { User } = require('../../models');

const router = new Router();

// 认证中间件
const authMiddleware = async (ctx, next) => {
  const token = ctx.headers.authorization?.replace('Bearer ', '');

  if (!token) {
    ctx.status = 401;
    ctx.body = {
      success: false,
      message: '请先登录',
      code: 401
    };
    return;
  }

  try {
    const decoded = jwt.verify(token, config.jwtSecret);
    const user = await User.findByPk(decoded.userId);
    
    if (!user) {
      ctx.status = 401;
      ctx.body = {
        success: false,
        message: '用户不存在',
        code: 401
      };
      return;
    }

    ctx.state.user = {
      id: user.id,
      userId: user.id,
      openid: user.openid,
      nickname: user.nickname
    };
    
    await next();
  } catch (error) {
    ctx.status = 401;
    ctx.body = {
      success: false,
      message: '认证失败',
      code: 401
    };
  }
};

// 获取用户余额信息
router.get('/info', authMiddleware, async (ctx) => {
  try {
    const userId = ctx.state.user.id;
    
    // 查询用户余额信息
    const user = await User.findByPk(userId, {
      attributes: ['id', 'balance', 'points', 'user_level']
    });

    if (!user) {
      ctx.status = 404;
      ctx.body = {
        success: false,
        message: '用户不存在'
      };
      return;
    }

    // 模拟余额记录（实际应该从余额记录表查询）
    const recentRecords = [
      {
        id: 1,
        type: 1,
        amount: '100.00',
        balance_after: user.balance,
        remark: '账户充值',
        created_at: new Date()
      }
    ];

    ctx.body = {
      success: true,
      data: {
        userId: user.id,
        balance: parseFloat(user.balance || 0).toFixed(2),
        balanceFloat: parseFloat(user.balance || 0),
        recentRecords,
        canPay: parseFloat(user.balance || 0) > 0,
        lastUpdated: new Date()
      },
      message: '获取余额信息成功'
    };
  } catch (error) {
    console.error('获取余额信息失败:', error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      message: '获取余额信息失败'
    };
  }
});

// 模拟用户充值
router.post('/recharge', authMiddleware, async (ctx) => {
  try {
    const userId = ctx.state.user.id;
    const { amount, paymentMethod = 'wechat', remark } = ctx.request.body;

    // 参数验证
    if (!amount || isNaN(amount) || parseFloat(amount) <= 0) {
      ctx.status = 400;
      ctx.body = {
        success: false,
        message: '充值金额必须大于0'
      };
      return;
    }

    if (parseFloat(amount) > 10000) {
      ctx.status = 400;
      ctx.body = {
        success: false,
        message: '单次充值金额不能超过10000元'
      };
      return;
    }

    // 查询用户当前余额
    const user = await User.findByPk(userId);
    if (!user) {
      ctx.status = 404;
      ctx.body = {
        success: false,
        message: '用户不存在'
      };
      return;
    }

    const currentBalance = parseFloat(user.balance || 0);
    const rechargeAmount = parseFloat(amount);
    const newBalance = currentBalance + rechargeAmount;

    // 更新用户余额
    await user.update({
      balance: newBalance
    });

    // 生成充值订单号
    const orderNo = 'RC' + Date.now() + Math.random().toString(36).substr(2, 6).toUpperCase();

    ctx.body = {
      success: true,
      data: {
        rechargeId: Date.now(),
        orderNo,
        amount: rechargeAmount.toFixed(2),
        newBalance: newBalance.toFixed(2),
        paymentMethod,
        message: '充值成功'
      },
      message: '充值成功'
    };
  } catch (error) {
    console.error('充值失败:', error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      message: '充值失败'
    };
  }
});

// 获取充值记录
router.get('/recharge/history', authMiddleware, async (ctx) => {
  try {
    const { page = 1, pageSize = 10 } = ctx.query;
    
    // 模拟充值记录数据
    const mockData = {
      list: [
        {
          id: 1,
          order_no: 'RC1690123456789123',
          total_amount: '100.00',
          payment_method: 1,
          payment_status: 1,
          created_at: new Date()
        }
      ],
      total: 1,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    };

    ctx.body = {
      success: true,
      data: mockData,
      message: '获取充值记录成功'
    };
  } catch (error) {
    console.error('获取充值记录失败:', error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      message: '获取充值记录失败'
    };
  }
});

// 获取余额变动记录
router.get('/history', authMiddleware, async (ctx) => {
  try {
    const { page = 1, pageSize = 10 } = ctx.query;
    
    // 模拟余额记录数据
    const mockData = {
      list: [
        {
          id: 1,
          type: 1,
          amount: '100.00',
          balance_after: '100.00',
          source: 1,
          remark: '账户充值',
          created_at: new Date()
        }
      ],
      total: 1,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    };

    ctx.body = {
      success: true,
      data: mockData,
      message: '获取余额记录成功'
    };
  } catch (error) {
    console.error('获取余额记录失败:', error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      message: '获取余额记录失败'
    };
  }
});

module.exports = router;
