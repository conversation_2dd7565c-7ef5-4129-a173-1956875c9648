const Router = require('@koa/router');
const cartController = require('../../controllers/front/cart');

const router = new Router();

// 获取购物车列表
router.get('/list', cartController.getCartList);

// 添加商品到购物车
router.post('/add', cartController.addToCart);

// 更新购物车商品数量
router.put('/update', cartController.updateCartItem);

// 删除购物车商品
router.delete('/remove/:cartItemId', cartController.removeFromCart);

// 清空购物车
router.delete('/clear', cartController.clearCart);

// 获取购物车商品数量
router.get('/count', cartController.getCartCount);

module.exports = router; 