// 购物车路由
const Router = require('@koa/router');
const cartController = require('../../controllers/front/cartController');

const router = new Router();

// === 购物车基础功能 ===
router.post('/add', cartController.addToCart);                    // 添加商品到购物车
router.get('/list', cartController.getCartList);                  // 获取购物车列表
router.put('/:cart_id/quantity', cartController.updateQuantity);  // 更新商品数量
router.delete('/remove', cartController.removeFromCart);          // 删除购物车商品
router.get('/count', cartController.getCartCount);                // 获取购物车商品数量

// === 购物车选择功能 ===
router.put('/selection', cartController.updateSelection);         // 更新商品选中状态
router.put('/select-all', cartController.selectAll);              // 全选/取消全选
router.get('/selected', cartController.getSelectedItems);         // 获取选中商品信息

// === 购物车管理功能 ===
router.delete('/clear', cartController.clearCart);                // 清空购物车
router.get('/check-stock', cartController.checkStock);            // 检查商品库存
router.post('/batch', cartController.batchOperation);             // 批量操作

// === 购物车扩展功能 ===
router.get('/recommendations', cartController.getRecommendations); // 购物车商品推荐

module.exports = router;
