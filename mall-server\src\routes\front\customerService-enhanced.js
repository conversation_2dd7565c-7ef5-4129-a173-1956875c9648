// 智能客服系统路由 - 优化完善版
const Router = require('@koa/router');
const customerServiceController = require('../../controllers/front/customerService-enhanced');

const router = new Router();

// === 智能客服会话管理 ===
router.post('/sessions/smart', customerServiceController.smartCreateSession);                // 智能创建客服会话
router.post('/sessions/:session_id/messages/smart', customerServiceController.smartSendMessage); // 智能发送消息
router.get('/sessions/:session_id/messages', customerServiceController.getSessionMessages);  // 获取会话消息（带分析）
router.get('/sessions', customerServiceController.getUserSessions);                          // 获取用户会话列表（带统计）
router.post('/sessions/:session_id/assign', customerServiceController.smartAssignSession);   // 智能会话分配
router.put('/sessions/:session_id/close', customerServiceController.closeSession);           // 关闭会话（带总结）
router.post('/sessions/:session_id/rate', customerServiceController.rateSession);            // 评价会话（带分析）
router.post('/sessions/:session_id/upload', customerServiceController.smartUploadFile);      // 智能文件上传

// === 智能客服辅助功能 ===
router.get('/faq/smart', customerServiceController.getSmartFAQ);                             // 获取智能FAQ
router.get('/auto-reply/smart', customerServiceController.getSmartAutoReply);                // 智能客服回复
router.get('/stats', customerServiceController.getServiceStats);                             // 获取客服统计

// === 兼容性路由（保持向后兼容） ===
router.post('/sessions', customerServiceController.smartCreateSession);                      // 创建客服会话（兼容）
router.post('/sessions/:session_id/messages', customerServiceController.smartSendMessage);   // 发送消息（兼容）
router.get('/faq', customerServiceController.getSmartFAQ);                                   // 获取FAQ（兼容）
router.get('/auto-reply', customerServiceController.getSmartAutoReply);                      // 自动回复（兼容）

module.exports = router;
