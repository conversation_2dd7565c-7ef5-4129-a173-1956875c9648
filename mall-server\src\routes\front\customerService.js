// 客服系统路由
const Router = require('@koa/router');
const customerServiceController = require('../../controllers/front/customerService');

const router = new Router();

// === 客服会话管理 ===
router.post('/sessions', customerServiceController.createSession);               // 创建客服会话
router.get('/sessions', customerServiceController.getUserSessions);             // 获取用户会话列表
router.post('/sessions/:session_id/messages', customerServiceController.sendMessage); // 发送消息
router.get('/sessions/:session_id/messages', customerServiceController.getSessionMessages); // 获取会话消息
router.put('/sessions/:session_id/close', customerServiceController.closeSession); // 关闭会话
router.post('/sessions/:session_id/rate', customerServiceController.rateSession); // 评价会话
router.post('/sessions/:session_id/upload', customerServiceController.uploadFile); // 上传文件

// === 客服辅助功能 ===
router.get('/faq', customerServiceController.getFAQ);                           // 获取常见问题
router.get('/auto-reply', customerServiceController.getAutoReply);              // 智能客服回复

module.exports = router;
