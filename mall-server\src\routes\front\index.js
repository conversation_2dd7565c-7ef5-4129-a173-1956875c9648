const Router = require('@koa/router');
const authRoutes = require('./auth');
const userRoutes = require('./user');
const productRoutes = require('./product');
const cartRoutes = require('./cart');
const orderRoutes = require('./order');
const addressRoutes = require('./address');
const paymentRoutes = require('./payment');
const bannerRoutes = require('./banner');
const categoryRoutes = require('./category');
const searchRoutes = require('./search');
const testRoutes = require('./test');
const reviewRoutes = require('./review');
const balanceRoutes = require('./balance');
const memberRoutes = require('./member');
const returnRequestRoutes = require('./returnRequest');

const router = new Router();

// 认证相关路由
router.use('/auth', authRoutes.routes(), authRoutes.allowedMethods());

// 用户相关路由
router.use('/user', userRoutes.routes(), userRoutes.allowedMethods());

// 商品相关路由
router.use('/product', productRoutes.routes(), productRoutes.allowedMethods());

// 购物车相关路由
router.use('/cart', cartRoutes.routes(), cartRoutes.allowedMethods());

// 订单相关路由
router.use('/order', orderRoutes.routes(), orderRoutes.allowedMethods());

// 地址相关路由
router.use('/address', addressRoutes.routes(), addressRoutes.allowedMethods());

// 支付相关路由
router.use('/payment', paymentRoutes.routes(), paymentRoutes.allowedMethods());

// 轮播图相关路由
router.use('/banner', bannerRoutes.routes(), bannerRoutes.allowedMethods());

// 分类相关路由
router.use('/category', categoryRoutes.routes(), categoryRoutes.allowedMethods());

// 搜索相关路由
router.use('/search', searchRoutes.routes(), searchRoutes.allowedMethods());

// 测试相关路由
router.use('/test', testRoutes.routes(), testRoutes.allowedMethods());

// 评论相关路由
router.use('/review', reviewRoutes.routes(), reviewRoutes.allowedMethods());

// 余额相关路由
router.use('/balance', balanceRoutes.routes(), balanceRoutes.allowedMethods());

// 会员相关路由
router.use('/member', memberRoutes.routes(), memberRoutes.allowedMethods());

// 退货相关路由
router.use('/return', returnRequestRoutes.routes(), returnRequestRoutes.allowedMethods());

module.exports = router;