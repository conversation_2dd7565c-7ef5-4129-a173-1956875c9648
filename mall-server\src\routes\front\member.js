const Router = require('@koa/router');
const jwt = require('jsonwebtoken');
const config = require('../../config');
const { User } = require('../../models');

const router = new Router();

// 认证中间件
const authMiddleware = async (ctx, next) => {
  const token = ctx.headers.authorization?.replace('Bearer ', '');

  if (!token) {
    ctx.status = 401;
    ctx.body = {
      success: false,
      message: '请先登录',
      code: 401
    };
    return;
  }

  try {
    const decoded = jwt.verify(token, config.jwtSecret);
    const user = await User.findByPk(decoded.userId);
    
    if (!user) {
      ctx.status = 401;
      ctx.body = {
        success: false,
        message: '用户不存在',
        code: 401
      };
      return;
    }

    ctx.state.user = {
      id: user.id,
      userId: user.id,
      openid: user.openid,
      nickname: user.nickname
    };
    
    await next();
  } catch (error) {
    ctx.status = 401;
    ctx.body = {
      success: false,
      message: '认证失败',
      code: 401
    };
  }
};

// 可选认证中间件
const optionalAuthMiddleware = async (ctx, next) => {
  const token = ctx.headers.authorization?.replace('Bearer ', '');

  if (token) {
    try {
      const decoded = jwt.verify(token, config.jwtSecret);
      const user = await User.findByPk(decoded.userId);
      
      if (user) {
        ctx.state.user = {
          id: user.id,
          userId: user.id,
          openid: user.openid,
          nickname: user.nickname
        };
      }
    } catch (error) {
      // 可选认证失败不影响后续处理
    }
  }
  
  await next();
};

// 获取用户会员信息
router.get('/info', authMiddleware, async (ctx) => {
  try {
    const userId = ctx.state.user.id;
    
    const user = await User.findByPk(userId, {
      attributes: ['id', 'nickname', 'phone', 'balance', 'points', 'user_level', 'created_at']
    });

    if (!user) {
      ctx.status = 404;
      ctx.body = {
        success: false,
        message: '用户不存在'
      };
      return;
    }

    // 模拟会员等级数据
    const memberLevels = {
      1: { id: 1, name: '普通会员', code: 'bronze', discountRate: 1.00, minPoints: 0 },
      2: { id: 2, name: '白银会员', code: 'silver', discountRate: 0.95, minPoints: 1000 },
      3: { id: 3, name: '黄金会员', code: 'gold', discountRate: 0.90, minPoints: 5000 }
    };

    const currentLevel = memberLevels[user.user_level] || memberLevels[1];
    const userPoints = user.points || 0;

    // 计算下一等级
    let nextLevel = null;
    for (const level of Object.values(memberLevels)) {
      if (level.minPoints > userPoints) {
        nextLevel = level;
        break;
      }
    }

    // 计算升级进度
    let upgradeProgress = { progress: 100, needPoints: 0, progressText: '已达最高等级' };
    if (nextLevel) {
      const needPoints = nextLevel.minPoints - userPoints;
      const progress = Math.min(100, (userPoints / nextLevel.minPoints) * 100);
      upgradeProgress = {
        progress: Math.round(progress),
        needPoints: Math.max(0, needPoints),
        progressText: needPoints > 0 ? `还需${needPoints}积分升级` : '可升级'
      };
    }

    // 模拟会员权益
    const benefits = [
      { id: 1, name: '购物折扣', description: `享受${((1 - currentLevel.discountRate) * 100).toFixed(0)}折优惠`, icon: '💰' },
      { id: 2, name: '积分奖励', description: '购物获得积分奖励', icon: '⭐' },
      { id: 3, name: '专属客服', description: '享受专属客服服务', icon: '👥' }
    ];

    ctx.body = {
      success: true,
      data: {
        userId: user.id,
        nickname: user.nickname,
        phone: user.phone,
        balance: parseFloat(user.balance || 0).toFixed(2),
        points: userPoints,
        currentLevel,
        nextLevel,
        upgradeProgress,
        benefits,
        memberExpireTime: null,
        joinTime: user.created_at,
        isVip: user.user_level > 1
      },
      message: '获取会员信息成功'
    };
  } catch (error) {
    console.error('获取会员信息失败:', error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      message: '获取会员信息失败'
    };
  }
});

// 获取会员等级列表
router.get('/levels', optionalAuthMiddleware, async (ctx) => {
  try {
    // 模拟会员等级数据
    const levels = [
      {
        id: 1,
        code: 'bronze',
        name: '普通会员',
        minPoints: 0,
        discountRate: 1.00,
        description: '享受基础服务',
        sortOrder: 1,
        discountText: '无折扣'
      },
      {
        id: 2,
        code: 'silver',
        name: '白银会员',
        minPoints: 1000,
        discountRate: 0.95,
        description: '享受95折优惠和优先服务',
        sortOrder: 2,
        discountText: '95折'
      },
      {
        id: 3,
        code: 'gold',
        name: '黄金会员',
        minPoints: 5000,
        discountRate: 0.90,
        description: '享受90折优惠和专属服务',
        sortOrder: 3,
        discountText: '90折'
      }
    ];

    ctx.body = {
      success: true,
      data: levels,
      message: '获取会员等级成功'
    };
  } catch (error) {
    console.error('获取会员等级失败:', error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      message: '获取会员等级失败'
    };
  }
});

// 获取会员权益
router.get('/benefits/:levelId', optionalAuthMiddleware, async (ctx) => {
  try {
    const { levelId } = ctx.params;

    if (!levelId || isNaN(levelId)) {
      ctx.status = 400;
      ctx.body = {
        success: false,
        message: '会员等级ID无效'
      };
      return;
    }

    // 模拟权益数据
    const benefitsMap = {
      1: [
        { id: 1, name: '基础服务', description: '享受基础购物服务', icon: '🛍️' }
      ],
      2: [
        { id: 2, name: '95折优惠', description: '全场商品95折', icon: '💰' },
        { id: 3, name: '优先客服', description: '享受优先客服服务', icon: '👥' }
      ],
      3: [
        { id: 4, name: '90折优惠', description: '全场商品90折', icon: '💰' },
        { id: 5, name: '专属客服', description: '享受专属客服服务', icon: '👑' },
        { id: 6, name: '生日特权', description: '生日月专属优惠', icon: '🎂' }
      ]
    };

    const benefits = benefitsMap[levelId] || benefitsMap[1];

    ctx.body = {
      success: true,
      data: benefits,
      message: '获取会员权益成功'
    };
  } catch (error) {
    console.error('获取会员权益失败:', error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      message: '获取会员权益失败'
    };
  }
});

// 获取积分记录
router.get('/points/history', authMiddleware, async (ctx) => {
  try {
    const { page = 1, pageSize = 10 } = ctx.query;
    
    // 模拟积分记录数据
    const mockData = {
      list: [
        {
          id: 1,
          type: 1,
          points: 100,
          points_before: 0,
          points_after: 100,
          source: 1,
          remark: '购物获得积分',
          created_at: new Date()
        }
      ],
      total: 1,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    };

    ctx.body = {
      success: true,
      data: mockData,
      message: '获取积分记录成功'
    };
  } catch (error) {
    console.error('获取积分记录失败:', error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      message: '获取积分记录失败'
    };
  }
});

module.exports = router;
