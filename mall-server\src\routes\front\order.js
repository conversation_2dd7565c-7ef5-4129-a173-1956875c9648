const Router = require('@koa/router');
const orderController = require('../../controllers/front/order');

const router = new Router();

// 创建订单
router.post('/create', orderController.createOrder);

// 获取订单列表
router.get('/list', orderController.getOrderList);

// 获取订单详情
router.get('/detail/:id', orderController.getOrderDetail);

// 取消订单
router.post('/cancel/:id', orderController.cancelOrder);

// 确认收货
router.post('/confirm/:id', orderController.confirmOrder);

// 申请退款
router.post('/refund/:id', orderController.applyRefund);

// 支付订单
router.post('/pay/:id', orderController.payOrder);

// 获取物流信息
router.get('/logistics/:id', orderController.getLogistics);

module.exports = router; 