const Router = require('@koa/router');
const paymentController = require('../../controllers/front/payment');

const router = new Router();

// 创建支付订单
router.post('/create', paymentController.createPayment);

// 支付回调
router.post('/callback', paymentController.paymentCallback);

// 查询支付状态
router.get('/status/:orderId', paymentController.getPaymentStatus);

// 申请退款
router.post('/refund', paymentController.applyRefund);

module.exports = router; 