const Router = require('@koa/router');
const productController = require('../../controllers/front/product');
const { apiCache } = require('../../middleware');

const router = new Router();

// 获取商品列表（带缓存）
router.get('/list', apiCache.cache(300), productController.getProductList);

// 获取商品详情（带缓存）
router.get('/detail/:id', apiCache.cache(600), productController.getProductDetail);

// 搜索商品（带缓存）
router.get('/search', apiCache.cache(180), productController.searchProducts);

// 获取推荐商品（带缓存）
router.get('/recommend', apiCache.cache(300), productController.getRecommendProducts);

// 获取热门商品（带缓存）
router.get('/hot', apiCache.cache(300), productController.getHotProducts);

module.exports = router; 