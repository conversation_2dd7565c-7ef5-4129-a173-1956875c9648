const Router = require('@koa/router');
const returnRequestController = require('../../controllers/front/returnRequest');

console.log('【路由挂载】加载 front returnRequest 路由');

const router = new Router();

// 检查订单是否可以退货
router.get('/check/:orderId', returnRequestController.checkOrderCanReturn);

// 提交退货申请
router.post('/submit', returnRequestController.submitReturnRequest);

// 获取用户退货申请列表
router.get('/list', returnRequestController.getUserReturnRequestList);

// 获取退货申请详情
router.get('/detail/:id', returnRequestController.getReturnRequestDetail);

// 填写退货物流信息
router.put('/express/:id', returnRequestController.updateReturnExpress);

// 取消退货申请
router.put('/cancel/:id', returnRequestController.cancelReturnRequest);

// 获取退货配置信息
router.get('/settings', returnRequestController.getReturnSettings);

module.exports = router;
