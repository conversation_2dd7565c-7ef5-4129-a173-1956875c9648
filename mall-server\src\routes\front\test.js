const Router = require('@koa/router');

const router = new Router();

// 测试POST接口
router.post('/post', async (ctx) => {
  try {
    console.log('=== 测试POST接口被调用 ===');
    console.log('请求方法:', ctx.request.method);
    console.log('请求URL:', ctx.request.url);
    console.log('请求头:', JSON.stringify(ctx.request.headers, null, 2));
    console.log('请求体类型:', typeof ctx.request.body);
    console.log('请求体:', ctx.request.body);
    console.log('请求体是否为undefined:', ctx.request.body === undefined);
    console.log('请求体是否为null:', ctx.request.body === null);
    console.log('请求体keys:', ctx.request.body ? Object.keys(ctx.request.body) : 'N/A');
    
    ctx.body = {
      code: 200,
      message: '测试成功',
      data: {
        receivedBody: ctx.request.body,
        bodyType: typeof ctx.request.body,
        bodyIsUndefined: ctx.request.body === undefined,
        bodyIsNull: ctx.request.body === null,
        method: ctx.request.method,
        url: ctx.request.url,
        contentType: ctx.request.headers['content-type']
      }
    };
  } catch (error) {
    console.error('测试接口错误:', error);
    ctx.status = 500;
    ctx.body = {
      code: 500,
      message: '测试失败',
      error: error.message
    };
  }
});

// 测试GET接口
router.get('/get', async (ctx) => {
  try {
    console.log('=== 测试GET接口被调用 ===');
    
    ctx.body = {
      code: 200,
      message: 'GET测试成功',
      data: {
        query: ctx.query,
        method: ctx.request.method,
        url: ctx.request.url
      }
    };
  } catch (error) {
    console.error('测试接口错误:', error);
    ctx.status = 500;
    ctx.body = {
      code: 500,
      message: '测试失败',
      error: error.message
    };
  }
});

module.exports = router; 