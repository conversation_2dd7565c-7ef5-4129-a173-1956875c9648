const Router = require('@koa/router');
const userController = require('../../controllers/front/user');
const jwt = require('jsonwebtoken');
const config = require('../../config');

const router = new Router();

// 认证中间件
const authMiddleware = async (ctx, next) => {
  const token = ctx.headers.authorization?.replace('Bearer ', '');

  if (!token) {
    ctx.status = 401;
    ctx.body = {
      code: 401,
      message: '未提供认证令牌'
    };
    return;
  }

  try {
    const decoded = jwt.verify(token, config.jwtSecret);
    ctx.state.user = {
      id: decoded.userId,
      userId: decoded.userId,
      openid: decoded.openid
    };
    await next();
  } catch (error) {
    ctx.status = 401;
    ctx.body = {
      code: 401,
      message: '认证令牌无效或已过期'
    };
  }
};

// 用户登录
router.post('/login', userController.login);

// 用户注册
router.post('/register', userController.register);

// 用户登出
router.post('/logout', userController.logout);

// 获取用户信息（需要认证）
router.get('/info', authMiddleware, userController.getUserInfo);

// 获取用户资料（兼容性接口，需要认证）
router.get('/profile', authMiddleware, userController.getUserInfo);

// 更新用户信息（需要认证）
router.put('/info', authMiddleware, userController.updateUserInfo);

// 修改密码
router.put('/password', userController.changePassword);

module.exports = router; 