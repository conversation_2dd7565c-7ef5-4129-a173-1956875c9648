// 用户功能优化路由 - 智能增强版
const Router = require('@koa/router');
const userFeaturesController = require('../../controllers/front/userFeatures-enhanced');

const router = new Router();

// === 智能收藏功能路由 ===
router.post('/favorites/smart', userFeaturesController.smartAddFavorite);                    // 智能添加收藏
router.get('/favorites/smart', userFeaturesController.getSmartFavoriteList);                 // 获取智能收藏列表
router.get('/favorites/trends', userFeaturesController.getFavoriteTrends);                   // 获取收藏趋势
router.get('/favorites/value-analysis', userFeaturesController.getFavoriteValueAnalysis);    // 获取收藏价值分析

// === 智能浏览历史路由 ===
router.post('/browse-history/smart', userFeaturesController.smartRecordBrowse);              // 智能记录浏览
router.get('/browse-history/smart', userFeaturesController.getSmartBrowseHistory);           // 获取智能浏览历史
router.get('/browse-history/recommendations/smart', userFeaturesController.getSmartRecommendations); // 获取智能推荐
router.get('/browse-history/heatmap', userFeaturesController.getBrowseHeatmap);              // 获取浏览热力图

// === 智能商品对比路由 ===
router.post('/compare/smart', userFeaturesController.smartAddToCompare);                     // 智能添加到对比
router.get('/compare/smart/details', userFeaturesController.getSmartCompareDetails);         // 获取智能对比详情
router.get('/compare/suggestions', userFeaturesController.getCompareSuggestions);            // 获取对比建议

// === 智能分享功能路由 ===
router.get('/share/smart/generate', userFeaturesController.smartGenerateShareContent);       // 智能生成分享内容
router.post('/share/smart/record', userFeaturesController.smartRecordShare);                 // 智能记录分享
router.get('/share/effect-analysis', userFeaturesController.getShareEffectAnalysis);         // 获取分享效果分析
router.get('/share/poster/generate', userFeaturesController.generateSharePoster);            // 生成分享海报
router.get('/share/ranking', userFeaturesController.getShareRanking);                        // 获取分享排行榜

// === 系统管理路由 ===
router.delete('/cache', userFeaturesController.clearCache);                                  // 清理缓存
router.get('/system/status', userFeaturesController.getSystemStatus);                        // 获取系统状态

// === 兼容性路由（保持向后兼容） ===
router.post('/favorites', userFeaturesController.smartAddFavorite);                          // 添加收藏（兼容）
router.get('/favorites', userFeaturesController.getSmartFavoriteList);                       // 获取收藏列表（兼容）
router.post('/browse-history', userFeaturesController.smartRecordBrowse);                    // 记录浏览（兼容）
router.get('/browse-history', userFeaturesController.getSmartBrowseHistory);                 // 获取浏览历史（兼容）
router.post('/compare', userFeaturesController.smartAddToCompare);                           // 添加对比（兼容）
router.get('/compare/details', userFeaturesController.getSmartCompareDetails);               // 获取对比详情（兼容）
router.get('/share/generate', userFeaturesController.smartGenerateShareContent);             // 生成分享（兼容）
router.post('/share/record', userFeaturesController.smartRecordShare);                       // 记录分享（兼容）

module.exports = router;
