// 用户功能路由 - 收藏、浏览历史、对比、分享
const Router = require('@koa/router');
const userFeaturesController = require('../../controllers/front/userFeatures');

const router = new Router();

// === 商品收藏路由 ===
router.post('/favorites', userFeaturesController.addFavorite);                    // 添加收藏
router.delete('/favorites/:product_id', userFeaturesController.removeFavorite);  // 取消收藏
router.get('/favorites', userFeaturesController.getFavoriteList);                // 获取收藏列表
router.post('/favorites/batch-check', userFeaturesController.batchCheckFavorited); // 批量检查收藏状态
router.delete('/favorites', userFeaturesController.clearFavorites);              // 清空收藏夹

// === 浏览历史路由 ===
router.post('/browse-history', userFeaturesController.recordBrowse);             // 记录浏览历史
router.get('/browse-history', userFeaturesController.getBrowseHistory);          // 获取浏览历史
router.get('/browse-history/recent', userFeaturesController.getRecentBrowsed);   // 获取最近浏览
router.delete('/browse-history', userFeaturesController.clearBrowseHistory);     // 清空浏览历史
router.get('/browse-history/recommendations', userFeaturesController.getRecommendedProducts); // 基于浏览历史推荐

// === 商品对比路由 ===
router.post('/compare', userFeaturesController.addToCompare);                    // 添加到对比
router.delete('/compare/:product_id', userFeaturesController.removeFromCompare); // 从对比中移除
router.get('/compare', userFeaturesController.getCompareList);                   // 获取对比列表
router.get('/compare/details', userFeaturesController.getCompareDetails);        // 获取对比详情
router.delete('/compare', userFeaturesController.clearCompareList);              // 清空对比列表

// === 分享功能路由 ===
router.get('/share/generate', userFeaturesController.generateShareData);         // 生成分享数据
router.post('/share/record', userFeaturesController.recordShare);                // 记录分享
router.get('/share/records', userFeaturesController.getShareRecords);            // 获取分享记录

module.exports = router;
