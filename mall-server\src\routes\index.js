const Router = require('@koa/router');
const frontRoutes = require('./front');
const adminRoutes = require('./admin');
const config = require('../config');

const router = new Router({
  prefix: config.apiPrefix
});

console.log('【路由挂载】主路由初始化，apiPrefix:', config.apiPrefix);

// 健康检查
router.get('/health', async (ctx) => {
  ctx.body = {
    code: 200,
    message: '服务运行正常',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  };
});

console.log('【路由挂载】挂载前端API路由 /front');
// 前端API路由
router.use('/front', frontRoutes.routes(), frontRoutes.allowedMethods());

console.log('【路由挂载】挂载管理后台API路由 /admin');
// 管理后台API路由
router.use('/admin', adminRoutes.routes(), adminRoutes.allowedMethods());

module.exports = router; 