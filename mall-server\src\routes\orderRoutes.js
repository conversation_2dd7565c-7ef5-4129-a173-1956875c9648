// 订单路由配置
const express = require('express');
const router = express.Router();
const OrderController = require('../controllers/OrderController');
const { body, query, param } = require('express-validator');
const authMiddleware = require('../middleware/auth');

// 应用认证中间件到所有订单路由
router.use(authMiddleware);

/**
 * 获取订单列表
 * GET /api/front/order/list
 */
router.get('/list', [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是大于0的整数'),
  query('size')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须是1-100之间的整数'),
  query('status')
    .optional()
    .isIn(['all', 'pending', 'paid', 'shipped', 'delivered', 'completed', 'cancelled', 'refunded'])
    .withMessage('订单状态参数无效'),
  query('keyword')
    .optional()
    .isLength({ max: 50 })
    .withMessage('搜索关键词长度不能超过50个字符')
], OrderController.getOrderList);

/**
 * 获取订单详情
 * GET /api/front/order/:id
 */
router.get('/:id', [
  param('id')
    .isInt({ min: 1 })
    .withMessage('订单ID必须是大于0的整数')
], OrderController.getOrderDetail);

/**
 * 创建订单
 * POST /api/front/order/create
 */
router.post('/create', [
  body('items')
    .isArray({ min: 1 })
    .withMessage('订单项不能为空'),
  body('items.*.productId')
    .isInt({ min: 1 })
    .withMessage('商品ID必须是大于0的整数'),
  body('items.*.productName')
    .notEmpty()
    .isLength({ max: 200 })
    .withMessage('商品名称不能为空且长度不能超过200个字符'),
  body('items.*.unitPrice')
    .isFloat({ min: 0.01 })
    .withMessage('商品单价必须大于0.01'),
  body('items.*.quantity')
    .isInt({ min: 1 })
    .withMessage('商品数量必须是大于0的整数'),
  body('receiverInfo.name')
    .notEmpty()
    .isLength({ max: 50 })
    .withMessage('收货人姓名不能为空且长度不能超过50个字符'),
  body('receiverInfo.phone')
    .notEmpty()
    .matches(/^1[3-9]\d{9}$/)
    .withMessage('收货人手机号格式不正确'),
  body('receiverInfo.address')
    .notEmpty()
    .isLength({ max: 200 })
    .withMessage('收货地址不能为空且长度不能超过200个字符'),
  body('paymentMethod')
    .optional()
    .isIn(['wechat', 'alipay', 'balance', 'cod'])
    .withMessage('支付方式参数无效'),
  body('remark')
    .optional()
    .isLength({ max: 200 })
    .withMessage('订单备注长度不能超过200个字符')
], OrderController.createOrder);

/**
 * 取消订单
 * PUT /api/front/order/:id/cancel
 */
router.put('/:id/cancel', [
  param('id')
    .isInt({ min: 1 })
    .withMessage('订单ID必须是大于0的整数'),
  body('reason')
    .optional()
    .isLength({ max: 200 })
    .withMessage('取消原因长度不能超过200个字符')
], OrderController.cancelOrder);

/**
 * 确认收货
 * PUT /api/front/order/:id/confirm
 */
router.put('/:id/confirm', [
  param('id')
    .isInt({ min: 1 })
    .withMessage('订单ID必须是大于0的整数')
], OrderController.confirmDelivery);

/**
 * 获取订单统计
 * GET /api/front/order/statistics
 */
router.get('/statistics', OrderController.getOrderStatistics);

/**
 * 申请退款
 * POST /api/front/order/:id/refund
 */
router.post('/:id/refund', [
  param('id')
    .isInt({ min: 1 })
    .withMessage('订单ID必须是大于0的整数'),
  body('reason')
    .notEmpty()
    .isLength({ max: 200 })
    .withMessage('退款原因不能为空且长度不能超过200个字符'),
  body('amount')
    .optional()
    .isFloat({ min: 0.01 })
    .withMessage('退款金额必须大于0.01'),
  body('items')
    .optional()
    .isArray()
    .withMessage('退款商品项必须是数组')
], OrderController.applyRefund);

/**
 * 删除订单
 * DELETE /api/front/order/:id
 */
router.delete('/:id', [
  param('id')
    .isInt({ min: 1 })
    .withMessage('订单ID必须是大于0的整数')
], OrderController.deleteOrder);

/**
 * 重新购买
 * POST /api/front/order/:id/rebuy
 */
router.post('/:id/rebuy', [
  param('id')
    .isInt({ min: 1 })
    .withMessage('订单ID必须是大于0的整数')
], async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未登录'
      });
    }
    
    // 获取原订单信息
    const orderResult = await OrderService.getOrderDetail(parseInt(id), userId);
    
    if (!orderResult.success) {
      return res.status(404).json({
        success: false,
        message: '原订单不存在'
      });
    }
    
    // 构建新订单数据
    const originalOrder = orderResult.data;
    const newOrderData = {
      userId,
      items: originalOrder.items.map(item => ({
        productId: item.productId,
        productName: item.productName,
        productImage: item.productImage,
        productSku: item.productSku,
        productSpec: item.productSpec,
        unitPrice: item.unitPrice,
        quantity: item.quantity,
        totalPrice: item.totalPrice,
        actualPrice: item.actualPrice
      })),
      receiverInfo: originalOrder.receiverInfo,
      paymentMethod: originalOrder.paymentMethod,
      remark: '重新购买订单'
    };
    
    // 创建新订单
    const createResult = await OrderService.createOrder(newOrderData);
    
    if (createResult.success) {
      res.status(201).json({
        success: true,
        data: createResult.data,
        message: '重新购买成功'
      });
    } else {
      res.status(400).json({
        success: false,
        message: createResult.message || '重新购买失败'
      });
    }
    
  } catch (error) {
    console.error('重新购买异常:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 获取物流信息
 * GET /api/front/order/:id/logistics
 */
router.get('/:id/logistics', [
  param('id')
    .isInt({ min: 1 })
    .withMessage('订单ID必须是大于0的整数')
], async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未登录'
      });
    }
    
    // 获取订单信息
    const orderResult = await OrderService.getOrderDetail(parseInt(id), userId);
    
    if (!orderResult.success) {
      return res.status(404).json({
        success: false,
        message: '订单不存在'
      });
    }
    
    const order = orderResult.data;
    
    // 模拟物流信息
    const logistics = {
      company: order.shippingInfo.company || '顺丰速运',
      trackingNo: order.shippingInfo.trackingNo || 'SF1234567890',
      status: order.shippingStatus,
      traces: [
        {
          time: '2024-01-20 10:00:00',
          status: '已发货',
          description: '您的订单已从【深圳分拣中心】发出'
        },
        {
          time: '2024-01-20 14:30:00',
          status: '运输中',
          description: '快件到达【广州转运中心】'
        },
        {
          time: '2024-01-21 09:15:00',
          status: '派送中',
          description: '快件正在派送中，派送员：张师傅，电话：138****5678'
        }
      ]
    };
    
    res.json({
      success: true,
      data: logistics,
      message: '获取物流信息成功'
    });
    
  } catch (error) {
    console.error('获取物流信息异常:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

module.exports = router;
