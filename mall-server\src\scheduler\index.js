// 定时任务调度器
const cron = require('node-cron');
const salesReportService = require('../services/salesReport');
const stockAlertService = require('../services/stockAlert');
const autoShippingService = require('../services/autoShipping');
const userBehaviorService = require('../services/userBehavior');

class TaskScheduler {
  
  constructor() {
    this.tasks = new Map();
    this.isRunning = false;
  }

  // 启动所有定时任务
  start() {
    if (this.isRunning) {
      console.log('定时任务调度器已在运行');
      return;
    }

    console.log('启动定时任务调度器...');
    
    // 每天凌晨2点生成昨日报表
    this.addTask('daily-report', '0 2 * * *', async () => {
      try {
        console.log('开始生成昨日销售报表...');
        await salesReportService.autoGenerateReports();
        console.log('昨日销售报表生成完成');
      } catch (error) {
        console.error('生成昨日销售报表失败:', error);
      }
    });

    // 每小时检查库存预警
    this.addTask('stock-check', '0 * * * *', async () => {
      try {
        console.log('开始检查库存预警...');
        const alertCount = await stockAlertService.checkAllProductsStock();
        console.log(`库存检查完成，生成 ${alertCount} 个预警`);
      } catch (error) {
        console.error('检查库存预警失败:', error);
      }
    });

    // 每30分钟处理自动发货
    this.addTask('auto-shipping', '*/30 * * * *', async () => {
      try {
        console.log('开始处理自动发货...');
        const processedCount = await autoShippingService.processAutoShipping();
        console.log(`自动发货处理完成，处理 ${processedCount} 个订单`);
      } catch (error) {
        console.error('自动发货处理失败:', error);
      }
    });

    // 每天凌晨3点清理旧数据
    this.addTask('cleanup', '0 3 * * *', async () => {
      try {
        console.log('开始清理旧数据...');
        
        // 清理90天前的用户行为数据
        const behaviorDeleted = await userBehaviorService.cleanupOldBehaviors(90);
        console.log(`清理了 ${behaviorDeleted} 条用户行为数据`);
        
        // 清理30天前的已解决预警
        const alertDeleted = await stockAlertService.cleanupOldAlerts(30);
        console.log(`清理了 ${alertDeleted} 个旧预警记录`);
        
        console.log('旧数据清理完成');
      } catch (error) {
        console.error('清理旧数据失败:', error);
      }
    });

    // 每周一凌晨1点生成上周报表
    this.addTask('weekly-report', '0 1 * * 1', async () => {
      try {
        console.log('开始生成上周销售报表...');
        const lastWeek = new Date();
        lastWeek.setDate(lastWeek.getDate() - 7);
        await salesReportService.generateWeeklyReport(lastWeek);
        console.log('上周销售报表生成完成');
      } catch (error) {
        console.error('生成上周销售报表失败:', error);
      }
    });

    // 每月1号凌晨1点生成上月报表
    this.addTask('monthly-report', '0 1 1 * *', async () => {
      try {
        console.log('开始生成上月销售报表...');
        const lastMonth = new Date();
        lastMonth.setMonth(lastMonth.getMonth() - 1);
        await salesReportService.generateMonthlyReport(lastMonth);
        console.log('上月销售报表生成完成');
      } catch (error) {
        console.error('生成上月销售报表失败:', error);
      }
    });

    // 每天凌晨4点检查失败的自动发货并重试
    this.addTask('retry-shipping', '0 4 * * *', async () => {
      try {
        console.log('开始重试失败的自动发货...');
        // 这里可以添加重试逻辑
        console.log('重试失败的自动发货完成');
      } catch (error) {
        console.error('重试失败的自动发货失败:', error);
      }
    });

    this.isRunning = true;
    console.log(`定时任务调度器启动完成，共 ${this.tasks.size} 个任务`);
  }

  // 停止所有定时任务
  stop() {
    if (!this.isRunning) {
      console.log('定时任务调度器未运行');
      return;
    }

    console.log('停止定时任务调度器...');
    
    this.tasks.forEach((task, name) => {
      if (task) {
        task.stop();
        console.log(`任务 ${name} 已停止`);
      }
    });

    this.tasks.clear();
    this.isRunning = false;
    console.log('定时任务调度器已停止');
  }

  // 添加定时任务
  addTask(name, schedule, handler) {
    try {
      const task = cron.schedule(schedule, handler, {
        scheduled: true,
        timezone: 'Asia/Shanghai'
      });

      this.tasks.set(name, task);
      console.log(`添加定时任务: ${name} (${schedule})`);
      
      return task;
    } catch (error) {
      console.error(`添加定时任务 ${name} 失败:`, error);
      return null;
    }
  }

  // 移除定时任务
  removeTask(name) {
    const task = this.tasks.get(name);
    if (task) {
      task.stop();
      this.tasks.delete(name);
      console.log(`移除定时任务: ${name}`);
      return true;
    }
    return false;
  }

  // 获取任务状态
  getTaskStatus() {
    const status = {
      isRunning: this.isRunning,
      taskCount: this.tasks.size,
      tasks: []
    };

    this.tasks.forEach((task, name) => {
      status.tasks.push({
        name,
        running: task.running
      });
    });

    return status;
  }

  // 手动执行任务
  async executeTask(taskName) {
    try {
      console.log(`手动执行任务: ${taskName}`);
      
      switch (taskName) {
        case 'daily-report':
          await salesReportService.autoGenerateReports();
          break;
        case 'stock-check':
          await stockAlertService.checkAllProductsStock();
          break;
        case 'auto-shipping':
          await autoShippingService.processAutoShipping();
          break;
        case 'cleanup':
          await userBehaviorService.cleanupOldBehaviors(90);
          await stockAlertService.cleanupOldAlerts(30);
          break;
        default:
          throw new Error(`未知任务: ${taskName}`);
      }
      
      console.log(`任务 ${taskName} 执行完成`);
      return true;
    } catch (error) {
      console.error(`执行任务 ${taskName} 失败:`, error);
      throw error;
    }
  }
}

// 创建全局调度器实例
const scheduler = new TaskScheduler();

// 进程退出时停止调度器
process.on('SIGINT', () => {
  console.log('收到退出信号，停止定时任务调度器...');
  scheduler.stop();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('收到终止信号，停止定时任务调度器...');
  scheduler.stop();
  process.exit(0);
});

module.exports = scheduler;
