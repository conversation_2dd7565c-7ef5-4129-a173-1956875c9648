// 优化版定时任务调度器 - 高效简洁版
const cron = require('node-cron');
const stockAlertService = require('../services/stockAlert-optimized');
const autoShippingService = require('../services/autoShipping-optimized');
const { tracker } = require('../middleware/behaviorTracker-optimized');

class OptimizedScheduler {
  
  constructor() {
    this.tasks = new Map();
    this.isRunning = false;
    this.stats = {
      totalRuns: 0,
      successRuns: 0,
      failedRuns: 0,
      lastRun: null
    };
  }

  // 启动调度器
  start() {
    if (this.isRunning) {
      console.log('⚠️ 调度器已在运行');
      return;
    }

    console.log('🚀 启动优化版定时任务调度器...');
    
    // 核心任务 - 更高频率，更重要
    this.addTask('stock-check', '0 */2 * * *', () => this.runStockCheck(), true);
    this.addTask('auto-shipping', '*/15 * * * *', () => this.runAutoShipping(), true);
    this.addTask('behavior-flush', '*/5 * * * *', () => this.runBehaviorFlush(), true);
    
    // 维护任务 - 低频率，后台运行
    this.addTask('cleanup', '0 3 * * *', () => this.runCleanup(), false);
    this.addTask('retry-shipping', '0 */6 * * *', () => this.runRetryShipping(), false);
    
    this.isRunning = true;
    console.log(`✅ 调度器启动完成，共 ${this.tasks.size} 个任务`);
  }

  // 停止调度器
  stop() {
    if (!this.isRunning) return;
    
    console.log('🛑 停止调度器...');
    this.tasks.forEach((task, name) => {
      task.destroy();
      console.log(`📴 任务 ${name} 已停止`);
    });
    
    this.tasks.clear();
    this.isRunning = false;
    console.log('✅ 调度器已停止');
  }

  // 添加任务
  addTask(name, schedule, handler, isCore = false) {
    try {
      const wrappedHandler = this.wrapHandler(name, handler, isCore);
      const task = cron.schedule(schedule, wrappedHandler, {
        scheduled: true,
        timezone: 'Asia/Shanghai'
      });

      this.tasks.set(name, task);
      console.log(`📅 添加任务: ${name} (${schedule}) ${isCore ? '[核心]' : '[维护]'}`);
      
    } catch (error) {
      console.error(`❌ 添加任务 ${name} 失败:`, error.message);
    }
  }

  // 包装处理器
  wrapHandler(name, handler, isCore) {
    return async () => {
      const startTime = Date.now();
      this.stats.totalRuns++;
      
      try {
        console.log(`🔄 开始执行任务: ${name}`);
        await handler();
        
        const duration = Date.now() - startTime;
        this.stats.successRuns++;
        this.stats.lastRun = new Date();
        
        console.log(`✅ 任务 ${name} 执行成功 (${duration}ms)`);
        
      } catch (error) {
        const duration = Date.now() - startTime;
        this.stats.failedRuns++;
        
        console.error(`❌ 任务 ${name} 执行失败 (${duration}ms):`, error.message);
        
        // 核心任务失败时发送告警
        if (isCore) {
          this.sendAlert(name, error);
        }
      }
    };
  }

  // === 任务实现 ===

  // 库存检查任务
  async runStockCheck() {
    const alertCount = await stockAlertService.checkAllProductsStock();
    console.log(`📦 库存检查完成，生成 ${alertCount} 个预警`);
    return alertCount;
  }

  // 自动发货任务
  async runAutoShipping() {
    const processedCount = await autoShippingService.processBatchShipping();
    console.log(`🚚 自动发货完成，处理 ${processedCount} 个订单`);
    return processedCount;
  }

  // 行为数据刷新任务
  async runBehaviorFlush() {
    await tracker.forceFlush();
    const status = tracker.getQueueStatus();
    console.log(`📊 行为数据刷新完成，队列剩余: ${status.queueLength}`);
    return status;
  }

  // 数据清理任务
  async runCleanup() {
    const results = await Promise.allSettled([
      stockAlertService.autoCleanup(30),
      this.cleanupOldLogs()
    ]);
    
    const cleaned = results.reduce((sum, result) => {
      return sum + (result.status === 'fulfilled' ? result.value : 0);
    }, 0);
    
    console.log(`🧹 数据清理完成，清理 ${cleaned} 条记录`);
    return cleaned;
  }

  // 重试失败发货任务
  async runRetryShipping() {
    const retryCount = await autoShippingService.retryFailedShipping();
    console.log(`🔄 重试发货完成，重试 ${retryCount} 个订单`);
    return retryCount;
  }

  // 清理旧日志
  async cleanupOldLogs() {
    // 这里可以添加日志清理逻辑
    console.log('🗂️ 日志清理完成');
    return 0;
  }

  // === 管理功能 ===

  // 手动执行任务
  async executeTask(taskName) {
    const taskMap = {
      'stock-check': () => this.runStockCheck(),
      'auto-shipping': () => this.runAutoShipping(),
      'behavior-flush': () => this.runBehaviorFlush(),
      'cleanup': () => this.runCleanup(),
      'retry-shipping': () => this.runRetryShipping()
    };

    const handler = taskMap[taskName];
    if (!handler) {
      throw new Error(`未知任务: ${taskName}`);
    }

    console.log(`🎯 手动执行任务: ${taskName}`);
    const result = await handler();
    console.log(`✅ 任务 ${taskName} 手动执行完成`);
    return result;
  }

  // 获取状态
  getStatus() {
    return {
      isRunning: this.isRunning,
      taskCount: this.tasks.size,
      stats: this.stats,
      tasks: Array.from(this.tasks.keys()).map(name => ({
        name,
        running: this.tasks.get(name)?.running || false
      }))
    };
  }

  // 获取性能指标
  getPerformanceMetrics() {
    const successRate = this.stats.totalRuns > 0 
      ? (this.stats.successRuns / this.stats.totalRuns * 100).toFixed(2)
      : 0;

    return {
      ...this.stats,
      successRate: `${successRate}%`,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      behaviorQueueStatus: tracker.getQueueStatus()
    };
  }

  // 发送告警
  sendAlert(taskName, error) {
    // 这里可以集成邮件、短信、钉钉等告警方式
    console.error(`🚨 核心任务告警: ${taskName} - ${error.message}`);
  }

  // 重启任务
  restartTask(taskName) {
    const task = this.tasks.get(taskName);
    if (task) {
      task.stop();
      task.start();
      console.log(`🔄 任务 ${taskName} 已重启`);
      return true;
    }
    return false;
  }

  // 暂停任务
  pauseTask(taskName) {
    const task = this.tasks.get(taskName);
    if (task) {
      task.stop();
      console.log(`⏸️ 任务 ${taskName} 已暂停`);
      return true;
    }
    return false;
  }

  // 恢复任务
  resumeTask(taskName) {
    const task = this.tasks.get(taskName);
    if (task) {
      task.start();
      console.log(`▶️ 任务 ${taskName} 已恢复`);
      return true;
    }
    return false;
  }
}

// 创建全局实例
const optimizedScheduler = new OptimizedScheduler();

// 优雅退出处理
const gracefulShutdown = (signal) => {
  console.log(`\n📡 收到 ${signal} 信号，正在优雅关闭...`);
  optimizedScheduler.stop();
  process.exit(0);
};

process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));

module.exports = optimizedScheduler;
