// 地址管理表初始化脚本
const { sequelize } = require('../config/sequelize');

/**
 * 创建地址管理数据表
 */
async function initAddressTables() {
  try {
    console.log('开始创建地址管理数据表...');
    
    // 创建用户地址表
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS user_addresses (
        id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '地址ID',
        user_id INT NOT NULL COMMENT '用户ID',
        receiver_name VARCHAR(50) NOT NULL COMMENT '收货人姓名',
        receiver_phone VARCHAR(20) NOT NULL COMMENT '收货人电话',
        province VARCHAR(50) NOT NULL COMMENT '省份',
        city VARCHAR(50) NOT NULL COMMENT '城市',
        district VARCHAR(50) NOT NULL COMMENT '区县',
        detail_address TEXT NOT NULL COMMENT '详细地址',
        postal_code VARCHAR(10) COMMENT '邮政编码',
        address_tag VARCHAR(20) DEFAULT '家' COMMENT '地址标签(家/公司/学校等)',
        is_default TINYINT(1) DEFAULT 0 COMMENT '是否默认地址(0否1是)',
        longitude DECIMAL(10,7) COMMENT '经度',
        latitude DECIMAL(10,7) COMMENT '纬度',
        status TINYINT(1) DEFAULT 1 COMMENT '状态(0删除1正常)',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        
        INDEX idx_user_id (user_id),
        INDEX idx_user_default (user_id, is_default),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户地址表';
    `);
    
    console.log('✅ 用户地址表创建成功');
    
    // 创建省市区数据表（简化版）
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS regions (
        id INT PRIMARY KEY AUTO_INCREMENT COMMENT '区域ID',
        name VARCHAR(50) NOT NULL COMMENT '区域名称',
        parent_id INT DEFAULT 0 COMMENT '父级ID(0为省份)',
        level TINYINT(1) NOT NULL COMMENT '级别(1省2市3区)',
        code VARCHAR(10) COMMENT '区域编码',
        status TINYINT(1) DEFAULT 1 COMMENT '状态(0禁用1启用)',
        sort_order INT DEFAULT 0 COMMENT '排序',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        
        INDEX idx_parent_id (parent_id),
        INDEX idx_level (level),
        INDEX idx_status (status),
        INDEX idx_sort (sort_order)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='省市区数据表';
    `);
    
    console.log('✅ 省市区数据表创建成功');
    
    // 插入基础省市区数据
    await insertBasicRegions();
    
    console.log('🎉 地址管理数据表初始化完成！');
    console.log('');
    console.log('📋 已创建的表:');
    console.log('   - user_addresses (用户地址表)');
    console.log('   - regions (省市区数据表)');
    console.log('');
    console.log('🚀 现在可以使用地址管理功能了！');
    
  } catch (error) {
    console.error('❌ 创建地址管理表失败:', error);
    throw error;
  }
}

/**
 * 插入基础省市区数据
 */
async function insertBasicRegions() {
  try {
    // 检查是否已有数据
    const [existingData] = await sequelize.query('SELECT COUNT(*) as count FROM regions');
    if (existingData[0].count > 0) {
      console.log('省市区数据已存在，跳过插入');
      return;
    }
    
    // 插入基础省份数据
    const provinces = [
      { name: '北京市', code: '110000', level: 1 },
      { name: '天津市', code: '120000', level: 1 },
      { name: '河北省', code: '130000', level: 1 },
      { name: '山西省', code: '140000', level: 1 },
      { name: '内蒙古自治区', code: '150000', level: 1 },
      { name: '辽宁省', code: '210000', level: 1 },
      { name: '吉林省', code: '220000', level: 1 },
      { name: '黑龙江省', code: '230000', level: 1 },
      { name: '上海市', code: '310000', level: 1 },
      { name: '江苏省', code: '320000', level: 1 },
      { name: '浙江省', code: '330000', level: 1 },
      { name: '安徽省', code: '340000', level: 1 },
      { name: '福建省', code: '350000', level: 1 },
      { name: '江西省', code: '360000', level: 1 },
      { name: '山东省', code: '370000', level: 1 },
      { name: '河南省', code: '410000', level: 1 },
      { name: '湖北省', code: '420000', level: 1 },
      { name: '湖南省', code: '430000', level: 1 },
      { name: '广东省', code: '440000', level: 1 },
      { name: '广西壮族自治区', code: '450000', level: 1 },
      { name: '海南省', code: '460000', level: 1 },
      { name: '重庆市', code: '500000', level: 1 },
      { name: '四川省', code: '510000', level: 1 },
      { name: '贵州省', code: '520000', level: 1 },
      { name: '云南省', code: '530000', level: 1 },
      { name: '西藏自治区', code: '540000', level: 1 },
      { name: '陕西省', code: '610000', level: 1 },
      { name: '甘肃省', code: '620000', level: 1 },
      { name: '青海省', code: '630000', level: 1 },
      { name: '宁夏回族自治区', code: '640000', level: 1 },
      { name: '新疆维吾尔自治区', code: '650000', level: 1 },
      { name: '台湾省', code: '710000', level: 1 },
      { name: '香港特别行政区', code: '810000', level: 1 },
      { name: '澳门特别行政区', code: '820000', level: 1 }
    ];
    
    for (const province of provinces) {
      await sequelize.query(`
        INSERT INTO regions (name, parent_id, level, code, sort_order) 
        VALUES (?, 0, ?, ?, ?)
      `, [province.name, province.level, province.code, 0]);
    }
    
    console.log('✅ 基础省份数据插入成功');
    
    // 插入一些主要城市数据（以广东省为例）
    const [guangdongResult] = await sequelize.query(`
      SELECT id FROM regions WHERE name = '广东省' AND level = 1
    `);
    
    if (guangdongResult.length > 0) {
      const guangdongId = guangdongResult[0].id;
      const cities = [
        { name: '广州市', code: '440100' },
        { name: '韶关市', code: '440200' },
        { name: '深圳市', code: '440300' },
        { name: '珠海市', code: '440400' },
        { name: '汕头市', code: '440500' },
        { name: '佛山市', code: '440600' },
        { name: '江门市', code: '440700' },
        { name: '湛江市', code: '440800' },
        { name: '茂名市', code: '440900' },
        { name: '肇庆市', code: '441200' },
        { name: '惠州市', code: '441300' },
        { name: '梅州市', code: '441400' },
        { name: '汕尾市', code: '441500' },
        { name: '河源市', code: '441600' },
        { name: '阳江市', code: '441700' },
        { name: '清远市', code: '441800' },
        { name: '东莞市', code: '441900' },
        { name: '中山市', code: '442000' },
        { name: '潮州市', code: '445100' },
        { name: '揭阳市', code: '445200' },
        { name: '云浮市', code: '445300' }
      ];
      
      for (const city of cities) {
        await sequelize.query(`
          INSERT INTO regions (name, parent_id, level, code, sort_order) 
          VALUES (?, ?, 2, ?, ?)
        `, [city.name, guangdongId, city.code, 0]);
      }
      
      console.log('✅ 广东省城市数据插入成功');
    }
    
  } catch (error) {
    console.error('插入省市区数据失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initAddressTables()
    .then(() => {
      console.log('地址管理数据表初始化完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('地址管理数据表初始化失败:', error);
      process.exit(1);
    });
}

module.exports = { initAddressTables };
