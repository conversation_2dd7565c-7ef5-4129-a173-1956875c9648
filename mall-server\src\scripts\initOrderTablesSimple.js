// 订单表初始化脚本 - 简化版
const { sequelize } = require('../config/sequelize');

/**
 * 创建订单相关数据表
 */
async function initOrderTables() {
  try {
    console.log('开始创建订单相关数据表...');
    
    // 创建订单表
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS orders (
        id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单ID',
        order_no VARCHAR(32) NOT NULL UNIQUE COMMENT '订单号',
        user_id INT NOT NULL COMMENT '用户ID',
        order_status ENUM('pending', 'paid', 'shipped', 'delivered', 'completed', 'cancelled', 'refunded') 
          DEFAULT 'pending' COMMENT '订单状态',
        payment_status ENUM('unpaid', 'paid', 'refunded', 'partial_refund') 
          DEFAULT 'unpaid' COMMENT '支付状态',
        shipping_status ENUM('unshipped', 'shipped', 'delivered') 
          DEFAULT 'unshipped' COMMENT '物流状态',
        total_amount DECIMAL(10,2) NOT NULL COMMENT '订单总金额',
        product_amount DECIMAL(10,2) NOT NULL COMMENT '商品总金额',
        shipping_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '运费',
        discount_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠金额',
        coupon_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠券金额',
        payment_method ENUM('wechat', 'alipay', 'balance', 'cod') COMMENT '支付方式',
        payment_time DATETIME COMMENT '支付时间',
        shipping_time DATETIME COMMENT '发货时间',
        delivery_time DATETIME COMMENT '收货时间',
        receiver_name VARCHAR(50) NOT NULL COMMENT '收货人姓名',
        receiver_phone VARCHAR(20) NOT NULL COMMENT '收货人电话',
        receiver_address TEXT NOT NULL COMMENT '收货地址',
        receiver_province VARCHAR(50) COMMENT '收货省份',
        receiver_city VARCHAR(50) COMMENT '收货城市',
        receiver_district VARCHAR(50) COMMENT '收货区县',
        shipping_company VARCHAR(50) COMMENT '物流公司',
        shipping_no VARCHAR(50) COMMENT '物流单号',
        remark TEXT COMMENT '订单备注',
        cancel_reason VARCHAR(200) COMMENT '取消原因',
        refund_reason VARCHAR(200) COMMENT '退款原因',
        extra_data JSON COMMENT '扩展数据',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        
        INDEX idx_order_no (order_no),
        INDEX idx_user_id (user_id),
        INDEX idx_order_status (order_status),
        INDEX idx_payment_status (payment_status),
        INDEX idx_created_at (created_at),
        INDEX idx_user_status (user_id, order_status),
        INDEX idx_user_created (user_id, created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';
    `);
    
    console.log('✅ 订单表创建成功');
    
    // 创建订单项表
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS order_items (
        id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单项ID',
        order_id BIGINT NOT NULL COMMENT '订单ID',
        product_id INT NOT NULL COMMENT '商品ID',
        product_name VARCHAR(200) NOT NULL COMMENT '商品名称',
        product_image VARCHAR(500) COMMENT '商品图片',
        product_sku VARCHAR(100) COMMENT '商品SKU',
        product_spec JSON COMMENT '商品规格',
        unit_price DECIMAL(10,2) NOT NULL COMMENT '单价',
        quantity INT NOT NULL COMMENT '数量',
        total_price DECIMAL(10,2) NOT NULL COMMENT '小计',
        discount_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠金额',
        actual_price DECIMAL(10,2) NOT NULL COMMENT '实付金额',
        refund_status ENUM('none', 'applying', 'approved', 'rejected', 'completed') 
          DEFAULT 'none' COMMENT '退款状态',
        refund_quantity INT DEFAULT 0 COMMENT '退款数量',
        refund_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '退款金额',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        
        INDEX idx_order_id (order_id),
        INDEX idx_product_id (product_id),
        INDEX idx_order_product (order_id, product_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单项表';
    `);
    
    console.log('✅ 订单项表创建成功');
    
    console.log('🎉 订单相关数据表初始化完成！');
    console.log('');
    console.log('📋 已创建的表:');
    console.log('   - orders (订单表)');
    console.log('   - order_items (订单项表)');
    console.log('');
    console.log('🚀 现在可以启动服务器测试订单功能了！');
    
  } catch (error) {
    console.error('❌ 创建订单表失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initOrderTables()
    .then(() => {
      console.log('数据库初始化完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('数据库初始化失败:', error);
      process.exit(1);
    });
}

module.exports = { initOrderTables };
