// 订单API测试脚本
const axios = require('axios');

const BASE_URL = 'http://localhost:4000/api/front';

// 模拟用户token（需要先登录获取）
let userToken = '';

/**
 * 设置用户token
 */
function setUserToken(token) {
  userToken = token;
}

/**
 * 创建HTTP请求配置
 */
function createRequestConfig() {
  return {
    headers: {
      'Authorization': `Bearer ${userToken}`,
      'Content-Type': 'application/json'
    }
  };
}

/**
 * 测试获取订单列表
 */
async function testGetOrderList() {
  try {
    console.log('📋 测试获取订单列表...');
    
    const response = await axios.get(
      `${BASE_URL}/order/list?page=1&size=10&status=all`,
      createRequestConfig()
    );
    
    console.log('✅ 获取订单列表成功:', response.data);
    return response.data;
    
  } catch (error) {
    console.error('❌ 获取订单列表失败:', error.response?.data || error.message);
    return null;
  }
}

/**
 * 测试创建订单
 */
async function testCreateOrder() {
  try {
    console.log('🛒 测试创建订单...');
    
    const orderData = {
      items: [
        {
          productId: 1,
          productName: '西湖龙井茶叶礼盒装',
          productImage: '/images/products/longjing.jpg',
          productSku: 'TEA001',
          unitPrice: 59.00,
          quantity: 2,
          totalPrice: 118.00,
          actualPrice: 118.00
        }
      ],
      receiverInfo: {
        name: '张三',
        phone: '13800138000',
        address: '广东省深圳市南山区科技园南区',
        province: '广东省',
        city: '深圳市',
        district: '南山区'
      },
      paymentMethod: 'wechat',
      remark: '测试订单'
    };
    
    const response = await axios.post(
      `${BASE_URL}/order/create`,
      orderData,
      createRequestConfig()
    );
    
    console.log('✅ 创建订单成功:', response.data);
    return response.data;
    
  } catch (error) {
    console.error('❌ 创建订单失败:', error.response?.data || error.message);
    return null;
  }
}

/**
 * 测试获取订单详情
 */
async function testGetOrderDetail(orderId) {
  try {
    console.log(`📄 测试获取订单详情 (ID: ${orderId})...`);
    
    const response = await axios.get(
      `${BASE_URL}/order/detail/${orderId}`,
      createRequestConfig()
    );
    
    console.log('✅ 获取订单详情成功:', response.data);
    return response.data;
    
  } catch (error) {
    console.error('❌ 获取订单详情失败:', error.response?.data || error.message);
    return null;
  }
}

/**
 * 测试取消订单
 */
async function testCancelOrder(orderId) {
  try {
    console.log(`❌ 测试取消订单 (ID: ${orderId})...`);
    
    const response = await axios.post(
      `${BASE_URL}/order/cancel/${orderId}`,
      { reason: '不想要了' },
      createRequestConfig()
    );
    
    console.log('✅ 取消订单成功:', response.data);
    return response.data;
    
  } catch (error) {
    console.error('❌ 取消订单失败:', error.response?.data || error.message);
    return null;
  }
}

/**
 * 测试确认收货
 */
async function testConfirmOrder(orderId) {
  try {
    console.log(`✅ 测试确认收货 (ID: ${orderId})...`);
    
    const response = await axios.post(
      `${BASE_URL}/order/confirm/${orderId}`,
      {},
      createRequestConfig()
    );
    
    console.log('✅ 确认收货成功:', response.data);
    return response.data;
    
  } catch (error) {
    console.error('❌ 确认收货失败:', error.response?.data || error.message);
    return null;
  }
}

/**
 * 测试获取物流信息
 */
async function testGetLogistics(orderId) {
  try {
    console.log(`🚚 测试获取物流信息 (ID: ${orderId})...`);
    
    const response = await axios.get(
      `${BASE_URL}/order/logistics/${orderId}`,
      createRequestConfig()
    );
    
    console.log('✅ 获取物流信息成功:', response.data);
    return response.data;
    
  } catch (error) {
    console.error('❌ 获取物流信息失败:', error.response?.data || error.message);
    return null;
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🧪 开始运行订单API测试...');
  console.log('');
  
  if (!userToken) {
    console.log('⚠️  请先设置用户token:');
    console.log('   node testOrderAPI.js <your-token>');
    return;
  }
  
  // 1. 测试获取订单列表
  await testGetOrderList();
  console.log('');
  
  // 2. 测试创建订单
  const createResult = await testCreateOrder();
  console.log('');
  
  if (createResult?.success && createResult?.data?.orderId) {
    const orderId = createResult.data.orderId;
    
    // 3. 测试获取订单详情
    await testGetOrderDetail(orderId);
    console.log('');
    
    // 4. 测试获取物流信息
    await testGetLogistics(orderId);
    console.log('');
    
    // 5. 测试取消订单
    await testCancelOrder(orderId);
    console.log('');
  }
  
  console.log('🎉 订单API测试完成！');
}

// 如果直接运行此脚本
if (require.main === module) {
  const token = process.argv[2];
  if (token) {
    setUserToken(token);
    runAllTests();
  } else {
    console.log('使用方法: node testOrderAPI.js <user-token>');
    console.log('');
    console.log('示例:');
    console.log('  node testOrderAPI.js eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...');
  }
}

module.exports = {
  setUserToken,
  testGetOrderList,
  testCreateOrder,
  testGetOrderDetail,
  testCancelOrder,
  testConfirmOrder,
  testGetLogistics,
  runAllTests
};
