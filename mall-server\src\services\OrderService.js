// 订单服务层 - 完整业务逻辑
const OrderModel = require('../models/OrderModel');
const OrderItem = require('../models/OrderItem');
const Product = require('../models/Product');
const User = require('../models/User');
const { Op } = require('sequelize');
const { sequelize } = require('../config/sequelize');

class OrderService {
  
  /**
   * 获取订单列表
   * @param {Object} params - 查询参数
   * @param {number} params.userId - 用户ID
   * @param {number} params.page - 页码
   * @param {number} params.size - 每页数量
   * @param {string} params.status - 订单状态
   * @param {string} params.keyword - 搜索关键词
   * @returns {Object} 订单列表数据
   */
  async getOrderList(params) {
    try {
      const { userId, page = 1, size = 10, status, keyword } = params;
      
      // 构建查询条件
      const whereCondition = {
        user_id: userId
      };
      
      // 状态筛选
      if (status && status !== 'all') {
        whereCondition.order_status = status;
      }
      
      // 关键词搜索
      if (keyword) {
        whereCondition[Op.or] = [
          { order_no: { [Op.like]: `%${keyword}%` } },
          { receiver_name: { [Op.like]: `%${keyword}%` } },
          { receiver_phone: { [Op.like]: `%${keyword}%` } }
        ];
      }
      
      // 分页参数
      const offset = (page - 1) * size;
      const limit = parseInt(size);
      
      // 查询订单列表
      const result = await OrderModel.findAndCountAll({
        where: whereCondition,
        include: [
          {
            model: OrderItem,
            as: 'items',
            include: [
              {
                model: Product,
                as: 'product',
                attributes: ['id', 'name', 'image', 'price', 'stock']
              }
            ]
          }
        ],
        order: [['created_at', 'DESC']],
        offset,
        limit,
        distinct: true
      });
      
      // 格式化订单数据
      const orders = result.rows.map(order => this.formatOrderData(order));
      
      return {
        success: true,
        data: {
          list: orders,
          total: result.count,
          page: parseInt(page),
          size: parseInt(size),
          totalPages: Math.ceil(result.count / size)
        }
      };
      
    } catch (error) {
      console.error('获取订单列表失败:', error);
      return {
        success: false,
        message: '获取订单列表失败',
        error: error.message
      };
    }
  }
  
  /**
   * 获取订单详情
   * @param {number} orderId - 订单ID
   * @param {number} userId - 用户ID
   * @returns {Object} 订单详情
   */
  async getOrderDetail(orderId, userId) {
    try {
      const order = await OrderModel.findOne({
        where: {
          id: orderId,
          user_id: userId
        },
        include: [
          {
            model: OrderItem,
            as: 'items',
            include: [
              {
                model: Product,
                as: 'product',
                attributes: ['id', 'name', 'image', 'price', 'stock', 'description']
              }
            ]
          },
          {
            model: User,
            as: 'user',
            attributes: ['id', 'username', 'phone', 'email']
          }
        ]
      });
      
      if (!order) {
        return {
          success: false,
          message: '订单不存在'
        };
      }
      
      return {
        success: true,
        data: this.formatOrderData(order)
      };
      
    } catch (error) {
      console.error('获取订单详情失败:', error);
      return {
        success: false,
        message: '获取订单详情失败',
        error: error.message
      };
    }
  }
  
  /**
   * 创建订单
   * @param {Object} orderData - 订单数据
   * @returns {Object} 创建结果
   */
  async createOrder(orderData) {
    const transaction = await sequelize.transaction();
    
    try {
      const {
        userId,
        items,
        receiverInfo,
        paymentMethod,
        remark,
        couponId,
        addressId
      } = orderData;
      
      // 生成订单号
      const orderNo = this.generateOrderNo();
      
      // 计算订单金额
      const amountInfo = await this.calculateOrderAmount(items, couponId);
      
      // 创建订单
      const order = await OrderModel.create({
        order_no: orderNo,
        user_id: userId,
        order_status: 'pending',
        payment_status: 'unpaid',
        shipping_status: 'unshipped',
        total_amount: amountInfo.totalAmount,
        product_amount: amountInfo.productAmount,
        shipping_fee: amountInfo.shippingFee,
        discount_amount: amountInfo.discountAmount,
        coupon_amount: amountInfo.couponAmount,
        payment_method: paymentMethod,
        receiver_name: receiverInfo.name,
        receiver_phone: receiverInfo.phone,
        receiver_address: receiverInfo.address,
        receiver_province: receiverInfo.province,
        receiver_city: receiverInfo.city,
        receiver_district: receiverInfo.district,
        remark: remark
      }, { transaction });
      
      // 创建订单项
      const orderItems = items.map(item => ({
        order_id: order.id,
        product_id: item.productId,
        product_name: item.productName,
        product_image: item.productImage,
        product_sku: item.productSku,
        product_spec: item.productSpec,
        unit_price: item.unitPrice,
        quantity: item.quantity,
        total_price: item.totalPrice,
        discount_amount: item.discountAmount || 0,
        actual_price: item.actualPrice
      }));
      
      await OrderItem.bulkCreate(orderItems, { transaction });
      
      // 减少商品库存
      await this.reduceProductStock(items, transaction);
      
      await transaction.commit();
      
      return {
        success: true,
        data: {
          orderId: order.id,
          orderNo: order.order_no,
          totalAmount: order.total_amount
        },
        message: '订单创建成功'
      };
      
    } catch (error) {
      await transaction.rollback();
      console.error('创建订单失败:', error);
      return {
        success: false,
        message: '创建订单失败',
        error: error.message
      };
    }
  }
  
  /**
   * 取消订单
   * @param {number} orderId - 订单ID
   * @param {number} userId - 用户ID
   * @param {string} reason - 取消原因
   * @returns {Object} 取消结果
   */
  async cancelOrder(orderId, userId, reason) {
    const transaction = await sequelize.transaction();
    
    try {
      const order = await OrderModel.findOne({
        where: {
          id: orderId,
          user_id: userId,
          order_status: { [Op.in]: ['pending', 'paid'] }
        },
        include: [
          {
            model: OrderItem,
            as: 'items'
          }
        ]
      });
      
      if (!order) {
        return {
          success: false,
          message: '订单不存在或无法取消'
        };
      }
      
      // 更新订单状态
      await order.update({
        order_status: 'cancelled',
        cancel_reason: reason,
        updated_at: new Date()
      }, { transaction });
      
      // 恢复商品库存
      await this.restoreProductStock(order.items, transaction);
      
      // 如果已支付，需要退款
      if (order.payment_status === 'paid') {
        await this.processRefund(order, transaction);
      }
      
      await transaction.commit();
      
      return {
        success: true,
        message: '订单取消成功'
      };
      
    } catch (error) {
      await transaction.rollback();
      console.error('取消订单失败:', error);
      return {
        success: false,
        message: '取消订单失败',
        error: error.message
      };
    }
  }
  
  /**
   * 确认收货
   * @param {number} orderId - 订单ID
   * @param {number} userId - 用户ID
   * @returns {Object} 确认结果
   */
  async confirmDelivery(orderId, userId) {
    try {
      const order = await OrderModel.findOne({
        where: {
          id: orderId,
          user_id: userId,
          order_status: 'shipped'
        }
      });
      
      if (!order) {
        return {
          success: false,
          message: '订单不存在或状态不正确'
        };
      }
      
      await order.update({
        order_status: 'delivered',
        shipping_status: 'delivered',
        delivery_time: new Date(),
        updated_at: new Date()
      });
      
      return {
        success: true,
        message: '确认收货成功'
      };
      
    } catch (error) {
      console.error('确认收货失败:', error);
      return {
        success: false,
        message: '确认收货失败',
        error: error.message
      };
    }
  }
  
  /**
   * 格式化订单数据
   * @param {Object} order - 原始订单数据
   * @returns {Object} 格式化后的订单数据
   */
  formatOrderData(order) {
    const orderData = order.toJSON();
    
    return {
      id: orderData.id,
      orderNo: orderData.order_no,
      orderStatus: orderData.order_status,
      paymentStatus: orderData.payment_status,
      shippingStatus: orderData.shipping_status,
      totalAmount: parseFloat(orderData.total_amount),
      productAmount: parseFloat(orderData.product_amount),
      shippingFee: parseFloat(orderData.shipping_fee),
      discountAmount: parseFloat(orderData.discount_amount),
      couponAmount: parseFloat(orderData.coupon_amount),
      paymentMethod: orderData.payment_method,
      paymentTime: orderData.payment_time,
      shippingTime: orderData.shipping_time,
      deliveryTime: orderData.delivery_time,
      receiverInfo: {
        name: orderData.receiver_name,
        phone: orderData.receiver_phone,
        address: orderData.receiver_address,
        province: orderData.receiver_province,
        city: orderData.receiver_city,
        district: orderData.receiver_district
      },
      shippingInfo: {
        company: orderData.shipping_company,
        trackingNo: orderData.shipping_no
      },
      remark: orderData.remark,
      cancelReason: orderData.cancel_reason,
      refundReason: orderData.refund_reason,
      items: orderData.items ? orderData.items.map(item => ({
        id: item.id,
        productId: item.product_id,
        productName: item.product_name,
        productImage: item.product_image,
        productSku: item.product_sku,
        productSpec: item.product_spec,
        unitPrice: parseFloat(item.unit_price),
        quantity: item.quantity,
        totalPrice: parseFloat(item.total_price),
        discountAmount: parseFloat(item.discount_amount),
        actualPrice: parseFloat(item.actual_price),
        refundStatus: item.refund_status,
        refundQuantity: item.refund_quantity,
        refundAmount: parseFloat(item.refund_amount),
        product: item.product
      })) : [],
      createdAt: orderData.created_at,
      updatedAt: orderData.updated_at
    };
  }
  
  /**
   * 生成订单号
   * @returns {string} 订单号
   */
  generateOrderNo() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');
    const second = String(now.getSeconds()).padStart(2, '0');
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    
    return `${year}${month}${day}${hour}${minute}${second}${random}`;
  }
  
  /**
   * 计算订单金额
   * @param {Array} items - 订单项
   * @param {number} couponId - 优惠券ID
   * @returns {Object} 金额信息
   */
  async calculateOrderAmount(items, couponId) {
    let productAmount = 0;
    
    // 计算商品总金额
    items.forEach(item => {
      productAmount += item.unitPrice * item.quantity;
    });
    
    // 计算运费（这里简化处理，实际应根据地址和重量计算）
    const shippingFee = productAmount >= 99 ? 0 : 10;
    
    // 计算优惠金额（这里简化处理）
    let discountAmount = 0;
    let couponAmount = 0;
    
    if (couponId) {
      // 根据优惠券计算优惠金额
      couponAmount = await this.calculateCouponDiscount(couponId, productAmount);
    }
    
    const totalAmount = productAmount + shippingFee - discountAmount - couponAmount;
    
    return {
      productAmount,
      shippingFee,
      discountAmount,
      couponAmount,
      totalAmount: Math.max(totalAmount, 0.01) // 最小金额0.01元
    };
  }
  
  /**
   * 减少商品库存
   * @param {Array} items - 订单项
   * @param {Object} transaction - 事务对象
   */
  async reduceProductStock(items, transaction) {
    for (const item of items) {
      await Product.decrement('stock', {
        by: item.quantity,
        where: { id: item.productId },
        transaction
      });
    }
  }
  
  /**
   * 恢复商品库存
   * @param {Array} items - 订单项
   * @param {Object} transaction - 事务对象
   */
  async restoreProductStock(items, transaction) {
    for (const item of items) {
      await Product.increment('stock', {
        by: item.quantity,
        where: { id: item.product_id },
        transaction
      });
    }
  }
  
  /**
   * 处理退款
   * @param {Object} order - 订单对象
   * @param {Object} transaction - 事务对象
   */
  async processRefund(order, transaction) {
    // 这里应该调用支付接口进行退款
    // 简化处理，只更新状态
    await order.update({
      payment_status: 'refunded',
      refund_reason: '订单取消退款'
    }, { transaction });
  }
  
  /**
   * 计算优惠券折扣
   * @param {number} couponId - 优惠券ID
   * @param {number} amount - 订单金额
   * @returns {number} 优惠金额
   */
  async calculateCouponDiscount(couponId, amount) {
    // 这里应该查询优惠券信息并计算折扣
    // 简化处理，返回固定折扣
    return 0;
  }
}

module.exports = new OrderService();
