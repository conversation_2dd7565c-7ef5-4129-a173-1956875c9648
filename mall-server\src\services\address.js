const { Address } = require('../models');

class AddressService {
  // 获取用户地址列表
  async getAddressList(userId) {
    return await Address.findAll({
      where: { user_id: userId },
      order: [['is_default', 'DESC'], ['created_at', 'DESC']]
    });
  }

  // 添加收货地址
  async addAddress(userId, addressData) {
    const { is_default } = addressData;

    // 如果设置为默认地址，先取消其他默认地址
    if (is_default) {
      await Address.update(
        { is_default: 0 },
        { where: { user_id: userId, is_default: 1 } }
      );
    }

    return await Address.create({
      ...addressData,
      user_id: userId
    });
  }

  // 更新收货地址
  async updateAddress(userId, addressId, addressData) {
    const address = await Address.findOne({
      where: { id: addressId, user_id: userId }
    });

    if (!address) {
      throw new Error('地址不存在');
    }

    const { is_default } = addressData;

    // 如果设置为默认地址，先取消其他默认地址
    if (is_default) {
      await Address.update(
        { is_default: 0 },
        { where: { user_id: userId, is_default: 1 } }
      );
    }

    await address.update(addressData);
    return address;
  }

  // 删除收货地址
  async deleteAddress(userId, addressId) {
    const address = await Address.findOne({
      where: { id: addressId, user_id: userId }
    });

    if (!address) {
      throw new Error('地址不存在');
    }

    await address.destroy();
    return true;
  }

  // 设置默认地址
  async setDefaultAddress(userId, addressId) {
    const address = await Address.findOne({
      where: { id: addressId, user_id: userId }
    });

    if (!address) {
      throw new Error('地址不存在');
    }

    // 先取消其他默认地址
    await Address.update(
      { is_default: 0 },
      { where: { user_id: userId, is_default: 1 } }
    );

    // 设置新的默认地址
    await address.update({ is_default: 1 });
    return address;
  }

  // 获取默认地址
  async getDefaultAddress(userId) {
    return await Address.findOne({
      where: { user_id: userId, is_default: 1 }
    });
  }

  // 获取地址详情
  async getAddressDetail(userId, addressId) {
    const address = await Address.findOne({
      where: { id: addressId, user_id: userId }
    });

    if (!address) {
      throw new Error('地址不存在');
    }

    return address;
  }

  // 验证地址信息
  validateAddress(addressData) {
    const { receiver, phone, province, city, district, detail_address } = addressData;

    if (!receiver || receiver.trim().length === 0) {
      throw new Error('收货人姓名不能为空');
    }

    if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
      throw new Error('手机号格式不正确');
    }

    if (!province || province.trim().length === 0) {
      throw new Error('省份不能为空');
    }

    if (!city || city.trim().length === 0) {
      throw new Error('城市不能为空');
    }

    if (!district || district.trim().length === 0) {
      throw new Error('区县不能为空');
    }

    if (!detail_address || detail_address.trim().length === 0) {
      throw new Error('详细地址不能为空');
    }

    return true;
  }
}

module.exports = new AddressService(); 