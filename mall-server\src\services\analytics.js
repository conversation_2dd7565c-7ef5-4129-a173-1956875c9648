// 数据统计分析服务
const { Op, sequelize } = require('sequelize');
const { 
  Order, 
  OrderItem, 
  Product, 
  User, 
  Category,
  UserBehavior,
  SalesReport 
} = require('../models');

class AnalyticsService {
  
  // 获取销售数据统计
  async getSalesAnalytics(startDate, endDate, type = 'daily') {
    try {
      const whereCondition = {
        pay_status: 1, // 已支付
        created_at: {
          [Op.between]: [startDate, endDate]
        }
      };

      // 基础销售数据
      const salesData = await Order.findAll({
        where: whereCondition,
        attributes: [
          [sequelize.fn('DATE', sequelize.col('created_at')), 'date'],
          [sequelize.fn('COUNT', sequelize.col('id')), 'order_count'],
          [sequelize.fn('SUM', sequelize.col('pay_amount')), 'total_amount'],
          [sequelize.fn('AVG', sequelize.col('pay_amount')), 'avg_amount']
        ],
        group: [sequelize.fn('DATE', sequelize.col('created_at'))],
        order: [[sequelize.fn('DATE', sequelize.col('created_at')), 'ASC']],
        raw: true
      });

      // 商品销售排行
      const productRanking = await OrderItem.findAll({
        include: [
          {
            model: Order,
            as: 'order',
            where: whereCondition,
            attributes: []
          },
          {
            model: Product,
            as: 'product',
            attributes: ['id', 'name', 'price', 'main_image']
          }
        ],
        attributes: [
          'product_id',
          [sequelize.fn('SUM', sequelize.col('quantity')), 'total_sales'],
          [sequelize.fn('SUM', sequelize.literal('quantity * price')), 'total_revenue']
        ],
        group: ['product_id'],
        order: [[sequelize.fn('SUM', sequelize.col('quantity')), 'DESC']],
        limit: 10,
        raw: false
      });

      // 分类销售统计
      const categoryStats = await OrderItem.findAll({
        include: [
          {
            model: Order,
            as: 'order',
            where: whereCondition,
            attributes: []
          },
          {
            model: Product,
            as: 'product',
            include: [
              {
                model: Category,
                as: 'category',
                attributes: ['id', 'name']
              }
            ],
            attributes: ['category_id']
          }
        ],
        attributes: [
          [sequelize.fn('SUM', sequelize.col('quantity')), 'total_sales'],
          [sequelize.fn('SUM', sequelize.literal('quantity * price')), 'total_revenue']
        ],
        group: ['product.category_id'],
        order: [[sequelize.fn('SUM', sequelize.literal('quantity * price')), 'DESC']],
        raw: false
      });

      return {
        salesTrend: salesData,
        productRanking,
        categoryStats,
        summary: {
          totalOrders: salesData.reduce((sum, item) => sum + parseInt(item.order_count), 0),
          totalRevenue: salesData.reduce((sum, item) => sum + parseFloat(item.total_amount || 0), 0),
          avgOrderValue: salesData.length > 0 
            ? salesData.reduce((sum, item) => sum + parseFloat(item.avg_amount || 0), 0) / salesData.length 
            : 0
        }
      };
    } catch (error) {
      console.error('获取销售分析数据失败:', error);
      throw new Error('获取销售分析数据失败');
    }
  }

  // 用户行为分析
  async getUserBehaviorAnalytics(startDate, endDate) {
    try {
      // 页面访问统计
      const pageViews = await UserBehavior.findAll({
        where: {
          behavior_type: 'view',
          created_at: {
            [Op.between]: [startDate, endDate]
          }
        },
        attributes: [
          'page_path',
          [sequelize.fn('COUNT', sequelize.col('id')), 'view_count'],
          [sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('user_id'))), 'unique_users']
        ],
        group: ['page_path'],
        order: [[sequelize.fn('COUNT', sequelize.col('id')), 'DESC']],
        limit: 20,
        raw: true
      });

      // 搜索关键词统计
      const searchKeywords = await UserBehavior.findAll({
        where: {
          behavior_type: 'search',
          search_keyword: { [Op.ne]: null },
          created_at: {
            [Op.between]: [startDate, endDate]
          }
        },
        attributes: [
          'search_keyword',
          [sequelize.fn('COUNT', sequelize.col('id')), 'search_count']
        ],
        group: ['search_keyword'],
        order: [[sequelize.fn('COUNT', sequelize.col('id')), 'DESC']],
        limit: 20,
        raw: true
      });

      // 用户活跃度分析
      const userActivity = await UserBehavior.findAll({
        where: {
          created_at: {
            [Op.between]: [startDate, endDate]
          }
        },
        attributes: [
          [sequelize.fn('DATE', sequelize.col('created_at')), 'date'],
          [sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('user_id'))), 'active_users'],
          [sequelize.fn('COUNT', sequelize.col('id')), 'total_actions']
        ],
        group: [sequelize.fn('DATE', sequelize.col('created_at'))],
        order: [[sequelize.fn('DATE', sequelize.col('created_at')), 'ASC']],
        raw: true
      });

      // 转化漏斗分析
      const funnelData = await this.getFunnelAnalysis(startDate, endDate);

      return {
        pageViews,
        searchKeywords,
        userActivity,
        funnelData
      };
    } catch (error) {
      console.error('获取用户行为分析数据失败:', error);
      throw new Error('获取用户行为分析数据失败');
    }
  }

  // 转化漏斗分析
  async getFunnelAnalysis(startDate, endDate) {
    try {
      const dateCondition = {
        created_at: {
          [Op.between]: [startDate, endDate]
        }
      };

      // 各阶段用户数统计
      const viewUsers = await UserBehavior.count({
        where: {
          behavior_type: 'view',
          target_type: 'product',
          ...dateCondition
        },
        distinct: true,
        col: 'user_id'
      });

      const cartUsers = await UserBehavior.count({
        where: {
          behavior_type: 'add_cart',
          ...dateCondition
        },
        distinct: true,
        col: 'user_id'
      });

      const orderUsers = await Order.count({
        where: dateCondition,
        distinct: true,
        col: 'user_id'
      });

      const payUsers = await Order.count({
        where: {
          pay_status: 1,
          ...dateCondition
        },
        distinct: true,
        col: 'user_id'
      });

      return {
        steps: [
          { name: '商品浏览', users: viewUsers, rate: 100 },
          { name: '加入购物车', users: cartUsers, rate: viewUsers > 0 ? (cartUsers / viewUsers * 100).toFixed(2) : 0 },
          { name: '下单', users: orderUsers, rate: viewUsers > 0 ? (orderUsers / viewUsers * 100).toFixed(2) : 0 },
          { name: '支付', users: payUsers, rate: viewUsers > 0 ? (payUsers / viewUsers * 100).toFixed(2) : 0 }
        ]
      };
    } catch (error) {
      console.error('获取转化漏斗数据失败:', error);
      throw new Error('获取转化漏斗数据失败');
    }
  }

  // 实时数据统计
  async getRealTimeStats() {
    try {
      const today = new Date();
      const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());

      // 今日数据
      const todayStats = await Order.findOne({
        where: {
          created_at: { [Op.gte]: todayStart }
        },
        attributes: [
          [sequelize.fn('COUNT', sequelize.col('id')), 'today_orders'],
          [sequelize.fn('SUM', sequelize.col('pay_amount')), 'today_revenue'],
          [sequelize.fn('COUNT', sequelize.literal('CASE WHEN pay_status = 1 THEN 1 END')), 'today_paid_orders']
        ],
        raw: true
      });

      // 在线用户（最近5分钟有行为的用户）
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
      const onlineUsers = await UserBehavior.count({
        where: {
          created_at: { [Op.gte]: fiveMinutesAgo }
        },
        distinct: true,
        col: 'user_id'
      });

      // 待处理订单
      const pendingOrders = await Order.count({
        where: {
          order_status: { [Op.in]: [0, 1] } // 待付款、待发货
        }
      });

      return {
        todayOrders: parseInt(todayStats.today_orders) || 0,
        todayRevenue: parseFloat(todayStats.today_revenue) || 0,
        todayPaidOrders: parseInt(todayStats.today_paid_orders) || 0,
        onlineUsers,
        pendingOrders
      };
    } catch (error) {
      console.error('获取实时统计数据失败:', error);
      throw new Error('获取实时统计数据失败');
    }
  }
}

module.exports = new AnalyticsService();
