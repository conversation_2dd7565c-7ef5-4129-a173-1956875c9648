const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const config = require('../config');
const adminModel = require('../models/admin');

class AuthService {
  // 管理员登录
  async login(username, password) {
    const admin = await adminModel.findByUsername(username);
    
    if (!admin) {
      throw new Error('管理员不存在');
    }
    
    const isValidPassword = await bcrypt.compare(password, admin.password);
    
    if (!isValidPassword) {
      throw new Error('密码错误');
    }
    
    // 生成JWT令牌
    const token = jwt.sign(
      { id: admin.id, username: admin.username, role: 'admin' },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );
    
    return {
      token,
      admin: {
        id: admin.id,
        username: admin.username,
        nickname: admin.nickname,
        avatar: admin.avatar,
        role: admin.role
      }
    };
  }

  // 管理员登出
  async logout(adminId) {
    // 这里可以实现令牌黑名单等逻辑
    return true;
  }

  // 获取管理员信息
  async getAdminInfo(adminId) {
    const admin = await adminModel.findById(adminId);
    
    if (!admin) {
      throw new Error('管理员不存在');
    }
    
    return {
      id: admin.id,
      username: admin.username,
      nickname: admin.nickname,
      avatar: admin.avatar,
      role: admin.role,
      createdAt: admin.createdAt
    };
  }

  // 修改管理员密码
  async changePassword(adminId, oldPassword, newPassword) {
    const admin = await adminModel.findById(adminId);
    
    if (!admin) {
      throw new Error('管理员不存在');
    }
    
    const isValidPassword = await bcrypt.compare(oldPassword, admin.password);
    
    if (!isValidPassword) {
      throw new Error('原密码错误');
    }
    
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    
    await adminModel.update(adminId, { password: hashedPassword });
    
    return true;
  }
}

module.exports = new AuthService(); 