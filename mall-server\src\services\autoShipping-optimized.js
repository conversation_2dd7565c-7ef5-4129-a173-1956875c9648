// 优化版自动发货服务 - 高效简洁版
const { Op, sequelize } = require('sequelize');
const { Order, OrderItem, Product, AutoShipping } = require('../models');

class OptimizedAutoShippingService {
  
  constructor() {
    this.config = {
      enabled: true,
      delayHours: 2,
      workingHours: { start: 9, end: 18 },
      excludeWeekends: false,
      batchSize: 50,
      maxRetries: 3
    };
    
    this.shippingProviders = [
      { code: 'SF', name: '顺丰速运', priority: 1, cost: 15 },
      { code: 'YTO', name: '圆通速递', priority: 2, cost: 10 },
      { code: 'ZTO', name: '中通快递', priority: 3, cost: 8 }
    ];
  }

  // 批量处理自动发货 - 优化版
  async processBatchShipping() {
    if (!this.config.enabled || !this.isWorkingTime()) {
      return 0;
    }

    try {
      console.log('🚚 开始批量处理自动发货...');
      
      // 单个SQL查询获取所有符合条件的订单
      const [eligibleOrders] = await sequelize.query(`
        SELECT 
          o.id, o.order_no, o.user_id, o.pay_amount,
          o.receiver_name, o.receiver_phone, o.receiver_address,
          o.pay_time, o.created_at,
          GROUP_CONCAT(
            CONCAT(oi.product_id, ':', oi.quantity, ':', p.stock, ':', p.weight)
          ) as items_info
        FROM orders o
        JOIN order_items oi ON o.id = oi.order_id
        JOIN products p ON oi.product_id = p.id
        WHERE o.order_status = 1 
        AND o.pay_status = 1
        AND o.pay_time <= DATE_SUB(NOW(), INTERVAL :delayHours HOUR)
        AND NOT EXISTS (
          SELECT 1 FROM auto_shipping ash WHERE ash.order_id = o.id
        )
        GROUP BY o.id
        HAVING MIN(p.stock >= oi.quantity) = 1
        ORDER BY o.pay_time ASC
        LIMIT :batchSize
      `, {
        replacements: {
          delayHours: this.config.delayHours,
          batchSize: this.config.batchSize
        },
        type: sequelize.QueryTypes.SELECT
      });

      if (eligibleOrders.length === 0) {
        console.log('✅ 无符合条件的订单需要发货');
        return 0;
      }

      // 批量处理发货
      const shippingRecords = [];
      const orderUpdates = [];
      const stockUpdates = [];

      for (const order of eligibleOrders) {
        try {
          const shippingInfo = this.generateShippingInfo(order);
          const items = this.parseItemsInfo(order.items_info);
          
          // 准备发货记录
          shippingRecords.push({
            order_id: order.id,
            shipping_company: shippingInfo.company,
            tracking_number: shippingInfo.trackingNumber,
            shipping_status: 'shipped',
            auto_generated: true,
            shipping_time: new Date(),
            estimated_delivery: shippingInfo.estimatedDelivery,
            shipping_cost: shippingInfo.cost,
            created_at: new Date(),
            updated_at: new Date()
          });

          // 准备订单更新
          orderUpdates.push({
            id: order.id,
            order_status: 2,
            delivery_company: shippingInfo.company,
            delivery_no: shippingInfo.trackingNumber,
            delivery_time: new Date()
          });

          // 准备库存更新
          items.forEach(item => {
            stockUpdates.push({
              productId: item.productId,
              quantity: item.quantity
            });
          });

        } catch (error) {
          console.error(`处理订单 ${order.order_no} 失败:`, error.message);
        }
      }

      // 批量执行数据库操作
      await this.executeBatchOperations(shippingRecords, orderUpdates, stockUpdates);
      
      console.log(`✅ 批量发货完成，处理 ${shippingRecords.length} 个订单`);
      return shippingRecords.length;

    } catch (error) {
      console.error('❌ 批量处理自动发货失败:', error);
      throw error;
    }
  }

  // 批量执行数据库操作
  async executeBatchOperations(shippingRecords, orderUpdates, stockUpdates) {
    const transaction = await sequelize.transaction();
    
    try {
      // 批量创建发货记录
      if (shippingRecords.length > 0) {
        await AutoShipping.bulkCreate(shippingRecords, { transaction });
      }

      // 批量更新订单状态
      for (const update of orderUpdates) {
        await Order.update({
          order_status: update.order_status,
          delivery_company: update.delivery_company,
          delivery_no: update.delivery_no,
          delivery_time: update.delivery_time
        }, {
          where: { id: update.id },
          transaction
        });
      }

      // 批量更新库存
      const stockUpdatePromises = stockUpdates.map(update => 
        Product.decrement('stock', {
          by: update.quantity,
          where: { id: update.productId },
          transaction
        })
      );
      await Promise.all(stockUpdatePromises);

      await transaction.commit();
      console.log('✅ 批量数据库操作完成');
      
    } catch (error) {
      await transaction.rollback();
      console.error('❌ 批量数据库操作失败:', error);
      throw error;
    }
  }

  // 生成发货信息
  generateShippingInfo(order) {
    const provider = this.selectShippingProvider(order);
    const trackingNumber = this.generateTrackingNumber(provider.code);
    const estimatedDelivery = new Date(Date.now() + 3 * 24 * 60 * 60 * 1000);

    return {
      company: provider.name,
      trackingNumber,
      estimatedDelivery,
      cost: provider.cost
    };
  }

  // 选择快递公司
  selectShippingProvider(order) {
    // 根据订单金额选择快递公司
    if (order.pay_amount >= 100) {
      return this.shippingProviders[0]; // 顺丰
    } else if (order.pay_amount >= 50) {
      return this.shippingProviders[1]; // 圆通
    } else {
      return this.shippingProviders[2]; // 中通
    }
  }

  // 生成快递单号
  generateTrackingNumber(providerCode) {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `${providerCode}${timestamp.slice(-8)}${random}`;
  }

  // 解析商品信息
  parseItemsInfo(itemsInfo) {
    return itemsInfo.split(',').map(item => {
      const [productId, quantity, stock, weight] = item.split(':');
      return {
        productId: parseInt(productId),
        quantity: parseInt(quantity),
        stock: parseInt(stock),
        weight: parseFloat(weight)
      };
    });
  }

  // 检查工作时间
  isWorkingTime() {
    const now = new Date();
    const hour = now.getHours();
    const dayOfWeek = now.getDay();

    // 检查周末
    if (this.config.excludeWeekends && (dayOfWeek === 0 || dayOfWeek === 6)) {
      return false;
    }

    // 检查工作时间
    return hour >= this.config.workingHours.start && hour < this.config.workingHours.end;
  }

  // 获取发货统计 - 优化版
  async getShippingStats(days = 7) {
    const [stats] = await sequelize.query(`
      SELECT 
        shipping_status,
        COUNT(*) as count,
        AVG(shipping_cost) as avg_cost,
        DATE(created_at) as date
      FROM auto_shipping
      WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
      GROUP BY shipping_status, DATE(created_at)
      ORDER BY date DESC, shipping_status
    `, {
      replacements: { days },
      type: sequelize.QueryTypes.SELECT
    });

    return this.formatShippingStats(stats);
  }

  // 格式化发货统计
  formatShippingStats(stats) {
    const result = {
      total: 0,
      byStatus: {},
      byDate: {},
      avgCost: 0
    };

    let totalCost = 0;
    stats.forEach(stat => {
      const count = parseInt(stat.count);
      result.total += count;
      
      if (!result.byStatus[stat.shipping_status]) {
        result.byStatus[stat.shipping_status] = 0;
      }
      result.byStatus[stat.shipping_status] += count;
      
      if (!result.byDate[stat.date]) {
        result.byDate[stat.date] = {};
      }
      result.byDate[stat.date][stat.shipping_status] = count;
      
      totalCost += parseFloat(stat.avg_cost || 0) * count;
    });

    result.avgCost = result.total > 0 ? (totalCost / result.total).toFixed(2) : 0;
    return result;
  }

  // 重试失败发货 - 优化版
  async retryFailedShipping(maxRetries = null) {
    const retryLimit = maxRetries || this.config.maxRetries;
    
    const failedShippings = await AutoShipping.findAll({
      where: {
        shipping_status: 'failed',
        retry_count: { [Op.lt]: retryLimit }
      },
      include: [{
        model: Order,
        as: 'order',
        where: { order_status: 1 }
      }],
      limit: 20
    });

    let retryCount = 0;
    for (const shipping of failedShippings) {
      try {
        await this.retryShipping(shipping);
        retryCount++;
      } catch (error) {
        console.error(`重试发货 ${shipping.id} 失败:`, error.message);
      }
    }

    console.log(`🔄 重试完成，成功重试 ${retryCount} 个发货`);
    return retryCount;
  }

  // 单个重试
  async retryShipping(shipping) {
    const newTrackingNumber = this.generateTrackingNumber('RT');
    
    await shipping.update({
      tracking_number: newTrackingNumber,
      shipping_status: 'shipped',
      retry_count: shipping.retry_count + 1,
      last_retry_at: new Date(),
      error_message: null
    });

    await shipping.order.update({
      delivery_no: newTrackingNumber,
      delivery_time: new Date()
    });
  }

  // 发货性能分析 - 新增
  async getPerformanceAnalysis() {
    const [analysis] = await sequelize.query(`
      SELECT 
        AVG(TIMESTAMPDIFF(MINUTE, o.pay_time, ash.shipping_time)) as avg_processing_minutes,
        COUNT(CASE WHEN ash.shipping_status = 'shipped' THEN 1 END) / COUNT(*) * 100 as success_rate,
        COUNT(CASE WHEN ash.retry_count > 0 THEN 1 END) / COUNT(*) * 100 as retry_rate
      FROM auto_shipping ash
      JOIN orders o ON ash.order_id = o.id
      WHERE ash.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    `, { type: sequelize.QueryTypes.SELECT });

    return analysis;
  }

  // 更新配置
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    console.log('📝 自动发货配置已更新:', this.config);
  }

  // 获取配置
  getConfig() {
    return this.config;
  }
}

module.exports = new OptimizedAutoShippingService();
