// 自动发货服务
const { Op, sequelize } = require('sequelize');
const { Order, AutoShipping, Product, OrderItem } = require('../models');

class AutoShippingService {

  // 自动发货配置
  constructor() {
    this.shippingCompanies = [
      { code: 'SF', name: '顺丰速运', priority: 1 },
      { code: 'YTO', name: '圆通速递', priority: 2 },
      { code: 'ZTO', name: '中通快递', priority: 3 },
      { code: 'STO', name: '申通快递', priority: 4 },
      { code: 'YD', name: '韵达速递', priority: 5 }
    ];
    
    this.autoShippingRules = {
      // 自动发货条件
      enableAutoShipping: true,
      // 支付后多少小时自动发货
      autoShippingDelay: 2,
      // 工作时间发货（9:00-18:00）
      workingHoursOnly: true,
      // 排除周末
      excludeWeekends: false
    };
  }

  // 检查待发货订单并自动发货
  async processAutoShipping() {
    try {
      console.log('开始检查待发货订单...');
      
      if (!this.autoShippingRules.enableAutoShipping) {
        console.log('自动发货功能已禁用');
        return 0;
      }

      // 获取符合自动发货条件的订单
      const eligibleOrders = await this.getEligibleOrders();
      
      let processedCount = 0;
      
      for (const order of eligibleOrders) {
        try {
          await this.processOrderShipping(order);
          processedCount++;
        } catch (error) {
          console.error(`处理订单 ${order.order_no} 发货失败:`, error.message);
        }
      }

      console.log(`自动发货处理完成，共处理 ${processedCount} 个订单`);
      return processedCount;
    } catch (error) {
      console.error('自动发货处理失败:', error);
      throw new Error('自动发货处理失败');
    }
  }

  // 获取符合自动发货条件的订单
  async getEligibleOrders() {
    try {
      const now = new Date();
      const delayHours = this.autoShippingRules.autoShippingDelay;
      const cutoffTime = new Date(now.getTime() - delayHours * 60 * 60 * 1000);

      // 工作时间检查
      if (this.autoShippingRules.workingHoursOnly) {
        const currentHour = now.getHours();
        if (currentHour < 9 || currentHour >= 18) {
          console.log('当前不在工作时间，跳过自动发货');
          return [];
        }
      }

      // 周末检查
      if (this.autoShippingRules.excludeWeekends) {
        const dayOfWeek = now.getDay();
        if (dayOfWeek === 0 || dayOfWeek === 6) {
          console.log('周末不自动发货');
          return [];
        }
      }

      const orders = await Order.findAll({
        where: {
          order_status: 1, // 待发货
          pay_status: 1,   // 已支付
          pay_time: {
            [Op.lte]: cutoffTime
          }
        },
        include: [
          {
            model: OrderItem,
            as: 'orderItems',
            include: [
              {
                model: Product,
                as: 'product',
                attributes: ['id', 'name', 'stock', 'weight']
              }
            ]
          }
        ]
      });

      // 过滤掉已有自动发货记录的订单
      const eligibleOrders = [];
      for (const order of orders) {
        const existingShipping = await AutoShipping.findOne({
          where: { order_id: order.id }
        });
        
        if (!existingShipping) {
          // 检查库存是否充足
          const hasStock = await this.checkOrderStock(order);
          if (hasStock) {
            eligibleOrders.push(order);
          }
        }
      }

      return eligibleOrders;
    } catch (error) {
      console.error('获取符合条件的订单失败:', error);
      throw error;
    }
  }

  // 检查订单库存
  async checkOrderStock(order) {
    try {
      for (const item of order.orderItems) {
        if (item.product.stock < item.quantity) {
          console.log(`订单 ${order.order_no} 商品 ${item.product.name} 库存不足`);
          return false;
        }
      }
      return true;
    } catch (error) {
      console.error('检查订单库存失败:', error);
      return false;
    }
  }

  // 处理订单发货
  async processOrderShipping(order) {
    try {
      console.log(`开始处理订单 ${order.order_no} 的自动发货`);

      // 选择快递公司
      const shippingCompany = this.selectShippingCompany(order);
      
      // 生成快递单号
      const trackingNumber = this.generateTrackingNumber(shippingCompany.code);
      
      // 计算预计送达时间
      const estimatedDelivery = this.calculateEstimatedDelivery(order);

      // 创建自动发货记录
      const autoShipping = await AutoShipping.create({
        order_id: order.id,
        shipping_company: shippingCompany.name,
        tracking_number: trackingNumber,
        shipping_status: 'processing',
        auto_generated: true,
        shipping_time: new Date(),
        estimated_delivery: estimatedDelivery,
        shipping_cost: this.calculateShippingCost(order)
      });

      // 更新订单状态
      await order.update({
        order_status: 2, // 待收货
        delivery_company: shippingCompany.name,
        delivery_no: trackingNumber,
        delivery_time: new Date()
      });

      // 扣减库存
      await this.deductStock(order);

      // 更新发货状态
      await autoShipping.update({
        shipping_status: 'shipped'
      });

      console.log(`订单 ${order.order_no} 自动发货成功，快递单号: ${trackingNumber}`);
      
      return autoShipping;
    } catch (error) {
      console.error(`订单 ${order.order_no} 自动发货失败:`, error);
      
      // 记录失败信息
      await AutoShipping.create({
        order_id: order.id,
        shipping_company: '',
        tracking_number: '',
        shipping_status: 'failed',
        auto_generated: true,
        error_message: error.message,
        retry_count: 0
      });
      
      throw error;
    }
  }

  // 选择快递公司
  selectShippingCompany(order) {
    // 根据订单金额、重量、地址等选择最优快递公司
    // 这里简化为按优先级选择
    return this.shippingCompanies[0]; // 默认选择顺丰
  }

  // 生成快递单号
  generateTrackingNumber(companyCode) {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `${companyCode}${timestamp}${random}`;
  }

  // 计算预计送达时间
  calculateEstimatedDelivery(order) {
    const now = new Date();
    // 默认3天后送达
    const deliveryDate = new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000);
    return deliveryDate;
  }

  // 计算运费成本
  calculateShippingCost(order) {
    // 根据重量、距离等计算运费
    // 这里简化为固定费用
    return 10.00;
  }

  // 扣减库存
  async deductStock(order) {
    try {
      for (const item of order.orderItems) {
        await Product.decrement('stock', {
          by: item.quantity,
          where: { id: item.product_id }
        });
        
        await Product.increment('sales', {
          by: item.quantity,
          where: { id: item.product_id }
        });
      }
    } catch (error) {
      console.error('扣减库存失败:', error);
      throw error;
    }
  }

  // 获取发货记录
  async getShippingRecords(filters = {}) {
    try {
      const {
        orderId,
        status,
        startDate,
        endDate,
        page = 1,
        limit = 20
      } = filters;

      const whereCondition = {};
      
      if (orderId) {
        whereCondition.order_id = orderId;
      }
      
      if (status) {
        whereCondition.shipping_status = status;
      }
      
      if (startDate && endDate) {
        whereCondition.created_at = {
          [Op.between]: [startDate, endDate]
        };
      }

      const offset = (page - 1) * limit;

      const { count, rows } = await AutoShipping.findAndCountAll({
        where: whereCondition,
        include: [
          {
            model: Order,
            as: 'order',
            attributes: ['id', 'order_no', 'receiver_name', 'receiver_phone', 'receiver_address']
          }
        ],
        order: [['created_at', 'DESC']],
        limit: parseInt(limit),
        offset
      });

      return {
        total: count,
        records: rows,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit)
      };
    } catch (error) {
      console.error('获取发货记录失败:', error);
      throw new Error('获取发货记录失败');
    }
  }

  // 重试失败的发货
  async retryFailedShipping(shippingId) {
    try {
      const shipping = await AutoShipping.findByPk(shippingId, {
        include: [
          {
            model: Order,
            as: 'order',
            include: [
              {
                model: OrderItem,
                as: 'orderItems',
                include: [
                  {
                    model: Product,
                    as: 'product'
                  }
                ]
              }
            ]
          }
        ]
      });

      if (!shipping || shipping.shipping_status !== 'failed') {
        throw new Error('发货记录不存在或状态不正确');
      }

      // 更新重试次数
      await shipping.update({
        retry_count: shipping.retry_count + 1,
        last_retry_at: new Date(),
        shipping_status: 'processing',
        error_message: null
      });

      // 重新处理发货
      await this.processOrderShipping(shipping.order);
      
      return shipping;
    } catch (error) {
      console.error('重试发货失败:', error);
      
      // 更新失败信息
      await AutoShipping.update({
        shipping_status: 'failed',
        error_message: error.message
      }, {
        where: { id: shippingId }
      });
      
      throw error;
    }
  }

  // 更新自动发货规则
  updateShippingRules(rules) {
    this.autoShippingRules = { ...this.autoShippingRules, ...rules };
    console.log('自动发货规则已更新:', this.autoShippingRules);
  }

  // 获取发货统计
  async getShippingStats() {
    try {
      const stats = await AutoShipping.findAll({
        attributes: [
          'shipping_status',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['shipping_status'],
        raw: true
      });

      const formattedStats = {
        total: 0,
        byStatus: {}
      };

      stats.forEach(stat => {
        const count = parseInt(stat.count);
        formattedStats.total += count;
        formattedStats.byStatus[stat.shipping_status] = count;
      });

      return formattedStats;
    } catch (error) {
      console.error('获取发货统计失败:', error);
      throw new Error('获取发货统计失败');
    }
  }
}

module.exports = new AutoShippingService();
