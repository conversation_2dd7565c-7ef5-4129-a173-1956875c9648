const { Banner } = require('../models');
const RedisUtils = require('../utils/redis');
const cdnUtils = require('../utils/cdn');
const config = require('../config');

class BannerService {
  // 缓存键名
  static CACHE_KEY = 'banners:list';

  // 清理轮播图缓存
  async clearCache() {
    try {
      await RedisUtils.del(BannerService.CACHE_KEY);
      console.log('✅ 轮播图缓存已清理');
      return true;
    } catch (error) {
      console.error('❌ 清理轮播图缓存失败:', error);
      return false;
    }
  }

  /**
   * 获取轮播图列表（带CDN支持）
   */
  async getBannerList() {
    try {
      const cacheKey = 'banners:list';
      let banners = await RedisUtils.get(cacheKey);

      if (!banners) {
        banners = await Banner.findAll({
          where: { status: 1 },
          order: [['sort_order', 'ASC'], ['created_at', 'DESC']],
          raw: true // 返回纯对象，不包含Sequelize内部属性
        });

        // 应用CDN转换后再缓存
        const transformedBanners = cdnUtils.transformImages(banners, 'large');
        await RedisUtils.set(cacheKey, JSON.stringify(transformedBanners), config.bannerCache.expireSeconds);
        
        return transformedBanners;
      }

      // 从缓存获取数据并解析
      try {
        const parsedBanners = JSON.parse(banners);
        // 强制再次转换，确保返回HTTPS URL
        const finalBanners = cdnUtils.transformImages(parsedBanners, 'large');
        return finalBanners;
      } catch (parseError) {
        console.warn('缓存数据解析失败，重新获取数据');
        // 清除错误缓存
        await RedisUtils.del(cacheKey);
        // 递归调用重新获取
        return await this.getBannerList();
      }
    } catch (error) {
      console.error('获取轮播图列表失败:', error);
      throw error;
    }
  }

  // 创建轮播图（带缓存清理）
  async createBanner(bannerData) {
    try {
      const banner = await Banner.create(bannerData);
      // 创建成功后清理缓存
      await this.clearCache();
      return banner;
    } catch (error) {
      console.error('创建轮播图失败:', error);
      throw error;
    }
  }

  // 更新轮播图（带缓存清理）
  async updateBanner(id, updateData) {
    try {
      const banner = await Banner.findByPk(id);
      if (!banner) {
        throw new Error('轮播图不存在');
      }
      await banner.update(updateData);
      // 更新成功后清理缓存
      await this.clearCache();
      return banner;
    } catch (error) {
      console.error('更新轮播图失败:', error);
      throw error;
    }
  }

  // 删除轮播图（带缓存清理）
  async deleteBanner(id) {
    try {
      const banner = await Banner.findByPk(id);
      if (!banner) {
        throw new Error('轮播图不存在');
      }
      await banner.destroy();
      // 删除成功后清理缓存
      await this.clearCache();
      return true;
    } catch (error) {
      console.error('删除轮播图失败:', error);
      throw error;
    }
  }
}

module.exports = new BannerService(); 