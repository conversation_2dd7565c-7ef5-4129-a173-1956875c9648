// 智能浏览历史服务 - 优化完善版
const { Op, sequelize } = require('sequelize');
const { BrowseHistory, Product, Category, User } = require('../models');

class EnhancedBrowseHistoryService {
  
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 3 * 60 * 1000; // 3分钟缓存
    this.behaviorQueue = [];
    this.batchSize = 50;
    this.flushInterval = 30000; // 30秒批量写入
    
    this.startBatchProcessor();
  }

  // 启动批量处理器
  startBatchProcessor() {
    setInterval(() => {
      if (this.behaviorQueue.length > 0) {
        this.flushBehaviorQueue();
      }
    }, this.flushInterval);
  }

  // 智能记录浏览行为
  async smartRecordBrowse(userId, productId, options = {}) {
    try {
      const {
        source = 'direct',
        duration = 0,
        deviceInfo = null,
        referrer = null,
        searchKeyword = null,
        sessionId = null
      } = options;

      // 添加到批量队列
      this.behaviorQueue.push({
        user_id: userId,
        product_id: productId,
        browse_time: new Date(),
        browse_duration: duration,
        source,
        device_info: deviceInfo,
        referrer,
        search_keyword: searchKeyword,
        session_id: sessionId,
        timestamp: Date.now()
      });

      // 如果队列满了，立即处理
      if (this.behaviorQueue.length >= this.batchSize) {
        await this.flushBehaviorQueue();
      }

      // 异步更新用户画像
      setImmediate(() => {
        this.updateUserProfile(userId, productId, options);
      });

      return { message: '浏览记录成功' };
    } catch (error) {
      console.error('智能记录浏览失败:', error);
      return null;
    }
  }

  // 批量写入浏览记录
  async flushBehaviorQueue() {
    if (this.behaviorQueue.length === 0) return;

    const behaviors = this.behaviorQueue.splice(0, this.batchSize);
    
    try {
      // 去重处理：同一用户同一商品同一天只保留最新记录
      const processedBehaviors = await this.deduplicateBehaviors(behaviors);
      
      if (processedBehaviors.length > 0) {
        await BrowseHistory.bulkCreate(processedBehaviors, {
          updateOnDuplicate: ['browse_time', 'browse_duration', 'source', 'device_info'],
          ignoreDuplicates: false
        });
        
        console.log(`📊 批量写入 ${processedBehaviors.length} 条浏览记录`);
      }
    } catch (error) {
      console.error('批量写入浏览记录失败:', error);
      // 失败的记录重新加入队列
      this.behaviorQueue.unshift(...behaviors);
    }
  }

  // 去重处理浏览记录
  async deduplicateBehaviors(behaviors) {
    const uniqueMap = new Map();
    
    behaviors.forEach(behavior => {
      const key = `${behavior.user_id}_${behavior.product_id}_${behavior.browse_time.toDateString()}`;
      const existing = uniqueMap.get(key);
      
      if (!existing || behavior.browse_duration > existing.browse_duration) {
        uniqueMap.set(key, behavior);
      }
    });
    
    return Array.from(uniqueMap.values());
  }

  // 获取智能浏览历史
  async getSmartBrowseHistory(userId, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        groupBy = 'date', // date, category, source
        timeRange = 30, // 天数
        includeAnalysis = true
      } = options;

      const cacheKey = `browse_history_${userId}_${page}_${limit}_${groupBy}_${timeRange}`;
      const cached = this.cache.get(cacheKey);
      
      if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }

      const offset = (page - 1) * limit;
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - timeRange);

      let groupClause = '';
      let orderClause = 'ORDER BY bh.browse_time DESC';

      switch (groupBy) {
        case 'date':
          groupClause = 'DATE(bh.browse_time)';
          break;
        case 'category':
          groupClause = 'p.category_id';
          orderClause = 'ORDER BY COUNT(*) DESC, MAX(bh.browse_time) DESC';
          break;
        case 'source':
          groupClause = 'bh.source';
          orderClause = 'ORDER BY COUNT(*) DESC, MAX(bh.browse_time) DESC';
          break;
      }

      const [results] = await sequelize.query(`
        SELECT 
          bh.id, bh.browse_time, bh.browse_duration, bh.source,
          p.id as product_id, p.name, p.price, p.main_image, p.sales, p.rating, p.stock,
          c.id as category_id, c.name as category_name,
          ${groupBy === 'date' ? 'DATE(bh.browse_time) as group_key,' : ''}
          ${groupBy === 'category' ? 'c.name as group_key,' : ''}
          ${groupBy === 'source' ? 'bh.source as group_key,' : ''}
          COUNT(*) OVER (PARTITION BY ${groupClause || 'bh.id'}) as group_count
        FROM browse_history bh
        JOIN products p ON bh.product_id = p.id
        JOIN categories c ON p.category_id = c.id
        WHERE bh.user_id = :userId 
        AND bh.browse_time >= :startDate
        AND p.status = 1
        ${orderClause}
        LIMIT :limit OFFSET :offset
      `, {
        replacements: { userId, startDate, limit, offset },
        type: sequelize.QueryTypes.SELECT
      });

      // 分组处理结果
      const groupedResults = this.groupHistoryResults(results, groupBy);

      // 获取分析数据
      let analysis = null;
      if (includeAnalysis) {
        analysis = await this.getBrowseAnalysis(userId, timeRange);
      }

      const response = {
        history: groupedResults,
        total: results.length,
        page: parseInt(page),
        limit: parseInt(limit),
        groupBy,
        timeRange,
        analysis
      };

      // 缓存结果
      this.cache.set(cacheKey, {
        data: response,
        timestamp: Date.now()
      });

      return response;
    } catch (error) {
      console.error('获取智能浏览历史失败:', error);
      throw new Error('获取浏览历史失败');
    }
  }

  // 分组历史结果
  groupHistoryResults(results, groupBy) {
    if (groupBy === 'none') return results;

    const groups = new Map();
    
    results.forEach(item => {
      const key = item.group_key;
      if (!groups.has(key)) {
        groups.set(key, {
          groupKey: key,
          groupCount: item.group_count,
          items: []
        });
      }
      groups.get(key).items.push(item);
    });

    return Array.from(groups.values());
  }

  // 获取浏览分析
  async getBrowseAnalysis(userId, days = 30) {
    try {
      const [analysis] = await sequelize.query(`
        SELECT 
          COUNT(*) as total_views,
          COUNT(DISTINCT product_id) as unique_products,
          COUNT(DISTINCT DATE(browse_time)) as active_days,
          AVG(browse_duration) as avg_duration,
          MAX(browse_time) as last_browse,
          (
            SELECT source 
            FROM browse_history 
            WHERE user_id = :userId 
            AND browse_time >= DATE_SUB(NOW(), INTERVAL :days DAY)
            GROUP BY source 
            ORDER BY COUNT(*) DESC 
            LIMIT 1
          ) as primary_source,
          (
            SELECT c.name
            FROM browse_history bh
            JOIN products p ON bh.product_id = p.id
            JOIN categories c ON p.category_id = c.id
            WHERE bh.user_id = :userId
            AND bh.browse_time >= DATE_SUB(NOW(), INTERVAL :days DAY)
            GROUP BY c.id
            ORDER BY COUNT(*) DESC
            LIMIT 1
          ) as favorite_category
        FROM browse_history
        WHERE user_id = :userId
        AND browse_time >= DATE_SUB(NOW(), INTERVAL :days DAY)
      `, {
        replacements: { userId, days },
        type: sequelize.QueryTypes.SELECT
      });

      // 获取浏览时段分析
      const [hourlyAnalysis] = await sequelize.query(`
        SELECT 
          HOUR(browse_time) as hour,
          COUNT(*) as count,
          AVG(browse_duration) as avg_duration
        FROM browse_history
        WHERE user_id = :userId
        AND browse_time >= DATE_SUB(NOW(), INTERVAL :days DAY)
        GROUP BY HOUR(browse_time)
        ORDER BY hour
      `, {
        replacements: { userId, days },
        type: sequelize.QueryTypes.SELECT
      });

      return {
        ...analysis,
        hourlyPattern: hourlyAnalysis,
        insights: this.generateBrowseInsights(analysis, hourlyAnalysis)
      };
    } catch (error) {
      console.error('获取浏览分析失败:', error);
      return null;
    }
  }

  // 生成浏览洞察
  generateBrowseInsights(analysis, hourlyPattern) {
    const insights = [];

    // 活跃度洞察
    if (analysis.active_days >= 20) {
      insights.push('您是我们的忠实用户，几乎每天都在浏览商品');
    } else if (analysis.active_days >= 10) {
      insights.push('您经常浏览我们的商品，是活跃用户');
    }

    // 浏览时长洞察
    if (analysis.avg_duration > 60) {
      insights.push('您对商品很感兴趣，平均浏览时间较长');
    }

    // 时段偏好洞察
    const peakHour = hourlyPattern.reduce((max, current) => 
      current.count > max.count ? current : max, { hour: 0, count: 0 });
    
    if (peakHour.hour >= 9 && peakHour.hour <= 17) {
      insights.push('您习惯在工作时间浏览商品');
    } else if (peakHour.hour >= 19 && peakHour.hour <= 22) {
      insights.push('您喜欢在晚上休闲时间浏览商品');
    }

    return insights;
  }

  // 智能推荐基于浏览历史
  async getSmartRecommendations(userId, options = {}) {
    try {
      const { limit = 10, algorithm = 'hybrid' } = options;
      
      const cacheKey = `recommendations_${userId}_${algorithm}_${limit}`;
      const cached = this.cache.get(cacheKey);
      
      if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }

      let recommendations = [];

      switch (algorithm) {
        case 'collaborative':
          recommendations = await this.getCollaborativeRecommendations(userId, limit);
          break;
        case 'content':
          recommendations = await this.getContentBasedRecommendations(userId, limit);
          break;
        case 'hybrid':
        default:
          const [collaborative, content] = await Promise.all([
            this.getCollaborativeRecommendations(userId, limit / 2),
            this.getContentBasedRecommendations(userId, limit / 2)
          ]);
          recommendations = [...collaborative, ...content];
          break;
      }

      // 去重并限制数量
      const uniqueRecommendations = this.deduplicateProducts(recommendations).slice(0, limit);

      // 缓存结果
      this.cache.set(cacheKey, {
        data: uniqueRecommendations,
        timestamp: Date.now()
      });

      return uniqueRecommendations;
    } catch (error) {
      console.error('获取智能推荐失败:', error);
      return [];
    }
  }

  // 协同过滤推荐
  async getCollaborativeRecommendations(userId, limit) {
    const [results] = await sequelize.query(`
      SELECT DISTINCT
        p.id, p.name, p.price, p.main_image, p.sales, p.rating,
        COUNT(*) as similarity_score
      FROM browse_history bh1
      JOIN browse_history bh2 ON bh1.product_id = bh2.product_id AND bh1.user_id != bh2.user_id
      JOIN browse_history bh3 ON bh2.user_id = bh3.user_id AND bh3.product_id != bh1.product_id
      JOIN products p ON bh3.product_id = p.id
      WHERE bh1.user_id = :userId
      AND p.status = 1
      AND p.id NOT IN (
        SELECT product_id FROM browse_history WHERE user_id = :userId
      )
      GROUP BY p.id
      ORDER BY similarity_score DESC, p.sales DESC
      LIMIT :limit
    `, {
      replacements: { userId, limit },
      type: sequelize.QueryTypes.SELECT
    });

    return results.map(item => ({ ...item, algorithm: 'collaborative' }));
  }

  // 基于内容的推荐
  async getContentBasedRecommendations(userId, limit) {
    const [results] = await sequelize.query(`
      SELECT 
        p.id, p.name, p.price, p.main_image, p.sales, p.rating,
        p.category_id, c.name as category_name,
        COUNT(*) as category_interest
      FROM browse_history bh
      JOIN products bp ON bh.product_id = bp.id
      JOIN products p ON bp.category_id = p.category_id AND p.id != bp.id
      JOIN categories c ON p.category_id = c.id
      WHERE bh.user_id = :userId
      AND p.status = 1
      AND p.id NOT IN (
        SELECT product_id FROM browse_history WHERE user_id = :userId
      )
      GROUP BY p.id
      ORDER BY category_interest DESC, p.sales DESC
      LIMIT :limit
    `, {
      replacements: { userId, limit },
      type: sequelize.QueryTypes.SELECT
    });

    return results.map(item => ({ ...item, algorithm: 'content' }));
  }

  // 去重商品
  deduplicateProducts(products) {
    const seen = new Set();
    return products.filter(product => {
      if (seen.has(product.id)) return false;
      seen.add(product.id);
      return true;
    });
  }

  // 更新用户画像
  async updateUserProfile(userId, productId, options) {
    try {
      // 这里可以实现用户画像更新逻辑
      // 例如：更新用户的兴趣标签、活跃时段、价格偏好等
      console.log(`更新用户 ${userId} 画像，基于浏览商品 ${productId}`);
    } catch (error) {
      console.error('更新用户画像失败:', error);
    }
  }

  // 获取浏览热力图数据
  async getBrowseHeatmap(userId, days = 30) {
    try {
      const [heatmapData] = await sequelize.query(`
        SELECT 
          DAYOFWEEK(browse_time) as day_of_week,
          HOUR(browse_time) as hour,
          COUNT(*) as activity_count,
          AVG(browse_duration) as avg_duration
        FROM browse_history
        WHERE user_id = :userId
        AND browse_time >= DATE_SUB(NOW(), INTERVAL :days DAY)
        GROUP BY DAYOFWEEK(browse_time), HOUR(browse_time)
        ORDER BY day_of_week, hour
      `, {
        replacements: { userId, days },
        type: sequelize.QueryTypes.SELECT
      });

      return heatmapData;
    } catch (error) {
      console.error('获取浏览热力图失败:', error);
      return [];
    }
  }

  // 清理缓存
  clearCache(pattern = null) {
    if (pattern) {
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }

  // 获取队列状态
  getQueueStatus() {
    return {
      queueLength: this.behaviorQueue.length,
      batchSize: this.batchSize,
      flushInterval: this.flushInterval
    };
  }
}

module.exports = new EnhancedBrowseHistoryService();
