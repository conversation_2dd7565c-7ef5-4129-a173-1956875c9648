// 浏览历史服务
const { Op, sequelize } = require('sequelize');
const { BrowseHistory, Product, Category } = require('../models');

class BrowseHistoryService {

  // 记录浏览历史
  async recordBrowse(userId, productId, options = {}) {
    try {
      const { source = 'direct', duration = 0, deviceInfo = null } = options;

      // 检查商品是否存在
      const product = await Product.findByPk(productId);
      if (!product) return null;

      // 检查今日是否已有浏览记录
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const existingRecord = await BrowseHistory.findOne({
        where: {
          user_id: userId,
          product_id: productId,
          browse_time: { [Op.gte]: today, [Op.lt]: tomorrow }
        }
      });

      if (existingRecord) {
        // 更新浏览时间和时长
        return await existingRecord.update({
          browse_time: new Date(),
          browse_duration: Math.max(existingRecord.browse_duration, duration),
          source, device_info: deviceInfo
        });
      } else {
        // 创建新记录
        return await BrowseHistory.create({
          user_id: userId, product_id: productId, browse_time: new Date(),
          browse_duration: duration, source, device_info: deviceInfo
        });
      }
    } catch (error) {
      console.error('记录浏览历史失败:', error);
      return null;
    }
  }

  // 获取浏览历史列表
  async getBrowseHistory(userId, page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;

      const { count, rows } = await BrowseHistory.findAndCountAll({
        where: { user_id: userId },
        include: [
          {
            model: Product,
            as: 'product',
            attributes: [
              'id', 'name', 'price', 'original_price', 'main_image',
              'sales', 'rating', 'stock', 'status'
            ],
            include: [
              {
                model: Category,
                as: 'category',
                attributes: ['id', 'name']
              }
            ]
          }
        ],
        order: [['browse_time', 'DESC']],
        limit: parseInt(limit),
        offset
      });

      // 过滤掉已下架的商品
      const validHistory = rows.filter(item => item.product && item.product.status === 1);

      return {
        total: count,
        history: validHistory,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit)
      };
    } catch (error) {
      console.error('获取浏览历史失败:', error);
      throw new Error('获取浏览历史失败');
    }
  }

  // 获取最近浏览的商品
  async getRecentBrowsed(userId, limit = 10) {
    try {
      const recentHistory = await BrowseHistory.findAll({
        where: { user_id: userId },
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['id', 'name', 'price', 'main_image', 'sales', 'rating'],
            where: { status: 1 }
          }
        ],
        order: [['browse_time', 'DESC']],
        limit: parseInt(limit)
      });

      return recentHistory.map(item => ({
        ...item.product.dataValues,
        browseTime: item.browse_time,
        browseDuration: item.browse_duration,
        source: item.source
      }));
    } catch (error) {
      console.error('获取最近浏览商品失败:', error);
      throw new Error('获取最近浏览商品失败');
    }
  }

  // 清空浏览历史
  async clearBrowseHistory(userId) {
    try {
      const result = await BrowseHistory.destroy({
        where: { user_id: userId }
      });

      return { message: '浏览历史已清空', deletedCount: result };
    } catch (error) {
      console.error('清空浏览历史失败:', error);
      throw new Error('清空浏览历史失败');
    }
  }

  // 删除指定商品的浏览记录
  async removeBrowseRecord(userId, productId) {
    try {
      const result = await BrowseHistory.destroy({
        where: { user_id: userId, product_id: productId }
      });

      if (result === 0) {
        throw new Error('浏览记录不存在');
      }

      return { message: '删除成功' };
    } catch (error) {
      console.error('删除浏览记录失败:', error);
      throw new Error('删除浏览记录失败');
    }
  }

  // 获取浏览统计
  async getBrowseStats(userId) {
    try {
      const totalCount = await BrowseHistory.count({
        where: { user_id: userId }
      });

      // 按来源统计
      const sourceStats = await BrowseHistory.findAll({
        where: { user_id: userId },
        attributes: [
          'source',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['source'],
        raw: true
      });

      // 按分类统计
      const categoryStats = await BrowseHistory.findAll({
        where: { user_id: userId },
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['category_id'],
            include: [
              {
                model: Category,
                as: 'category',
                attributes: ['id', 'name']
              }
            ]
          }
        ],
        attributes: [
          [sequelize.fn('COUNT', sequelize.col('BrowseHistory.id')), 'count']
        ],
        group: ['product.category_id'],
        raw: false
      });

      // 最近7天浏览趋势
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      const dailyStats = await BrowseHistory.findAll({
        where: {
          user_id: userId,
          browse_time: { [Op.gte]: sevenDaysAgo }
        },
        attributes: [
          [sequelize.fn('DATE', sequelize.col('browse_time')), 'date'],
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: [sequelize.fn('DATE', sequelize.col('browse_time'))],
        order: [[sequelize.fn('DATE', sequelize.col('browse_time')), 'ASC']],
        raw: true
      });

      return {
        totalCount,
        sourceStats,
        categoryStats: categoryStats.map(item => ({
          categoryId: item.product?.category_id,
          categoryName: item.product?.category?.name,
          count: parseInt(item.dataValues.count)
        })),
        dailyStats
      };
    } catch (error) {
      console.error('获取浏览统计失败:', error);
      throw new Error('获取浏览统计失败');
    }
  }

  // 基于浏览历史推荐商品
  async getRecommendedProducts(userId, limit = 10) {
    try {
      // 获取用户浏览过的分类
      const browsedCategories = await BrowseHistory.findAll({
        where: { user_id: userId },
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['category_id']
          }
        ],
        attributes: [
          'product.category_id',
          [sequelize.fn('COUNT', sequelize.col('BrowseHistory.id')), 'browse_count']
        ],
        group: ['product.category_id'],
        order: [[sequelize.fn('COUNT', sequelize.col('BrowseHistory.id')), 'DESC']],
        limit: 5,
        raw: false
      });

      if (browsedCategories.length === 0) {
        return [];
      }

      const categoryIds = browsedCategories.map(item => item.product.category_id);

      // 获取用户已浏览过的商品ID
      const browsedProductIds = await BrowseHistory.findAll({
        where: { user_id: userId },
        attributes: ['product_id'],
        raw: true
      }).then(records => records.map(r => r.product_id));

      // 推荐同分类的其他商品
      const recommendedProducts = await Product.findAll({
        where: {
          category_id: { [Op.in]: categoryIds },
          id: { [Op.notIn]: browsedProductIds },
          status: 1
        },
        attributes: ['id', 'name', 'price', 'main_image', 'sales', 'rating'],
        order: [
          ['is_recommend', 'DESC'],
          ['sales', 'DESC'],
          ['rating', 'DESC']
        ],
        limit: parseInt(limit)
      });

      return recommendedProducts;
    } catch (error) {
      console.error('获取推荐商品失败:', error);
      throw new Error('获取推荐商品失败');
    }
  }

  // 自动清理旧的浏览记录
  async cleanupOldRecords(daysOld = 90) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const deletedCount = await BrowseHistory.destroy({
        where: {
          browse_time: { [Op.lt]: cutoffDate }
        }
      });

      console.log(`清理了 ${deletedCount} 条旧的浏览记录`);
      return deletedCount;
    } catch (error) {
      console.error('清理旧浏览记录失败:', error);
      throw new Error('清理旧浏览记录失败');
    }
  }
}

module.exports = new BrowseHistoryService();
