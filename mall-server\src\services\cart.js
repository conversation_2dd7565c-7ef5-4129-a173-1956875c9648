const { CartItem, Product } = require('../models');
const RedisUtils = require('../utils/redis');

class CartService {
  // 获取购物车列表
  async getCartList(userId) {
    const cartItems = await CartItem.findAll({
      where: { user_id: userId },
      include: [
        {
          model: Product,
          as: 'product',
          where: { status: 1 },
          required: false
        }
      ],
      order: [['created_at', 'DESC']]
    });

    // 过滤掉已下架的商品
    return cartItems.filter(item => item.product);
  }

  // 添加商品到购物车
  async addToCart(userId, productId, quantity = 1) {
    // 检查商品是否存在
    const product = await Product.findOne({
      where: { id: productId, status: 1 }
    });

    if (!product) {
      throw new Error('商品不存在或已下架');
    }

    // 检查库存
    if (product.stock < quantity) {
      throw new Error('商品库存不足');
    }

    // 检查购物车是否已有该商品
    const existingItem = await CartItem.findOne({
      where: { user_id: userId, product_id: productId }
    });

    if (existingItem) {
      // 更新数量
      const newQuantity = existingItem.quantity + quantity;
      if (product.stock < newQuantity) {
        throw new Error('商品库存不足');
      }
      
      await existingItem.update({ quantity: newQuantity });
      return existingItem;
    } else {
      // 新增购物车项
      return await CartItem.create({
        user_id: userId,
        product_id: productId,
        quantity,
        selected: 1
      });
    }
  }

  // 更新购物车商品数量
  async updateCartItem(userId, cartItemId, quantity) {
    const cartItem = await CartItem.findOne({
      where: { id: cartItemId, user_id: userId },
      include: [
        {
          model: Product,
          as: 'product'
        }
      ]
    });

    if (!cartItem) {
      throw new Error('购物车商品不存在');
    }

    if (!cartItem.product || cartItem.product.status !== 1) {
      throw new Error('商品已下架');
    }

    if (cartItem.product.stock < quantity) {
      throw new Error('商品库存不足');
    }

    await cartItem.update({ quantity });
    return cartItem;
  }

  // 删除购物车商品
  async removeFromCart(userId, cartItemId) {
    const cartItem = await CartItem.findOne({
      where: { id: cartItemId, user_id: userId }
    });

    if (!cartItem) {
      throw new Error('购物车商品不存在');
    }

    await cartItem.destroy();
    return true;
  }

  // 选择/取消选择购物车商品
  async toggleCartItem(userId, cartItemId, selected) {
    const cartItem = await CartItem.findOne({
      where: { id: cartItemId, user_id: userId }
    });

    if (!cartItem) {
      throw new Error('购物车商品不存在');
    }

    await cartItem.update({ selected });
    return cartItem;
  }

  // 全选/取消全选购物车商品
  async toggleAllCartItems(userId, selected) {
    await CartItem.update(
      { selected },
      { where: { user_id: userId } }
    );

    return true;
  }

  // 清空购物车
  async clearCart(userId) {
    await CartItem.destroy({
      where: { user_id: userId }
    });

    return true;
  }

  // 获取购物车统计
  async getCartStats(userId) {
    const cartItems = await CartItem.findAll({
      where: { user_id: userId },
      include: [
        {
          model: Product,
          as: 'product',
          where: { status: 1 },
          required: false
        }
      ]
    });

    const validItems = cartItems.filter(item => item.product);
    
    const totalCount = validItems.reduce((sum, item) => sum + item.quantity, 0);
    const totalAmount = validItems.reduce((sum, item) => {
      return sum + (item.product.price * item.quantity * item.selected);
    }, 0);

    const selectedCount = validItems
      .filter(item => item.selected)
      .reduce((sum, item) => sum + item.quantity, 0);

    const selectedAmount = validItems
      .filter(item => item.selected)
      .reduce((sum, item) => sum + (item.product.price * item.quantity), 0);

    return {
      totalCount,
      totalAmount,
      selectedCount,
      selectedAmount,
      itemCount: validItems.length
    };
  }

  // 获取购物车商品数量
  async getCartCount(userId) {
    const count = await CartItem.count({
      where: { user_id: userId },
      include: [
        {
          model: Product,
          as: 'product',
          where: { status: 1 },
          required: true
        }
      ]
    });

    return count;
  }

  // 批量删除选中的购物车商品
  async removeSelectedItems(userId) {
    await CartItem.destroy({
      where: { 
        user_id: userId,
        selected: 1
      }
    });

    return true;
  }

  // 检查购物车商品库存
  async checkCartStock(userId) {
    const cartItems = await CartItem.findAll({
      where: { user_id: userId },
      include: [
        {
          model: Product,
          as: 'product'
        }
      ]
    });

    const stockIssues = [];

    for (const item of cartItems) {
      if (!item.product || item.product.status !== 1) {
        stockIssues.push({
          cartItemId: item.id,
          productName: item.product?.name || '未知商品',
          issue: '商品已下架'
        });
      } else if (item.product.stock < item.quantity) {
        stockIssues.push({
          cartItemId: item.id,
          productName: item.product.name,
          issue: `库存不足，当前库存: ${item.product.stock}，需要: ${item.quantity}`
        });
      }
    }

    return stockIssues;
  }
}

module.exports = new CartService(); 