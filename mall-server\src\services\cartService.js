// 购物车服务
const { Op, sequelize } = require('sequelize');
const { Cart, Product, Category } = require('../models');

class CartService {

  // 添加商品到购物车
  async addToCart(userId, cartData) {
    try {
      const {
        product_id,
        sku_id = null,
        quantity = 1,
        specifications = null
      } = cartData;

      // 验证商品是否存在且可购买
      const product = await Product.findByPk(product_id, {
        attributes: ['id', 'name', 'price', 'stock', 'status', 'max_buy_count']
      });

      if (!product) {
        throw new Error('商品不存在');
      }

      if (product.status !== 1) {
        throw new Error('商品已下架');
      }

      if (product.stock < quantity) {
        throw new Error('商品库存不足');
      }

      // 检查是否已存在相同商品
      const existingCart = await Cart.findOne({
        where: {
          user_id: userId,
          product_id: product_id,
          sku_id: sku_id
        }
      });

      if (existingCart) {
        // 更新数量
        const newQuantity = existingCart.quantity + quantity;
        
        // 检查最大购买数量限制
        if (product.max_buy_count && newQuantity > product.max_buy_count) {
          throw new Error(`该商品最多只能购买${product.max_buy_count}件`);
        }

        if (newQuantity > product.stock) {
          throw new Error('商品库存不足');
        }

        await existingCart.update({
          quantity: newQuantity,
          price: product.price, // 更新为最新价格
          specifications: specifications || existingCart.specifications,
          updated_at: new Date()
        });

        return { message: '购物车商品数量已更新', cart: existingCart };
      } else {
        // 添加新商品
        if (product.max_buy_count && quantity > product.max_buy_count) {
          throw new Error(`该商品最多只能购买${product.max_buy_count}件`);
        }

        const cart = await Cart.create({
          user_id: userId,
          product_id: product_id,
          sku_id: sku_id,
          quantity: quantity,
          price: product.price,
          specifications: specifications,
          selected: true
        });

        return { message: '商品已添加到购物车', cart };
      }

    } catch (error) {
      console.error('添加购物车失败:', error);
      throw new Error(error.message || '添加购物车失败');
    }
  }

  // 获取购物车列表
  async getCartList(userId) {
    try {
      const cartItems = await Cart.findAll({
        where: { user_id: userId },
        include: [{
          model: Product,
          as: 'product',
          attributes: [
            'id', 'name', 'price', 'original_price', 'main_image', 'images',
            'stock', 'status', 'sales', 'rating', 'max_buy_count'
          ],
          include: [{
            model: Category,
            as: 'category',
            attributes: ['id', 'name']
          }]
        }],
        order: [['created_at', 'DESC']]
      });

      // 过滤无效商品并计算统计信息
      const validItems = [];
      const invalidItems = [];
      let totalAmount = 0;
      let totalQuantity = 0;
      let selectedCount = 0;

      for (const item of cartItems) {
        if (!item.product || item.product.status !== 1) {
          // 商品已下架或删除
          invalidItems.push(item);
          continue;
        }

        // 检查库存
        if (item.quantity > item.product.stock) {
          // 自动调整数量到库存上限
          await item.update({ quantity: item.product.stock });
          item.quantity = item.product.stock;
        }

        // 更新价格（如果商品价格有变化）
        if (item.price !== item.product.price) {
          await item.update({ price: item.product.price });
          item.price = item.product.price;
        }

        validItems.push(item);

        if (item.selected) {
          totalAmount += parseFloat(item.price) * item.quantity;
          totalQuantity += item.quantity;
          selectedCount++;
        }
      }

      // 删除无效商品
      if (invalidItems.length > 0) {
        const invalidIds = invalidItems.map(item => item.id);
        await Cart.destroy({
          where: { id: { [Op.in]: invalidIds } }
        });
      }

      return {
        items: validItems,
        summary: {
          totalItems: validItems.length,
          selectedItems: selectedCount,
          totalQuantity: totalQuantity,
          totalAmount: totalAmount.toFixed(2),
          invalidItemsRemoved: invalidItems.length
        }
      };

    } catch (error) {
      console.error('获取购物车列表失败:', error);
      throw new Error('获取购物车列表失败');
    }
  }

  // 更新购物车商品数量
  async updateCartQuantity(userId, cartId, quantity) {
    try {
      const cartItem = await Cart.findOne({
        where: { id: cartId, user_id: userId },
        include: [{
          model: Product,
          as: 'product',
          attributes: ['id', 'stock', 'status', 'max_buy_count']
        }]
      });

      if (!cartItem) {
        throw new Error('购物车商品不存在');
      }

      if (!cartItem.product || cartItem.product.status !== 1) {
        throw new Error('商品已下架');
      }

      if (quantity <= 0) {
        throw new Error('商品数量必须大于0');
      }

      if (quantity > cartItem.product.stock) {
        throw new Error('商品库存不足');
      }

      if (cartItem.product.max_buy_count && quantity > cartItem.product.max_buy_count) {
        throw new Error(`该商品最多只能购买${cartItem.product.max_buy_count}件`);
      }

      await cartItem.update({
        quantity: quantity,
        updated_at: new Date()
      });

      return { message: '购物车商品数量已更新', cart: cartItem };

    } catch (error) {
      console.error('更新购物车数量失败:', error);
      throw new Error(error.message || '更新购物车数量失败');
    }
  }

  // 删除购物车商品
  async removeFromCart(userId, cartIds) {
    try {
      const ids = Array.isArray(cartIds) ? cartIds : [cartIds];
      
      const deletedCount = await Cart.destroy({
        where: {
          id: { [Op.in]: ids },
          user_id: userId
        }
      });

      if (deletedCount === 0) {
        throw new Error('没有找到要删除的商品');
      }

      return { 
        message: `成功删除${deletedCount}件商品`,
        deletedCount 
      };

    } catch (error) {
      console.error('删除购物车商品失败:', error);
      throw new Error(error.message || '删除购物车商品失败');
    }
  }

  // 更新商品选中状态
  async updateCartSelection(userId, cartIds, selected) {
    try {
      const ids = Array.isArray(cartIds) ? cartIds : [cartIds];
      
      const [updatedCount] = await Cart.update(
        { selected: selected },
        {
          where: {
            id: { [Op.in]: ids },
            user_id: userId
          }
        }
      );

      if (updatedCount === 0) {
        throw new Error('没有找到要更新的商品');
      }

      return { 
        message: `成功更新${updatedCount}件商品的选中状态`,
        updatedCount 
      };

    } catch (error) {
      console.error('更新商品选中状态失败:', error);
      throw new Error(error.message || '更新商品选中状态失败');
    }
  }

  // 全选/取消全选
  async selectAll(userId, selected = true) {
    try {
      const [updatedCount] = await Cart.update(
        { selected: selected },
        { where: { user_id: userId } }
      );

      return { 
        message: selected ? '已全选所有商品' : '已取消全选',
        updatedCount 
      };

    } catch (error) {
      console.error('全选操作失败:', error);
      throw new Error('全选操作失败');
    }
  }

  // 清空购物车
  async clearCart(userId) {
    try {
      const deletedCount = await Cart.destroy({
        where: { user_id: userId }
      });

      return { 
        message: '购物车已清空',
        deletedCount 
      };

    } catch (error) {
      console.error('清空购物车失败:', error);
      throw new Error('清空购物车失败');
    }
  }

  // 获取购物车商品数量
  async getCartCount(userId) {
    try {
      const count = await Cart.count({
        where: { user_id: userId }
      });

      return { count };

    } catch (error) {
      console.error('获取购物车数量失败:', error);
      throw new Error('获取购物车数量失败');
    }
  }

  // 检查商品库存
  async checkStock(userId, cartIds = null) {
    try {
      const whereCondition = { user_id: userId };
      if (cartIds) {
        whereCondition.id = { [Op.in]: Array.isArray(cartIds) ? cartIds : [cartIds] };
      }

      const cartItems = await Cart.findAll({
        where: whereCondition,
        include: [{
          model: Product,
          as: 'product',
          attributes: ['id', 'name', 'stock', 'status']
        }]
      });

      const stockIssues = [];
      const validItems = [];

      for (const item of cartItems) {
        if (!item.product || item.product.status !== 1) {
          stockIssues.push({
            cartId: item.id,
            productName: item.product?.name || '未知商品',
            issue: '商品已下架'
          });
        } else if (item.quantity > item.product.stock) {
          stockIssues.push({
            cartId: item.id,
            productName: item.product.name,
            issue: `库存不足，当前库存${item.product.stock}件`
          });
        } else {
          validItems.push(item);
        }
      }

      return {
        valid: stockIssues.length === 0,
        validItems,
        stockIssues
      };

    } catch (error) {
      console.error('检查库存失败:', error);
      throw new Error('检查库存失败');
    }
  }

  // 获取选中商品信息（用于结算）
  async getSelectedItems(userId) {
    try {
      const selectedItems = await Cart.findAll({
        where: { 
          user_id: userId,
          selected: true
        },
        include: [{
          model: Product,
          as: 'product',
          attributes: [
            'id', 'name', 'price', 'main_image', 'stock', 'status', 'weight'
          ]
        }],
        order: [['created_at', 'DESC']]
      });

      // 检查库存和计算总价
      let totalAmount = 0;
      let totalQuantity = 0;
      let totalWeight = 0;
      const validItems = [];
      const issues = [];

      for (const item of selectedItems) {
        if (!item.product || item.product.status !== 1) {
          issues.push(`${item.product?.name || '商品'}已下架`);
          continue;
        }

        if (item.quantity > item.product.stock) {
          issues.push(`${item.product.name}库存不足`);
          continue;
        }

        validItems.push(item);
        totalAmount += parseFloat(item.price) * item.quantity;
        totalQuantity += item.quantity;
        totalWeight += (item.product.weight || 0) * item.quantity;
      }

      return {
        items: validItems,
        summary: {
          totalAmount: totalAmount.toFixed(2),
          totalQuantity,
          totalWeight: totalWeight.toFixed(2),
          itemCount: validItems.length
        },
        issues
      };

    } catch (error) {
      console.error('获取选中商品失败:', error);
      throw new Error('获取选中商品失败');
    }
  }
}

module.exports = new CartService();
