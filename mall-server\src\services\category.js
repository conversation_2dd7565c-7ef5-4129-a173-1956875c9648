const { Category } = require('../models');
const RedisUtils = require('../utils/redis');

class CategoryService {
  // 缓存键名
  static CACHE_KEY = 'categories:list';

  // 清理分类缓存
  async clearCache() {
    try {
      await RedisUtils.del(CategoryService.CACHE_KEY);
      console.log('✅ 分类缓存已清理');
      return true;
    } catch (error) {
      console.error('❌ 清理分类缓存失败:', error);
      return false;
    }
  }

  // 获取分类列表
  async getCategoryList() {
    const cacheKey = CategoryService.CACHE_KEY;
    let categories = await RedisUtils.get(cacheKey);

    if (!categories) {
      categories = await Category.findAll({
        where: { 
          status: 1
        },
        order: [['sort_order', 'ASC'], ['created_at', 'DESC']]
      });

      // 如果数据库为空，强制清理缓存并返回空数组
      if (!categories || categories.length === 0) {
        await RedisUtils.del(cacheKey);
        console.log('✅ 数据库为空，已清理分类缓存');
        return [];
      }

      // 缓存分类
      await RedisUtils.set(cacheKey, categories, 3600);
    }

    return categories;
  }

  // 获取分类详情
  async getCategoryDetail(categoryId) {
    const cacheKey = `category:${categoryId}`;
    let category = await RedisUtils.get(cacheKey);

    if (!category) {
      category = await Category.findOne({
        where: { 
          id: categoryId,
          status: 1
        }
      });

      if (!category) {
        throw new Error('分类不存在');
      }

      // 缓存分类详情
      await RedisUtils.set(cacheKey, category, 3600);
    }

    return category;
  }

  // 创建分类（带缓存清理）
  async createCategory(categoryData) {
    try {
      const category = await Category.create(categoryData);
      // 创建成功后清理缓存
      await this.clearCache();
      return category;
    } catch (error) {
      console.error('创建分类失败:', error);
      throw error;
    }
  }

  // 更新分类（带缓存清理）
  async updateCategory(id, updateData) {
    try {
      const category = await Category.findByPk(id);
      if (!category) {
        throw new Error('分类不存在');
      }
      await category.update(updateData);
      // 更新成功后清理缓存
      await this.clearCache();
      return category;
    } catch (error) {
      console.error('更新分类失败:', error);
      throw error;
    }
  }

  // 删除分类（带缓存清理）
  async deleteCategory(id) {
    try {
      const category = await Category.findByPk(id);
      if (!category) {
        throw new Error('分类不存在');
      }
      await category.destroy();
      // 删除成功后清理缓存
      await this.clearCache();
      return true;
    } catch (error) {
      console.error('删除分类失败:', error);
      throw error;
    }
  }
}

module.exports = new CategoryService(); 