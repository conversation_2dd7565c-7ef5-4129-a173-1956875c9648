// 优惠券服务
const { Coupon, UserCoupon, User } = require('../models');
const { Op } = require('sequelize');

class CouponService {
  // 获取可用优惠券列表
  async getAvailableCoupons(userId) {
    const now = new Date();
    
    const coupons = await Coupon.findAll({
      where: {
        status: 1,
        start_time: { [Op.lte]: now },
        end_time: { [Op.gte]: now },
        [Op.or]: [
          { total_quantity: 0 }, // 无限量
          { 
            [Op.and]: [
              { total_quantity: { [Op.gt]: 0 } },
              { used_quantity: { [Op.lt]: sequelize.col('total_quantity') } }
            ]
          }
        ]
      },
      order: [['created_at', 'DESC']]
    });

    // 检查用户是否已领取
    if (userId) {
      const userCouponIds = await UserCoupon.findAll({
        where: { user_id: userId },
        attributes: ['coupon_id'],
        group: ['coupon_id'],
        having: sequelize.literal('COUNT(*) >= MAX(per_user_limit)')
      });

      const receivedCouponIds = userCouponIds.map(uc => uc.coupon_id);
      
      return coupons.map(coupon => ({
        ...coupon.toJSON(),
        canReceive: !receivedCouponIds.includes(coupon.id)
      }));
    }

    return coupons.map(coupon => ({
      ...coupon.toJSON(),
      canReceive: true
    }));
  }

  // 领取优惠券
  async receiveCoupon(userId, couponId) {
    const coupon = await Coupon.findByPk(couponId);
    
    if (!coupon) {
      throw new Error('优惠券不存在');
    }

    if (coupon.status !== 1) {
      throw new Error('优惠券已停用');
    }

    const now = new Date();
    if (now < coupon.start_time || now > coupon.end_time) {
      throw new Error('优惠券不在有效期内');
    }

    // 检查库存
    if (coupon.total_quantity > 0 && coupon.used_quantity >= coupon.total_quantity) {
      throw new Error('优惠券已领完');
    }

    // 检查用户领取限制
    const userReceivedCount = await UserCoupon.count({
      where: { user_id: userId, coupon_id: couponId }
    });

    if (userReceivedCount >= coupon.per_user_limit) {
      throw new Error('已达到领取上限');
    }

    // 创建用户优惠券记录
    const expiredAt = new Date(coupon.end_time);
    const userCoupon = await UserCoupon.create({
      user_id: userId,
      coupon_id: couponId,
      expired_at: expiredAt
    });

    // 更新优惠券使用数量
    await coupon.increment('used_quantity');

    return userCoupon;
  }

  // 获取用户优惠券列表
  async getUserCoupons(userId, status = null) {
    const where = { user_id: userId };
    
    if (status !== null) {
      where.status = status;
    }

    const userCoupons = await UserCoupon.findAll({
      where,
      include: [{
        model: Coupon,
        as: 'coupon',
        attributes: ['id', 'name', 'type', 'discount_type', 'discount_value', 'min_amount', 'max_discount', 'description']
      }],
      order: [['created_at', 'DESC']]
    });

    return userCoupons;
  }

  // 获取订单可用优惠券
  async getOrderAvailableCoupons(userId, orderAmount) {
    const now = new Date();
    
    const userCoupons = await UserCoupon.findAll({
      where: {
        user_id: userId,
        status: 0, // 未使用
        expired_at: { [Op.gte]: now }
      },
      include: [{
        model: Coupon,
        as: 'coupon',
        where: {
          status: 1,
          min_amount: { [Op.lte]: orderAmount }
        }
      }],
      order: [['created_at', 'DESC']]
    });

    return userCoupons.map(uc => {
      const coupon = uc.coupon;
      let discountAmount = 0;

      if (coupon.discount_type === 1) {
        // 固定金额
        discountAmount = coupon.discount_value;
      } else {
        // 百分比
        discountAmount = orderAmount * (coupon.discount_value / 100);
        if (coupon.max_discount && discountAmount > coupon.max_discount) {
          discountAmount = coupon.max_discount;
        }
      }

      return {
        ...uc.toJSON(),
        discountAmount: Math.min(discountAmount, orderAmount)
      };
    });
  }

  // 使用优惠券
  async useCoupon(userId, userCouponId, orderId) {
    const userCoupon = await UserCoupon.findOne({
      where: {
        id: userCouponId,
        user_id: userId,
        status: 0
      },
      include: [{
        model: Coupon,
        as: 'coupon'
      }]
    });

    if (!userCoupon) {
      throw new Error('优惠券不存在或已使用');
    }

    const now = new Date();
    if (now > userCoupon.expired_at) {
      await userCoupon.update({ status: 2 }); // 标记为过期
      throw new Error('优惠券已过期');
    }

    // 标记为已使用
    await userCoupon.update({
      status: 1,
      order_id: orderId,
      used_at: now
    });

    return userCoupon;
  }

  // 计算优惠金额
  calculateDiscount(coupon, orderAmount) {
    if (orderAmount < coupon.min_amount) {
      return 0;
    }

    let discountAmount = 0;

    if (coupon.discount_type === 1) {
      // 固定金额
      discountAmount = coupon.discount_value;
    } else {
      // 百分比
      discountAmount = orderAmount * (coupon.discount_value / 100);
      if (coupon.max_discount && discountAmount > coupon.max_discount) {
        discountAmount = coupon.max_discount;
      }
    }

    return Math.min(discountAmount, orderAmount);
  }
}

module.exports = new CouponService();
