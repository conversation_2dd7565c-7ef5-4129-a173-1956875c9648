// 智能客服系统服务 - 优化完善版
const { Op, sequelize } = require('sequelize');
const { CustomerSession, CustomerMessage, User, AdminUser } = require('../models');

class EnhancedCustomerServiceService {
  
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
    this.messageQueue = [];
    this.batchSize = 20;
    this.flushInterval = 10000; // 10秒批量处理
    this.aiResponses = this.initAIResponses();
    this.sessionTimeouts = new Map();
    
    this.startMessageProcessor();
  }

  // 初始化AI自动回复
  initAIResponses() {
    return {
      greetings: ['你好', '您好', 'hi', 'hello'],
      farewells: ['再见', '拜拜', '谢谢'],
      product_inquiry: {
        keywords: ['商品', '产品', '茶叶', '价格', '规格', '产地'],
        responses: [
          '我来为您介绍我们的茶叶产品。请问您对哪类茶叶感兴趣？',
          '我们有绿茶、红茶、乌龙茶等多种类型，您可以告诉我您的喜好。',
          '关于商品信息，我可以为您详细介绍，请问具体想了解什么？'
        ]
      },
      order_inquiry: {
        keywords: ['订单', '发货', '物流', '配送', '快递'],
        responses: [
          '关于订单问题，请提供您的订单号，我来帮您查询。',
          '订单一般在付款后1-2个工作日发货，您可以在订单详情中查看物流信息。',
          '如果订单有问题，请提供订单号，我会立即为您处理。'
        ]
      },
      payment_inquiry: {
        keywords: ['支付', '付款', '退款', '优惠', '折扣'],
        responses: [
          '我们支持微信支付、支付宝等多种支付方式。',
          '关于退款，一般3-7个工作日到账，具体以银行处理时间为准。',
          '优惠活动请关注我们的首页公告，会有最新的优惠信息。'
        ]
      },
      after_sales: {
        keywords: ['退货', '换货', '质量', '问题', '投诉'],
        responses: [
          '如果商品有质量问题，我们支持7天无理由退换货。',
          '请保持商品包装完整，联系客服办理退换货手续。',
          '我们会认真处理每一个质量问题，请详细描述遇到的情况。'
        ]
      }
    };
  }

  // 启动消息处理器
  startMessageProcessor() {
    setInterval(() => {
      if (this.messageQueue.length > 0) {
        this.flushMessageQueue();
      }
    }, this.flushInterval);
  }

  // 智能创建客服会话
  async smartCreateSession(userId, sessionData = {}) {
    try {
      const {
        category = 'other',
        title = '用户咨询',
        priority = 'normal',
        tags = [],
        initialMessage = null
      } = sessionData;

      // 检查是否有未关闭的会话
      const existingSession = await CustomerSession.findOne({
        where: {
          user_id: userId,
          session_status: { [Op.in]: ['waiting', 'active'] }
        }
      });

      if (existingSession) {
        // 如果有新消息，发送到现有会话
        if (initialMessage) {
          await this.smartSendMessage(existingSession.id, userId, 'user', {
            messageType: 'text',
            content: initialMessage
          });
        }
        
        return { 
          message: '已有进行中的会话', 
          session: existingSession,
          isExisting: true
        };
      }

      // 智能分析优先级
      const smartPriority = this.analyzePriority(title, initialMessage, tags);

      // 创建新会话
      const session = await CustomerSession.create({
        user_id: userId,
        category: this.smartCategorizeSession(title, initialMessage),
        title,
        priority: smartPriority,
        tags: Array.isArray(tags) ? tags : [],
        session_status: 'waiting'
      });

      // 发送系统欢迎消息
      await this.sendSystemMessage(session.id, this.generateWelcomeMessage(category));

      // 如果有初始消息，发送用户消息
      if (initialMessage) {
        await this.smartSendMessage(session.id, userId, 'user', {
          messageType: 'text',
          content: initialMessage
        });

        // 尝试AI自动回复
        const aiResponse = await this.generateAIResponse(initialMessage, category);
        if (aiResponse) {
          setTimeout(() => {
            this.sendSystemMessage(session.id, aiResponse);
          }, 2000); // 2秒后发送AI回复
        }
      }

      // 设置会话超时
      this.setSessionTimeout(session.id);

      return { 
        message: '会话创建成功', 
        session,
        isExisting: false
      };

    } catch (error) {
      console.error('智能创建客服会话失败:', error);
      throw new Error('创建客服会话失败');
    }
  }

  // 智能分析优先级
  analyzePriority(title, message, tags) {
    const urgentKeywords = ['紧急', '投诉', '退款', '质量问题', '无法使用'];
    const highKeywords = ['订单问题', '发货', '物流', '支付失败'];
    
    const text = `${title} ${message || ''}`.toLowerCase();
    
    if (urgentKeywords.some(keyword => text.includes(keyword))) {
      return 'urgent';
    }
    
    if (highKeywords.some(keyword => text.includes(keyword)) || tags.includes('vip')) {
      return 'high';
    }
    
    return 'normal';
  }

  // 智能分类会话
  smartCategorizeSession(title, message) {
    const text = `${title} ${message || ''}`.toLowerCase();
    
    if (text.includes('商品') || text.includes('产品') || text.includes('茶叶')) {
      return 'product';
    }
    if (text.includes('订单') || text.includes('发货') || text.includes('物流')) {
      return 'order';
    }
    if (text.includes('支付') || text.includes('付款') || text.includes('退款')) {
      return 'payment';
    }
    if (text.includes('配送') || text.includes('快递') || text.includes('收货')) {
      return 'delivery';
    }
    if (text.includes('退货') || text.includes('换货') || text.includes('质量')) {
      return 'refund';
    }
    
    return 'other';
  }

  // 生成欢迎消息
  generateWelcomeMessage(category) {
    const welcomeMessages = {
      product: '您好！欢迎咨询我们的茶叶产品，我会为您详细介绍。',
      order: '您好！关于订单问题，我来为您查询和处理。',
      payment: '您好！关于支付相关问题，我来为您解答。',
      delivery: '您好！关于配送问题，我来为您跟进处理。',
      refund: '您好！关于售后问题，我会认真为您处理。',
      other: '您好！欢迎使用心洁茶叶客服系统，请问有什么可以帮助您的？'
    };
    
    return welcomeMessages[category] || welcomeMessages.other;
  }

  // 智能发送消息
  async smartSendMessage(sessionId, senderId, senderType, messageData) {
    try {
      const {
        messageType = 'text',
        content,
        extraData = null
      } = messageData;

      // 验证会话
      const session = await CustomerSession.findByPk(sessionId);
      if (!session) {
        throw new Error('会话不存在');
      }

      // 验证权限
      if (senderType === 'user' && session.user_id !== senderId) {
        throw new Error('无权限发送消息');
      }

      // 内容过滤和处理
      const processedContent = this.processMessageContent(content, messageType);

      // 添加到消息队列
      this.messageQueue.push({
        session_id: sessionId,
        sender_type: senderType,
        sender_id: senderId,
        message_type: messageType,
        content: processedContent,
        extra_data: extraData,
        created_at: new Date()
      });

      // 立即处理重要消息
      if (session.priority === 'urgent' || this.messageQueue.length >= this.batchSize) {
        await this.flushMessageQueue();
      }

      // 更新会话状态
      await this.updateSessionStatus(session, senderType, senderId);

      // 重置会话超时
      this.resetSessionTimeout(sessionId);

      return { message: '消息发送成功' };

    } catch (error) {
      console.error('智能发送消息失败:', error);
      throw new Error('发送消息失败');
    }
  }

  // 批量处理消息队列
  async flushMessageQueue() {
    if (this.messageQueue.length === 0) return;

    const messages = this.messageQueue.splice(0, this.batchSize);
    
    try {
      await CustomerMessage.bulkCreate(messages, {
        ignoreDuplicates: true
      });
      
      console.log(`📨 批量处理 ${messages.length} 条客服消息`);
    } catch (error) {
      console.error('批量处理消息失败:', error);
      // 失败的消息重新加入队列
      this.messageQueue.unshift(...messages);
    }
  }

  // 处理消息内容
  processMessageContent(content, messageType) {
    if (messageType !== 'text') return content;

    // 内容过滤
    const sensitiveWords = ['垃圾', '骗子', '差评'];
    let processedContent = content;
    
    sensitiveWords.forEach(word => {
      const regex = new RegExp(word, 'gi');
      processedContent = processedContent.replace(regex, '*'.repeat(word.length));
    });

    // 长度限制
    if (processedContent.length > 1000) {
      processedContent = processedContent.substring(0, 1000) + '...';
    }

    return processedContent;
  }

  // 更新会话状态
  async updateSessionStatus(session, senderType, senderId) {
    const updateData = { updated_at: new Date() };

    if (session.session_status === 'waiting' && senderType === 'admin') {
      updateData.session_status = 'active';
      updateData.admin_id = senderId;
    }

    await session.update(updateData);
  }

  // 生成AI自动回复
  async generateAIResponse(message, category) {
    try {
      const lowerMessage = message.toLowerCase();

      // 问候语检测
      if (this.aiResponses.greetings.some(greeting => lowerMessage.includes(greeting))) {
        return '您好！很高兴为您服务，请问有什么可以帮助您的？';
      }

      // 告别语检测
      if (this.aiResponses.farewells.some(farewell => lowerMessage.includes(farewell))) {
        return '感谢您的咨询，祝您生活愉快！如有其他问题随时联系我们。';
      }

      // 根据关键词匹配回复
      for (const [type, config] of Object.entries(this.aiResponses)) {
        if (typeof config === 'object' && config.keywords) {
          const hasKeyword = config.keywords.some(keyword => lowerMessage.includes(keyword));
          if (hasKeyword) {
            const responses = config.responses;
            return responses[Math.floor(Math.random() * responses.length)];
          }
        }
      }

      // 默认回复
      return '我已收到您的消息，正在为您转接人工客服，请稍候...';

    } catch (error) {
      console.error('生成AI回复失败:', error);
      return null;
    }
  }

  // 设置会话超时
  setSessionTimeout(sessionId) {
    // 30分钟无活动自动超时
    const timeout = setTimeout(async () => {
      try {
        const session = await CustomerSession.findByPk(sessionId);
        if (session && session.session_status === 'waiting') {
          await session.update({ 
            session_status: 'timeout',
            closed_at: new Date()
          });
          
          await this.sendSystemMessage(sessionId, '由于长时间无响应，会话已自动关闭。如需帮助请重新发起咨询。');
        }
      } catch (error) {
        console.error('处理会话超时失败:', error);
      }
    }, 30 * 60 * 1000);

    this.sessionTimeouts.set(sessionId, timeout);
  }

  // 重置会话超时
  resetSessionTimeout(sessionId) {
    const existingTimeout = this.sessionTimeouts.get(sessionId);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }
    this.setSessionTimeout(sessionId);
  }

  // 发送系统消息
  async sendSystemMessage(sessionId, content) {
    try {
      this.messageQueue.push({
        session_id: sessionId,
        sender_type: 'system',
        sender_id: 0,
        message_type: 'system',
        content,
        extra_data: null,
        created_at: new Date()
      });

      return { message: '系统消息发送成功' };
    } catch (error) {
      console.error('发送系统消息失败:', error);
      return null;
    }
  }

  // 智能会话分配
  async smartAssignSession(sessionId) {
    try {
      const session = await CustomerSession.findByPk(sessionId, {
        include: [{ model: User, as: 'user', attributes: ['id', 'nickName', 'vip_level'] }]
      });

      if (!session || session.session_status !== 'waiting') {
        return { message: '会话状态不正确' };
      }

      // 智能分配算法
      const [availableAdmins] = await sequelize.query(`
        SELECT 
          au.id,
          au.name,
          au.skill_tags,
          COUNT(cs.id) as active_sessions,
          AVG(cs.satisfaction) as avg_satisfaction,
          au.online_status
        FROM admin_users au
        LEFT JOIN customer_sessions cs ON au.id = cs.admin_id AND cs.session_status = 'active'
        WHERE au.role = 'customer_service' 
        AND au.online_status = 'online'
        GROUP BY au.id
        ORDER BY 
          CASE WHEN :priority = 'urgent' THEN avg_satisfaction ELSE active_sessions END ASC,
          avg_satisfaction DESC
        LIMIT 1
      `, {
        replacements: { priority: session.priority },
        type: sequelize.QueryTypes.SELECT
      });

      if (availableAdmins.length === 0) {
        return { message: '暂无可用客服' };
      }

      const assignedAdmin = availableAdmins[0];
      
      await session.update({
        admin_id: assignedAdmin.id,
        session_status: 'active'
      });

      await this.sendSystemMessage(sessionId, `客服 ${assignedAdmin.name} 已为您服务。`);

      return { 
        message: '会话分配成功', 
        adminId: assignedAdmin.id,
        adminName: assignedAdmin.name
      };

    } catch (error) {
      console.error('智能会话分配失败:', error);
      return { message: '分配失败' };
    }
  }

  // 获取智能会话统计
  async getSmartSessionStats(adminId = null, days = 30) {
    try {
      const cacheKey = `session_stats_${adminId || 'all'}_${days}`;
      const cached = this.cache.get(cacheKey);
      
      if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }

      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const whereCondition = adminId ? 'cs.admin_id = :adminId AND' : '';

      const [stats] = await sequelize.query(`
        SELECT 
          COUNT(*) as total_sessions,
          COUNT(CASE WHEN cs.session_status = 'closed' THEN 1 END) as closed_sessions,
          COUNT(CASE WHEN cs.session_status = 'active' THEN 1 END) as active_sessions,
          COUNT(CASE WHEN cs.session_status = 'waiting' THEN 1 END) as waiting_sessions,
          AVG(cs.satisfaction) as avg_satisfaction,
          AVG(TIMESTAMPDIFF(MINUTE, cs.created_at, cs.closed_at)) as avg_duration_minutes,
          COUNT(CASE WHEN cs.priority = 'urgent' THEN 1 END) as urgent_count,
          COUNT(CASE WHEN cs.priority = 'high' THEN 1 END) as high_count
        FROM customer_sessions cs
        WHERE ${whereCondition} cs.created_at >= :startDate
      `, {
        replacements: { adminId, startDate },
        type: sequelize.QueryTypes.SELECT
      });

      // 分类统计
      const [categoryStats] = await sequelize.query(`
        SELECT 
          cs.category,
          COUNT(*) as count,
          AVG(cs.satisfaction) as avg_satisfaction
        FROM customer_sessions cs
        WHERE ${whereCondition} cs.created_at >= :startDate
        GROUP BY cs.category
        ORDER BY count DESC
      `, {
        replacements: { adminId, startDate },
        type: sequelize.QueryTypes.SELECT
      });

      const result = {
        overview: stats,
        byCategory: categoryStats,
        insights: this.generateSessionInsights(stats, categoryStats)
      };

      // 缓存结果
      this.cache.set(cacheKey, {
        data: result,
        timestamp: Date.now()
      });

      return result;

    } catch (error) {
      console.error('获取智能会话统计失败:', error);
      throw new Error('获取会话统计失败');
    }
  }

  // 生成会话洞察
  generateSessionInsights(stats, categoryStats) {
    const insights = [];

    // 满意度洞察
    const satisfaction = parseFloat(stats.avg_satisfaction || 0);
    if (satisfaction >= 4.5) {
      insights.push('客服服务质量优秀，用户满意度很高');
    } else if (satisfaction >= 4.0) {
      insights.push('客服服务质量良好，还有提升空间');
    } else if (satisfaction > 0) {
      insights.push('客服服务需要改进，建议加强培训');
    }

    // 响应效率洞察
    const avgDuration = parseFloat(stats.avg_duration_minutes || 0);
    if (avgDuration > 0 && avgDuration < 15) {
      insights.push('会话处理效率很高，平均用时较短');
    } else if (avgDuration > 30) {
      insights.push('会话处理时间较长，建议优化流程');
    }

    // 问题分布洞察
    if (categoryStats.length > 0) {
      const topCategory = categoryStats[0];
      insights.push(`${this.getCategoryName(topCategory.category)}是最常见的咨询类型`);
    }

    return insights;
  }

  // 获取分类名称
  getCategoryName(category) {
    const categoryNames = {
      product: '商品咨询',
      order: '订单问题',
      payment: '支付问题',
      delivery: '配送问题',
      refund: '售后服务',
      other: '其他问题'
    };
    return categoryNames[category] || category;
  }

  // 清理缓存
  clearCache(pattern = null) {
    if (pattern) {
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }

  // 获取队列状态
  getQueueStatus() {
    return {
      messageQueueLength: this.messageQueue.length,
      activeTimeouts: this.sessionTimeouts.size,
      batchSize: this.batchSize,
      flushInterval: this.flushInterval
    };
  }
}

module.exports = new EnhancedCustomerServiceService();
