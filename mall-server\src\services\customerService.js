// 客服系统服务
const { Op, sequelize } = require('sequelize');
const { CustomerSession, CustomerMessage, User, AdminUser } = require('../models');

class CustomerServiceService {

  // 创建客服会话
  async createSession(userId, sessionData = {}) {
    try {
      const {
        category = 'other',
        title = '用户咨询',
        priority = 'normal',
        tags = []
      } = sessionData;

      // 检查是否有未关闭的会话
      const existingSession = await CustomerSession.findOne({
        where: {
          user_id: userId,
          session_status: { [Op.in]: ['waiting', 'active'] }
        }
      });

      if (existingSession) {
        return { message: '已有进行中的会话', session: existingSession };
      }

      const session = await CustomerSession.create({
        user_id: userId,
        category,
        title,
        priority,
        tags,
        session_status: 'waiting'
      });

      // 发送系统欢迎消息
      await this.sendSystemMessage(session.id, '您好！欢迎使用心洁茶叶客服系统，我们将尽快为您服务。');

      return { message: '会话创建成功', session };
    } catch (error) {
      console.error('创建客服会话失败:', error);
      throw new Error('创建客服会话失败');
    }
  }

  // 发送消息
  async sendMessage(sessionId, senderId, senderType, messageData) {
    try {
      const {
        messageType = 'text',
        content,
        extraData = null
      } = messageData;

      // 验证会话是否存在
      const session = await CustomerSession.findByPk(sessionId);
      if (!session) {
        throw new Error('会话不存在');
      }

      // 验证发送权限
      if (senderType === 'user' && session.user_id !== senderId) {
        throw new Error('无权限发送消息');
      }

      const message = await CustomerMessage.create({
        session_id: sessionId,
        sender_type: senderType,
        sender_id: senderId,
        message_type: messageType,
        content,
        extra_data: extraData
      });

      // 更新会话状态
      if (session.session_status === 'waiting' && senderType === 'admin') {
        await session.update({ 
          session_status: 'active',
          admin_id: senderId
        });
      }

      await session.update({ updated_at: new Date() });

      return { message: '消息发送成功', messageRecord: message };
    } catch (error) {
      console.error('发送消息失败:', error);
      throw new Error('发送消息失败');
    }
  }

  // 发送系统消息
  async sendSystemMessage(sessionId, content) {
    try {
      return await this.sendMessage(sessionId, 0, 'system', {
        messageType: 'system',
        content
      });
    } catch (error) {
      console.error('发送系统消息失败:', error);
      return null;
    }
  }

  // 获取会话消息列表
  async getSessionMessages(sessionId, page = 1, limit = 50) {
    try {
      const offset = (page - 1) * limit;

      const { count, rows } = await CustomerMessage.findAndCountAll({
        where: { session_id: sessionId },
        order: [['created_at', 'ASC']],
        limit: parseInt(limit), offset
      });

      // 标记消息为已读（如果是用户查看）
      await CustomerMessage.update(
        { is_read: true, read_at: new Date() },
        { 
          where: { 
            session_id: sessionId, 
            is_read: false,
            sender_type: { [Op.ne]: 'user' }
          } 
        }
      );

      return {
        total: count, messages: rows, page: parseInt(page),
        limit: parseInt(limit), totalPages: Math.ceil(count / limit)
      };
    } catch (error) {
      console.error('获取会话消息失败:', error);
      throw new Error('获取会话消息失败');
    }
  }

  // 获取用户会话列表
  async getUserSessions(userId, page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;

      const { count, rows } = await CustomerSession.findAndCountAll({
        where: { user_id: userId },
        include: [{
          model: CustomerMessage, as: 'messages',
          attributes: ['content', 'created_at', 'sender_type'],
          limit: 1,
          order: [['created_at', 'DESC']]
        }],
        order: [['updated_at', 'DESC']],
        limit: parseInt(limit), offset
      });

      return {
        total: count, sessions: rows, page: parseInt(page),
        limit: parseInt(limit), totalPages: Math.ceil(count / limit)
      };
    } catch (error) {
      console.error('获取用户会话列表失败:', error);
      throw new Error('获取用户会话列表失败');
    }
  }

  // 获取客服待处理会话
  async getPendingSessions(adminId = null, page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;
      const whereCondition = { session_status: 'waiting' };
      
      if (adminId) {
        whereCondition[Op.or] = [
          { admin_id: null },
          { admin_id: adminId }
        ];
      }

      const { count, rows } = await CustomerSession.findAndCountAll({
        where: whereCondition,
        include: [
          {
            model: User, as: 'user',
            attributes: ['id', 'nickName', 'avatarUrl']
          },
          {
            model: CustomerMessage, as: 'messages',
            attributes: ['content', 'created_at'],
            limit: 1,
            order: [['created_at', 'DESC']]
          }
        ],
        order: [
          ['priority', 'DESC'],
          ['created_at', 'ASC']
        ],
        limit: parseInt(limit), offset
      });

      return {
        total: count, sessions: rows, page: parseInt(page),
        limit: parseInt(limit), totalPages: Math.ceil(count / limit)
      };
    } catch (error) {
      console.error('获取待处理会话失败:', error);
      throw new Error('获取待处理会话失败');
    }
  }

  // 接受会话
  async acceptSession(sessionId, adminId) {
    try {
      const session = await CustomerSession.findByPk(sessionId);
      if (!session) {
        throw new Error('会话不存在');
      }

      if (session.session_status !== 'waiting') {
        throw new Error('会话状态不正确');
      }

      await session.update({
        admin_id: adminId,
        session_status: 'active'
      });

      // 发送系统消息
      await this.sendSystemMessage(sessionId, '客服已接入，为您服务。');

      return { message: '会话接受成功', session };
    } catch (error) {
      console.error('接受会话失败:', error);
      throw new Error('接受会话失败');
    }
  }

  // 关闭会话
  async closeSession(sessionId, closerId, feedback = null) {
    try {
      const session = await CustomerSession.findByPk(sessionId);
      if (!session) {
        throw new Error('会话不存在');
      }

      const updateData = {
        session_status: 'closed',
        closed_at: new Date()
      };

      if (feedback) {
        updateData.feedback = feedback;
      }

      await session.update(updateData);

      // 发送系统消息
      await this.sendSystemMessage(sessionId, '会话已结束，感谢您的使用。');

      return { message: '会话关闭成功' };
    } catch (error) {
      console.error('关闭会话失败:', error);
      throw new Error('关闭会话失败');
    }
  }

  // 评价会话
  async rateSession(sessionId, userId, satisfaction, feedback = '') {
    try {
      const session = await CustomerSession.findOne({
        where: { id: sessionId, user_id: userId }
      });

      if (!session) {
        throw new Error('会话不存在或无权限');
      }

      await session.update({
        satisfaction: parseInt(satisfaction),
        feedback
      });

      return { message: '评价提交成功' };
    } catch (error) {
      console.error('评价会话失败:', error);
      throw new Error('评价会话失败');
    }
  }

  // 获取客服统计
  async getServiceStats(adminId = null, days = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const whereCondition = { created_at: { [Op.gte]: startDate } };
      if (adminId) {
        whereCondition.admin_id = adminId;
      }

      // 会话统计
      const sessionStats = await CustomerSession.findOne({
        where: whereCondition,
        attributes: [
          [sequelize.fn('COUNT', sequelize.col('id')), 'total_sessions'],
          [sequelize.fn('COUNT', sequelize.literal('CASE WHEN session_status = "closed" THEN 1 END')), 'closed_sessions'],
          [sequelize.fn('AVG', sequelize.col('satisfaction')), 'avg_satisfaction']
        ],
        raw: true
      });

      // 按分类统计
      const categoryStats = await CustomerSession.findAll({
        where: whereCondition,
        attributes: [
          'category',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['category'], raw: true
      });

      // 每日趋势
      const dailyStats = await CustomerSession.findAll({
        where: whereCondition,
        attributes: [
          [sequelize.fn('DATE', sequelize.col('created_at')), 'date'],
          [sequelize.fn('COUNT', sequelize.col('id')), 'session_count']
        ],
        group: [sequelize.fn('DATE', sequelize.col('created_at'))],
        order: [[sequelize.fn('DATE', sequelize.col('created_at')), 'ASC']],
        raw: true
      });

      return {
        sessionStats: {
          totalSessions: parseInt(sessionStats.total_sessions) || 0,
          closedSessions: parseInt(sessionStats.closed_sessions) || 0,
          avgSatisfaction: parseFloat(sessionStats.avg_satisfaction) || 0,
          closeRate: sessionStats.total_sessions > 0 
            ? ((sessionStats.closed_sessions / sessionStats.total_sessions) * 100).toFixed(2)
            : 0
        },
        categoryStats, dailyStats
      };
    } catch (error) {
      console.error('获取客服统计失败:', error);
      throw new Error('获取客服统计失败');
    }
  }

  // 自动分配客服
  async autoAssignAdmin(sessionId) {
    try {
      // 简单的负载均衡：找到当前活跃会话最少的客服
      const availableAdmins = await AdminUser.findAll({
        attributes: [
          'id',
          [sequelize.fn('COUNT', sequelize.col('CustomerSessions.id')), 'active_sessions']
        ],
        include: [{
          model: CustomerSession,
          as: 'CustomerSessions',
          where: { session_status: 'active' },
          required: false
        }],
        group: ['AdminUser.id'],
        order: [[sequelize.fn('COUNT', sequelize.col('CustomerSessions.id')), 'ASC']],
        limit: 1,
        raw: false
      });

      if (availableAdmins.length > 0) {
        const adminId = availableAdmins[0].id;
        await this.acceptSession(sessionId, adminId);
        return { message: '自动分配客服成功', adminId };
      }

      return { message: '暂无可用客服' };
    } catch (error) {
      console.error('自动分配客服失败:', error);
      return null;
    }
  }
}

module.exports = new CustomerServiceService();
