// 折扣服务
const { Discount, ProductDiscount, CategoryDiscount, UserDiscountUsage } = require('../models');
const { Op } = require('sequelize');

class DiscountService {
  /**
   * 获取商品的有效折扣
   * @param {number} productId - 商品ID
   * @param {number} categoryId - 商品分类ID
   * @param {number} userId - 用户ID（可选）
   * @param {number} quantity - 购买数量（默认1）
   * @returns {Promise<Object>} 折扣信息
   */
  static async getProductDiscount(productId, categoryId, userId = null, quantity = 1) {
    try {
      const now = new Date();
      
      // 查找所有有效的折扣
      const discounts = await Discount.findAll({
        where: {
          status: 1,
          start_time: { [Op.lte]: now },
          end_time: { [Op.gte]: now },
          [Op.or]: [
            { usage_limit: null },
            { used_count: { [Op.lt]: Discount.sequelize.col('usage_limit') } }
          ]
        },
        include: [
          {
            model: ProductDiscount,
            as: 'productDiscounts',
            required: false,
            where: { product_id: productId }
          },
          {
            model: CategoryDiscount,
            as: 'categoryDiscounts',
            required: false,
            where: { category_id: categoryId }
          }
        ],
        order: [['priority', 'DESC'], ['created_at', 'ASC']]
      });

      // 筛选适用的折扣
      const applicableDiscounts = discounts.filter(discount => {
        // 全部商品
        if (discount.applicable_to === 1) return true;
        
        // 指定商品
        if (discount.applicable_to === 2) {
          return discount.productDiscounts && discount.productDiscounts.length > 0;
        }
        
        // 指定分类
        if (discount.applicable_to === 3) {
          return discount.categoryDiscounts && discount.categoryDiscounts.length > 0;
        }
        
        return false;
      });

      // 如果有用户ID，检查用户使用限制
      if (userId) {
        for (let discount of applicableDiscounts) {
          if (discount.user_limit) {
            const userUsage = await UserDiscountUsage.count({
              where: {
                user_id: userId,
                discount_id: discount.id
              }
            });
            
            if (userUsage >= discount.user_limit) {
              // 移除已达到用户使用限制的折扣
              const index = applicableDiscounts.indexOf(discount);
              if (index > -1) {
                applicableDiscounts.splice(index, 1);
              }
            }
          }
        }
      }

      // 返回优先级最高的折扣
      return applicableDiscounts.length > 0 ? applicableDiscounts[0] : null;
    } catch (error) {
      console.error('获取商品折扣失败:', error);
      return null;
    }
  }

  /**
   * 计算折扣后的价格
   * @param {number} originalPrice - 原价
   * @param {Object} discount - 折扣对象
   * @param {number} quantity - 数量
   * @param {number} totalAmount - 总金额（用于满减折扣）
   * @returns {Object} 价格信息
   */
  static calculateDiscountPrice(originalPrice, discount, quantity = 1, totalAmount = null) {
    if (!discount) {
      return {
        originalPrice,
        discountPrice: originalPrice,
        discountAmount: 0,
        hasDiscount: false
      };
    }

    let discountPrice = originalPrice;
    let discountAmount = 0;

    switch (discount.type) {
      case 1: // 百分比折扣
        discountAmount = originalPrice * (discount.value / 100);
        discountPrice = originalPrice - discountAmount;
        
        // 应用最大折扣限制
        if (discount.max_discount && discountAmount > discount.max_discount) {
          discountAmount = discount.max_discount;
          discountPrice = originalPrice - discountAmount;
        }
        break;

      case 2: // 固定金额折扣
        // 检查是否满足最低消费金额
        const itemTotal = originalPrice * quantity;
        if (discount.min_amount && itemTotal >= discount.min_amount) {
          discountAmount = Math.min(discount.value, originalPrice);
          discountPrice = originalPrice - discountAmount;
        }
        break;

      case 3: // 满减折扣
        // 满减折扣通常在订单级别计算，这里只做标记
        const orderTotal = totalAmount || (originalPrice * quantity);
        if (discount.min_amount && orderTotal >= discount.min_amount) {
          // 按比例分摊折扣金额
          const itemRatio = (originalPrice * quantity) / orderTotal;
          discountAmount = discount.value * itemRatio;
          discountPrice = originalPrice - (discountAmount / quantity);
        }
        break;
    }

    // 确保折扣价不为负数
    discountPrice = Math.max(0, discountPrice);
    discountAmount = originalPrice - discountPrice;

    return {
      originalPrice,
      discountPrice: Math.round(discountPrice * 100) / 100,
      discountAmount: Math.round(discountAmount * 100) / 100,
      hasDiscount: discountAmount > 0,
      discount: {
        id: discount.id,
        name: discount.name,
        type: discount.type,
        value: discount.value
      }
    };
  }

  /**
   * 为商品列表添加折扣信息
   * @param {Array} products - 商品列表
   * @param {number} userId - 用户ID（可选）
   * @returns {Promise<Array>} 包含折扣信息的商品列表
   */
  static async addDiscountToProducts(products, userId = null) {
    if (!products || products.length === 0) return products;

    const productsWithDiscount = await Promise.all(
      products.map(async (product) => {
        const discount = await this.getProductDiscount(
          product.id,
          product.category_id,
          userId
        );

        const priceInfo = this.calculateDiscountPrice(
          product.price,
          discount,
          1
        );

        return {
          ...product.toJSON ? product.toJSON() : product,
          ...priceInfo
        };
      })
    );

    return productsWithDiscount;
  }

  /**
   * 计算购物车总价（包含折扣）
   * @param {Array} cartItems - 购物车商品列表
   * @param {number} userId - 用户ID
   * @returns {Promise<Object>} 价格统计信息
   */
  static async calculateCartTotal(cartItems, userId) {
    if (!cartItems || cartItems.length === 0) {
      return {
        originalTotal: 0,
        discountTotal: 0,
        totalDiscount: 0,
        items: []
      };
    }

    let originalTotal = 0;
    let discountTotal = 0;
    const itemsWithDiscount = [];

    for (const item of cartItems) {
      const discount = await this.getProductDiscount(
        item.product_id || item.id,
        item.category_id,
        userId,
        item.quantity
      );

      const priceInfo = this.calculateDiscountPrice(
        item.price,
        discount,
        item.quantity
      );

      const itemOriginalTotal = item.price * item.quantity;
      const itemDiscountTotal = priceInfo.discountPrice * item.quantity;

      originalTotal += itemOriginalTotal;
      discountTotal += itemDiscountTotal;

      itemsWithDiscount.push({
        ...item,
        ...priceInfo,
        itemOriginalTotal,
        itemDiscountTotal
      });
    }

    return {
      originalTotal: Math.round(originalTotal * 100) / 100,
      discountTotal: Math.round(discountTotal * 100) / 100,
      totalDiscount: Math.round((originalTotal - discountTotal) * 100) / 100,
      items: itemsWithDiscount
    };
  }

  /**
   * 记录折扣使用
   * @param {number} userId - 用户ID
   * @param {number} discountId - 折扣ID
   * @param {number} orderId - 订单ID
   * @param {number} discountAmount - 折扣金额
   */
  static async recordDiscountUsage(userId, discountId, orderId, discountAmount) {
    try {
      // 记录用户折扣使用
      await UserDiscountUsage.create({
        user_id: userId,
        discount_id: discountId,
        order_id: orderId,
        discount_amount: discountAmount,
        used_at: new Date()
      });

      // 更新折扣使用次数
      await Discount.increment('used_count', {
        where: { id: discountId }
      });

      return true;
    } catch (error) {
      console.error('记录折扣使用失败:', error);
      return false;
    }
  }
}

module.exports = DiscountService;
