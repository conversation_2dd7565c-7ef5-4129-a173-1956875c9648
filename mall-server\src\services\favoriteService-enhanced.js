// 智能收藏服务 - 优化完善版
const { Op, sequelize } = require('sequelize');
const { Favorite, Product, Category, User } = require('../models');

class EnhancedFavoriteService {
  
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
    this.batchSize = 100;
  }

  // 智能添加收藏（带推荐逻辑）
  async smartAddFavorite(userId, productId, source = 'manual') {
    try {
      const transaction = await sequelize.transaction();
      
      try {
        // 检查商品是否存在且可收藏
        const product = await Product.findByPk(productId, {
          attributes: ['id', 'name', 'category_id', 'price', 'status', 'tags'],
          transaction
        });

        if (!product || product.status !== 1) {
          throw new Error('商品不存在或已下架');
        }

        // 检查是否已收藏
        const existingFavorite = await Favorite.findOne({
          where: { user_id: userId, product_id: productId },
          transaction
        });

        if (existingFavorite) {
          await transaction.rollback();
          return { 
            message: '商品已在收藏夹中', 
            favorite: existingFavorite,
            recommendations: await this.getSmartRecommendations(userId, productId)
          };
        }

        // 创建收藏记录
        const favorite = await Favorite.create({
          user_id: userId,
          product_id: productId,
          source,
          created_at: new Date()
        }, { transaction });

        // 更新商品收藏数（通过触发器自动处理）
        
        // 记录用户偏好（异步）
        setImmediate(() => {
          this.updateUserPreferences(userId, product);
        });

        await transaction.commit();

        // 获取智能推荐
        const recommendations = await this.getSmartRecommendations(userId, productId);

        return { 
          message: '收藏成功', 
          favorite,
          recommendations,
          insights: await this.getFavoriteInsights(userId)
        };

      } catch (error) {
        await transaction.rollback();
        throw error;
      }
    } catch (error) {
      console.error('智能添加收藏失败:', error);
      throw new Error('添加收藏失败');
    }
  }

  // 获取智能推荐
  async getSmartRecommendations(userId, baseProductId, limit = 6) {
    try {
      const cacheKey = `recommendations_${userId}_${baseProductId}`;
      const cached = this.cache.get(cacheKey);
      
      if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }

      // 获取基础商品信息
      const baseProduct = await Product.findByPk(baseProductId, {
        attributes: ['category_id', 'price', 'tags']
      });

      if (!baseProduct) return [];

      // 多维度推荐算法
      const [categoryRecommendations, priceRecommendations, tagRecommendations] = await Promise.all([
        this.getCategoryBasedRecommendations(userId, baseProduct.category_id, limit / 2),
        this.getPriceBasedRecommendations(userId, baseProduct.price, limit / 3),
        this.getTagBasedRecommendations(userId, baseProduct.tags, limit / 3)
      ]);

      // 合并去重推荐结果
      const allRecommendations = [
        ...categoryRecommendations,
        ...priceRecommendations,
        ...tagRecommendations
      ];

      const uniqueRecommendations = this.deduplicateRecommendations(allRecommendations, baseProductId);
      const finalRecommendations = uniqueRecommendations.slice(0, limit);

      // 缓存结果
      this.cache.set(cacheKey, {
        data: finalRecommendations,
        timestamp: Date.now()
      });

      return finalRecommendations;
    } catch (error) {
      console.error('获取智能推荐失败:', error);
      return [];
    }
  }

  // 基于分类的推荐
  async getCategoryBasedRecommendations(userId, categoryId, limit) {
    const [results] = await sequelize.query(`
      SELECT p.id, p.name, p.price, p.main_image, p.sales, p.rating,
             p.favorite_count, p.view_count,
             (p.sales * 0.3 + p.rating * 0.2 + p.favorite_count * 0.3 + p.view_count * 0.2) as score
      FROM products p
      WHERE p.category_id = :categoryId
      AND p.status = 1
      AND p.id NOT IN (
        SELECT product_id FROM favorites WHERE user_id = :userId
      )
      ORDER BY score DESC, p.created_at DESC
      LIMIT :limit
    `, {
      replacements: { categoryId, userId, limit },
      type: sequelize.QueryTypes.SELECT
    });

    return results.map(item => ({ ...item, reason: '同类商品推荐' }));
  }

  // 基于价格的推荐
  async getPriceBasedRecommendations(userId, basePrice, limit) {
    const priceRange = basePrice * 0.4; // 40%价格浮动
    
    const [results] = await sequelize.query(`
      SELECT p.id, p.name, p.price, p.main_image, p.sales, p.rating,
             ABS(p.price - :basePrice) as price_diff
      FROM products p
      WHERE p.price BETWEEN :minPrice AND :maxPrice
      AND p.status = 1
      AND p.id NOT IN (
        SELECT product_id FROM favorites WHERE user_id = :userId
      )
      ORDER BY price_diff ASC, p.sales DESC
      LIMIT :limit
    `, {
      replacements: {
        basePrice,
        minPrice: basePrice - priceRange,
        maxPrice: basePrice + priceRange,
        userId,
        limit
      },
      type: sequelize.QueryTypes.SELECT
    });

    return results.map(item => ({ ...item, reason: '价格相近推荐' }));
  }

  // 基于标签的推荐
  async getTagBasedRecommendations(userId, baseTags, limit) {
    if (!baseTags) return [];

    const tags = Array.isArray(baseTags) ? baseTags : JSON.parse(baseTags || '[]');
    if (tags.length === 0) return [];

    const [results] = await sequelize.query(`
      SELECT p.id, p.name, p.price, p.main_image, p.sales, p.rating, p.tags,
             JSON_LENGTH(JSON_EXTRACT(p.tags, '$')) as tag_count
      FROM products p
      WHERE p.status = 1
      AND p.tags IS NOT NULL
      AND p.id NOT IN (
        SELECT product_id FROM favorites WHERE user_id = :userId
      )
      ORDER BY tag_count DESC, p.sales DESC
      LIMIT :limit
    `, {
      replacements: { userId, limit: limit * 2 },
      type: sequelize.QueryTypes.SELECT
    });

    // 计算标签相似度
    return results
      .map(item => {
        const itemTags = JSON.parse(item.tags || '[]');
        const similarity = this.calculateTagSimilarity(tags, itemTags);
        return { ...item, similarity, reason: '标签相似推荐' };
      })
      .filter(item => item.similarity > 0.3)
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, limit);
  }

  // 计算标签相似度
  calculateTagSimilarity(tags1, tags2) {
    const set1 = new Set(tags1);
    const set2 = new Set(tags2);
    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);
    return intersection.size / union.size;
  }

  // 去重推荐结果
  deduplicateRecommendations(recommendations, excludeId) {
    const seen = new Set([excludeId]);
    return recommendations.filter(item => {
      if (seen.has(item.id)) return false;
      seen.add(item.id);
      return true;
    });
  }

  // 获取收藏洞察
  async getFavoriteInsights(userId) {
    try {
      const [insights] = await sequelize.query(`
        SELECT 
          COUNT(*) as total_favorites,
          COUNT(DISTINCT p.category_id) as categories_count,
          AVG(p.price) as avg_price,
          MAX(f.created_at) as last_favorite_time,
          (
            SELECT c.name 
            FROM categories c 
            JOIN products p2 ON c.id = p2.category_id
            JOIN favorites f2 ON p2.id = f2.product_id
            WHERE f2.user_id = :userId
            GROUP BY c.id
            ORDER BY COUNT(*) DESC
            LIMIT 1
          ) as favorite_category
        FROM favorites f
        JOIN products p ON f.product_id = p.id
        WHERE f.user_id = :userId
      `, {
        replacements: { userId },
        type: sequelize.QueryTypes.SELECT
      });

      return insights;
    } catch (error) {
      console.error('获取收藏洞察失败:', error);
      return null;
    }
  }

  // 更新用户偏好（异步）
  async updateUserPreferences(userId, product) {
    try {
      // 这里可以实现用户偏好学习算法
      // 例如：更新用户的分类偏好、价格偏好、品牌偏好等
      console.log(`更新用户 ${userId} 的偏好，基于商品 ${product.id}`);
    } catch (error) {
      console.error('更新用户偏好失败:', error);
    }
  }

  // 智能收藏列表（带个性化排序）
  async getSmartFavoriteList(userId, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = 'smart', // smart, time, price, popularity
        category = null,
        priceRange = null
      } = options;

      const offset = (page - 1) * limit;
      let orderClause = '';

      switch (sortBy) {
        case 'smart':
          orderClause = `
            ORDER BY (
              p.sales * 0.2 + 
              p.rating * 0.2 + 
              p.favorite_count * 0.2 + 
              p.view_count * 0.1 +
              (CASE WHEN p.stock > 0 THEN 0.3 ELSE 0 END)
            ) DESC, f.created_at DESC
          `;
          break;
        case 'time':
          orderClause = 'ORDER BY f.created_at DESC';
          break;
        case 'price':
          orderClause = 'ORDER BY p.price ASC';
          break;
        case 'popularity':
          orderClause = 'ORDER BY p.sales DESC, p.rating DESC';
          break;
        default:
          orderClause = 'ORDER BY f.created_at DESC';
      }

      let whereClause = 'WHERE f.user_id = :userId AND p.status = 1';
      const replacements = { userId, limit, offset };

      if (category) {
        whereClause += ' AND p.category_id = :category';
        replacements.category = category;
      }

      if (priceRange) {
        whereClause += ' AND p.price BETWEEN :minPrice AND :maxPrice';
        replacements.minPrice = priceRange.min;
        replacements.maxPrice = priceRange.max;
      }

      const [results] = await sequelize.query(`
        SELECT 
          f.id as favorite_id,
          f.created_at as favorite_time,
          p.id, p.name, p.price, p.original_price, p.main_image,
          p.sales, p.rating, p.stock, p.favorite_count, p.view_count,
          c.id as category_id, c.name as category_name,
          (p.sales * 0.2 + p.rating * 0.2 + p.favorite_count * 0.2 + p.view_count * 0.1) as popularity_score
        FROM favorites f
        JOIN products p ON f.product_id = p.id
        JOIN categories c ON p.category_id = c.id
        ${whereClause}
        ${orderClause}
        LIMIT :limit OFFSET :offset
      `, {
        replacements,
        type: sequelize.QueryTypes.SELECT
      });

      // 获取总数
      const [countResult] = await sequelize.query(`
        SELECT COUNT(*) as total
        FROM favorites f
        JOIN products p ON f.product_id = p.id
        ${whereClause}
      `, {
        replacements: { userId, category, minPrice: priceRange?.min, maxPrice: priceRange?.max },
        type: sequelize.QueryTypes.SELECT
      });

      return {
        favorites: results,
        total: countResult.total,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(countResult.total / limit),
        sortBy,
        insights: await this.getFavoriteInsights(userId)
      };

    } catch (error) {
      console.error('获取智能收藏列表失败:', error);
      throw new Error('获取收藏列表失败');
    }
  }

  // 收藏趋势分析
  async getFavoriteTrends(userId, days = 30) {
    try {
      const [trends] = await sequelize.query(`
        SELECT 
          DATE(f.created_at) as date,
          COUNT(*) as daily_count,
          AVG(p.price) as avg_price,
          GROUP_CONCAT(DISTINCT c.name) as categories
        FROM favorites f
        JOIN products p ON f.product_id = p.id
        JOIN categories c ON p.category_id = c.id
        WHERE f.user_id = :userId
        AND f.created_at >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
        GROUP BY DATE(f.created_at)
        ORDER BY date DESC
      `, {
        replacements: { userId, days },
        type: sequelize.QueryTypes.SELECT
      });

      return trends;
    } catch (error) {
      console.error('获取收藏趋势失败:', error);
      return [];
    }
  }

  // 收藏价值分析
  async getFavoriteValueAnalysis(userId) {
    try {
      const [analysis] = await sequelize.query(`
        SELECT 
          COUNT(*) as total_items,
          SUM(p.price) as total_value,
          AVG(p.price) as avg_value,
          MIN(p.price) as min_value,
          MAX(p.price) as max_value,
          SUM(CASE WHEN p.original_price > p.price THEN p.original_price - p.price ELSE 0 END) as total_savings,
          COUNT(CASE WHEN p.stock = 0 THEN 1 END) as out_of_stock_count,
          COUNT(CASE WHEN p.stock <= 10 THEN 1 END) as low_stock_count
        FROM favorites f
        JOIN products p ON f.product_id = p.id
        WHERE f.user_id = :userId AND p.status = 1
      `, {
        replacements: { userId },
        type: sequelize.QueryTypes.SELECT
      });

      return analysis;
    } catch (error) {
      console.error('获取收藏价值分析失败:', error);
      return null;
    }
  }

  // 清理缓存
  clearCache(pattern = null) {
    if (pattern) {
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }
}

module.exports = new EnhancedFavoriteService();
