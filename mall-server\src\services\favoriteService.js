// 商品收藏服务
const { Op, sequelize } = require('sequelize');
const { Favorite, Product, Category } = require('../models');

class FavoriteService {

  // 添加收藏
  async addFavorite(userId, productId) {
    try {
      // 检查商品是否存在
      const product = await Product.findByPk(productId);
      if (!product) {
        throw new Error('商品不存在');
      }

      // 检查是否已收藏
      const existingFavorite = await Favorite.findOne({
        where: { user_id: userId, product_id: productId }
      });

      if (existingFavorite) {
        return { message: '商品已在收藏夹中', favorite: existingFavorite };
      }

      // 添加收藏
      const favorite = await Favorite.create({
        user_id: userId,
        product_id: productId
      });

      return { message: '收藏成功', favorite };
    } catch (error) {
      console.error('添加收藏失败:', error);
      throw new Error('添加收藏失败');
    }
  }

  // 取消收藏
  async removeFavorite(userId, productId) {
    try {
      const result = await Favorite.destroy({
        where: { user_id: userId, product_id: productId }
      });

      if (result === 0) {
        throw new Error('收藏记录不存在');
      }

      return { message: '取消收藏成功' };
    } catch (error) {
      console.error('取消收藏失败:', error);
      throw new Error('取消收藏失败');
    }
  }

  // 获取收藏列表
  async getFavoriteList(userId, page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;

      const { count, rows } = await Favorite.findAndCountAll({
        where: { user_id: userId },
        include: [
          {
            model: Product,
            as: 'product',
            attributes: [
              'id', 'name', 'price', 'original_price', 'main_image', 
              'sales', 'rating', 'stock', 'status'
            ],
            include: [
              {
                model: Category,
                as: 'category',
                attributes: ['id', 'name']
              }
            ]
          }
        ],
        order: [['created_at', 'DESC']],
        limit: parseInt(limit),
        offset
      });

      // 过滤掉已下架的商品
      const validFavorites = rows.filter(item => item.product && item.product.status === 1);

      return {
        total: count,
        favorites: validFavorites,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit)
      };
    } catch (error) {
      console.error('获取收藏列表失败:', error);
      throw new Error('获取收藏列表失败');
    }
  }

  // 检查商品是否已收藏
  async isFavorited(userId, productId) {
    try {
      const favorite = await Favorite.findOne({
        where: { user_id: userId, product_id: productId }
      });

      return !!favorite;
    } catch (error) {
      console.error('检查收藏状态失败:', error);
      return false;
    }
  }

  // 批量检查收藏状态
  async batchCheckFavorited(userId, productIds) {
    try {
      const favorites = await Favorite.findAll({
        where: {
          user_id: userId,
          product_id: { [Op.in]: productIds }
        },
        attributes: ['product_id']
      });

      const favoritedIds = favorites.map(f => f.product_id);
      
      return productIds.reduce((result, productId) => {
        result[productId] = favoritedIds.includes(productId);
        return result;
      }, {});
    } catch (error) {
      console.error('批量检查收藏状态失败:', error);
      return {};
    }
  }

  // 获取收藏统计
  async getFavoriteStats(userId) {
    try {
      const totalCount = await Favorite.count({
        where: { user_id: userId }
      });

      // 按分类统计
      const categoryStats = await Favorite.findAll({
        where: { user_id: userId },
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['category_id'],
            include: [
              {
                model: Category,
                as: 'category',
                attributes: ['id', 'name']
              }
            ]
          }
        ],
        attributes: [
          [sequelize.fn('COUNT', sequelize.col('Favorite.id')), 'count']
        ],
        group: ['product.category_id'],
        raw: false
      });

      return {
        totalCount,
        categoryStats: categoryStats.map(item => ({
          categoryId: item.product?.category_id,
          categoryName: item.product?.category?.name,
          count: parseInt(item.dataValues.count)
        }))
      };
    } catch (error) {
      console.error('获取收藏统计失败:', error);
      throw new Error('获取收藏统计失败');
    }
  }

  // 清空收藏夹
  async clearFavorites(userId) {
    try {
      const result = await Favorite.destroy({
        where: { user_id: userId }
      });

      return { message: '收藏夹已清空', deletedCount: result };
    } catch (error) {
      console.error('清空收藏夹失败:', error);
      throw new Error('清空收藏夹失败');
    }
  }

  // 批量删除收藏
  async batchRemoveFavorites(userId, productIds) {
    try {
      const result = await Favorite.destroy({
        where: {
          user_id: userId,
          product_id: { [Op.in]: productIds }
        }
      });

      return { message: '批量删除成功', deletedCount: result };
    } catch (error) {
      console.error('批量删除收藏失败:', error);
      throw new Error('批量删除收藏失败');
    }
  }

  // 获取热门收藏商品
  async getPopularFavorites(limit = 10) {
    try {
      const popularProducts = await Favorite.findAll({
        attributes: [
          'product_id',
          [sequelize.fn('COUNT', sequelize.col('product_id')), 'favorite_count']
        ],
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['id', 'name', 'price', 'main_image', 'sales', 'rating'],
            where: { status: 1 }
          }
        ],
        group: ['product_id'],
        order: [[sequelize.fn('COUNT', sequelize.col('product_id')), 'DESC']],
        limit: parseInt(limit),
        raw: false
      });

      return popularProducts.map(item => ({
        ...item.product.dataValues,
        favoriteCount: parseInt(item.dataValues.favorite_count)
      }));
    } catch (error) {
      console.error('获取热门收藏商品失败:', error);
      throw new Error('获取热门收藏商品失败');
    }
  }
}

module.exports = new FavoriteService();
