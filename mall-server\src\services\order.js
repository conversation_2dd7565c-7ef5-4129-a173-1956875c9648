const { v4: uuidv4 } = require('uuid');
const { Op } = require('sequelize');
const { Order, OrderItem, Product, User, Address } = require('../models');
const ProductService = require('./product');

class OrderService {
  // 创建订单
  async createOrder(userId, orderData) {
    const { addressId, items, remark } = orderData;

    // 获取收货地址
    const address = await Address.findOne({
      where: { id: addressId, user_id: userId }
    });

    if (!address) {
      throw new Error('收货地址不存在');
    }

    // 验证商品库存
    let totalAmount = 0;
    const orderItems = [];

    for (const item of items) {
      const product = await Product.findByPk(item.productId);
      
      if (!product || product.status !== 1) {
        throw new Error(`商品 ${product?.name || item.productId} 不存在或已下架`);
      }

      if (product.stock < item.quantity) {
        throw new Error(`商品 ${product.name} 库存不足`);
      }

      const itemTotal = product.price * item.quantity;
      totalAmount += itemTotal;

      orderItems.push({
        product_id: product.id,
        product_name: product.name,
        product_image: product.main_image,
        price: product.price,
        quantity: item.quantity,
        total_amount: itemTotal
      });
    }

    // 生成订单号
    const orderNo = this.generateOrderNo();

    // 创建订单
    const order = await Order.create({
      order_no: orderNo,
      user_id: userId,
      total_amount: totalAmount,
      pay_amount: totalAmount, // 暂时不考虑优惠
      freight_amount: 0, // 暂时免运费
      discount_amount: 0,
      receiver_name: address.receiver,
      receiver_phone: address.phone,
      receiver_address: `${address.province}${address.city}${address.district}${address.detail_address}`,
      pay_status: 0,
      order_status: 0,
      remark
    });

    // 创建订单商品
    await OrderItem.bulkCreate(
      orderItems.map(item => ({
        ...item,
        order_id: order.id
      }))
    );

    // 扣减库存
    for (const item of items) {
      await ProductService.updateStock(item.productId, item.quantity, 'decrease');
    }

    return order;
  }

  // 获取订单列表
  async getOrderList(userId, options = {}) {
    const { page = 1, limit = 10, status } = options;
    const offset = (page - 1) * limit;

    const where = { user_id: userId };
    if (status !== undefined) {
      where.order_status = status;
    }

    const { count, rows } = await Order.findAndCountAll({
      where,
      include: [
        {
          model: OrderItem,
          as: 'orderItems',
          include: [
            {
              model: Product,
              as: 'product'
            }
          ]
        }
      ],
      order: [['created_at', 'DESC']],
      limit,
      offset
    });

    return {
      orders: rows,
      total: count,
      page,
      limit,
      totalPages: Math.ceil(count / limit)
    };
  }

  // 获取订单详情
  async getOrderDetail(orderId, userId) {
    const order = await Order.findOne({
      where: { id: orderId, user_id: userId },
      include: [
        {
          model: OrderItem,
          as: 'orderItems',
          include: [
            {
              model: Product,
              as: 'product'
            }
          ]
        },
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'nickname', 'phone']
        }
      ]
    });

    if (!order) {
      throw new Error('订单不存在');
    }

    return order;
  }

  // 取消订单
  async cancelOrder(orderId, userId) {
    const order = await Order.findOne({
      where: { id: orderId, user_id: userId }
    });

    if (!order) {
      throw new Error('订单不存在');
    }

    if (order.order_status !== 0) {
      throw new Error('订单状态不允许取消');
    }

    // 更新订单状态
    await order.update({ order_status: 4 });

    // 恢复库存
    const orderItems = await OrderItem.findAll({
      where: { order_id: orderId }
    });

    for (const item of orderItems) {
      await ProductService.updateStock(item.product_id, item.quantity, 'increase');
    }

    return order;
  }

  // 确认收货
  async confirmOrder(orderId, userId) {
    const order = await Order.findOne({
      where: { id: orderId, user_id: userId }
    });

    if (!order) {
      throw new Error('订单不存在');
    }

    if (order.order_status !== 2) {
      throw new Error('订单状态不允许确认收货');
    }

    // 更新订单状态
    await order.update({
      order_status: 3,
      receive_time: new Date()
    });

    // 增加销量
    const orderItems = await OrderItem.findAll({
      where: { order_id: orderId }
    });

    for (const item of orderItems) {
      await ProductService.updateSales(item.product_id, item.quantity);
    }

    return order;
  }

  // 申请退款
  async applyRefund(orderId, userId, reason) {
    const order = await Order.findOne({
      where: { id: orderId, user_id: userId }
    });

    if (!order) {
      throw new Error('订单不存在');
    }

    if (order.order_status !== 1 && order.order_status !== 2 && order.order_status !== 3) {
      throw new Error('订单状态不允许申请退款');
    }

    // 更新订单状态为申请退款
    await order.update({
      order_status: 5, // 5表示申请退款
      refund_reason: reason,
      refund_time: new Date()
    });

    return order;
  }

  // 支付订单
  async payOrder(orderId, userId, payType) {
    const order = await Order.findOne({
      where: { id: orderId, user_id: userId }
    });

    if (!order) {
      throw new Error('订单不存在');
    }

    if (order.pay_status !== 0) {
      throw new Error('订单已支付');
    }

    // 更新支付状态
    await order.update({
      pay_status: 1,
      pay_type: payType,
      pay_time: new Date(),
      order_status: 1 // 待发货
    });

    return order;
  }

  // 发货
  async shipOrder(orderId, deliveryCompany, deliveryNo) {
    const order = await Order.findOne({
      where: { id: orderId }
    });

    if (!order) {
      throw new Error('订单不存在');
    }

    if (order.order_status !== 1) {
      throw new Error('订单状态不允许发货');
    }

    // 更新发货信息
    await order.update({
      delivery_company: deliveryCompany,
      delivery_no: deliveryNo,
      delivery_time: new Date(),
      order_status: 2 // 待收货
    });

    return order;
  }

  // 生成订单号
  generateOrderNo() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `ORD${timestamp}${random}`;
  }

  // 获取订单统计
  async getOrderStats(userId) {
    const stats = await Order.findAll({
      where: { user_id: userId },
      attributes: [
        'order_status',
        [Order.sequelize.fn('COUNT', Order.sequelize.col('id')), 'count']
      ],
      group: ['order_status'],
      raw: true
    });

    return stats;
  }

  // 获取物流信息
  async getLogistics(orderId, userId) {
    const order = await Order.findOne({
      where: { id: orderId, user_id: userId },
      attributes: ['delivery_company', 'delivery_no', 'delivery_time']
    });

    if (!order) {
      throw new Error('订单不存在');
    }

    if (!order.delivery_company || !order.delivery_no) {
      throw new Error('暂无物流信息');
    }

    // 这里可以集成第三方物流查询API
    // 暂时返回基本信息
    return {
      company: order.delivery_company,
      trackingNo: order.delivery_no,
      shipTime: order.delivery_time,
      status: '运输中',
      traces: [
        {
          time: order.delivery_time,
          content: `快件已由${order.delivery_company}揽收`
        }
      ]
    };
  }

  // 批量更新订单状态
  async batchUpdateStatus(orderIds, status, remark = '') {
    const orders = await Order.findAll({
      where: { id: orderIds }
    });

    const updatePromises = orders.map(order => {
      const updateData = { order_status: status };
      
      if (remark) {
        updateData.remark = remark;
      }

      // 根据状态设置相应时间
      if (status === 1) { // 待发货
        updateData.pay_time = new Date();
        updateData.pay_status = 1;
      } else if (status === 3) { // 已完成
        updateData.receive_time = new Date();
      } else if (status === 4) { // 已取消
        updateData.pay_status = 2; // 已退款
      }

      return order.update(updateData);
    });

    await Promise.all(updatePromises);
    return orders;
  }

  // 批量发货
  async batchShip(orderIds, deliveryCompany, deliveryNo) {
    const orders = await Order.findAll({
      where: { 
        id: orderIds,
        order_status: 1 // 只处理待发货的订单
      }
    });

    const updatePromises = orders.map(order => 
      order.update({
        delivery_company: deliveryCompany,
        delivery_no: deliveryNo,
        delivery_time: new Date(),
        order_status: 2 // 待收货
      })
    );

    await Promise.all(updatePromises);
    return orders;
  }

  // 导出订单数据
  async exportOrders(filters = {}) {
    const where = {};
    
    if (filters.orderNo) {
      where.order_no = { [Op.like]: `%${filters.orderNo}%` };
    }
    
    if (filters.receiverName) {
      where.receiver_name = { [Op.like]: `%${filters.receiverName}%` };
    }
    
    if (filters.orderStatus !== undefined && filters.orderStatus !== '') {
      where.order_status = parseInt(filters.orderStatus);
    }
    
    if (filters.startDate) {
      where.created_at = { [Op.gte]: new Date(filters.startDate) };
    }
    
    if (filters.endDate) {
      where.created_at = { 
        ...where.created_at,
        [Op.lte]: new Date(filters.endDate + ' 23:59:59')
      };
    }

    const orders = await Order.findAll({
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['username', 'nickname', 'phone']
        },
        {
          model: OrderItem,
          as: 'orderItems',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['name']
            }
          ]
        }
      ],
      order: [['created_at', 'DESC']]
    });

    return orders;
  }
}

module.exports = new OrderService(); 