const crypto = require('crypto');
const axios = require('axios');
const config = require('../config');
const { Order } = require('../models');

class PaymentService {
  // 创建微信支付订单
  async createWechatPayment(orderId, userId) {
    const { Order } = require('../models');

    const order = await Order.findOne({
      where: { id: orderId },
      include: [{
        model: require('../models').OrderItem,
        as: 'orderItems',
        include: [{
          model: require('../models').Product,
          as: 'product',
          attributes: ['id', 'name', 'image', 'price']
        }]
      }]
    });

    if (!order) {
      throw new Error('订单不存在');
    }

    if (userId && order.user_id !== userId) {
      throw new Error('无权限访问此订单');
    }

    if (order.pay_status !== 0) {
      throw new Error('订单已支付');
    }

    // 生成微信支付参数（开发环境使用模拟数据）
    const timeStamp = Math.floor(Date.now() / 1000).toString();
    const nonceStr = this.generateNonceStr();
    const prepayId = this.generatePrepayId();

    const paymentData = {
      appId: config.wxAppId || 'wx8792033d9e7052f1',
      timeStamp,
      nonceStr,
      package: `prepay_id=${prepayId}`,
      signType: 'MD5',
      paySign: this.generatePaySign(timeStamp, nonceStr, prepayId)
    };

    return {
      orderId: order.id,
      orderNo: order.order_no,
      amount: order.total_amount,
      paymentData,
      success: true
    };
  }

  // 创建支付宝支付订单
  async createAlipayPayment(orderId, userId) {
    const order = await Order.findOne({
      where: { id: orderId, user_id: userId }
    });

    if (!order) {
      throw new Error('订单不存在');
    }

    if (order.pay_status !== 0) {
      throw new Error('订单已支付');
    }

    // 这里应该调用支付宝API
    // 由于需要真实的支付宝配置，这里返回模拟数据
    const paymentUrl = `https://openapi.alipay.com/gateway.do?${this.buildAlipayParams(order)}`;

    return {
      orderId: order.id,
      orderNo: order.order_no,
      amount: order.pay_amount,
      paymentUrl
    };
  }

  // 处理微信支付回调
  async handleWechatCallback(callbackData) {
    // 验证签名
    if (!this.verifyWechatSign(callbackData)) {
      throw new Error('签名验证失败');
    }

    const { out_trade_no, result_code } = callbackData;

    if (result_code === 'SUCCESS') {
      const order = await Order.findOne({
        where: { order_no: out_trade_no }
      });

      if (!order) {
        throw new Error('订单不存在');
      }

      if (order.pay_status === 0) {
        await order.update({
          pay_status: 1,
          pay_type: 1,
          pay_time: new Date(),
          order_status: 1
        });
      }
    }

    return true;
  }

  // 处理支付宝回调
  async handleAlipayCallback(callbackData) {
    // 验证签名
    if (!this.verifyAlipaySign(callbackData)) {
      throw new Error('签名验证失败');
    }

    const { out_trade_no, trade_status } = callbackData;

    if (trade_status === 'TRADE_SUCCESS') {
      const order = await Order.findOne({
        where: { order_no: out_trade_no }
      });

      if (!order) {
        throw new Error('订单不存在');
      }

      if (order.pay_status === 0) {
        await order.update({
          pay_status: 1,
          pay_type: 2,
          pay_time: new Date(),
          order_status: 1
        });
      }
    }

    return true;
  }

  // 查询支付状态
  async queryPaymentStatus(orderId) {
    const order = await Order.findByPk(orderId);

    if (!order) {
      throw new Error('订单不存在');
    }

    return {
      orderId: order.id,
      orderNo: order.order_no,
      payStatus: order.pay_status,
      payType: order.pay_type,
      payTime: order.pay_time,
      amount: order.pay_amount
    };
  }

  // 申请退款
  async refund(orderId, amount, reason) {
    const order = await Order.findByPk(orderId);

    if (!order) {
      throw new Error('订单不存在');
    }

    if (order.pay_status !== 1) {
      throw new Error('订单未支付，无法退款');
    }

    if (amount > order.pay_amount) {
      throw new Error('退款金额不能大于支付金额');
    }

    // 这里应该调用支付平台的退款API
    // 由于需要真实的支付配置，这里只是模拟

    // 更新订单状态
    await order.update({
      pay_status: 2, // 已退款
      remark: `退款原因: ${reason}`
    });

    return {
      orderId: order.id,
      orderNo: order.order_no,
      refundAmount: amount,
      refundTime: new Date()
    };
  }

  // 生成随机字符串
  generateNonceStr(length = 32) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  // 生成预支付ID（模拟）
  generatePrepayId() {
    return 'wx' + Date.now() + this.generateNonceStr(10);
  }

  // 生成支付签名（模拟）
  generatePaySign(timeStamp, nonceStr, prepayId) {
    const config = require('../config');
    const appId = config.wxAppId || 'wx8792033d9e7052f1';
    const apiKey = config.payment?.wechat?.apiKey || 'test_api_key';

    const signStr = `appId=${appId}&nonceStr=${nonceStr}&package=prepay_id=${prepayId}&signType=MD5&timeStamp=${timeStamp}`;
    return require('crypto').createHash('md5').update(signStr + '&key=' + apiKey).digest('hex').toUpperCase();
  }

  // 查询支付状态
  async queryPaymentStatus(orderId, userId) {
    const { Order } = require('../models');

    const order = await Order.findOne({
      where: { id: orderId },
      attributes: ['id', 'order_no', 'pay_status', 'order_status', 'total_amount', 'pay_time']
    });

    if (!order) {
      throw new Error('订单不存在');
    }

    if (userId && order.user_id !== userId) {
      throw new Error('无权限访问此订单');
    }

    return {
      orderId: order.id,
      orderNo: order.order_no,
      isPaid: order.pay_status === 1,
      payStatus: order.pay_status,
      orderStatus: order.order_status,
      amount: order.total_amount,
      payTime: order.pay_time,
      success: true
    };
  }

  // 验证微信签名
  verifyWechatSign(data) {
    // 这里应该实现真实的签名验证
    // 由于需要真实的微信支付配置，这里返回true
    return true;
  }

  // 验证支付宝签名
  verifyAlipaySign(data) {
    // 这里应该实现真实的签名验证
    // 由于需要真实的支付宝配置，这里返回true
    return true;
  }

  // 构建支付宝参数
  buildAlipayParams(order) {
    const params = {
      app_id: config.payment.alipay.appId,
      method: 'alipay.trade.page.pay',
      charset: 'utf-8',
      sign_type: 'RSA2',
      timestamp: new Date().toISOString(),
      version: '1.0',
      biz_content: JSON.stringify({
        out_trade_no: order.order_no,
        total_amount: order.pay_amount,
        subject: '心洁茶叶商城订单',
        product_code: 'FAST_INSTANT_TRADE_PAY'
      }),
      return_url: config.payment.alipay.returnUrl,
      notify_url: config.payment.alipay.notifyUrl
    };

    // 这里应该添加签名
    return Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&');
  }
}

module.exports = new PaymentService(); 