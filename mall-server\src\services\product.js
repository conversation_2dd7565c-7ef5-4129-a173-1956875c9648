const { Op } = require('sequelize');
const { Product, Category, Review } = require('../models');
const RedisUtils = require('../utils/redis');
const cdnUtils = require('../utils/cdn');

class ProductService {
  // 缓存键名
  static CACHE_KEYS = {
    RECOMMEND: 'products:recommend',
    HOT: 'products:hot',
    NEW: 'products:new'
  };

  // 清理商品缓存
  async clearCache() {
    try {
      const keys = Object.values(ProductService.CACHE_KEYS);
      for (const key of keys) {
        await RedisUtils.del(key);
      }
      console.log('✅ 商品缓存已清理');
      return true;
    } catch (error) {
      console.error('❌ 清理商品缓存失败:', error);
      return false;
    }
  }

  /**
   * 获取商品列表（带缓存）
   */
  async getProductList(params = {}) {
    const {
      page = 1,
      limit = 10,
      categoryId,
      keyword,
      sortBy = 'created_at',
      sortOrder = 'DESC',
      isHot,
      isRecommend
    } = params;

    const offset = (page - 1) * limit;
    const where = { status: 1 };

    if (categoryId) {
      where.category_id = categoryId;
    }

    if (keyword) {
      where.name = {
        [Op.like]: `%${keyword}%`
      };
    }

    if (isHot !== undefined) {
      where.is_hot = isHot;
    }

    if (isRecommend !== undefined) {
      where.is_recommend = isRecommend;
    }

    try {
      const { count, rows } = await Product.findAndCountAll({
        where,
        include: [
          {
            model: Category,
            as: 'category',
            attributes: ['id', 'name']
          }
        ],
        order: [[sortBy, sortOrder]],
        limit: parseInt(limit),
        offset: parseInt(offset),
        raw: true,
        nest: true
      });

      // 直接返回原始数据
      const products = rows;

      return {
        list: products,
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit)
      };
    } catch (error) {
      console.error('获取商品列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取商品详情（带缓存）
   */
  async getProductDetail(id) {
    try {
      const product = await Product.findByPk(id, {
        include: [
          {
            model: Category,
            as: 'category',
            attributes: ['id', 'name']
          }
        ]
      });

      if (!product) {
        throw new Error('商品不存在');
      }

      // 应用CDN转换，包含响应式图片
      const transformedProduct = cdnUtils.transformImages(product.toJSON(), 'large');
      
      // 添加响应式图片URL
      if (transformedProduct.main_image) {
        transformedProduct.responsive_images = cdnUtils.getResponsiveImages(transformedProduct.main_image);
      }

      return transformedProduct;
    } catch (error) {
      console.error('获取商品详情失败:', error);
      throw error;
    }
  }

  // 获取推荐商品
  async getRecommendProducts(limit = 10) {
    const cacheKey = ProductService.CACHE_KEYS.RECOMMEND;
    let products = await RedisUtils.get(cacheKey);

    if (!products) {
      products = await Product.findAll({
        where: { 
          is_recommend: 1,
          status: 1
        },
        include: [
          {
            model: Category,
            as: 'category',
            attributes: ['id', 'name']
          }
        ],
        limit,
        order: [['sort_order', 'ASC'], ['created_at', 'DESC']],
        raw: true
      });

      // 应用CDN转换后再缓存
      const transformedProducts = cdnUtils.transformImages(products, 'medium');
      await RedisUtils.set(cacheKey, JSON.stringify(transformedProducts), 1800);
      
      return transformedProducts;
    }

    // 从缓存获取数据并解析
    try {
      const parsedProducts = JSON.parse(products);
      return parsedProducts;
    } catch (parseError) {
      console.warn('缓存数据解析失败，重新获取数据');
      // 清除错误缓存
      await RedisUtils.del(cacheKey);
      // 递归调用重新获取
      return await this.getRecommendProducts(limit);
    }
  }

  // 获取热销商品
  async getHotProducts(limit = 10) {
    const cacheKey = ProductService.CACHE_KEYS.HOT;
    let products = await RedisUtils.get(cacheKey);

    if (!products) {
      products = await Product.findAll({
        where: { 
          is_hot: 1,
          status: 1
        },
        include: [
          {
            model: Category,
            as: 'category',
            attributes: ['id', 'name']
          }
        ],
        limit,
        order: [['sales', 'DESC'], ['created_at', 'DESC']],
        raw: true
      });

      // 应用CDN转换后再缓存
      const transformedProducts = cdnUtils.transformImages(products, 'medium');
      await RedisUtils.set(cacheKey, JSON.stringify(transformedProducts), 1800);
      
      return transformedProducts;
    }

    // 从缓存获取数据并解析
    try {
      const parsedProducts = JSON.parse(products);
      return parsedProducts;
    } catch (parseError) {
      console.warn('缓存数据解析失败，重新获取数据');
      // 清除错误缓存
      await RedisUtils.del(cacheKey);
      // 递归调用重新获取
      return await this.getHotProducts(limit);
    }
  }

  // 获取新品商品
  async getNewProducts(limit = 10) {
    const cacheKey = ProductService.CACHE_KEYS.NEW;
    let products = await RedisUtils.get(cacheKey);

    if (!products) {
      products = await Product.findAll({
        where: { 
          status: 1
        },
        include: [
          {
            model: Category,
            as: 'category',
            attributes: ['id', 'name']
          }
        ],
        limit,
        order: [['created_at', 'DESC']]
      });

      // 缓存新品商品
      await RedisUtils.set(cacheKey, products, 1800);
    }

    return products;
  }

  // 搜索商品
  async searchProducts(keyword, page = 1, limit = 10) {
    const offset = (page - 1) * limit;

    const { count, rows } = await Product.findAndCountAll({
      where: {
        [Op.or]: [
          { name: { [Op.like]: `%${keyword}%` } },
          { description: { [Op.like]: `%${keyword}%` } }
        ],
        status: 1
      },
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name']
        }
      ],
      order: [['sales', 'DESC'], ['created_at', 'DESC']],
      limit,
      offset
    });

    return {
      products: rows,
      total: count,
      page,
      limit,
      totalPages: Math.ceil(count / limit),
      keyword
    };
  }

  // 更新商品库存
  async updateStock(productId, quantity, type = 'decrease') {
    const product = await Product.findByPk(productId);
    
    if (!product) {
      throw new Error('商品不存在');
    }

    if (type === 'decrease') {
      if (product.stock < quantity) {
        throw new Error('库存不足');
      }
      product.stock -= quantity;
    } else {
      product.stock += quantity;
    }

    await product.save();
    return product;
  }

  // 更新商品销量
  async updateSales(productId, quantity) {
    const product = await Product.findByPk(productId);
    
    if (!product) {
      throw new Error('商品不存在');
    }

    product.sales += quantity;
    await product.save();
    return product;
  }

  // 创建商品（带缓存清理）
  async createProduct(productData) {
    try {
      const product = await Product.create(productData);
      // 创建成功后清理缓存
      await this.clearCache();
      return product;
    } catch (error) {
      console.error('创建商品失败:', error);
      throw error;
    }
  }

  // 更新商品（带缓存清理）
  async updateProduct(id, updateData) {
    try {
      const product = await Product.findByPk(id);
      if (!product) {
        throw new Error('商品不存在');
      }
      await product.update(updateData);
      // 更新成功后清理缓存
      await this.clearCache();
      return product;
    } catch (error) {
      console.error('更新商品失败:', error);
      throw error;
    }
  }

  // 删除商品（带缓存清理）
  async deleteProduct(id) {
    try {
      const product = await Product.findByPk(id);
      if (!product) {
        throw new Error('商品不存在');
      }
      await product.destroy();
      // 删除成功后清理缓存
      await this.clearCache();
      return true;
    } catch (error) {
      console.error('删除商品失败:', error);
      throw error;
    }
  }
}

module.exports = new ProductService(); 