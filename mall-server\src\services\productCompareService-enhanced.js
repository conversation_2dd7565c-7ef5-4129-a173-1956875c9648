// 智能商品对比服务 - 优化完善版
const { Op, sequelize } = require('sequelize');
const { ProductCompare, Product, Category } = require('../models');

class EnhancedProductCompareService {
  
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 10 * 60 * 1000; // 10分钟缓存
    this.maxCompareItems = 4;
    this.compareAttributes = [
      'price', 'original_price', 'sales', 'rating', 'stock', 
      'specifications', 'tags', 'brand', 'origin'
    ];
  }

  // 智能添加到对比
  async smartAddToCompare(userId, productId, compareGroup = 'default') {
    try {
      // 检查商品是否存在
      const product = await Product.findByPk(productId, {
        attributes: ['id', 'name', 'category_id', 'price', 'status']
      });

      if (!product || product.status !== 1) {
        throw new Error('商品不存在或已下架');
      }

      // 检查对比组商品数量
      const existingCount = await ProductCompare.count({
        where: { user_id: userId, compare_group: compareGroup }
      });

      if (existingCount >= this.maxCompareItems) {
        // 获取最旧的对比商品，准备替换
        const oldestCompare = await ProductCompare.findOne({
          where: { user_id: userId, compare_group: compareGroup },
          order: [['added_at', 'ASC']]
        });

        if (oldestCompare) {
          await oldestCompare.destroy();
        }
      }

      // 检查是否已在对比列表中
      const existingCompare = await ProductCompare.findOne({
        where: { user_id: userId, product_id: productId, compare_group: compareGroup }
      });

      if (existingCompare) {
        // 更新时间，移到最新位置
        await existingCompare.update({ 
          added_at: new Date(),
          sort_order: existingCount - 1
        });

        return { 
          message: '商品已在对比列表中，已更新位置', 
          compare: existingCompare,
          suggestions: await this.getSmartCompareSuggestions(userId, productId, compareGroup)
        };
      }

      // 添加到对比
      const compare = await ProductCompare.create({
        user_id: userId,
        product_id: productId,
        compare_group: compareGroup,
        sort_order: existingCount,
        added_at: new Date()
      });

      // 获取智能对比建议
      const suggestions = await this.getSmartCompareSuggestions(userId, productId, compareGroup);

      return { 
        message: '添加对比成功', 
        compare,
        suggestions,
        compareCount: existingCount + 1
      };

    } catch (error) {
      console.error('智能添加对比失败:', error);
      throw new Error(error.message || '添加对比失败');
    }
  }

  // 获取智能对比建议
  async getSmartCompareSuggestions(userId, baseProductId, compareGroup, limit = 6) {
    try {
      const cacheKey = `compare_suggestions_${userId}_${baseProductId}_${compareGroup}`;
      const cached = this.cache.get(cacheKey);
      
      if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }

      // 获取基础商品信息
      const baseProduct = await Product.findByPk(baseProductId, {
        attributes: ['category_id', 'price', 'tags', 'brand']
      });

      if (!baseProduct) return [];

      // 获取当前对比组中的商品ID
      const existingCompareIds = await ProductCompare.findAll({
        where: { user_id: userId, compare_group: compareGroup },
        attributes: ['product_id']
      }).then(items => items.map(item => item.product_id));

      // 多维度推荐算法
      const [categoryMatches, priceMatches, brandMatches] = await Promise.all([
        this.getCategorySimilarProducts(baseProduct.category_id, existingCompareIds, limit / 2),
        this.getPriceSimilarProducts(baseProduct.price, existingCompareIds, limit / 3),
        this.getBrandSimilarProducts(baseProduct.brand, existingCompareIds, limit / 3)
      ]);

      // 合并并去重
      const allSuggestions = [...categoryMatches, ...priceMatches, ...brandMatches];
      const uniqueSuggestions = this.deduplicateProducts(allSuggestions, existingCompareIds);
      const finalSuggestions = uniqueSuggestions.slice(0, limit);

      // 缓存结果
      this.cache.set(cacheKey, {
        data: finalSuggestions,
        timestamp: Date.now()
      });

      return finalSuggestions;
    } catch (error) {
      console.error('获取智能对比建议失败:', error);
      return [];
    }
  }

  // 获取同分类相似商品
  async getCategorySimilarProducts(categoryId, excludeIds, limit) {
    const [results] = await sequelize.query(`
      SELECT 
        p.id, p.name, p.price, p.main_image, p.sales, p.rating,
        (p.sales * 0.3 + p.rating * 0.3 + p.favorite_count * 0.2 + p.view_count * 0.2) as score
      FROM products p
      WHERE p.category_id = :categoryId
      AND p.status = 1
      AND p.id NOT IN (${excludeIds.map(() => '?').join(',') || 'NULL'})
      ORDER BY score DESC, p.created_at DESC
      LIMIT :limit
    `, {
      replacements: [categoryId, ...excludeIds, limit],
      type: sequelize.QueryTypes.SELECT
    });

    return results.map(item => ({ ...item, reason: '同类商品' }));
  }

  // 获取价格相似商品
  async getPriceSimilarProducts(basePrice, excludeIds, limit) {
    const priceRange = basePrice * 0.3; // 30%价格浮动
    
    const [results] = await sequelize.query(`
      SELECT 
        p.id, p.name, p.price, p.main_image, p.sales, p.rating,
        ABS(p.price - :basePrice) as price_diff
      FROM products p
      WHERE p.price BETWEEN :minPrice AND :maxPrice
      AND p.status = 1
      AND p.id NOT IN (${excludeIds.map(() => '?').join(',') || 'NULL'})
      ORDER BY price_diff ASC, p.sales DESC
      LIMIT :limit
    `, {
      replacements: [
        basePrice,
        basePrice - priceRange,
        basePrice + priceRange,
        ...excludeIds,
        limit
      ],
      type: sequelize.QueryTypes.SELECT
    });

    return results.map(item => ({ ...item, reason: '价格相近' }));
  }

  // 获取品牌相似商品
  async getBrandSimilarProducts(brand, excludeIds, limit) {
    if (!brand) return [];

    const [results] = await sequelize.query(`
      SELECT 
        p.id, p.name, p.price, p.main_image, p.sales, p.rating
      FROM products p
      WHERE p.brand = :brand
      AND p.status = 1
      AND p.id NOT IN (${excludeIds.map(() => '?').join(',') || 'NULL'})
      ORDER BY p.sales DESC, p.rating DESC
      LIMIT :limit
    `, {
      replacements: [brand, ...excludeIds, limit],
      type: sequelize.QueryTypes.SELECT
    });

    return results.map(item => ({ ...item, reason: '同品牌' }));
  }

  // 获取智能对比详情
  async getSmartCompareDetails(userId, compareGroup = 'default') {
    try {
      const cacheKey = `compare_details_${userId}_${compareGroup}`;
      const cached = this.cache.get(cacheKey);
      
      if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }

      const compareItems = await ProductCompare.findAll({
        where: { user_id: userId, compare_group: compareGroup },
        include: [{
          model: Product, as: 'product',
          attributes: [
            'id', 'name', 'price', 'original_price', 'main_image', 'images',
            'sales', 'rating', 'stock', 'status', 'description', 'specifications',
            'tags', 'brand', 'origin', 'weight', 'shelf_life'
          ],
          include: [{ model: Category, as: 'category', attributes: ['id', 'name'] }]
        }],
        order: [['sort_order', 'ASC'], ['added_at', 'ASC']]
      });

      if (compareItems.length < 2) {
        throw new Error('至少需要2个商品才能进行对比');
      }

      // 过滤有效商品
      const validItems = compareItems.filter(item => item.product && item.product.status === 1);

      // 构建对比数据
      const compareData = this.buildCompareMatrix(validItems);

      // 生成对比分析
      const analysis = this.generateCompareAnalysis(validItems);

      // 获取优势分析
      const advantages = this.analyzeProductAdvantages(validItems);

      const result = {
        compareGroup,
        count: validItems.length,
        products: validItems.map(item => item.product),
        compareMatrix: compareData,
        analysis,
        advantages,
        recommendations: await this.getCompareRecommendations(validItems)
      };

      // 缓存结果
      this.cache.set(cacheKey, {
        data: result,
        timestamp: Date.now()
      });

      return result;
    } catch (error) {
      console.error('获取智能对比详情失败:', error);
      throw new Error('获取对比详情失败');
    }
  }

  // 构建对比矩阵
  buildCompareMatrix(compareItems) {
    const matrix = {
      basic: [],      // 基本信息
      price: [],      // 价格信息
      sales: [],      // 销售信息
      specs: [],      // 规格参数
      features: []    // 特色功能
    };

    compareItems.forEach(item => {
      const product = item.product;

      // 基本信息
      matrix.basic.push({
        id: product.id,
        name: product.name,
        image: product.main_image,
        category: product.category?.name,
        brand: product.brand,
        origin: product.origin
      });

      // 价格信息
      matrix.price.push({
        id: product.id,
        price: product.price,
        originalPrice: product.original_price,
        discount: this.calculateDiscount(product.price, product.original_price),
        priceLevel: this.getPriceLevel(product.price, compareItems)
      });

      // 销售信息
      matrix.sales.push({
        id: product.id,
        sales: product.sales,
        rating: product.rating,
        stock: product.stock,
        popularity: this.calculatePopularity(product)
      });

      // 规格参数
      let specifications = {};
      try {
        specifications = typeof product.specifications === 'string' 
          ? JSON.parse(product.specifications) 
          : product.specifications || {};
      } catch (e) {
        specifications = {};
      }

      matrix.specs.push({
        id: product.id,
        weight: product.weight,
        shelfLife: product.shelf_life,
        specifications
      });

      // 特色功能
      let tags = [];
      try {
        tags = typeof product.tags === 'string' 
          ? JSON.parse(product.tags) 
          : product.tags || [];
      } catch (e) {
        tags = [];
      }

      matrix.features.push({
        id: product.id,
        tags,
        features: this.extractFeatures(product.description)
      });
    });

    return matrix;
  }

  // 计算折扣
  calculateDiscount(price, originalPrice) {
    if (!originalPrice || originalPrice <= price) return 0;
    return Math.round((1 - price / originalPrice) * 100);
  }

  // 获取价格等级
  getPriceLevel(price, compareItems) {
    const prices = compareItems.map(item => item.product.price).sort((a, b) => a - b);
    const index = prices.indexOf(price);
    const total = prices.length;
    
    if (index < total * 0.33) return 'low';
    if (index < total * 0.67) return 'medium';
    return 'high';
  }

  // 计算人气度
  calculatePopularity(product) {
    return product.sales * 0.4 + product.rating * 20 + (product.favorite_count || 0) * 0.3;
  }

  // 提取特色功能
  extractFeatures(description) {
    if (!description) return [];
    
    const featureKeywords = ['有机', '无农药', '手工', '传统', '新鲜', '香醇', '回甘', '耐泡'];
    const features = [];
    
    featureKeywords.forEach(keyword => {
      if (description.includes(keyword)) {
        features.push(keyword);
      }
    });
    
    return features;
  }

  // 生成对比分析
  generateCompareAnalysis(compareItems) {
    const products = compareItems.map(item => item.product);
    const analysis = {
      priceRange: {
        min: Math.min(...products.map(p => p.price)),
        max: Math.max(...products.map(p => p.price)),
        avg: products.reduce((sum, p) => sum + p.price, 0) / products.length
      },
      salesRange: {
        min: Math.min(...products.map(p => p.sales)),
        max: Math.max(...products.map(p => p.sales)),
        total: products.reduce((sum, p) => sum + p.sales, 0)
      },
      ratingRange: {
        min: Math.min(...products.map(p => p.rating)),
        max: Math.max(...products.map(p => p.rating)),
        avg: products.reduce((sum, p) => sum + p.rating, 0) / products.length
      },
      categories: [...new Set(products.map(p => p.category?.name))],
      brands: [...new Set(products.map(p => p.brand))].filter(Boolean)
    };

    return analysis;
  }

  // 分析商品优势
  analyzeProductAdvantages(compareItems) {
    const products = compareItems.map(item => item.product);
    const advantages = {};

    products.forEach(product => {
      advantages[product.id] = [];

      // 价格优势
      const minPrice = Math.min(...products.map(p => p.price));
      if (product.price === minPrice) {
        advantages[product.id].push('价格最低');
      }

      // 销量优势
      const maxSales = Math.max(...products.map(p => p.sales));
      if (product.sales === maxSales) {
        advantages[product.id].push('销量最高');
      }

      // 评分优势
      const maxRating = Math.max(...products.map(p => p.rating));
      if (product.rating === maxRating) {
        advantages[product.id].push('评分最高');
      }

      // 库存优势
      const maxStock = Math.max(...products.map(p => p.stock));
      if (product.stock === maxStock && product.stock > 0) {
        advantages[product.id].push('库存充足');
      }

      // 折扣优势
      const discount = this.calculateDiscount(product.price, product.original_price);
      if (discount > 0) {
        const maxDiscount = Math.max(...products.map(p => 
          this.calculateDiscount(p.price, p.original_price)
        ));
        if (discount === maxDiscount) {
          advantages[product.id].push(`折扣最大(${discount}%)`);
        }
      }
    });

    return advantages;
  }

  // 获取对比推荐
  async getCompareRecommendations(compareItems) {
    const recommendations = [];

    // 性价比推荐
    const costEffective = compareItems.reduce((best, current) => {
      const currentScore = current.product.rating / current.product.price * 100;
      const bestScore = best.product.rating / best.product.price * 100;
      return currentScore > bestScore ? current : best;
    });
    
    recommendations.push({
      type: 'cost_effective',
      productId: costEffective.product.id,
      reason: '性价比最高'
    });

    // 人气推荐
    const mostPopular = compareItems.reduce((best, current) => {
      return current.product.sales > best.product.sales ? current : best;
    });
    
    recommendations.push({
      type: 'popular',
      productId: mostPopular.product.id,
      reason: '最受欢迎'
    });

    // 品质推荐
    const highestQuality = compareItems.reduce((best, current) => {
      return current.product.rating > best.product.rating ? current : best;
    });
    
    recommendations.push({
      type: 'quality',
      productId: highestQuality.product.id,
      reason: '品质最佳'
    });

    return recommendations;
  }

  // 去重商品
  deduplicateProducts(products, excludeIds = []) {
    const seen = new Set(excludeIds);
    return products.filter(product => {
      if (seen.has(product.id)) return false;
      seen.add(product.id);
      return true;
    });
  }

  // 清理缓存
  clearCache(pattern = null) {
    if (pattern) {
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }
}

module.exports = new EnhancedProductCompareService();
