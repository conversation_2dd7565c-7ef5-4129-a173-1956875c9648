// 商品对比服务
const { Op, sequelize } = require('sequelize');
const { ProductCompare, Product, Category } = require('../models');

class ProductCompareService {

  // 添加商品到对比
  async addToCompare(userId, productId, compareGroup = 'default') {
    try {
      // 检查商品是否存在
      const product = await Product.findByPk(productId);
      if (!product) {
        throw new Error('商品不存在');
      }

      // 检查对比组商品数量限制（最多4个）
      const existingCount = await ProductCompare.count({
        where: { user_id: userId, compare_group: compareGroup }
      });

      if (existingCount >= 4) {
        throw new Error('对比商品数量已达上限（最多4个）');
      }

      // 检查是否已在对比列表中
      const existingCompare = await ProductCompare.findOne({
        where: { user_id: userId, product_id: productId, compare_group: compareGroup }
      });

      if (existingCompare) {
        return { message: '商品已在对比列表中', compare: existingCompare };
      }

      // 添加到对比
      const compare = await ProductCompare.create({
        user_id: userId,
        product_id: productId,
        compare_group: compareGroup,
        sort_order: existingCount
      });

      return { message: '添加对比成功', compare };
    } catch (error) {
      console.error('添加商品对比失败:', error);
      throw new Error(error.message || '添加商品对比失败');
    }
  }

  // 从对比中移除商品
  async removeFromCompare(userId, productId, compareGroup = 'default') {
    try {
      const result = await ProductCompare.destroy({
        where: { user_id: userId, product_id: productId, compare_group: compareGroup }
      });

      if (result === 0) {
        throw new Error('对比记录不存在');
      }

      // 重新排序剩余商品
      await this.reorderCompareItems(userId, compareGroup);

      return { message: '移除对比成功' };
    } catch (error) {
      console.error('移除商品对比失败:', error);
      throw new Error('移除商品对比失败');
    }
  }

  // 获取对比列表
  async getCompareList(userId, compareGroup = 'default') {
    try {
      const compareItems = await ProductCompare.findAll({
        where: { user_id: userId, compare_group: compareGroup },
        include: [{
          model: Product, as: 'product',
          attributes: [
            'id', 'name', 'price', 'original_price', 'main_image', 'images',
            'sales', 'rating', 'stock', 'status', 'description', 'specifications'
          ],
          include: [{ model: Category, as: 'category', attributes: ['id', 'name'] }]
        }],
        order: [['sort_order', 'ASC'], ['added_at', 'ASC']]
      });

      // 过滤掉已下架的商品
      const validItems = compareItems.filter(item => item.product && item.product.status === 1);

      // 如果有商品下架，清理无效记录
      if (validItems.length < compareItems.length) {
        const invalidIds = compareItems
          .filter(item => !item.product || item.product.status !== 1)
          .map(item => item.id);
        
        await ProductCompare.destroy({
          where: { id: { [Op.in]: invalidIds } }
        });
      }

      return {
        compareGroup,
        products: validItems.map(item => ({
          ...item.product.dataValues,
          addedAt: item.added_at,
          sortOrder: item.sort_order
        })),
        count: validItems.length
      };
    } catch (error) {
      console.error('获取对比列表失败:', error);
      throw new Error('获取对比列表失败');
    }
  }

  // 获取详细对比数据
  async getCompareDetails(userId, compareGroup = 'default') {
    try {
      const compareList = await this.getCompareList(userId, compareGroup);
      
      if (compareList.products.length < 2) {
        throw new Error('至少需要2个商品才能进行对比');
      }

      // 提取对比维度
      const compareData = {
        basic: [], // 基本信息
        price: [], // 价格信息
        sales: [], // 销售信息
        specifications: [] // 规格参数
      };

      compareList.products.forEach(product => {
        // 基本信息
        compareData.basic.push({
          id: product.id,
          name: product.name,
          image: product.main_image,
          category: product.category?.name
        });

        // 价格信息
        compareData.price.push({
          id: product.id,
          price: product.price,
          originalPrice: product.original_price,
          discount: product.original_price > product.price 
            ? Math.round((1 - product.price / product.original_price) * 100) 
            : 0
        });

        // 销售信息
        compareData.sales.push({
          id: product.id,
          sales: product.sales,
          rating: product.rating,
          stock: product.stock
        });

        // 规格参数
        let specifications = {};
        try {
          specifications = typeof product.specifications === 'string' 
            ? JSON.parse(product.specifications) 
            : product.specifications || {};
        } catch (e) {
          specifications = {};
        }

        compareData.specifications.push({
          id: product.id,
          specifications
        });
      });

      return {
        compareGroup,
        count: compareList.products.length,
        compareData
      };
    } catch (error) {
      console.error('获取对比详情失败:', error);
      throw new Error('获取对比详情失败');
    }
  }

  // 清空对比列表
  async clearCompareList(userId, compareGroup = 'default') {
    try {
      const result = await ProductCompare.destroy({
        where: { user_id: userId, compare_group: compareGroup }
      });

      return { message: '对比列表已清空', deletedCount: result };
    } catch (error) {
      console.error('清空对比列表失败:', error);
      throw new Error('清空对比列表失败');
    }
  }

  // 检查商品是否在对比列表中
  async isInCompare(userId, productId, compareGroup = 'default') {
    try {
      const compare = await ProductCompare.findOne({
        where: { user_id: userId, product_id: productId, compare_group: compareGroup }
      });

      return !!compare;
    } catch (error) {
      console.error('检查对比状态失败:', error);
      return false;
    }
  }

  // 批量检查对比状态
  async batchCheckInCompare(userId, productIds, compareGroup = 'default') {
    try {
      const compares = await ProductCompare.findAll({
        where: {
          user_id: userId,
          product_id: { [Op.in]: productIds },
          compare_group: compareGroup
        },
        attributes: ['product_id']
      });

      const compareIds = compares.map(c => c.product_id);
      
      return productIds.reduce((result, productId) => {
        result[productId] = compareIds.includes(productId);
        return result;
      }, {});
    } catch (error) {
      console.error('批量检查对比状态失败:', error);
      return {};
    }
  }

  // 调整商品对比顺序
  async reorderCompareItems(userId, compareGroup = 'default', productIds = []) {
    try {
      if (productIds.length === 0) {
        // 自动重新排序
        const items = await ProductCompare.findAll({
          where: { user_id: userId, compare_group: compareGroup },
          order: [['added_at', 'ASC']]
        });

        for (let i = 0; i < items.length; i++) {
          await items[i].update({ sort_order: i });
        }
      } else {
        // 按指定顺序排序
        for (let i = 0; i < productIds.length; i++) {
          await ProductCompare.update(
            { sort_order: i },
            { 
              where: { 
                user_id: userId, 
                product_id: productIds[i], 
                compare_group: compareGroup 
              } 
            }
          );
        }
      }

      return { message: '排序更新成功' };
    } catch (error) {
      console.error('调整对比顺序失败:', error);
      throw new Error('调整对比顺序失败');
    }
  }

  // 获取用户所有对比组
  async getUserCompareGroups(userId) {
    try {
      const groups = await ProductCompare.findAll({
        where: { user_id: userId },
        attributes: [
          'compare_group',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
          [sequelize.fn('MAX', sequelize.col('added_at')), 'last_added']
        ],
        group: ['compare_group'],
        order: [[sequelize.fn('MAX', sequelize.col('added_at')), 'DESC']],
        raw: true
      });

      return groups.map(group => ({
        groupName: group.compare_group,
        count: parseInt(group.count),
        lastAdded: group.last_added
      }));
    } catch (error) {
      console.error('获取对比组失败:', error);
      throw new Error('获取对比组失败');
    }
  }

  // 智能推荐对比商品
  async getRecommendedCompareProducts(userId, productId, limit = 5) {
    try {
      const product = await Product.findByPk(productId, {
        attributes: ['category_id', 'price']
      });

      if (!product) {
        throw new Error('商品不存在');
      }

      // 推荐同分类、价格相近的商品
      const priceRange = product.price * 0.3; // 价格浮动30%
      
      const recommendedProducts = await Product.findAll({
        where: {
          category_id: product.category_id,
          id: { [Op.ne]: productId },
          price: {
            [Op.between]: [product.price - priceRange, product.price + priceRange]
          },
          status: 1
        },
        attributes: ['id', 'name', 'price', 'main_image', 'sales', 'rating'],
        order: [['sales', 'DESC'], ['rating', 'DESC']],
        limit: parseInt(limit)
      });

      return recommendedProducts;
    } catch (error) {
      console.error('获取推荐对比商品失败:', error);
      throw new Error('获取推荐对比商品失败');
    }
  }
}

module.exports = new ProductCompareService();
