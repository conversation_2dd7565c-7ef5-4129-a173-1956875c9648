// 销售报表服务
const { Op, sequelize } = require('sequelize');
const { 
  Order, 
  OrderItem, 
  Product, 
  User, 
  Category,
  SalesReport 
} = require('../models');

class SalesReportService {

  // 生成日报表
  async generateDailyReport(date = new Date()) {
    try {
      const reportDate = new Date(date);
      const startOfDay = new Date(reportDate.getFullYear(), reportDate.getMonth(), reportDate.getDate());
      const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);

      const reportData = await this.calculateReportData(startOfDay, endOfDay);
      
      // 保存或更新报表
      const [report, created] = await SalesReport.upsert({
        report_date: startOfDay.toISOString().split('T')[0],
        report_type: 'daily',
        ...reportData
      });

      return report;
    } catch (error) {
      console.error('生成日报表失败:', error);
      throw new Error('生成日报表失败');
    }
  }

  // 生成周报表
  async generateWeeklyReport(date = new Date()) {
    try {
      const reportDate = new Date(date);
      const startOfWeek = new Date(reportDate);
      startOfWeek.setDate(reportDate.getDate() - reportDate.getDay());
      startOfWeek.setHours(0, 0, 0, 0);
      
      const endOfWeek = new Date(startOfWeek.getTime() + 7 * 24 * 60 * 60 * 1000);

      const reportData = await this.calculateReportData(startOfWeek, endOfWeek);
      
      const [report, created] = await SalesReport.upsert({
        report_date: startOfWeek.toISOString().split('T')[0],
        report_type: 'weekly',
        ...reportData
      });

      return report;
    } catch (error) {
      console.error('生成周报表失败:', error);
      throw new Error('生成周报表失败');
    }
  }

  // 生成月报表
  async generateMonthlyReport(date = new Date()) {
    try {
      const reportDate = new Date(date);
      const startOfMonth = new Date(reportDate.getFullYear(), reportDate.getMonth(), 1);
      const endOfMonth = new Date(reportDate.getFullYear(), reportDate.getMonth() + 1, 1);

      const reportData = await this.calculateReportData(startOfMonth, endOfMonth);
      
      const [report, created] = await SalesReport.upsert({
        report_date: startOfMonth.toISOString().split('T')[0],
        report_type: 'monthly',
        ...reportData
      });

      return report;
    } catch (error) {
      console.error('生成月报表失败:', error);
      throw new Error('生成月报表失败');
    }
  }

  // 计算报表数据
  async calculateReportData(startDate, endDate) {
    try {
      const whereCondition = {
        created_at: {
          [Op.between]: [startDate, endDate]
        }
      };

      // 基础订单统计
      const orderStats = await Order.findOne({
        where: whereCondition,
        attributes: [
          [sequelize.fn('COUNT', sequelize.col('id')), 'total_orders'],
          [sequelize.fn('SUM', sequelize.col('total_amount')), 'total_amount'],
          [sequelize.fn('COUNT', sequelize.literal('CASE WHEN pay_status = 1 THEN 1 END')), 'paid_orders'],
          [sequelize.fn('SUM', sequelize.literal('CASE WHEN pay_status = 1 THEN pay_amount ELSE 0 END')), 'paid_amount'],
          [sequelize.fn('COUNT', sequelize.literal('CASE WHEN order_status = 4 THEN 1 END')), 'refund_orders'],
          [sequelize.fn('SUM', sequelize.literal('CASE WHEN order_status = 4 THEN pay_amount ELSE 0 END')), 'refund_amount'],
          [sequelize.fn('AVG', sequelize.col('pay_amount')), 'avg_order_amount']
        ],
        raw: true
      });

      // 用户统计
      const userStats = await User.findOne({
        where: {
          createdAt: whereCondition.created_at
        },
        attributes: [
          [sequelize.fn('COUNT', sequelize.col('id')), 'new_users']
        ],
        raw: true
      });

      // 活跃用户统计
      const activeUsers = await Order.count({
        where: whereCondition,
        distinct: true,
        col: 'user_id'
      });

      // 热销商品TOP10
      const topProducts = await OrderItem.findAll({
        include: [
          {
            model: Order,
            as: 'order',
            where: {
              ...whereCondition,
              pay_status: 1
            },
            attributes: []
          },
          {
            model: Product,
            as: 'product',
            attributes: ['id', 'name', 'price', 'main_image']
          }
        ],
        attributes: [
          'product_id',
          [sequelize.fn('SUM', sequelize.col('quantity')), 'total_sales'],
          [sequelize.fn('SUM', sequelize.literal('quantity * price')), 'total_revenue']
        ],
        group: ['product_id'],
        order: [[sequelize.fn('SUM', sequelize.col('quantity')), 'DESC']],
        limit: 10,
        raw: false
      });

      // 热销分类TOP10
      const topCategories = await OrderItem.findAll({
        include: [
          {
            model: Order,
            as: 'order',
            where: {
              ...whereCondition,
              pay_status: 1
            },
            attributes: []
          },
          {
            model: Product,
            as: 'product',
            include: [
              {
                model: Category,
                as: 'category',
                attributes: ['id', 'name']
              }
            ],
            attributes: ['category_id']
          }
        ],
        attributes: [
          [sequelize.fn('SUM', sequelize.col('quantity')), 'total_sales'],
          [sequelize.fn('SUM', sequelize.literal('quantity * price')), 'total_revenue']
        ],
        group: ['product.category_id'],
        order: [[sequelize.fn('SUM', sequelize.literal('quantity * price')), 'DESC']],
        limit: 10,
        raw: false
      });

      // 计算转化率
      const conversionRate = orderStats.total_orders > 0 
        ? (orderStats.paid_orders / orderStats.total_orders).toFixed(4)
        : 0;

      return {
        total_orders: parseInt(orderStats.total_orders) || 0,
        total_amount: parseFloat(orderStats.total_amount) || 0,
        paid_orders: parseInt(orderStats.paid_orders) || 0,
        paid_amount: parseFloat(orderStats.paid_amount) || 0,
        refund_orders: parseInt(orderStats.refund_orders) || 0,
        refund_amount: parseFloat(orderStats.refund_amount) || 0,
        new_users: parseInt(userStats.new_users) || 0,
        active_users: activeUsers,
        conversion_rate: parseFloat(conversionRate),
        avg_order_amount: parseFloat(orderStats.avg_order_amount) || 0,
        top_products: topProducts.map(item => ({
          product_id: item.product_id,
          product_name: item.product?.name,
          total_sales: parseInt(item.dataValues.total_sales),
          total_revenue: parseFloat(item.dataValues.total_revenue)
        })),
        top_categories: topCategories.map(item => ({
          category_id: item.product?.category_id,
          category_name: item.product?.category?.name,
          total_sales: parseInt(item.dataValues.total_sales),
          total_revenue: parseFloat(item.dataValues.total_revenue)
        })),
        user_analysis: {
          new_users: parseInt(userStats.new_users) || 0,
          active_users: activeUsers,
          retention_rate: 0 // 可以后续计算留存率
        }
      };
    } catch (error) {
      console.error('计算报表数据失败:', error);
      throw new Error('计算报表数据失败');
    }
  }

  // 获取报表列表
  async getReports(type, startDate, endDate, page = 1, limit = 20) {
    try {
      const whereCondition = {
        report_type: type
      };

      if (startDate && endDate) {
        whereCondition.report_date = {
          [Op.between]: [startDate, endDate]
        };
      }

      const offset = (page - 1) * limit;

      const { count, rows } = await SalesReport.findAndCountAll({
        where: whereCondition,
        order: [['report_date', 'DESC']],
        limit: parseInt(limit),
        offset
      });

      return {
        total: count,
        reports: rows,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit)
      };
    } catch (error) {
      console.error('获取报表列表失败:', error);
      throw new Error('获取报表列表失败');
    }
  }

  // 自动生成报表（定时任务调用）
  async autoGenerateReports() {
    try {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);

      // 生成昨日日报表
      await this.generateDailyReport(yesterday);

      // 如果是周一，生成上周周报表
      if (yesterday.getDay() === 0) {
        await this.generateWeeklyReport(yesterday);
      }

      // 如果是月初，生成上月月报表
      if (yesterday.getDate() === 1) {
        const lastMonth = new Date(yesterday);
        lastMonth.setMonth(lastMonth.getMonth() - 1);
        await this.generateMonthlyReport(lastMonth);
      }

      console.log('自动生成报表完成');
    } catch (error) {
      console.error('自动生成报表失败:', error);
    }
  }
}

module.exports = new SalesReportService();
