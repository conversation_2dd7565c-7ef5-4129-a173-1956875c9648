const { Op } = require('sequelize');
const { Product, Category } = require('../models');

class SearchService {
  // 搜索商品
  async searchProducts(keyword, page = 1, limit = 10) {
    const offset = (page - 1) * limit;

    const { count, rows } = await Product.findAndCountAll({
      where: {
        [Op.or]: [
          { name: { [Op.like]: `%${keyword}%` } },
          { description: { [Op.like]: `%${keyword}%` } }
        ],
        status: 1
      },
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name']
        }
      ],
      order: [['sales', 'DESC'], ['created_at', 'DESC']],
      limit,
      offset
    });

    return {
      products: rows,
      total: count,
      page,
      limit,
      totalPages: Math.ceil(count / limit),
      keyword
    };
  }

  // 搜索建议
  async getSearchSuggestions(keyword) {
    if (!keyword || keyword.length < 2) {
      return [];
    }

    const products = await Product.findAll({
      where: {
        name: {
          [Op.like]: `%${keyword}%`
        },
        status: 1
      },
      attributes: ['name'],
      limit: 10,
      order: [['sales', 'DESC']]
    });

    return products.map(product => product.name);
  }
}

module.exports = new SearchService(); 