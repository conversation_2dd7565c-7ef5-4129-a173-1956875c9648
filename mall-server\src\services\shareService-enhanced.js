// 智能分享服务 - 优化完善版
const { Op, sequelize } = require('sequelize');
const { ShareRecord, Product, User, Category } = require('../models');

class EnhancedShareService {
  
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 10 * 60 * 1000; // 10分钟缓存
    this.shareTemplates = this.initShareTemplates();
    this.platformConfig = this.initPlatformConfig();
  }

  // 初始化分享模板
  initShareTemplates() {
    return {
      product: {
        wechat: {
          title: '【心洁茶叶】{productName}',
          desc: '优质好茶，现价￥{price}{discount}，{feature}。点击查看详情！',
          image: '{productImage}'
        },
        moments: {
          title: '发现了一款好茶：{productName}',
          desc: '￥{price}{discount}，{sales}人已购买，{rating}分好评。{feature}',
          image: '{productImage}'
        },
        qq: {
          title: '{productName} - 心洁茶叶',
          desc: '价格：￥{price}{discount}\n销量：{sales}件\n评分：{rating}分\n{feature}',
          image: '{productImage}'
        }
      },
      category: {
        wechat: {
          title: '【心洁茶叶】{categoryName}专区',
          desc: '精选{categoryName}，品质保证，多款选择。快来挑选您喜欢的茶叶！',
          image: '/static/images/category-share.png'
        }
      },
      page: {
        wechat: {
          title: '心洁茶叶商城',
          desc: '优质茶叶，品味生活。新用户注册享受专属优惠！',
          image: '/static/images/logo-share.png'
        }
      }
    };
  }

  // 初始化平台配置
  initPlatformConfig() {
    return {
      wechat: { name: '微信好友', icon: 'wechat', color: '#07C160' },
      moments: { name: '朋友圈', icon: 'moments', color: '#07C160' },
      qq: { name: 'QQ好友', icon: 'qq', color: '#12B7F5' },
      weibo: { name: '微博', icon: 'weibo', color: '#E6162D' },
      copy_link: { name: '复制链接', icon: 'link', color: '#666666' }
    };
  }

  // 智能生成分享内容
  async smartGenerateShareContent(shareType, targetId, platform, userId = null) {
    try {
      const cacheKey = `share_content_${shareType}_${targetId}_${platform}`;
      const cached = this.cache.get(cacheKey);
      
      if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }

      let shareContent = {};

      switch (shareType) {
        case 'product':
          shareContent = await this.generateProductShareContent(targetId, platform, userId);
          break;
        case 'category':
          shareContent = await this.generateCategoryShareContent(targetId, platform);
          break;
        case 'page':
          shareContent = await this.generatePageShareContent(platform);
          break;
        default:
          throw new Error('不支持的分享类型');
      }

      // 缓存结果
      this.cache.set(cacheKey, {
        data: shareContent,
        timestamp: Date.now()
      });

      return shareContent;
    } catch (error) {
      console.error('智能生成分享内容失败:', error);
      throw new Error('生成分享内容失败');
    }
  }

  // 生成商品分享内容
  async generateProductShareContent(productId, platform, userId) {
    const product = await Product.findByPk(productId, {
      attributes: [
        'id', 'name', 'price', 'original_price', 'main_image', 
        'sales', 'rating', 'description', 'tags', 'category_id'
      ],
      include: [{
        model: Category,
        as: 'category',
        attributes: ['name']
      }]
    });

    if (!product) {
      throw new Error('商品不存在');
    }

    // 获取商品特色
    const features = this.extractProductFeatures(product);
    const discount = this.calculateDiscount(product.price, product.original_price);
    
    // 获取模板
    const template = this.shareTemplates.product[platform] || this.shareTemplates.product.wechat;
    
    // 替换模板变量
    const shareContent = {
      shareType: 'product',
      targetId: productId,
      platform,
      shareTitle: this.replaceTemplate(template.title, {
        productName: product.name,
        categoryName: product.category?.name
      }),
      shareDesc: this.replaceTemplate(template.desc, {
        productName: product.name,
        price: product.price,
        discount: discount > 0 ? `，原价￥${product.original_price}` : '',
        sales: product.sales,
        rating: product.rating,
        feature: features.join('，') || '品质保证'
      }),
      shareImage: product.main_image,
      shareUrl: this.generateShareUrl('product', productId, userId),
      extraData: {
        productName: product.name,
        productPrice: product.price,
        productImage: product.main_image,
        categoryName: product.category?.name,
        features,
        discount
      }
    };

    return shareContent;
  }

  // 生成分类分享内容
  async generateCategoryShareContent(categoryId, platform) {
    const category = await Category.findByPk(categoryId, {
      attributes: ['id', 'name', 'image']
    });

    if (!category) {
      throw new Error('分类不存在');
    }

    const template = this.shareTemplates.category[platform] || this.shareTemplates.category.wechat;
    
    return {
      shareType: 'category',
      targetId: categoryId,
      platform,
      shareTitle: this.replaceTemplate(template.title, {
        categoryName: category.name
      }),
      shareDesc: this.replaceTemplate(template.desc, {
        categoryName: category.name
      }),
      shareImage: category.image || '/static/images/category-default.png',
      shareUrl: this.generateShareUrl('category', categoryId),
      extraData: {
        categoryName: category.name
      }
    };
  }

  // 生成页面分享内容
  async generatePageShareContent(platform) {
    const template = this.shareTemplates.page[platform] || this.shareTemplates.page.wechat;
    
    return {
      shareType: 'page',
      targetId: null,
      platform,
      shareTitle: template.title,
      shareDesc: template.desc,
      shareImage: template.image,
      shareUrl: this.generateShareUrl('page'),
      extraData: {
        siteName: '心洁茶叶商城'
      }
    };
  }

  // 提取商品特色
  extractProductFeatures(product) {
    const features = [];
    
    // 从标签提取
    if (product.tags) {
      try {
        const tags = typeof product.tags === 'string' ? JSON.parse(product.tags) : product.tags;
        features.push(...tags.slice(0, 3));
      } catch (e) {}
    }

    // 从描述提取关键词
    if (product.description) {
      const keywords = ['有机', '无农药', '手工', '传统', '新鲜', '香醇', '回甘', '耐泡', '高山', '野生'];
      keywords.forEach(keyword => {
        if (product.description.includes(keyword) && !features.includes(keyword)) {
          features.push(keyword);
        }
      });
    }

    // 根据销量和评分添加特色
    if (product.sales > 1000) features.push('热销');
    if (product.rating >= 4.8) features.push('高评分');

    return features.slice(0, 3);
  }

  // 计算折扣
  calculateDiscount(price, originalPrice) {
    if (!originalPrice || originalPrice <= price) return 0;
    return Math.round((1 - price / originalPrice) * 100);
  }

  // 替换模板变量
  replaceTemplate(template, variables) {
    let result = template;
    Object.keys(variables).forEach(key => {
      const regex = new RegExp(`{${key}}`, 'g');
      result = result.replace(regex, variables[key] || '');
    });
    return result;
  }

  // 生成分享链接
  generateShareUrl(type, targetId = null, userId = null) {
    const baseUrl = process.env.FRONTEND_URL || 'https://your-domain.com';
    let path = '';

    switch (type) {
      case 'product':
        path = `/pages/product/detail?id=${targetId}`;
        break;
      case 'category':
        path = `/pages/category/list?id=${targetId}`;
        break;
      case 'page':
      default:
        path = '/pages/index/index';
        break;
    }

    // 添加分享追踪参数
    const params = new URLSearchParams();
    if (userId) params.append('share_from', userId);
    params.append('share_time', Date.now());
    
    const queryString = params.toString();
    return `${baseUrl}${path}${queryString ? '&' + queryString : ''}`;
  }

  // 智能记录分享
  async smartRecordShare(userId, shareData) {
    try {
      const {
        shareType,
        targetId,
        sharePlatform,
        shareTitle,
        shareDesc,
        shareImage,
        shareUrl,
        extraData = {}
      } = shareData;

      // 检查是否重复分享（同一用户同一商品同一平台1小时内）
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      const existingShare = await ShareRecord.findOne({
        where: {
          user_id: userId,
          share_type: shareType,
          target_id: targetId,
          share_platform: sharePlatform,
          share_time: { [Op.gte]: oneHourAgo }
        }
      });

      if (existingShare) {
        return { 
          message: '分享记录已存在', 
          shareRecord: existingShare,
          isDuplicate: true
        };
      }

      // 创建分享记录
      const shareRecord = await ShareRecord.create({
        user_id: userId,
        share_type: shareType,
        target_id: targetId,
        share_platform: sharePlatform,
        share_title: shareTitle,
        share_desc: shareDesc,
        share_image: shareImage,
        share_url: shareUrl,
        extra_data: {
          ...extraData,
          userAgent: extraData.userAgent,
          deviceInfo: extraData.deviceInfo,
          location: extraData.location
        }
      });

      // 异步更新用户分享统计
      setImmediate(() => {
        this.updateUserShareStats(userId, shareType, sharePlatform);
      });

      return { 
        message: '分享记录成功', 
        shareRecord,
        isDuplicate: false
      };

    } catch (error) {
      console.error('智能记录分享失败:', error);
      throw new Error('记录分享失败');
    }
  }

  // 更新用户分享统计
  async updateUserShareStats(userId, shareType, platform) {
    try {
      // 这里可以实现用户分享行为分析
      console.log(`用户 ${userId} 分享了 ${shareType} 到 ${platform}`);
    } catch (error) {
      console.error('更新用户分享统计失败:', error);
    }
  }

  // 获取分享效果分析
  async getShareEffectAnalysis(userId = null, days = 30) {
    try {
      const whereCondition = {};
      if (userId) whereCondition.user_id = userId;

      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      whereCondition.share_time = { [Op.gte]: startDate };

      // 总体效果统计
      const [overallStats] = await sequelize.query(`
        SELECT 
          COUNT(*) as total_shares,
          SUM(click_count) as total_clicks,
          SUM(conversion_count) as total_conversions,
          AVG(click_count) as avg_clicks_per_share,
          CASE 
            WHEN SUM(click_count) > 0 THEN (SUM(conversion_count) / SUM(click_count) * 100)
            ELSE 0 
          END as conversion_rate
        FROM share_records
        WHERE ${userId ? 'user_id = :userId AND' : ''} 
        share_time >= :startDate
      `, {
        replacements: { userId, startDate },
        type: sequelize.QueryTypes.SELECT
      });

      // 平台效果对比
      const [platformStats] = await sequelize.query(`
        SELECT 
          share_platform,
          COUNT(*) as share_count,
          SUM(click_count) as total_clicks,
          SUM(conversion_count) as total_conversions,
          AVG(click_count) as avg_clicks,
          CASE 
            WHEN SUM(click_count) > 0 THEN (SUM(conversion_count) / SUM(click_count) * 100)
            ELSE 0 
          END as conversion_rate
        FROM share_records
        WHERE ${userId ? 'user_id = :userId AND' : ''} 
        share_time >= :startDate
        GROUP BY share_platform
        ORDER BY total_clicks DESC
      `, {
        replacements: { userId, startDate },
        type: sequelize.QueryTypes.SELECT
      });

      // 内容类型效果
      const [contentStats] = await sequelize.query(`
        SELECT 
          share_type,
          COUNT(*) as share_count,
          SUM(click_count) as total_clicks,
          AVG(click_count) as avg_clicks
        FROM share_records
        WHERE ${userId ? 'user_id = :userId AND' : ''} 
        share_time >= :startDate
        GROUP BY share_type
        ORDER BY avg_clicks DESC
      `, {
        replacements: { userId, startDate },
        type: sequelize.QueryTypes.SELECT
      });

      // 时间趋势
      const [timeStats] = await sequelize.query(`
        SELECT 
          DATE(share_time) as date,
          COUNT(*) as share_count,
          SUM(click_count) as click_count
        FROM share_records
        WHERE ${userId ? 'user_id = :userId AND' : ''} 
        share_time >= :startDate
        GROUP BY DATE(share_time)
        ORDER BY date ASC
      `, {
        replacements: { userId, startDate },
        type: sequelize.QueryTypes.SELECT
      });

      return {
        overall: overallStats,
        byPlatform: platformStats,
        byContent: contentStats,
        timetrend: timeStats,
        insights: this.generateShareInsights(overallStats, platformStats)
      };

    } catch (error) {
      console.error('获取分享效果分析失败:', error);
      throw new Error('获取分享效果分析失败');
    }
  }

  // 生成分享洞察
  generateShareInsights(overallStats, platformStats) {
    const insights = [];

    // 总体表现洞察
    if (overallStats.total_shares > 0) {
      const avgClicks = parseFloat(overallStats.avg_clicks_per_share);
      if (avgClicks > 5) {
        insights.push('您的分享内容很受欢迎，平均点击率较高');
      } else if (avgClicks > 2) {
        insights.push('分享效果良好，继续保持');
      } else {
        insights.push('可以尝试优化分享内容，提高吸引力');
      }

      const conversionRate = parseFloat(overallStats.conversion_rate);
      if (conversionRate > 10) {
        insights.push('分享转化率很高，内容质量优秀');
      } else if (conversionRate > 5) {
        insights.push('分享有一定转化效果');
      }
    }

    // 平台表现洞察
    if (platformStats.length > 1) {
      const bestPlatform = platformStats.reduce((best, current) => 
        current.avg_clicks > best.avg_clicks ? current : best
      );
      insights.push(`${this.platformConfig[bestPlatform.share_platform]?.name || bestPlatform.share_platform}是您的最佳分享平台`);
    }

    return insights;
  }

  // 生成分享海报
  async generateSharePoster(shareType, targetId, options = {}) {
    try {
      const {
        template = 'default',
        customText = '',
        qrCodeSize = 120,
        watermark = true
      } = options;

      let posterData = {
        template,
        qrCodeSize,
        watermark,
        brandInfo: {
          name: '心洁茶叶',
          logo: '/static/images/logo.png',
          slogan: '品质茶叶，品味生活'
        }
      };

      if (shareType === 'product') {
        const product = await Product.findByPk(targetId, {
          attributes: ['id', 'name', 'price', 'original_price', 'main_image', 'sales', 'rating'],
          include: [{ model: Category, as: 'category', attributes: ['name'] }]
        });

        if (!product) throw new Error('商品不存在');

        posterData = {
          ...posterData,
          type: 'product',
          product: {
            name: product.name,
            price: `￥${product.price}`,
            originalPrice: product.original_price > product.price ? `￥${product.original_price}` : null,
            image: product.main_image,
            sales: `已售${product.sales}件`,
            rating: `${product.rating}分好评`,
            category: product.category?.name
          },
          qrCode: this.generateShareUrl('product', targetId),
          customText: customText || `发现好茶：${product.name}`
        };
      }

      return posterData;
    } catch (error) {
      console.error('生成分享海报失败:', error);
      throw new Error('生成分享海报失败');
    }
  }

  // 获取分享排行榜
  async getShareRanking(type = 'product', limit = 10, days = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const [ranking] = await sequelize.query(`
        SELECT 
          sr.target_id,
          COUNT(*) as share_count,
          SUM(sr.click_count) as total_clicks,
          SUM(sr.conversion_count) as total_conversions,
          p.name, p.price, p.main_image, p.sales, p.rating
        FROM share_records sr
        JOIN products p ON sr.target_id = p.id
        WHERE sr.share_type = :type
        AND sr.share_time >= :startDate
        AND p.status = 1
        GROUP BY sr.target_id
        ORDER BY share_count DESC, total_clicks DESC
        LIMIT :limit
      `, {
        replacements: { type, startDate, limit },
        type: sequelize.QueryTypes.SELECT
      });

      return ranking;
    } catch (error) {
      console.error('获取分享排行榜失败:', error);
      return [];
    }
  }

  // 清理缓存
  clearCache(pattern = null) {
    if (pattern) {
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }
}

module.exports = new EnhancedShareService();
