// 分享功能服务
const { Op, sequelize } = require('sequelize');
const { ShareRecord, Product, User } = require('../models');

class ShareService {

  // 创建分享记录
  async createShareRecord(userId, shareData) {
    try {
      const {
        shareType,
        targetId,
        sharePlatform,
        shareTitle,
        shareDesc,
        shareImage,
        shareUrl,
        extraData
      } = shareData;

      // 验证分享类型和目标
      if (shareType === 'product' && targetId) {
        const product = await Product.findByPk(targetId);
        if (!product) {
          throw new Error('分享的商品不存在');
        }
      }

      const shareRecord = await ShareRecord.create({
        user_id: userId,
        share_type: shareType,
        target_id: targetId,
        share_platform: sharePlatform,
        share_title: shareTitle,
        share_desc: shareDesc,
        share_image: shareImage,
        share_url: shareUrl,
        extra_data: extraData
      });

      return { message: '分享记录创建成功', shareRecord };
    } catch (error) {
      console.error('创建分享记录失败:', error);
      throw new Error('创建分享记录失败');
    }
  }

  // 生成商品分享数据
  async generateProductShareData(productId, userId = null) {
    try {
      const product = await Product.findByPk(productId, {
        attributes: ['id', 'name', 'price', 'original_price', 'main_image', 'description', 'sales']
      });

      if (!product) {
        throw new Error('商品不存在');
      }

      // 生成分享链接
      const shareUrl = `${process.env.FRONTEND_URL || 'https://your-domain.com'}/pages/product/detail?id=${productId}`;
      
      // 生成分享标题和描述
      const shareTitle = `【心洁茶叶】${product.name}`;
      const shareDesc = `优质好茶，现价￥${product.price}${product.original_price > product.price ? `，原价￥${product.original_price}` : ''}，已售${product.sales}件。`;
      
      const shareData = {
        shareType: 'product',
        targetId: productId,
        shareTitle,
        shareDesc,
        shareImage: product.main_image,
        shareUrl,
        extraData: {
          productName: product.name,
          productPrice: product.price,
          productImage: product.main_image
        }
      };

      return shareData;
    } catch (error) {
      console.error('生成商品分享数据失败:', error);
      throw new Error('生成商品分享数据失败');
    }
  }

  // 记录分享行为
  async recordShare(userId, shareType, targetId, sharePlatform, extraData = {}) {
    try {
      let shareData = {};

      if (shareType === 'product') {
        shareData = await this.generateProductShareData(targetId, userId);
      } else if (shareType === 'page') {
        shareData = {
          shareType: 'page',
          targetId: null,
          shareTitle: '心洁茶叶商城',
          shareDesc: '优质茶叶，品味生活',
          shareImage: '/static/images/logo.png',
          shareUrl: process.env.FRONTEND_URL || 'https://your-domain.com'
        };
      }

      const shareRecord = await this.createShareRecord(userId, {
        ...shareData,
        sharePlatform,
        extraData
      });

      return shareRecord;
    } catch (error) {
      console.error('记录分享行为失败:', error);
      throw new Error('记录分享行为失败');
    }
  }

  // 记录分享点击
  async recordShareClick(shareId) {
    try {
      const shareRecord = await ShareRecord.findByPk(shareId);
      if (!shareRecord) {
        throw new Error('分享记录不存在');
      }

      await shareRecord.increment('click_count');
      return { message: '点击记录成功' };
    } catch (error) {
      console.error('记录分享点击失败:', error);
      // 不抛出错误，避免影响用户体验
      return null;
    }
  }

  // 记录分享转化
  async recordShareConversion(shareId) {
    try {
      const shareRecord = await ShareRecord.findByPk(shareId);
      if (!shareRecord) {
        throw new Error('分享记录不存在');
      }

      await shareRecord.increment('conversion_count');
      return { message: '转化记录成功' };
    } catch (error) {
      console.error('记录分享转化失败:', error);
      return null;
    }
  }

  // 获取用户分享记录
  async getUserShareRecords(userId, page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;

      const { count, rows } = await ShareRecord.findAndCountAll({
        where: { user_id: userId },
        include: [{
          model: Product, as: 'product',
          attributes: ['id', 'name', 'price', 'main_image', 'status'],
          required: false
        }],
        order: [['share_time', 'DESC']],
        limit: parseInt(limit), offset
      });

      return {
        total: count, records: rows, page: parseInt(page),
        limit: parseInt(limit), totalPages: Math.ceil(count / limit)
      };
    } catch (error) {
      console.error('获取用户分享记录失败:', error);
      throw new Error('获取用户分享记录失败');
    }
  }

  // 获取分享统计
  async getShareStats(userId = null, days = 30) {
    try {
      const whereCondition = {};
      if (userId) {
        whereCondition.user_id = userId;
      }

      // 时间范围
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      whereCondition.share_time = { [Op.gte]: startDate };

      // 总体统计
      const totalStats = await ShareRecord.findOne({
        where: whereCondition,
        attributes: [
          [sequelize.fn('COUNT', sequelize.col('id')), 'total_shares'],
          [sequelize.fn('SUM', sequelize.col('click_count')), 'total_clicks'],
          [sequelize.fn('SUM', sequelize.col('conversion_count')), 'total_conversions']
        ],
        raw: true
      });

      // 按平台统计
      const platformStats = await ShareRecord.findAll({
        where: whereCondition,
        attributes: [
          'share_platform',
          [sequelize.fn('COUNT', sequelize.col('id')), 'share_count'],
          [sequelize.fn('SUM', sequelize.col('click_count')), 'click_count'],
          [sequelize.fn('SUM', sequelize.col('conversion_count')), 'conversion_count']
        ],
        group: ['share_platform'], raw: true
      });

      // 按类型统计
      const typeStats = await ShareRecord.findAll({
        where: whereCondition,
        attributes: [
          'share_type',
          [sequelize.fn('COUNT', sequelize.col('id')), 'share_count'],
          [sequelize.fn('SUM', sequelize.col('click_count')), 'click_count']
        ],
        group: ['share_type'], raw: true
      });

      // 每日趋势
      const dailyStats = await ShareRecord.findAll({
        where: whereCondition,
        attributes: [
          [sequelize.fn('DATE', sequelize.col('share_time')), 'date'],
          [sequelize.fn('COUNT', sequelize.col('id')), 'share_count'],
          [sequelize.fn('SUM', sequelize.col('click_count')), 'click_count']
        ],
        group: [sequelize.fn('DATE', sequelize.col('share_time'))],
        order: [[sequelize.fn('DATE', sequelize.col('share_time')), 'ASC']],
        raw: true
      });

      return {
        totalStats: {
          totalShares: parseInt(totalStats.total_shares) || 0,
          totalClicks: parseInt(totalStats.total_clicks) || 0,
          totalConversions: parseInt(totalStats.total_conversions) || 0,
          clickRate: totalStats.total_shares > 0 
            ? ((totalStats.total_clicks / totalStats.total_shares) * 100).toFixed(2)
            : 0,
          conversionRate: totalStats.total_clicks > 0
            ? ((totalStats.total_conversions / totalStats.total_clicks) * 100).toFixed(2)
            : 0
        },
        platformStats, typeStats, dailyStats
      };
    } catch (error) {
      console.error('获取分享统计失败:', error);
      throw new Error('获取分享统计失败');
    }
  }

  // 获取热门分享商品
  async getPopularSharedProducts(limit = 10, days = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const popularProducts = await ShareRecord.findAll({
        where: {
          share_type: 'product',
          target_id: { [Op.ne]: null },
          share_time: { [Op.gte]: startDate }
        },
        attributes: [
          'target_id',
          [sequelize.fn('COUNT', sequelize.col('ShareRecord.id')), 'share_count'],
          [sequelize.fn('SUM', sequelize.col('click_count')), 'total_clicks']
        ],
        include: [{
          model: Product, as: 'product',
          attributes: ['id', 'name', 'price', 'main_image', 'sales', 'rating'],
          where: { status: 1 }
        }],
        group: ['target_id'],
        order: [[sequelize.fn('COUNT', sequelize.col('ShareRecord.id')), 'DESC']],
        limit: parseInt(limit), raw: false
      });

      return popularProducts.map(item => ({
        ...item.product.dataValues,
        shareCount: parseInt(item.dataValues.share_count),
        totalClicks: parseInt(item.dataValues.total_clicks) || 0
      }));
    } catch (error) {
      console.error('获取热门分享商品失败:', error);
      throw new Error('获取热门分享商品失败');
    }
  }

  // 生成分享海报数据
  async generateSharePoster(shareType, targetId, userId = null) {
    try {
      let posterData = {};

      if (shareType === 'product') {
        const product = await Product.findByPk(targetId, {
          attributes: ['id', 'name', 'price', 'original_price', 'main_image', 'sales', 'rating']
        });

        if (!product) {
          throw new Error('商品不存在');
        }

        posterData = {
          type: 'product',
          title: product.name,
          price: `￥${product.price}`,
          originalPrice: product.original_price > product.price ? `￥${product.original_price}` : null,
          image: product.main_image,
          sales: `已售${product.sales}件`,
          rating: `${product.rating}分`,
          qrCode: `${process.env.FRONTEND_URL}/pages/product/detail?id=${targetId}`,
          brandLogo: '/static/images/logo.png',
          brandName: '心洁茶叶'
        };
      }

      return posterData;
    } catch (error) {
      console.error('生成分享海报数据失败:', error);
      throw new Error('生成分享海报数据失败');
    }
  }
}

module.exports = new ShareService();
