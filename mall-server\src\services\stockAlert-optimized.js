// 优化版库存预警服务 - 高效简洁版
const { Op, sequelize } = require('sequelize');
const { Product, StockAlert } = require('../models');

class OptimizedStockAlertService {
  
  constructor() {
    // 预警配置
    this.config = {
      thresholds: {
        critical: 5,    // 严重缺货
        low: 10,        // 低库存
        overstock: 1000 // 库存过多
      },
      batchSize: 100,   // 批处理大小
      enableCache: true
    };
    
    this.alertQueue = [];
  }

  // 批量检查库存 - 优化版
  async checkAllProductsStock() {
    try {
      console.log('🔍 开始批量检查库存...');
      
      // 使用单个SQL查询获取所有需要预警的商品
      const [products] = await sequelize.query(`
        SELECT 
          p.id, p.name, p.stock, p.sales, p.main_image,
          CASE 
            WHEN p.stock = 0 THEN 'out_of_stock'
            WHEN p.stock <= :critical THEN 'critical_low'
            WHEN p.stock <= :low THEN 'low_stock'
            WHEN p.stock >= :overstock THEN 'overstock'
            ELSE 'normal'
          END as alert_type,
          CASE 
            WHEN p.stock = 0 THEN 'critical'
            WHEN p.stock <= :critical THEN 'critical'
            WHEN p.stock <= :low THEN 'high'
            WHEN p.stock >= :overstock THEN 'medium'
            ELSE 'normal'
          END as alert_level
        FROM products p
        WHERE p.status = 1 
        AND (p.stock = 0 OR p.stock <= :low OR p.stock >= :overstock)
        AND NOT EXISTS (
          SELECT 1 FROM stock_alerts sa 
          WHERE sa.product_id = p.id 
          AND sa.status = 'active'
          AND sa.alert_type = CASE 
            WHEN p.stock = 0 THEN 'out_of_stock'
            WHEN p.stock <= :critical THEN 'low_stock'
            WHEN p.stock <= :low THEN 'low_stock'
            WHEN p.stock >= :overstock THEN 'overstock'
          END
        )
      `, {
        replacements: this.config.thresholds,
        type: sequelize.QueryTypes.SELECT
      });

      if (products.length === 0) {
        console.log('✅ 无需要预警的商品');
        return 0;
      }

      // 批量创建预警
      const alerts = products.map(product => ({
        product_id: product.id,
        alert_type: product.alert_type === 'critical_low' ? 'low_stock' : product.alert_type,
        current_stock: product.stock,
        threshold_value: this.getThresholdValue(product.alert_type),
        alert_level: product.alert_level,
        status: 'active',
        message: this.generateAlertMessage(product),
        created_at: new Date(),
        updated_at: new Date()
      }));

      await StockAlert.bulkCreate(alerts, { ignoreDuplicates: true });
      
      console.log(`✅ 批量检查完成，生成 ${alerts.length} 个预警`);
      return alerts.length;
      
    } catch (error) {
      console.error('❌ 批量检查库存失败:', error);
      throw error;
    }
  }

  // 获取阈值
  getThresholdValue(alertType) {
    switch (alertType) {
      case 'out_of_stock': return 0;
      case 'critical_low':
      case 'low_stock': return this.config.thresholds.low;
      case 'overstock': return this.config.thresholds.overstock;
      default: return 0;
    }
  }

  // 生成预警消息
  generateAlertMessage(product) {
    const messages = {
      'out_of_stock': `商品"${product.name}"已缺货`,
      'critical_low': `商品"${product.name}"库存严重不足(${product.stock})`,
      'low_stock': `商品"${product.name}"库存不足(${product.stock})`,
      'overstock': `商品"${product.name}"库存过多(${product.stock})`
    };
    return messages[product.alert_type] || `商品"${product.name}"库存异常`;
  }

  // 获取预警统计 - 优化版
  async getAlertStats() {
    const [stats] = await sequelize.query(`
      SELECT 
        alert_type,
        alert_level,
        COUNT(*) as count,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_count
      FROM stock_alerts
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
      GROUP BY alert_type, alert_level
      WITH ROLLUP
    `, { type: sequelize.QueryTypes.SELECT });

    return this.formatStatsData(stats);
  }

  // 格式化统计数据
  formatStatsData(stats) {
    const result = {
      total: 0,
      active: 0,
      byType: {},
      byLevel: {}
    };

    stats.forEach(stat => {
      if (!stat.alert_type && !stat.alert_level) {
        // 总计行
        result.total = parseInt(stat.count);
        result.active = parseInt(stat.active_count);
      } else if (stat.alert_type && !stat.alert_level) {
        // 按类型汇总
        result.byType[stat.alert_type] = parseInt(stat.count);
      } else if (!stat.alert_type && stat.alert_level) {
        // 按级别汇总
        result.byLevel[stat.alert_level] = parseInt(stat.count);
      }
    });

    return result;
  }

  // 批量解决预警 - 新增
  async batchResolveAlerts(alertIds, adminUserId, note = '') {
    try {
      const result = await StockAlert.update({
        status: 'resolved',
        resolved_at: new Date(),
        resolved_by: adminUserId,
        message: sequelize.literal(`CONCAT(message, ' | 批量处理: ${note}')`)
      }, {
        where: {
          id: { [Op.in]: alertIds },
          status: 'active'
        }
      });

      console.log(`✅ 批量解决 ${result[0]} 个预警`);
      return result[0];
    } catch (error) {
      console.error('❌ 批量解决预警失败:', error);
      throw error;
    }
  }

  // 智能预警建议 - 新增
  async getSmartRecommendations() {
    const [recommendations] = await sequelize.query(`
      SELECT 
        p.id, p.name, p.stock, p.sales,
        ROUND(p.sales / 30, 2) as daily_sales,
        ROUND(p.stock / (p.sales / 30), 1) as days_remaining,
        CASE 
          WHEN p.stock / (p.sales / 30) < 7 THEN 'urgent'
          WHEN p.stock / (p.sales / 30) < 14 THEN 'soon'
          ELSE 'normal'
        END as urgency
      FROM products p
      WHERE p.status = 1 
      AND p.sales > 0
      AND p.stock > 0
      AND p.stock / (p.sales / 30) < 30
      ORDER BY days_remaining ASC
      LIMIT 20
    `, { type: sequelize.QueryTypes.SELECT });

    return recommendations.map(item => ({
      ...item,
      recommendation: this.generateRecommendation(item)
    }));
  }

  // 生成补货建议
  generateRecommendation(product) {
    const { days_remaining, daily_sales, urgency } = product;
    
    if (urgency === 'urgent') {
      return `紧急补货！预计${days_remaining}天后缺货，建议补货${Math.ceil(daily_sales * 30)}件`;
    } else if (urgency === 'soon') {
      return `需要补货，预计${days_remaining}天后缺货，建议补货${Math.ceil(daily_sales * 20)}件`;
    }
    return `库存充足，${days_remaining}天后需要补货`;
  }

  // 自动清理过期预警 - 优化版
  async autoCleanup(daysOld = 30) {
    try {
      const result = await sequelize.query(`
        DELETE FROM stock_alerts 
        WHERE status IN ('resolved', 'ignored') 
        AND resolved_at < DATE_SUB(NOW(), INTERVAL :days DAY)
      `, {
        replacements: { days: daysOld },
        type: sequelize.QueryTypes.DELETE
      });

      console.log(`🧹 清理了 ${result[1]} 个过期预警`);
      return result[1];
    } catch (error) {
      console.error('❌ 清理过期预警失败:', error);
      throw error;
    }
  }

  // 预警趋势分析 - 新增
  async getAlertTrends(days = 30) {
    const [trends] = await sequelize.query(`
      SELECT 
        DATE(created_at) as date,
        alert_type,
        alert_level,
        COUNT(*) as count
      FROM stock_alerts
      WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
      GROUP BY DATE(created_at), alert_type, alert_level
      ORDER BY date DESC
    `, {
      replacements: { days },
      type: sequelize.QueryTypes.SELECT
    });

    return trends;
  }

  // 更新配置
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    console.log('📝 库存预警配置已更新:', this.config);
  }

  // 获取配置
  getConfig() {
    return this.config;
  }
}

module.exports = new OptimizedStockAlertService();
