// 库存预警服务
const { Op, sequelize } = require('sequelize');
const { Product, StockAlert, AdminUser } = require('../models');

class StockAlertService {

  // 库存预警配置
  constructor() {
    this.alertThresholds = {
      low_stock: 10,      // 低库存阈值
      critical_stock: 5,  // 严重缺货阈值
      overstock: 1000     // 库存过多阈值
    };
  }

  // 检查所有商品库存并生成预警
  async checkAllProductsStock() {
    try {
      console.log('开始检查商品库存...');
      
      const products = await Product.findAll({
        where: {
          status: 1 // 只检查上架商品
        },
        attributes: ['id', 'name', 'stock', 'sales', 'main_image']
      });

      let alertCount = 0;
      
      for (const product of products) {
        const alerts = await this.checkProductStock(product);
        alertCount += alerts.length;
      }

      console.log(`库存检查完成，生成 ${alertCount} 个预警`);
      return alertCount;
    } catch (error) {
      console.error('检查库存失败:', error);
      throw new Error('检查库存失败');
    }
  }

  // 检查单个商品库存
  async checkProductStock(product) {
    try {
      const alerts = [];
      const currentStock = product.stock;

      // 检查缺货预警
      if (currentStock === 0) {
        alerts.push(await this.createAlert(product, 'out_of_stock', currentStock, 0, 'critical'));
      }
      // 检查低库存预警
      else if (currentStock <= this.alertThresholds.critical_stock) {
        alerts.push(await this.createAlert(product, 'low_stock', currentStock, this.alertThresholds.critical_stock, 'critical'));
      }
      else if (currentStock <= this.alertThresholds.low_stock) {
        alerts.push(await this.createAlert(product, 'low_stock', currentStock, this.alertThresholds.low_stock, 'high'));
      }
      // 检查库存过多预警
      else if (currentStock >= this.alertThresholds.overstock) {
        alerts.push(await this.createAlert(product, 'overstock', currentStock, this.alertThresholds.overstock, 'medium'));
      }

      return alerts;
    } catch (error) {
      console.error(`检查商品 ${product.id} 库存失败:`, error);
      return [];
    }
  }

  // 创建预警记录
  async createAlert(product, alertType, currentStock, threshold, level) {
    try {
      // 检查是否已存在相同的活跃预警
      const existingAlert = await StockAlert.findOne({
        where: {
          product_id: product.id,
          alert_type: alertType,
          status: 'active'
        }
      });

      if (existingAlert) {
        // 更新现有预警
        await existingAlert.update({
          current_stock: currentStock,
          alert_level: level,
          updated_at: new Date()
        });
        return existingAlert;
      }

      // 创建新预警
      const message = this.generateAlertMessage(product, alertType, currentStock, threshold);
      
      const alert = await StockAlert.create({
        product_id: product.id,
        alert_type: alertType,
        current_stock: currentStock,
        threshold_value: threshold,
        alert_level: level,
        message,
        status: 'active'
      });

      console.log(`创建预警: ${product.name} - ${message}`);
      return alert;
    } catch (error) {
      console.error('创建预警失败:', error);
      throw error;
    }
  }

  // 生成预警消息
  generateAlertMessage(product, alertType, currentStock, threshold) {
    switch (alertType) {
      case 'out_of_stock':
        return `商品"${product.name}"已缺货，当前库存：${currentStock}`;
      case 'low_stock':
        return `商品"${product.name}"库存不足，当前库存：${currentStock}，建议补货`;
      case 'overstock':
        return `商品"${product.name}"库存过多，当前库存：${currentStock}，建议促销`;
      default:
        return `商品"${product.name}"库存异常，当前库存：${currentStock}`;
    }
  }

  // 获取预警列表
  async getAlerts(filters = {}) {
    try {
      const {
        status = 'active',
        alertType,
        alertLevel,
        page = 1,
        limit = 20
      } = filters;

      const whereCondition = { status };
      
      if (alertType) {
        whereCondition.alert_type = alertType;
      }
      
      if (alertLevel) {
        whereCondition.alert_level = alertLevel;
      }

      const offset = (page - 1) * limit;

      const { count, rows } = await StockAlert.findAndCountAll({
        where: whereCondition,
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['id', 'name', 'stock', 'price', 'main_image', 'sales']
          }
        ],
        order: [
          ['alert_level', 'DESC'], // 按预警级别排序
          ['created_at', 'DESC']
        ],
        limit: parseInt(limit),
        offset
      });

      return {
        total: count,
        alerts: rows,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit)
      };
    } catch (error) {
      console.error('获取预警列表失败:', error);
      throw new Error('获取预警列表失败');
    }
  }

  // 解决预警
  async resolveAlert(alertId, adminUserId, note = '') {
    try {
      const alert = await StockAlert.findByPk(alertId);
      
      if (!alert) {
        throw new Error('预警记录不存在');
      }

      await alert.update({
        status: 'resolved',
        resolved_at: new Date(),
        resolved_by: adminUserId,
        message: alert.message + (note ? ` | 处理说明: ${note}` : '')
      });

      console.log(`预警 ${alertId} 已解决`);
      return alert;
    } catch (error) {
      console.error('解决预警失败:', error);
      throw new Error('解决预警失败');
    }
  }

  // 忽略预警
  async ignoreAlert(alertId, adminUserId, note = '') {
    try {
      const alert = await StockAlert.findByPk(alertId);
      
      if (!alert) {
        throw new Error('预警记录不存在');
      }

      await alert.update({
        status: 'ignored',
        resolved_at: new Date(),
        resolved_by: adminUserId,
        message: alert.message + (note ? ` | 忽略原因: ${note}` : '')
      });

      console.log(`预警 ${alertId} 已忽略`);
      return alert;
    } catch (error) {
      console.error('忽略预警失败:', error);
      throw new Error('忽略预警失败');
    }
  }

  // 获取预警统计
  async getAlertStats() {
    try {
      const stats = await StockAlert.findAll({
        where: {
          status: 'active'
        },
        attributes: [
          'alert_type',
          'alert_level',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['alert_type', 'alert_level'],
        raw: true
      });

      // 格式化统计数据
      const formattedStats = {
        total: 0,
        byType: {},
        byLevel: {}
      };

      stats.forEach(stat => {
        const count = parseInt(stat.count);
        formattedStats.total += count;
        
        if (!formattedStats.byType[stat.alert_type]) {
          formattedStats.byType[stat.alert_type] = 0;
        }
        formattedStats.byType[stat.alert_type] += count;
        
        if (!formattedStats.byLevel[stat.alert_level]) {
          formattedStats.byLevel[stat.alert_level] = 0;
        }
        formattedStats.byLevel[stat.alert_level] += count;
      });

      return formattedStats;
    } catch (error) {
      console.error('获取预警统计失败:', error);
      throw new Error('获取预警统计失败');
    }
  }

  // 自动清理已解决的旧预警
  async cleanupOldAlerts(daysOld = 30) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const deletedCount = await StockAlert.destroy({
        where: {
          status: { [Op.in]: ['resolved', 'ignored'] },
          resolved_at: { [Op.lt]: cutoffDate }
        }
      });

      console.log(`清理了 ${deletedCount} 个旧预警记录`);
      return deletedCount;
    } catch (error) {
      console.error('清理旧预警失败:', error);
      throw new Error('清理旧预警失败');
    }
  }

  // 设置预警阈值
  setAlertThresholds(thresholds) {
    this.alertThresholds = { ...this.alertThresholds, ...thresholds };
    console.log('预警阈值已更新:', this.alertThresholds);
  }
}

module.exports = new StockAlertService();
