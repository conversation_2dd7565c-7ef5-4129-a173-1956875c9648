const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { Op } = require('sequelize');
const config = require('../config');
const { User, Address, Order, CartItem } = require('../models');
const RedisUtils = require('../utils/redis');

class UserService {
  // 用户登录
  async login(username, password) {
    const user = await User.findOne({
      where: { 
        username,
        status: 1
      }
    });
    
    if (!user) {
      throw new Error('用户不存在或已被禁用');
    }
    
    const isValidPassword = await bcrypt.compare(password, user.password);
    
    if (!isValidPassword) {
      throw new Error('密码错误');
    }
    
    // 更新最后登录信息
    await user.update({
      last_login_time: new Date(),
      last_login_ip: '127.0.0.1' // 实际应该从请求中获取
    });
    
    // 生成JWT令牌
    const token = jwt.sign(
      { 
        id: user.id, 
        username: user.username, 
        role: 'user' 
      },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );
    
    // 缓存用户信息
    await RedisUtils.set(`user:${user.id}`, {
      id: user.id,
      username: user.username,
      nickname: user.nickname,
      avatar: user.avatar,
      phone: user.phone
    }, 3600);
    
    return {
      token,
      user: {
        id: user.id,
        username: user.username,
        nickname: user.nickname,
        avatar: user.avatar,
        phone: user.phone,
        email: user.email,
        user_level: user.user_level,
        points: user.points,
        balance: user.balance
      }
    };
  }

  // 用户注册
  async register(userData) {
    const existingUser = await User.findOne({
      where: {
        [Op.or]: [
          { username: userData.username },
          { phone: userData.phone }
        ]
      }
    });
    
    if (existingUser) {
      throw new Error('用户名或手机号已存在');
    }
    
    // 加密密码
    const hashedPassword = await bcrypt.hash(userData.password, 10);
    
    const user = await User.create({
      ...userData,
      password: hashedPassword
    });
    
    return {
      id: user.id,
      username: user.username,
      nickname: user.nickname,
      phone: user.phone
    };
  }

  // 用户登出
  async logout(userId) {
    // 清除缓存
    await RedisUtils.del(`user:${userId}`);
    return true;
  }

  // 获取用户信息
  async getUserInfo(userId) {
    try {
      const user = await User.findByPk(userId);

      if (!user) {
        throw new Error('用户不存在');
      }

      // 安全解析用户信息
      let parsedUserInfo = null;
      try {
        if (user.userInfo && typeof user.userInfo === 'string') {
          parsedUserInfo = JSON.parse(user.userInfo);
        } else if (user.userInfo && typeof user.userInfo === 'object') {
          parsedUserInfo = user.userInfo;
        }
      } catch (e) {
        console.warn('解析用户信息失败:', e.message);
        parsedUserInfo = null;
      }

      const userInfo = {
        id: user.id,
        openid: user.openid,
        nickname: user.nickname,
        avatar: user.avatar,
        phone: user.phone,
        email: user.email,
        gender: user.gender,
        userInfo: parsedUserInfo,
        created_at: user.created_at,
        updated_at: user.updated_at
      };

      return userInfo;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw new Error('获取失败');
    }
  }

  // 更新用户信息
  async updateUserInfo(userId, userData) {
    try {
      console.log('用户服务 - 更新用户信息:', { userId, userData });

      const user = await User.findByPk(userId);

      if (!user) {
        throw new Error('用户不存在');
      }

      console.log('找到用户:', { id: user.id, nickname: user.nickname });

      // 准备更新数据
      const updateData = {};
      if (userData.nickname) updateData.nickname = userData.nickname;
      if (userData.avatar) updateData.avatar = userData.avatar;
      if (userData.phone) updateData.phone = userData.phone;
      if (userData.email) updateData.email = userData.email;
      if (userData.gender !== undefined) updateData.gender = userData.gender;

      console.log('准备更新的数据:', updateData);

      if (Object.keys(updateData).length === 0) {
        throw new Error('没有需要更新的数据');
      }

      // 更新用户信息
      const updatedUser = await user.update(updateData);

      console.log('更新后的用户:', {
        id: updatedUser.id,
        nickname: updatedUser.nickname,
        phone: updatedUser.phone,
        email: updatedUser.email
      });

      return {
        id: updatedUser.id,
        openid: updatedUser.openid,
        nickname: updatedUser.nickname,
        avatar: updatedUser.avatar,
        phone: updatedUser.phone,
        email: updatedUser.email,
        gender: updatedUser.gender
      };
    } catch (error) {
      console.error('更新用户信息失败:', error);
      throw new Error('更新失败');
    }
  }

  // 修改密码
  async changePassword(userId, oldPassword, newPassword) {
    const user = await User.findByPk(userId);
    
    if (!user) {
      throw new Error('用户不存在');
    }
    
    const isValidPassword = await bcrypt.compare(oldPassword, user.password);
    
    if (!isValidPassword) {
      throw new Error('原密码错误');
    }
    
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    
    await user.update({ password: hashedPassword });
    
    return true;
  }

  // 获取用户地址列表
  async getUserAddresses(userId) {
    return await Address.findAll({
      where: { user_id: userId },
      order: [['is_default', 'DESC'], ['created_at', 'DESC']]
    });
  }

  // 获取用户订单列表
  async getUserOrders(userId, page = 1, limit = 10) {
    const offset = (page - 1) * limit;
    
    const { count, rows } = await Order.findAndCountAll({
      where: { user_id: userId },
      include: [
        {
          model: require('../models').OrderItem,
          as: 'orderItems',
          include: [
            {
              model: require('../models').Product,
              as: 'product'
            }
          ]
        }
      ],
      order: [['created_at', 'DESC']],
      limit,
      offset
    });
    
    return {
      orders: rows,
      total: count,
      page,
      limit,
      totalPages: Math.ceil(count / limit)
    };
  }

  // 获取用户购物车
  async getUserCart(userId) {
    return await CartItem.findAll({
      where: { user_id: userId },
      include: [
        {
          model: require('../models').Product,
          as: 'product'
        }
      ],
      order: [['created_at', 'DESC']]
    });
  }
}

module.exports = new UserService(); 