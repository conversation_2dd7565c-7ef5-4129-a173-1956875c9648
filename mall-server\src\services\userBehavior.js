// 用户行为分析服务
const { Op, sequelize } = require('sequelize');
const { UserBehavior, User, Product, Order } = require('../models');

class UserBehaviorService {

  // 记录用户行为
  async recordBehavior(behaviorData) {
    try {
      const {
        userId,
        openid,
        behaviorType,
        targetType,
        targetId,
        pagePath,
        searchKeyword,
        sessionId,
        ipAddress,
        userAgent,
        extraData
      } = behaviorData;

      const behavior = await UserBehavior.create({
        user_id: userId,
        openid: openid,
        behavior_type: behaviorType,
        target_type: targetType,
        target_id: targetId,
        page_path: pagePath,
        search_keyword: searchKeyword,
        session_id: sessionId,
        ip_address: ipAddress,
        user_agent: userAgent,
        extra_data: extraData
      });

      return behavior;
    } catch (error) {
      console.error('记录用户行为失败:', error);
      // 不抛出错误，避免影响主业务流程
      return null;
    }
  }

  // 批量记录用户行为
  async recordBehaviors(behaviorsData) {
    try {
      const behaviors = await UserBehavior.bulkCreate(behaviorsData, {
        ignoreDuplicates: true
      });
      return behaviors;
    } catch (error) {
      console.error('批量记录用户行为失败:', error);
      return [];
    }
  }

  // 获取用户行为分析
  async getUserBehaviorAnalysis(startDate, endDate, filters = {}) {
    try {
      const whereCondition = {
        created_at: {
          [Op.between]: [startDate, endDate]
        }
      };

      if (filters.userId) {
        whereCondition.user_id = filters.userId;
      }

      if (filters.behaviorType) {
        whereCondition.behavior_type = filters.behaviorType;
      }

      // 行为类型统计
      const behaviorTypeStats = await UserBehavior.findAll({
        where: whereCondition,
        attributes: [
          'behavior_type',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
          [sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('user_id'))), 'unique_users']
        ],
        group: ['behavior_type'],
        order: [[sequelize.fn('COUNT', sequelize.col('id')), 'DESC']],
        raw: true
      });

      // 页面访问统计
      const pageViewStats = await UserBehavior.findAll({
        where: {
          ...whereCondition,
          behavior_type: 'view',
          page_path: { [Op.ne]: null }
        },
        attributes: [
          'page_path',
          [sequelize.fn('COUNT', sequelize.col('id')), 'view_count'],
          [sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('user_id'))), 'unique_visitors']
        ],
        group: ['page_path'],
        order: [[sequelize.fn('COUNT', sequelize.col('id')), 'DESC']],
        limit: 20,
        raw: true
      });

      // 热门搜索词
      const searchKeywordStats = await UserBehavior.findAll({
        where: {
          ...whereCondition,
          behavior_type: 'search',
          search_keyword: { [Op.ne]: null }
        },
        attributes: [
          'search_keyword',
          [sequelize.fn('COUNT', sequelize.col('id')), 'search_count']
        ],
        group: ['search_keyword'],
        order: [[sequelize.fn('COUNT', sequelize.col('id')), 'DESC']],
        limit: 20,
        raw: true
      });

      // 用户活跃度趋势
      const activityTrend = await UserBehavior.findAll({
        where: whereCondition,
        attributes: [
          [sequelize.fn('DATE', sequelize.col('created_at')), 'date'],
          [sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('user_id'))), 'active_users'],
          [sequelize.fn('COUNT', sequelize.col('id')), 'total_actions']
        ],
        group: [sequelize.fn('DATE', sequelize.col('created_at'))],
        order: [[sequelize.fn('DATE', sequelize.col('created_at')), 'ASC']],
        raw: true
      });

      return {
        behaviorTypeStats,
        pageViewStats,
        searchKeywordStats,
        activityTrend
      };
    } catch (error) {
      console.error('获取用户行为分析失败:', error);
      throw new Error('获取用户行为分析失败');
    }
  }

  // 获取用户路径分析
  async getUserPathAnalysis(startDate, endDate) {
    try {
      // 获取用户会话路径
      const userPaths = await UserBehavior.findAll({
        where: {
          behavior_type: 'view',
          page_path: { [Op.ne]: null },
          session_id: { [Op.ne]: null },
          created_at: {
            [Op.between]: [startDate, endDate]
          }
        },
        attributes: ['session_id', 'page_path', 'created_at'],
        order: [['session_id', 'ASC'], ['created_at', 'ASC']],
        raw: true
      });

      // 按会话分组分析路径
      const sessionPaths = {};
      userPaths.forEach(path => {
        if (!sessionPaths[path.session_id]) {
          sessionPaths[path.session_id] = [];
        }
        sessionPaths[path.session_id].push(path.page_path);
      });

      // 统计常见路径
      const pathFrequency = {};
      Object.values(sessionPaths).forEach(path => {
        if (path.length >= 2) {
          for (let i = 0; i < path.length - 1; i++) {
            const pathKey = `${path[i]} -> ${path[i + 1]}`;
            pathFrequency[pathKey] = (pathFrequency[pathKey] || 0) + 1;
          }
        }
      });

      // 排序并取前20个路径
      const topPaths = Object.entries(pathFrequency)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 20)
        .map(([path, count]) => ({ path, count }));

      return {
        totalSessions: Object.keys(sessionPaths).length,
        avgPathLength: Object.values(sessionPaths).reduce((sum, path) => sum + path.length, 0) / Object.keys(sessionPaths).length,
        topPaths
      };
    } catch (error) {
      console.error('获取用户路径分析失败:', error);
      throw new Error('获取用户路径分析失败');
    }
  }

  // 获取商品浏览分析
  async getProductViewAnalysis(startDate, endDate) {
    try {
      const productViews = await UserBehavior.findAll({
        where: {
          behavior_type: 'view',
          target_type: 'product',
          target_id: { [Op.ne]: null },
          created_at: {
            [Op.between]: [startDate, endDate]
          }
        },
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['id', 'name', 'price', 'main_image', 'sales']
          }
        ],
        attributes: [
          'target_id',
          [sequelize.fn('COUNT', sequelize.col('UserBehavior.id')), 'view_count'],
          [sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('user_id'))), 'unique_viewers']
        ],
        group: ['target_id'],
        order: [[sequelize.fn('COUNT', sequelize.col('UserBehavior.id')), 'DESC']],
        limit: 20,
        raw: false
      });

      return productViews.map(item => ({
        product_id: item.target_id,
        product_name: item.product?.name,
        product_price: item.product?.price,
        product_image: item.product?.main_image,
        product_sales: item.product?.sales,
        view_count: parseInt(item.dataValues.view_count),
        unique_viewers: parseInt(item.dataValues.unique_viewers)
      }));
    } catch (error) {
      console.error('获取商品浏览分析失败:', error);
      throw new Error('获取商品浏览分析失败');
    }
  }

  // 获取用户留存分析
  async getUserRetentionAnalysis(startDate, endDate) {
    try {
      // 获取新用户
      const newUsers = await User.findAll({
        where: {
          createdAt: {
            [Op.between]: [startDate, endDate]
          }
        },
        attributes: ['id', 'createdAt'],
        raw: true
      });

      // 计算留存率
      const retentionData = [];
      
      for (let day = 1; day <= 7; day++) {
        let retainedUsers = 0;
        
        for (const user of newUsers) {
          const userCreateDate = new Date(user.createdAt);
          const checkDate = new Date(userCreateDate.getTime() + day * 24 * 60 * 60 * 1000);
          const nextDay = new Date(checkDate.getTime() + 24 * 60 * 60 * 1000);
          
          const hasActivity = await UserBehavior.count({
            where: {
              user_id: user.id,
              created_at: {
                [Op.between]: [checkDate, nextDay]
              }
            }
          });
          
          if (hasActivity > 0) {
            retainedUsers++;
          }
        }
        
        retentionData.push({
          day: day,
          retained_users: retainedUsers,
          retention_rate: newUsers.length > 0 ? (retainedUsers / newUsers.length * 100).toFixed(2) : 0
        });
      }

      return {
        total_new_users: newUsers.length,
        retention_data: retentionData
      };
    } catch (error) {
      console.error('获取用户留存分析失败:', error);
      throw new Error('获取用户留存分析失败');
    }
  }

  // 获取转化漏斗分析
  async getConversionFunnelAnalysis(startDate, endDate) {
    try {
      const dateCondition = {
        created_at: {
          [Op.between]: [startDate, endDate]
        }
      };

      // 各阶段用户数
      const viewUsers = await UserBehavior.count({
        where: {
          behavior_type: 'view',
          target_type: 'product',
          ...dateCondition
        },
        distinct: true,
        col: 'user_id'
      });

      const cartUsers = await UserBehavior.count({
        where: {
          behavior_type: 'add_cart',
          ...dateCondition
        },
        distinct: true,
        col: 'user_id'
      });

      const orderUsers = await Order.count({
        where: dateCondition,
        distinct: true,
        col: 'user_id'
      });

      const payUsers = await Order.count({
        where: {
          pay_status: 1,
          ...dateCondition
        },
        distinct: true,
        col: 'user_id'
      });

      return {
        funnel_steps: [
          {
            step: 1,
            name: '商品浏览',
            users: viewUsers,
            conversion_rate: 100
          },
          {
            step: 2,
            name: '加入购物车',
            users: cartUsers,
            conversion_rate: viewUsers > 0 ? (cartUsers / viewUsers * 100).toFixed(2) : 0
          },
          {
            step: 3,
            name: '创建订单',
            users: orderUsers,
            conversion_rate: viewUsers > 0 ? (orderUsers / viewUsers * 100).toFixed(2) : 0
          },
          {
            step: 4,
            name: '完成支付',
            users: payUsers,
            conversion_rate: viewUsers > 0 ? (payUsers / viewUsers * 100).toFixed(2) : 0
          }
        ],
        overall_conversion_rate: viewUsers > 0 ? (payUsers / viewUsers * 100).toFixed(2) : 0
      };
    } catch (error) {
      console.error('获取转化漏斗分析失败:', error);
      throw new Error('获取转化漏斗分析失败');
    }
  }

  // 清理旧的行为数据
  async cleanupOldBehaviors(daysOld = 90) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const deletedCount = await UserBehavior.destroy({
        where: {
          created_at: { [Op.lt]: cutoffDate }
        }
      });

      console.log(`清理了 ${deletedCount} 条旧的用户行为数据`);
      return deletedCount;
    } catch (error) {
      console.error('清理旧行为数据失败:', error);
      throw new Error('清理旧行为数据失败');
    }
  }
}

module.exports = new UserBehaviorService();
