const config = require('../config');

/**
 * CDN工具类
 * 用于处理图片URL转换、图片尺寸优化等功能
 */
class CDNUtils {
  constructor() {
    this.config = config.cdn;
  }

  /**
   * 获取CDN图片URL
   * @param {string} originalUrl - 原始图片URL
   * @param {string} size - 图片尺寸 (thumbnail, small, medium, large)
   * @returns {string} CDN图片URL
   */
  getImageUrl(originalUrl, size = 'medium') {
    if (!originalUrl) {
      return originalUrl;
    }

    // 开发环境处理：保持HTTP协议，生产环境转换为HTTPS
    let processedUrl = originalUrl;
    const isDevelopment = process.env.NODE_ENV === 'development';

    if (isDevelopment) {
      // 开发环境：保持HTTP协议，确保与开发工具兼容
      if (processedUrl.includes('https://localhost:4443')) {
        processedUrl = processedUrl.replace('https://localhost:4443', 'http://localhost:4000');
      } else if (processedUrl.includes('http://localhost:4443')) {
        processedUrl = processedUrl.replace('http://localhost:4443', 'http://localhost:4000');
      }
    } else {
      // 生产环境：转换为HTTPS
      if (processedUrl.includes('http://localhost:4000')) {
        processedUrl = processedUrl.replace('http://localhost:4000', 'https://localhost:4443');
      } else if (processedUrl.includes('http://localhost:4443')) {
        processedUrl = processedUrl.replace('http://localhost:4443', 'https://localhost:4443');
      }
    }

    // 如果已经是完整URL，直接返回
    if (processedUrl.startsWith('http://') || processedUrl.startsWith('https://')) {
      return processedUrl;
    }

    // 如果CDN启用且是相对路径，转换为CDN URL
    if (this.config.enabled && processedUrl.startsWith('/')) {
      // 如果已经是CDN URL，直接返回
      if (processedUrl.startsWith(this.config.domain)) {
        return processedUrl;
      }

      // 提取文件路径
      const urlParts = processedUrl.split('/');
      const fileName = urlParts[urlParts.length - 1];
      
      // 获取尺寸配置
      const imageSize = this.config.imageSizes[size] || this.config.imageSizes.medium;
      
      // 构建CDN URL
      const cdnUrl = `${this.config.domain}${this.config.imagePath}/${imageSize}/${fileName}`;
      
      return cdnUrl;
    }

    // 如果是相对路径且CDN未启用，转换为本地完整URL
    if (processedUrl.startsWith('/')) {
      return `${this.config.fallbackUrl}${processedUrl}`;
    }

    return processedUrl;
  }

  /**
   * 批量转换图片URL
   * @param {Array|Object} data - 包含图片URL的数据
   * @param {string} size - 图片尺寸
   * @returns {Array|Object} 转换后的数据
   */
  transformImages(data, size = 'medium') {
    // 防止无限递归，限制递归深度
    return this._transformImagesRecursive(data, size, 0);
  }

  /**
   * 递归转换图片URL（带深度限制）
   * @param {Array|Object} data - 包含图片URL的数据
   * @param {string} size - 图片尺寸
   * @param {number} depth - 当前递归深度
   * @returns {Array|Object} 转换后的数据
   */
  _transformImagesRecursive(data, size = 'medium', depth = 0) {
    // 限制递归深度，防止无限递归
    if (depth > 10) {
      console.warn('CDN图片转换递归深度超过限制，停止处理');
      return data;
    }

    if (Array.isArray(data)) {
      return data.map(item => this._transformImagesRecursive(item, size, depth + 1));
    }

    if (typeof data === 'object' && data !== null) {
      const transformed = {};
      for (const [key, value] of Object.entries(data)) {
        if (typeof value === 'string' && this.isImageUrl(value)) {
          transformed[key] = this.getImageUrl(value, size);
        } else if (typeof value === 'object' && value !== null) {
          transformed[key] = this._transformImagesRecursive(value, size, depth + 1);
        } else {
          transformed[key] = value;
        }
      }
      return transformed;
    }

    return data;
  }

  /**
   * 判断是否为图片URL
   * @param {string} url - URL字符串
   * @returns {boolean} 是否为图片URL
   */
  isImageUrl(url) {
    if (!url || typeof url !== 'string') {
      return false;
    }
    
    const imageExtensions = this.config.imageFormats;
    const urlLower = url.toLowerCase();
    
    return imageExtensions.some(ext => urlLower.includes(`.${ext}`));
  }

  /**
   * 获取图片尺寸配置
   * @returns {Object} 图片尺寸配置
   */
  getImageSizes() {
    return this.config.imageSizes;
  }

  /**
   * 检查CDN是否启用
   * @returns {boolean} CDN是否启用
   */
  isEnabled() {
    return this.config.enabled;
  }

  /**
   * 获取CDN域名
   * @returns {string} CDN域名
   */
  getDomain() {
    return this.config.domain;
  }

  /**
   * 生成响应式图片URL
   * @param {string} originalUrl - 原始图片URL
   * @returns {Object} 包含不同尺寸的图片URL对象
   */
  getResponsiveImages(originalUrl) {
    if (!this.config.enabled || !originalUrl) {
      return { original: originalUrl };
    }

    const responsive = {};
    for (const [size, dimensions] of Object.entries(this.config.imageSizes)) {
      responsive[size] = this.getImageUrl(originalUrl, size);
    }
    responsive.original = originalUrl;

    return responsive;
  }
}

module.exports = new CDNUtils(); 