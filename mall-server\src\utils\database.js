const { pool } = require('../models');

class DatabaseUtils {
  // 开始事务
  static async beginTransaction() {
    const connection = await pool.getConnection();
    await connection.beginTransaction();
    return connection;
  }

  // 提交事务
  static async commitTransaction(connection) {
    await connection.commit();
    connection.release();
  }

  // 回滚事务
  static async rollbackTransaction(connection) {
    await connection.rollback();
    connection.release();
  }

  // 执行事务
  static async executeTransaction(callback) {
    const connection = await this.beginTransaction();
    
    try {
      const result = await callback(connection);
      await this.commitTransaction(connection);
      return result;
    } catch (error) {
      await this.rollbackTransaction(connection);
      throw error;
    }
  }

  // 构建WHERE条件
  static buildWhereClause(conditions) {
    const whereClause = [];
    const values = [];
    
    Object.keys(conditions).forEach(key => {
      if (conditions[key] !== undefined && conditions[key] !== null) {
        whereClause.push(`${key} = ?`);
        values.push(conditions[key]);
      }
    });
    
    return {
      clause: whereClause.length > 0 ? `WHERE ${whereClause.join(' AND ')}` : '',
      values
    };
  }

  // 构建分页
  static buildPagination(page = 1, limit = 10) {
    const offset = (page - 1) * limit;
    return { limit, offset };
  }

  // 构建排序
  static buildOrderBy(sortBy = 'created_at', sortOrder = 'DESC') {
    return `ORDER BY ${sortBy} ${sortOrder.toUpperCase()}`;
  }
}

module.exports = DatabaseUtils; 