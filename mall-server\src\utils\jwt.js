const jwt = require('jsonwebtoken');
const config = require('../config/jwt');

class JwtUtils {
  // 生成令牌
  static generateToken(payload, options = {}) {
    const defaultOptions = {
      expiresIn: config.expiresIn,
      issuer: config.issuer,
      audience: config.audience
    };
    
    return jwt.sign(payload, config.secret, { ...defaultOptions, ...options });
  }

  // 验证令牌
  static verifyToken(token, options = {}) {
    const defaultOptions = {
      issuer: config.issuer,
      audience: config.audience
    };
    
    try {
      return jwt.verify(token, config.secret, { ...defaultOptions, ...options });
    } catch (error) {
      throw new Error('令牌验证失败');
    }
  }

  // 解码令牌（不验证）
  static decodeToken(token) {
    try {
      return jwt.decode(token);
    } catch (error) {
      throw new Error('令牌解码失败');
    }
  }

  // 检查令牌是否过期
  static isTokenExpired(token) {
    try {
      const decoded = jwt.decode(token);
      if (!decoded || !decoded.exp) {
        return true;
      }
      
      const currentTime = Math.floor(Date.now() / 1000);
      return decoded.exp < currentTime;
    } catch (error) {
      return true;
    }
  }

  // 获取令牌剩余时间
  static getTokenRemainingTime(token) {
    try {
      const decoded = jwt.decode(token);
      if (!decoded || !decoded.exp) {
        return 0;
      }
      
      const currentTime = Math.floor(Date.now() / 1000);
      return Math.max(0, decoded.exp - currentTime);
    } catch (error) {
      return 0;
    }
  }
}

module.exports = JwtUtils; 