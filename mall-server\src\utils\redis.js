const Redis = require('ioredis');
const config = require('../config/redis');

const redis = new Redis(config);

class RedisUtils {
  // 设置缓存
  static async set(key, value, expire = 3600) {
    try {
      if (typeof value === 'object') {
        value = JSON.stringify(value);
      }
      // 确保expire是数字
      const expireSeconds = parseInt(expire) || 3600;
      await redis.set(key, value, 'EX', expireSeconds);
      return true;
    } catch (error) {
      console.error('Redis SET Error:', error);
      return false;
    }
  }

  // 获取缓存
  static async get(key) {
    try {
      const value = await redis.get(key);
      if (!value) return null;
      
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    } catch (error) {
      console.error('Redis GET Error:', error);
      return null;
    }
  }

  // 删除缓存
  static async del(key) {
    try {
      await redis.del(key);
      return true;
    } catch (error) {
      console.error('Redis DEL Error:', error);
      return false;
    }
  }

  // 批量删除缓存
  static async delPattern(pattern) {
    try {
      const keys = await redis.keys(pattern);
      if (keys.length > 0) {
        await redis.del(...keys);
      }
      return true;
    } catch (error) {
      console.error('Redis DEL Pattern Error:', error);
      return false;
    }
  }

  // 获取匹配模式的键
  static async keys(pattern) {
    try {
      return await redis.keys(pattern);
    } catch (error) {
      console.error('Redis KEYS Error:', error);
      return [];
    }
  }

  // 检查键是否存在
  static async exists(key) {
    try {
      const result = await redis.exists(key);
      return result === 1;
    } catch (error) {
      console.error('Redis EXISTS Error:', error);
      return false;
    }
  }

  // 设置过期时间
  static async expire(key, seconds) {
    try {
      await redis.expire(key, seconds);
      return true;
    } catch (error) {
      console.error('Redis EXPIRE Error:', error);
      return false;
    }
  }

  // 获取剩余过期时间
  static async ttl(key) {
    try {
      return await redis.ttl(key);
    } catch (error) {
      console.error('Redis TTL Error:', error);
      return -1;
    }
  }

  // 递增
  static async incr(key) {
    try {
      return await redis.incr(key);
    } catch (error) {
      console.error('Redis INCR Error:', error);
      return 0;
    }
  }

  // 递减
  static async decr(key) {
    try {
      return await redis.decr(key);
    } catch (error) {
      console.error('Redis DECR Error:', error);
      return 0;
    }
  }
}

module.exports = RedisUtils; 