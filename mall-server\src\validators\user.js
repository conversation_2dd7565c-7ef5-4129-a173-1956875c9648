const Joi = require('joi');

// 用户登录验证
const validateUserLogin = Joi.object({
  username: Jo<PERSON>.string().min(3).max(20).required().messages({
    'string.min': '用户名至少3个字符',
    'string.max': '用户名最多20个字符',
    'any.required': '用户名不能为空'
  }),
  password: Joi.string().min(6).max(20).required().messages({
    'string.min': '密码至少6个字符',
    'string.max': '密码最多20个字符',
    'any.required': '密码不能为空'
  })
});

// 用户注册验证
const validateUserRegister = Joi.object({
  username: Joi.string().min(3).max(20).required().messages({
    'string.min': '用户名至少3个字符',
    'string.max': '用户名最多20个字符',
    'any.required': '用户名不能为空'
  }),
  password: Joi.string().min(6).max(20).required().messages({
    'string.min': '密码至少6个字符',
    'string.max': '密码最多20个字符',
    'any.required': '密码不能为空'
  }),
  nickname: Joi.string().min(2).max(20).required().messages({
    'string.min': '昵称至少2个字符',
    'string.max': '昵称最多20个字符',
    'any.required': '昵称不能为空'
  }),
  phone: Joi.string().pattern(/^1[3-9]\d{9}$/).required().messages({
    'string.pattern.base': '手机号格式不正确',
    'any.required': '手机号不能为空'
  }),
  email: Joi.string().email().optional().messages({
    'string.email': '邮箱格式不正确'
  })
});

// 更新用户信息验证
const validateUserUpdate = Joi.object({
  nickname: Joi.string().min(2).max(20).optional().messages({
    'string.min': '昵称至少2个字符',
    'string.max': '昵称最多20个字符'
  }),
  phone: Joi.string().pattern(/^1[3-9]\d{9}$/).optional().messages({
    'string.pattern.base': '手机号格式不正确'
  }),
  email: Joi.string().email().optional().messages({
    'string.email': '邮箱格式不正确'
  }),
  avatar: Joi.string().uri().optional().messages({
    'string.uri': '头像URL格式不正确'
  })
});

// 修改密码验证
const validateChangePassword = Joi.object({
  oldPassword: Joi.string().min(6).max(20).required().messages({
    'string.min': '原密码至少6个字符',
    'string.max': '原密码最多20个字符',
    'any.required': '原密码不能为空'
  }),
  newPassword: Joi.string().min(6).max(20).required().messages({
    'string.min': '新密码至少6个字符',
    'string.max': '新密码最多20个字符',
    'any.required': '新密码不能为空'
  })
});

module.exports = {
  validateUserLogin,
  validateUserRegister,
  validateUserUpdate,
  validateChangePassword
}; 