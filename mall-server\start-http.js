const Koa = require('koa');
const helmet = require('koa-helmet');
const compress = require('koa-compress');
const logger = require('koa-logger');
const static = require('koa-static');
const bodyParser = require('koa-bodyparser');
const cors = require('koa-cors');
const path = require('path');
const http = require('http');
require('dotenv').config();
const mount = require('koa-mount');

const config = require('./src/config');
const routes = require('./src/routes');

const app = new Koa();

// 请求体解析中间件（必须放在最前面）
app.use(bodyParser({
  enableTypes: ['json', 'form', 'text'],
  jsonLimit: '10mb',
  formLimit: '10mb',
  textLimit: '10mb',
  strict: false,
  onerror: function (err, ctx) {
    console.error('Body parser error:', err);
    ctx.throw(422, 'body parse error');
  }
}));

// CORS中间件
app.use(cors({
  origin: '*',
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization', 'Accept'],
  credentials: true
}));

// 安全中间件（开发环境禁用CSP）
app.use(helmet({
  contentSecurityPolicy: false
}));

// 压缩中间件
app.use(compress());

// 日志中间件
if (config.env !== 'test') {
  app.use(logger());
}

// 静态文件服务
const uploadsPath = path.join(__dirname, 'uploads');
console.log('📁 mall-server HTTP 静态资源目录:', uploadsPath);
app.use(mount('/uploads', static(uploadsPath)));
app.use(mount('/images', static(path.join(__dirname, '../xinjie-mall-miniprogram/images'))));

// 应用路由
app.use(routes.routes());
app.use(routes.allowedMethods());

// 错误处理
app.on('error', (err, ctx) => {
  console.error('Server Error:', err);
  ctx.status = err.status || 500;
  ctx.body = {
    code: 500,
    message: '服务器内部错误',
    error: config.env === 'development' ? err.message : undefined
  };
});

// 启动HTTP服务器
const PORT = 4000; // HTTP服务器端口
const httpServer = http.createServer(app.callback());

httpServer.listen(PORT, () => {
  console.log(`🚀 心洁茶叶商城HTTP后端服务启动成功`);
  console.log(`📍 HTTP服务地址: http://localhost:${PORT}`);
  console.log(`🌍 环境: ${config.env}`);
  console.log(`⏰ 时间: ${new Date().toLocaleString()}`);
  console.log(`📝 API文档: http://localhost:${PORT}/api/docs`);
  console.log(`📸 图片访问: http://localhost:${PORT}/uploads/categories/`);
  console.log(`💡 提示: 此HTTP服务专为微信开发者工具设计，避免HTTPS证书问题`);
});

module.exports = app; 