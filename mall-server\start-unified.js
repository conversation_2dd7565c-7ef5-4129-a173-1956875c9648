const Koa = require('koa');
const helmet = require('koa-helmet');
const compress = require('koa-compress');
const logger = require('koa-logger');
const static = require('koa-static');
const path = require('path');
const http = require('http');
const https = require('https');
const fs = require('fs');
require('dotenv').config();
const mount = require('koa-mount');

const config = require('./src/config');
const routes = require('./src/routes');

const app = new Koa();

// 安全中间件（开发环境禁用CSP）
app.use(helmet({
  contentSecurityPolicy: false
}));

// 压缩中间件
app.use(compress());

// 日志中间件
if (config.env !== 'test') {
  app.use(logger());
}

// 静态文件服务
const uploadsPath = path.join(__dirname, 'uploads');
console.log('📁 统一服务静态资源目录:', uploadsPath);
app.use(mount('/uploads', static(uploadsPath)));
app.use(mount('/images', static(path.join(__dirname, '../xinjie-mall-miniprogram/images'))));

// 应用路由
app.use(routes.routes());
app.use(routes.allowedMethods());

// 错误处理
app.on('error', (err, ctx) => {
  console.error('Server Error:', err);
  ctx.status = err.status || 500;
  ctx.body = {
    code: 500,
    message: '服务器内部错误',
    error: config.env === 'development' ? err.message : undefined
  };
});

// 端口配置
const HTTP_PORT = config.port || 4000;
const HTTPS_PORT = 4443;

// 启动HTTP服务器（开发环境必需）
const httpServer = http.createServer(app.callback());
httpServer.listen(HTTP_PORT, () => {
  console.log(`🚀 心洁茶叶商城HTTP服务启动成功`);
  console.log(`📍 HTTP服务地址: http://localhost:${HTTP_PORT}`);
  console.log(`🌍 环境: ${config.env}`);
  console.log(`⏰ 时间: ${new Date().toLocaleString()}`);
  console.log(`📝 API文档: http://localhost:${HTTP_PORT}/api/docs`);
  console.log(`📸 图片访问: http://localhost:${HTTP_PORT}/uploads/`);
  console.log(`💡 开发环境: 微信小程序开发工具使用此HTTP服务`);
});

// SSL证书配置
const certDir = path.join(__dirname, 'ssl');
const certPath = path.join(certDir, 'localhost.pem');
const keyPath = path.join(certDir, 'localhost-key.pem');

let httpsOptions = {};
if (fs.existsSync(certPath) && fs.existsSync(keyPath)) {
  httpsOptions = {
    cert: fs.readFileSync(certPath),
    key: fs.readFileSync(keyPath)
  };
}

// 启动HTTPS服务器（生产环境必需）
if (Object.keys(httpsOptions).length > 0) {
  const httpsServer = https.createServer(httpsOptions, app.callback());
  httpsServer.listen(HTTPS_PORT, () => {
    console.log(`🔒 HTTPS服务启动成功`);
    console.log(`📍 HTTPS服务地址: https://localhost:${HTTPS_PORT}`);
    console.log(`📝 HTTPS API文档: https://localhost:${HTTPS_PORT}/api/docs`);
    console.log(`📸 HTTPS图片访问: https://localhost:${HTTPS_PORT}/uploads/`);
    console.log(`🌐 生产环境: 微信小程序正式版使用此HTTPS服务`);
  });
} else {
  console.log(`⚠️ HTTPS服务未启动（缺少SSL证书）`);
  console.log(`💡 提示: 生产环境需要配置SSL证书`);
  console.log(`🔧 证书路径: ${certPath}`);
  console.log(`🔧 密钥路径: ${keyPath}`);
}

// 服务状态总结
console.log(`\n📊 服务状态总结:`);
console.log(`   ✅ HTTP服务: http://localhost:${HTTP_PORT} (开发环境)`);
console.log(`   ${Object.keys(httpsOptions).length > 0 ? '✅' : '❌'} HTTPS服务: https://localhost:${HTTPS_PORT} (生产环境)`);
console.log(`   🔧 微信配置: AppID=${config.wxAppId}, AppSecret=${config.wxAppSecret ? '已配置' : '未配置'}`);

module.exports = app;
