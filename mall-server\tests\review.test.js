/**
 * 评论功能测试
 * 测试评论系统的各项功能，确保只有购买过商品的用户才能评论
 */

const request = require('supertest');
const app = require('../app');
const { User, Product, Order, OrderItem, Review } = require('../src/models');

describe('评论功能测试', () => {
  let userToken;
  let userId;
  let productId;
  let orderId;

  beforeAll(async () => {
    // 创建测试用户
    const user = await User.create({
      phone: '13800000001',
      nickname: '测试用户',
      password: 'password123',
      status: 1
    });
    userId = user.id;

    // 创建测试商品
    const product = await Product.create({
      name: '测试茶叶',
      price: 99.00,
      stock: 100,
      status: 1,
      category_id: 1
    });
    productId = product.id;

    // 创建测试订单
    const order = await Order.create({
      user_id: userId,
      order_no: 'TEST' + Date.now(),
      total_amount: 99.00,
      order_status: 3, // 已完成
      payment_status: 1
    });
    orderId = order.id;

    // 创建订单商品
    await OrderItem.create({
      order_id: orderId,
      product_id: productId,
      quantity: 1,
      price: 99.00
    });

    // 获取用户token
    const loginRes = await request(app)
      .post('/api/front/auth/login')
      .send({
        phone: '13800000001',
        password: 'password123'
      });
    
    userToken = loginRes.body.data.token;
  });

  afterAll(async () => {
    // 清理测试数据
    await Review.destroy({ where: { user_id: userId } });
    await OrderItem.destroy({ where: { order_id: orderId } });
    await Order.destroy({ where: { id: orderId } });
    await Product.destroy({ where: { id: productId } });
    await User.destroy({ where: { id: userId } });
  });

  describe('评论权限验证', () => {
    test('检查用户是否可以评价商品 - 可以评价', async () => {
      const res = await request(app)
        .get(`/api/front/review/check/${productId}`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(res.status).toBe(200);
      expect(res.body.code).toBe(200);
      expect(res.body.data.canReview).toBe(true);
      expect(res.body.data.orderId).toBe(orderId);
    });

    test('检查用户是否可以评价商品 - 未购买商品', async () => {
      // 创建一个未购买的商品
      const newProduct = await Product.create({
        name: '未购买商品',
        price: 199.00,
        stock: 100,
        status: 1,
        category_id: 1
      });

      const res = await request(app)
        .get(`/api/front/review/check/${newProduct.id}`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(res.status).toBe(200);
      expect(res.body.code).toBe(200);
      expect(res.body.data.canReview).toBe(false);
      expect(res.body.data.reason).toBe('未购买或订单未完成');

      // 清理测试数据
      await Product.destroy({ where: { id: newProduct.id } });
    });
  });

  describe('发表评论', () => {
    test('成功发表评论', async () => {
      const reviewData = {
        orderId: orderId,
        productId: productId,
        rating: 5,
        content: '茶叶质量很好，包装精美，值得推荐！',
        images: ['/uploads/review1.jpg', '/uploads/review2.jpg'],
        isAnonymous: false
      };

      const res = await request(app)
        .post('/api/front/review')
        .set('Authorization', `Bearer ${userToken}`)
        .send(reviewData);

      expect(res.status).toBe(200);
      expect(res.body.code).toBe(200);
      expect(res.body.message).toBe('评价发表成功');
      expect(res.body.data.rating).toBe(5);
      expect(res.body.data.content).toBe(reviewData.content);
    });

    test('重复评价同一商品 - 应该失败', async () => {
      const reviewData = {
        orderId: orderId,
        productId: productId,
        rating: 4,
        content: '重复评价测试',
        isAnonymous: false
      };

      const res = await request(app)
        .post('/api/front/review')
        .set('Authorization', `Bearer ${userToken}`)
        .send(reviewData);

      expect(res.status).toBe(200);
      expect(res.body.code).toBe(400);
      expect(res.body.message).toBe('您已经评价过该商品');
    });

    test('评价未购买的商品 - 应该失败', async () => {
      const newProduct = await Product.create({
        name: '未购买商品2',
        price: 299.00,
        stock: 100,
        status: 1,
        category_id: 1
      });

      const reviewData = {
        orderId: orderId,
        productId: newProduct.id,
        rating: 5,
        content: '尝试评价未购买商品',
        isAnonymous: false
      };

      const res = await request(app)
        .post('/api/front/review')
        .set('Authorization', `Bearer ${userToken}`)
        .send(reviewData);

      expect(res.status).toBe(200);
      expect(res.body.code).toBe(400);
      expect(res.body.message).toBe('该订单中不包含此商品');

      // 清理测试数据
      await Product.destroy({ where: { id: newProduct.id } });
    });

    test('评分超出范围 - 应该失败', async () => {
      const reviewData = {
        orderId: orderId,
        productId: productId,
        rating: 6, // 超出1-5范围
        content: '评分测试',
        isAnonymous: false
      };

      const res = await request(app)
        .post('/api/front/review')
        .set('Authorization', `Bearer ${userToken}`)
        .send(reviewData);

      expect(res.status).toBe(200);
      expect(res.body.code).toBe(400);
      expect(res.body.message).toBe('评分必须在1-5之间');
    });

    test('评价内容过长 - 应该失败', async () => {
      const longContent = 'a'.repeat(501); // 超过500字限制
      
      const reviewData = {
        orderId: orderId,
        productId: productId,
        rating: 5,
        content: longContent,
        isAnonymous: false
      };

      const res = await request(app)
        .post('/api/front/review')
        .set('Authorization', `Bearer ${userToken}`)
        .send(reviewData);

      expect(res.status).toBe(200);
      expect(res.body.code).toBe(400);
      expect(res.body.message).toBe('评价内容不能超过500字');
    });

    test('上传图片过多 - 应该失败', async () => {
      const tooManyImages = Array(6).fill('/uploads/test.jpg'); // 超过5张限制
      
      const reviewData = {
        orderId: orderId,
        productId: productId,
        rating: 5,
        content: '图片测试',
        images: tooManyImages,
        isAnonymous: false
      };

      const res = await request(app)
        .post('/api/front/review')
        .set('Authorization', `Bearer ${userToken}`)
        .send(reviewData);

      expect(res.status).toBe(200);
      expect(res.body.code).toBe(400);
      expect(res.body.message).toBe('评价图片最多只能上传5张');
    });
  });

  describe('获取评论', () => {
    test('获取商品评论列表', async () => {
      const res = await request(app)
        .get(`/api/front/review/product/${productId}`)
        .query({ page: 1, limit: 10 });

      expect(res.status).toBe(200);
      expect(res.body.code).toBe(200);
      expect(res.body.data.list).toBeInstanceOf(Array);
      expect(res.body.data.list.length).toBeGreaterThan(0);
      expect(res.body.data.pagination).toBeDefined();
    });

    test('获取用户评论列表', async () => {
      const res = await request(app)
        .get('/api/front/review/user')
        .set('Authorization', `Bearer ${userToken}`)
        .query({ page: 1, limit: 10 });

      expect(res.status).toBe(200);
      expect(res.body.code).toBe(200);
      expect(res.body.data.list).toBeInstanceOf(Array);
      expect(res.body.data.pagination).toBeDefined();
    });

    test('获取待评价商品列表', async () => {
      const res = await request(app)
        .get('/api/front/review/pending')
        .set('Authorization', `Bearer ${userToken}`)
        .query({ page: 1, limit: 10 });

      expect(res.status).toBe(200);
      expect(res.body.code).toBe(200);
      expect(res.body.data.list).toBeInstanceOf(Array);
      expect(res.body.data.pagination).toBeDefined();
    });
  });

  describe('删除评论', () => {
    test('用户删除自己的评论', async () => {
      // 先获取用户的评论
      const userReviews = await request(app)
        .get('/api/front/review/user')
        .set('Authorization', `Bearer ${userToken}`);

      const reviewId = userReviews.body.data.list[0].id;

      const res = await request(app)
        .delete(`/api/front/review/${reviewId}`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(res.status).toBe(200);
      expect(res.body.code).toBe(200);
      expect(res.body.message).toBe('评价删除成功');
    });
  });
});
