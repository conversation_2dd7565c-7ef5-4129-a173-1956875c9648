# Nginx配置详细说明

## Nginx的作用

### 1. 反向代理
```
用户请求 → Nginx → Node.js应用
```
- 用户访问 `https://api.xinjie-tea.com/api/products`
- Nginx接收请求，转发给 `http://127.0.0.1:4000/api/products`
- Node.js处理请求，返回结果给Nginx
- Nginx将结果返回给用户

### 2. 静态文件服务
```
用户请求图片 → Nginx直接返回 → 不经过Node.js
```
- 提高性能，减少Node.js负担
- 图片、CSS、JS等静态资源直接由Nginx提供

### 3. SSL终止
```
HTTPS请求 → Nginx解密 → HTTP转发给Node.js
```
- 用户访问HTTPS，Nginx处理SSL加密解密
- 内部使用HTTP通信，提高性能

## 配置文件创建

### 1. 创建配置文件
```bash
# 在服务器上创建配置文件
nano /etc/nginx/sites-available/xinjie-tea
```

### 2. 配置内容
```nginx
# 上游服务器配置
upstream xinjie_api {
    server 127.0.0.1:4000;
    keepalive 32;
}

upstream xinjie_admin {
    server 127.0.0.1:8081;
    keepalive 32;
}

# API服务器配置 - HTTP重定向到HTTPS
server {
    listen 80;
    server_name api.xinjie-tea.com;
    return 301 https://$server_name$request_uri;
}

# API服务器配置 - HTTPS
server {
    listen 443 ssl http2;
    server_name api.xinjie-tea.com;

    # SSL证书配置（稍后配置）
    ssl_certificate /etc/letsencrypt/live/api.xinjie-tea.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.xinjie-tea.com/privkey.pem;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # API代理
    location /api/ {
        proxy_pass http://xinjie_api;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # 静态文件服务
    location /uploads/ {
        alias /var/www/xinjie-tea/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}

# 管理后台配置 - HTTP重定向到HTTPS
server {
    listen 80;
    server_name admin.xinjie-tea.com;
    return 301 https://$server_name$request_uri;
}

# 管理后台配置 - HTTPS
server {
    listen 443 ssl http2;
    server_name admin.xinjie-tea.com;

    # SSL证书配置
    ssl_certificate /etc/letsencrypt/live/admin.xinjie-tea.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/admin.xinjie-tea.com/privkey.pem;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # 管理后台代理
    location / {
        proxy_pass http://xinjie_admin;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 3. 启用配置
```bash
# 创建软链接
ln -s /etc/nginx/sites-available/xinjie-tea /etc/nginx/sites-enabled/

# 删除默认配置
rm /etc/nginx/sites-enabled/default

# 测试配置
nginx -t

# 重启Nginx
systemctl restart nginx
```

## 配置说明

### upstream配置
```nginx
upstream xinjie_api {
    server 127.0.0.1:4000;  # Node.js API服务地址
    keepalive 32;           # 保持连接数
}
```
- 定义后端服务器地址
- keepalive提高性能

### proxy_pass配置
```nginx
location /api/ {
    proxy_pass http://xinjie_api;  # 转发到upstream
    # ... 其他代理设置
}
```
- 将 `/api/` 开头的请求转发给Node.js
- 设置必要的HTTP头

### 静态文件配置
```nginx
location /uploads/ {
    alias /var/www/xinjie-tea/uploads/;  # 文件实际路径
    expires 1y;                          # 缓存1年
    add_header Cache-Control "public, immutable";
}
```
- 直接提供静态文件，不经过Node.js
- 设置缓存策略提高性能

### 安全头配置
```nginx
add_header X-Frame-Options DENY;              # 防止点击劫持
add_header X-Content-Type-Options nosniff;    # 防止MIME类型嗅探
add_header X-XSS-Protection "1; mode=block";  # XSS防护
```
- 增强网站安全性
- 防止常见的Web攻击

## 测试配置

### 1. 语法检查
```bash
nginx -t
# 输出: nginx: configuration file /etc/nginx/nginx.conf test is successful
```

### 2. 重启服务
```bash
systemctl restart nginx
systemctl status nginx
```

### 3. 检查端口监听
```bash
netstat -tlnp | grep nginx
# 应该看到80和443端口被监听
```

### 4. 测试访问
```bash
# 测试HTTP重定向
curl -I http://api.xinjie-tea.com
# 应该返回301重定向

# 测试健康检查
curl http://服务器IP/health
# 应该返回 "healthy"
```
