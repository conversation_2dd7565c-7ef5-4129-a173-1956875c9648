# ✅ 心洁茶叶商城生产环境检查清单

## 🎯 部署前检查 (Pre-deployment)

### 📋 基础设施准备
- [ ] **服务器购买完成**
  - [ ] 配置: 2核4GB内存，40GB SSD
  - [ ] 操作系统: Ubuntu 20.04 LTS
  - [ ] 公网IP地址已获取
  - [ ] SSH访问正常

- [ ] **域名配置完成**
  - [ ] 主域名已购买: `xinjie-tea.com`
  - [ ] DNS解析已配置:
    - [ ] `api.xinjie-tea.com` → 服务器IP
    - [ ] `admin.xinjie-tea.com` → 服务器IP
  - [ ] DNS解析生效验证: `ping api.xinjie-tea.com`

- [ ] **安全配置**
  - [ ] 服务器密码已修改为复杂密码
  - [ ] 数据库密码已设置为安全密码
  - [ ] JWT密钥已生成随机值
  - [ ] 敏感信息不在代码中硬编码

### 🔧 代码准备
- [ ] **代码质量检查**
  - [ ] 所有功能测试通过
  - [ ] 生产环境配置文件准备完成
  - [ ] API地址已更新为生产域名
  - [ ] 数据库连接配置正确

- [ ] **依赖检查**
  - [ ] package.json依赖版本锁定
  - [ ] 生产环境不包含开发依赖
  - [ ] 所有必要的npm包已安装

## 🚀 部署过程检查 (During Deployment)

### 🖥️ 服务器环境
- [ ] **系统更新**
  - [ ] `apt update && apt upgrade -y` 执行完成
  - [ ] 系统时区设置为 Asia/Shanghai

- [ ] **软件安装**
  - [ ] Node.js 18.x 安装完成: `node --version`
  - [ ] MySQL 8.0 安装完成: `mysql --version`
  - [ ] Redis 安装完成: `redis-cli --version`
  - [ ] Nginx 安装完成: `nginx -v`
  - [ ] PM2 安装完成: `pm2 --version`
  - [ ] Certbot 安装完成: `certbot --version`

- [ ] **服务启动**
  - [ ] MySQL服务运行: `systemctl status mysql`
  - [ ] Redis服务运行: `systemctl status redis`
  - [ ] Nginx服务运行: `systemctl status nginx`

### 🗄️ 数据库配置
- [ ] **数据库创建**
  - [ ] 数据库 `xinjie_mall` 创建成功
  - [ ] 用户 `xinjie_user` 创建成功
  - [ ] 权限配置正确
  - [ ] 字符集设置为 utf8mb4

- [ ] **表结构导入**
  - [ ] 数据库建表语句执行成功
  - [ ] 所有必要表已创建
  - [ ] 索引创建完成
  - [ ] 初始数据导入完成

### 📁 代码部署
- [ ] **文件上传**
  - [ ] 代码上传到 `/var/www/xinjie-tea/`
  - [ ] 文件权限设置正确
  - [ ] 上传目录创建: `uploads/`
  - [ ] 日志目录创建: `logs/`

- [ ] **依赖安装**
  - [ ] 后端依赖安装: `cd mall-server && npm install --production`
  - [ ] 管理后台依赖安装: `cd xinjie.mall-admin && npm install --production`
  - [ ] 管理后台构建: `npm run build`

- [ ] **环境配置**
  - [ ] `.env` 文件配置完成
  - [ ] 数据库连接信息正确
  - [ ] 微信小程序配置正确
  - [ ] JWT密钥配置完成

### 🌐 Web服务配置
- [ ] **Nginx配置**
  - [ ] 配置文件创建: `/etc/nginx/sites-available/xinjie-tea`
  - [ ] 站点启用: `ln -s` 软链接创建
  - [ ] 默认站点删除
  - [ ] 配置语法检查: `nginx -t`
  - [ ] Nginx重载: `systemctl reload nginx`

- [ ] **SSL证书**
  - [ ] API域名证书申请: `certbot --nginx -d api.xinjie-tea.com`
  - [ ] 管理后台证书申请: `certbot --nginx -d admin.xinjie-tea.com`
  - [ ] 证书自动续期设置: `crontab -l`
  - [ ] HTTPS强制跳转配置

### 🔥 应用启动
- [ ] **PM2配置**
  - [ ] `ecosystem.config.js` 配置文件创建
  - [ ] 应用启动: `pm2 start ecosystem.config.js --env production`
  - [ ] 配置保存: `pm2 save`
  - [ ] 开机自启: `pm2 startup`

- [ ] **防火墙配置**
  - [ ] SSH端口开放: `ufw allow ssh`
  - [ ] HTTP/HTTPS端口开放: `ufw allow 'Nginx Full'`
  - [ ] 应用端口开放: `ufw allow 4000 && ufw allow 8081`
  - [ ] 防火墙启用: `ufw enable`

## 🧪 部署后验证 (Post-deployment)

### 🔍 服务状态检查
- [ ] **系统服务**
  - [ ] Nginx状态正常: `systemctl status nginx`
  - [ ] MySQL状态正常: `systemctl status mysql`
  - [ ] Redis状态正常: `systemctl status redis`

- [ ] **应用服务**
  - [ ] PM2状态正常: `pm2 status`
  - [ ] 应用日志无错误: `pm2 logs`
  - [ ] 进程CPU/内存使用正常

### 🌐 网络访问测试
- [ ] **API服务测试**
  - [ ] 健康检查: `curl https://api.xinjie-tea.com/health`
  - [ ] 分类接口: `curl https://api.xinjie-tea.com/api/front/category/list`
  - [ ] 轮播图接口: `curl https://api.xinjie-tea.com/api/front/banner/list`
  - [ ] 响应时间 < 1秒

- [ ] **管理后台测试**
  - [ ] 页面访问: `curl -I https://admin.xinjie-tea.com`
  - [ ] 登录功能正常
  - [ ] 管理功能正常

- [ ] **HTTPS验证**
  - [ ] SSL证书有效
  - [ ] HTTP自动跳转HTTPS
  - [ ] 安全头配置正确

### 📱 小程序配置
- [ ] **代码更新**
  - [ ] API地址更新为生产域名
  - [ ] 图片地址更新为生产域名
  - [ ] 小程序重新编译

- [ ] **微信公众平台配置**
  - [ ] request合法域名: `https://api.xinjie-tea.com`
  - [ ] uploadFile合法域名: `https://api.xinjie-tea.com`
  - [ ] downloadFile合法域名: `https://api.xinjie-tea.com`
  - [ ] 业务域名配置（如需要）

## 🔒 安全检查 (Security)

### 🛡️ 服务器安全
- [ ] **访问控制**
  - [ ] SSH密钥认证（推荐）
  - [ ] 禁用root远程登录（可选）
  - [ ] 修改SSH默认端口（可选）
  - [ ] 防火墙规则最小化

- [ ] **应用安全**
  - [ ] 数据库用户权限最小化
  - [ ] 敏感文件权限设置正确
  - [ ] 日志文件权限安全
  - [ ] 上传目录权限控制

### 🔐 数据安全
- [ ] **密码安全**
  - [ ] 数据库密码复杂度足够
  - [ ] JWT密钥随机生成
  - [ ] Session密钥安全设置

- [ ] **数据备份**
  - [ ] 数据库备份脚本设置
  - [ ] 代码文件备份计划
  - [ ] 备份文件存储安全

## 📊 监控和维护 (Monitoring)

### 📈 性能监控
- [ ] **服务器监控**
  - [ ] CPU使用率监控
  - [ ] 内存使用率监控
  - [ ] 磁盘空间监控
  - [ ] 网络流量监控

- [ ] **应用监控**
  - [ ] PM2监控面板: `pm2 monit`
  - [ ] 应用响应时间监控
  - [ ] 错误日志监控
  - [ ] 数据库性能监控

### 📝 日志管理
- [ ] **日志配置**
  - [ ] 应用日志轮转设置
  - [ ] Nginx访问日志配置
  - [ ] 错误日志级别设置
  - [ ] 日志文件大小限制

- [ ] **日志监控**
  - [ ] 错误日志实时监控
  - [ ] 异常访问监控
  - [ ] 性能日志分析

## 🎯 上线完成确认

### ✅ 最终检查
- [ ] **所有服务正常运行**
  - [ ] API服务响应正常
  - [ ] 管理后台功能完整
  - [ ] 数据库连接稳定
  - [ ] SSL证书有效

- [ ] **功能完整性测试**
  - [ ] 用户注册登录
  - [ ] 商品浏览购买
  - [ ] 订单创建支付
  - [ ] 管理后台操作

- [ ] **性能指标达标**
  - [ ] 页面加载时间 < 3秒
  - [ ] API响应时间 < 1秒
  - [ ] 服务器资源使用正常

### 📋 文档整理
- [ ] **部署文档**
  - [ ] 服务器配置信息记录
  - [ ] 数据库连接信息记录
  - [ ] 域名和证书信息记录
  - [ ] 应用配置信息记录

- [ ] **运维手册**
  - [ ] 常用运维命令整理
  - [ ] 故障排查指南
  - [ ] 备份恢复流程
  - [ ] 更新部署流程

## 🎉 上线成功！

当所有检查项都完成后，恭喜您的心洁茶叶商城成功上线！

**下一步建议:**
- 🚀 性能优化（CDN、缓存优化）
- 📊 数据分析（用户行为、销售数据）
- 🔄 持续集成/持续部署（CI/CD）
- 📱 移动端体验优化
- 🛡️ 安全加固和定期审计

记住定期检查服务器状态，及时更新系统和应用，确保商城稳定运行！
