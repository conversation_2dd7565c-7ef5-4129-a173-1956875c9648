#!/usr/bin/env node

/**
 * 心洁茶叶商城 - 生产环境问题诊断工具
 * 专门用于诊断线上部署后的问题
 */

const https = require('https');
const http = require('http');
const { execSync } = require('child_process');

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(level, message) {
  const timestamp = new Date().toLocaleString('zh-CN');
  const color = colors[level] || colors.reset;
  console.log(`${color}[${timestamp}] ${message}${colors.reset}`);
}

// 生产环境配置
const PRODUCTION_CONFIG = {
  domains: {
    api: 'api.xinjie-tea.com',
    admin: 'admin.xinjie-tea.com'
  },
  ports: {
    api: 4000,
    admin: 8081
  },
  paths: {
    deploy: '/var/www/xinjie-tea',
    logs: '/var/www/xinjie-tea/logs'
  }
};

// HTTP请求工具
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    
    const req = client.request(url, {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Production-Debug-Tool/1.0',
        ...options.headers
      },
      timeout: 10000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });

    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    
    if (options.data) {
      req.write(JSON.stringify(options.data));
    }
    
    req.end();
  });
}

// 1. 检查域名解析
async function checkDNS() {
  log('blue', '🔍 检查域名解析...');
  
  const domains = [PRODUCTION_CONFIG.domains.api, PRODUCTION_CONFIG.domains.admin];
  
  for (const domain of domains) {
    try {
      const result = execSync(`nslookup ${domain}`, { encoding: 'utf8' });
      if (result.includes('121.199.72.228')) {
        log('green', `✅ ${domain} 解析正确 → 121.199.72.228`);
      } else {
        log('yellow', `⚠️ ${domain} 解析异常`);
        console.log(result);
      }
    } catch (error) {
      log('red', `❌ ${domain} DNS查询失败: ${error.message}`);
    }
  }
}

// 2. 检查SSL证书
async function checkSSL() {
  log('blue', '🔍 检查SSL证书...');
  
  const domains = [PRODUCTION_CONFIG.domains.api, PRODUCTION_CONFIG.domains.admin];
  
  for (const domain of domains) {
    try {
      const response = await makeRequest(`https://${domain}/`);
      log('green', `✅ ${domain} SSL证书正常`);
    } catch (error) {
      if (error.code === 'CERT_HAS_EXPIRED') {
        log('red', `❌ ${domain} SSL证书已过期`);
      } else if (error.code === 'UNABLE_TO_VERIFY_LEAF_SIGNATURE') {
        log('red', `❌ ${domain} SSL证书验证失败`);
      } else {
        log('yellow', `⚠️ ${domain} SSL检查异常: ${error.message}`);
      }
    }
  }
}

// 3. 检查服务状态
async function checkServices() {
  log('blue', '🔍 检查服务状态...');
  
  // 检查进程
  try {
    const processes = execSync('pm2 status', { encoding: 'utf8' });
    console.log(processes);
    
    if (processes.includes('xinjie-api') && processes.includes('online')) {
      log('green', '✅ API服务运行正常');
    } else {
      log('red', '❌ API服务未运行');
    }
    
    if (processes.includes('xinjie-admin') && processes.includes('online')) {
      log('green', '✅ 管理后台服务运行正常');
    } else {
      log('red', '❌ 管理后台服务未运行');
    }
  } catch (error) {
    log('red', `❌ 无法获取PM2状态: ${error.message}`);
  }

  // 检查端口监听
  try {
    const netstat = execSync('netstat -tlnp | grep -E ":4000|:8081"', { encoding: 'utf8' });
    if (netstat.includes(':4000')) {
      log('green', '✅ 端口4000正在监听');
    } else {
      log('red', '❌ 端口4000未监听');
    }
    
    if (netstat.includes(':8081')) {
      log('green', '✅ 端口8081正在监听');
    } else {
      log('red', '❌ 端口8081未监听');
    }
  } catch (error) {
    log('yellow', '⚠️ 无法检查端口状态');
  }
}

// 4. 检查API路由
async function checkAPIRoutes() {
  log('blue', '🔍 检查API路由...');
  
  const routes = [
    { name: '健康检查', url: `https://${PRODUCTION_CONFIG.domains.api}/api/health` },
    { name: '管理员登录', url: `https://${PRODUCTION_CONFIG.domains.api}/api/admin/auth/login`, method: 'POST' },
    { name: '分类列表', url: `https://${PRODUCTION_CONFIG.domains.api}/api/front/category/list` },
    { name: '轮播图列表', url: `https://${PRODUCTION_CONFIG.domains.api}/api/front/banner/list` }
  ];

  for (const route of routes) {
    try {
      const options = {
        method: route.method || 'GET'
      };
      
      if (route.method === 'POST') {
        options.data = { username: 'test', password: 'test' };
      }
      
      const response = await makeRequest(route.url, options);
      
      if (response.status === 200) {
        log('green', `✅ ${route.name} 路由正常`);
      } else if (response.status === 400 || response.status === 401) {
        log('green', `✅ ${route.name} 路由存在 (认证错误正常)`);
      } else if (response.status === 404) {
        log('red', `❌ ${route.name} 路由不存在 (404)`);
      } else {
        log('yellow', `⚠️ ${route.name} 响应异常 (${response.status})`);
      }
    } catch (error) {
      log('red', `❌ ${route.name} 请求失败: ${error.message}`);
    }
  }
}

// 5. 检查Nginx配置
async function checkNginx() {
  log('blue', '🔍 检查Nginx配置...');
  
  try {
    // 检查Nginx状态
    const nginxStatus = execSync('systemctl status nginx --no-pager', { encoding: 'utf8' });
    if (nginxStatus.includes('active (running)')) {
      log('green', '✅ Nginx服务运行正常');
    } else {
      log('red', '❌ Nginx服务异常');
    }
    
    // 测试Nginx配置
    const nginxTest = execSync('nginx -t', { encoding: 'utf8' });
    if (nginxTest.includes('syntax is ok')) {
      log('green', '✅ Nginx配置语法正确');
    } else {
      log('red', '❌ Nginx配置语法错误');
      console.log(nginxTest);
    }
  } catch (error) {
    log('red', `❌ Nginx检查失败: ${error.message}`);
  }
}

// 6. 检查日志
async function checkLogs() {
  log('blue', '🔍 检查应用日志...');
  
  const logFiles = [
    '/var/www/xinjie-tea/logs/api-error.log',
    '/var/www/xinjie-tea/logs/admin-error.log',
    '/var/log/nginx/error.log'
  ];

  for (const logFile of logFiles) {
    try {
      const logs = execSync(`tail -20 ${logFile}`, { encoding: 'utf8' });
      if (logs.trim()) {
        log('yellow', `⚠️ ${logFile} 最近错误:`);
        console.log(logs);
      } else {
        log('green', `✅ ${logFile} 无错误`);
      }
    } catch (error) {
      log('yellow', `⚠️ 无法读取日志: ${logFile}`);
    }
  }
}

// 7. 检查数据库连接
async function checkDatabase() {
  log('blue', '🔍 检查数据库连接...');
  
  try {
    const dbTest = execSync('mysql -u root -p"ZCaini10000nian!" -e "USE xinjie_mall; SELECT COUNT(*) FROM admin_users;"', { encoding: 'utf8' });
    if (dbTest.includes('COUNT')) {
      log('green', '✅ 数据库连接正常');
    } else {
      log('red', '❌ 数据库查询异常');
    }
  } catch (error) {
    log('red', `❌ 数据库连接失败: ${error.message}`);
  }
}

// 8. 生成修复建议
function generateFixSuggestions() {
  log('magenta', '🔧 修复建议:');
  console.log('');
  console.log('1. 🚀 重启服务:');
  console.log('   pm2 restart all');
  console.log('   systemctl restart nginx');
  console.log('');
  console.log('2. 🔍 查看详细日志:');
  console.log('   pm2 logs');
  console.log('   tail -f /var/log/nginx/error.log');
  console.log('');
  console.log('3. 🔧 重新部署:');
  console.log('   cd /var/www/xinjie-tea');
  console.log('   git pull');
  console.log('   pm2 restart all');
  console.log('');
  console.log('4. 🔐 重置管理员密码:');
  console.log('   node quick-admin-reset.js');
  console.log('');
  console.log('5. 🌐 测试访问:');
  console.log(`   curl https://${PRODUCTION_CONFIG.domains.api}/api/health`);
  console.log(`   curl -X POST https://${PRODUCTION_CONFIG.domains.api}/api/admin/auth/login`);
}

// 主函数
async function main() {
  console.log('🔍 心洁茶叶商城生产环境诊断工具');
  console.log('━'.repeat(60));
  console.log(`🌐 API域名: ${PRODUCTION_CONFIG.domains.api}`);
  console.log(`🖥️ 管理后台: ${PRODUCTION_CONFIG.domains.admin}`);
  console.log(`📁 部署路径: ${PRODUCTION_CONFIG.paths.deploy}`);
  console.log('━'.repeat(60));
  console.log('');

  try {
    await checkDNS();
    console.log('');
    
    await checkSSL();
    console.log('');
    
    await checkServices();
    console.log('');
    
    await checkAPIRoutes();
    console.log('');
    
    await checkNginx();
    console.log('');
    
    await checkDatabase();
    console.log('');
    
    await checkLogs();
    console.log('');
    
    generateFixSuggestions();
    
  } catch (error) {
    log('red', `❌ 诊断过程中发生错误: ${error.message}`);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { checkServices, checkAPIRoutes, checkNginx };
