#!/bin/bash

# ========================================
# 心洁茶叶商城生产环境快速修复脚本
# ========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
DEPLOY_PATH="/var/www/xinjie-tea"
API_DOMAIN="api.xinjie-tea.com"
ADMIN_DOMAIN="admin.xinjie-tea.com"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo bash quick-fix-production.sh"
        exit 1
    fi
}

# 1. 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 检查PM2进程
    if command -v pm2 &> /dev/null; then
        log_info "PM2进程状态:"
        pm2 status || true
    else
        log_warning "PM2未安装"
    fi
    
    # 检查端口监听
    log_info "检查端口监听状态:"
    netstat -tlnp | grep -E ":4000|:8081" || log_warning "API端口未监听"
    
    # 检查Nginx状态
    systemctl status nginx --no-pager || log_warning "Nginx状态异常"
}

# 2. 重启所有服务
restart_services() {
    log_info "重启所有服务..."
    
    # 重启PM2应用
    if command -v pm2 &> /dev/null; then
        log_info "重启PM2应用..."
        pm2 restart all || log_warning "PM2重启失败"
        pm2 status
    fi
    
    # 重启Nginx
    log_info "重启Nginx..."
    systemctl restart nginx
    systemctl status nginx --no-pager
    
    log_success "服务重启完成"
}

# 3. 检查和修复路由配置
fix_routes() {
    log_info "检查和修复路由配置..."
    
    cd $DEPLOY_PATH
    
    # 检查前端登录路由配置
    if [ -f "xinjie.mall-admin/src/pages/Login.jsx" ]; then
        log_info "检查前端登录路由..."
        
        # 确保使用正确的登录路由
        sed -i "s|'/admin/login'|'/admin/auth/login'|g" xinjie.mall-admin/src/pages/Login.jsx
        sed -i "s|'/admin/auth/login'|'/admin/auth/login'|g" xinjie.mall-admin/src/pages/Login.jsx
        
        log_success "前端路由配置已修复"
    fi
    
    # 重新构建前端
    if [ -d "xinjie.mall-admin" ]; then
        log_info "重新构建管理后台..."
        cd xinjie.mall-admin
        npm run build || log_warning "前端构建失败"
        cd ..
    fi
}

# 4. 检查数据库连接
check_database() {
    log_info "检查数据库连接..."
    
    if mysql -u root -p"ZCaini10000nian!" -e "USE xinjie_mall; SELECT COUNT(*) FROM admin_users;" &> /dev/null; then
        log_success "数据库连接正常"
        
        # 显示管理员账户信息
        log_info "管理员账户信息:"
        mysql -u root -p"ZCaini10000nian!" -D xinjie_mall -e "
        SELECT id, username, real_name, email, 
               CASE status WHEN 1 THEN '正常' ELSE '禁用' END as status
        FROM admin_users ORDER BY id;"
    else
        log_error "数据库连接失败"
        return 1
    fi
}

# 5. 测试API接口
test_api() {
    log_info "测试API接口..."
    
    # 等待服务启动
    sleep 5
    
    # 测试健康检查
    if curl -f -s "https://$API_DOMAIN/api/health" > /dev/null; then
        log_success "API健康检查正常"
    else
        log_error "API健康检查失败"
    fi
    
    # 测试登录接口
    response=$(curl -s -w "%{http_code}" -X POST "https://$API_DOMAIN/api/admin/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"username":"test","password":"test"}' -o /dev/null)
    
    if [ "$response" = "400" ] || [ "$response" = "401" ]; then
        log_success "登录接口存在 (认证错误正常)"
    elif [ "$response" = "404" ]; then
        log_error "登录接口不存在 (404)"
    else
        log_warning "登录接口响应异常 ($response)"
    fi
    
    # 测试管理后台
    if curl -f -s "https://$ADMIN_DOMAIN" > /dev/null; then
        log_success "管理后台访问正常"
    else
        log_error "管理后台访问失败"
    fi
}

# 6. 查看错误日志
check_logs() {
    log_info "检查错误日志..."
    
    # PM2日志
    if command -v pm2 &> /dev/null; then
        log_info "PM2错误日志 (最近10行):"
        pm2 logs --lines 10 --err || true
    fi
    
    # Nginx错误日志
    if [ -f "/var/log/nginx/error.log" ]; then
        log_info "Nginx错误日志 (最近10行):"
        tail -10 /var/log/nginx/error.log || true
    fi
    
    # 应用错误日志
    if [ -f "$DEPLOY_PATH/logs/api-error.log" ]; then
        log_info "API错误日志 (最近10行):"
        tail -10 $DEPLOY_PATH/logs/api-error.log || true
    fi
}

# 7. 重置管理员密码
reset_admin_password() {
    log_info "重置管理员密码..."
    
    cd $DEPLOY_PATH
    
    if [ -f "quick-admin-reset.js" ]; then
        log_info "使用快速重置工具..."
        node quick-admin-reset.js || log_warning "密码重置工具执行失败"
    else
        log_info "使用SQL直接重置..."
        mysql -u root -p"ZCaini10000nian!" -D xinjie_mall -e "
        UPDATE admin_users SET 
            password = '\$2b\$12\$rQJ8vHKzJX.Nh8LGvQJ8vOzJX.Nh8LGvQJ8vOzJX.Nh8LGvQJ8vO',
            password_changed_at = NOW(),
            password_expires_at = DATE_ADD(NOW(), INTERVAL 90 DAY),
            login_attempts = 0,
            status = 1,
            updated_at = NOW()
        WHERE username = 'admin';
        "
        
        if [ $? -eq 0 ]; then
            log_success "管理员密码重置成功！"
            log_info "新密码: Admin123!"
        else
            log_error "密码重置失败"
        fi
    fi
}

# 8. 显示诊断结果
show_results() {
    echo
    log_success "🎉 快速修复完成！"
    echo
    echo "📋 服务信息:"
    echo "  API服务: https://$API_DOMAIN"
    echo "  管理后台: https://$ADMIN_DOMAIN"
    echo "  健康检查: https://$API_DOMAIN/api/health"
    echo
    echo "🔑 管理员登录:"
    echo "  用户名: admin"
    echo "  密码: Admin123!"
    echo "  登录地址: https://$ADMIN_DOMAIN"
    echo
    echo "🔧 常用命令:"
    echo "  查看服务状态: pm2 status"
    echo "  查看日志: pm2 logs"
    echo "  重启服务: pm2 restart all"
    echo "  重启Nginx: systemctl restart nginx"
    echo
    echo "📞 如果问题仍然存在:"
    echo "  1. 检查防火墙设置: ufw status"
    echo "  2. 检查SSL证书: certbot certificates"
    echo "  3. 查看详细日志: tail -f /var/log/nginx/error.log"
    echo
}

# 主函数
main() {
    echo "========================================="
    echo "    心洁茶叶商城生产环境快速修复"
    echo "========================================="
    echo
    
    check_root
    
    log_info "开始快速修复流程..."
    
    check_services
    echo
    
    restart_services
    echo
    
    fix_routes
    echo
    
    check_database
    echo
    
    reset_admin_password
    echo
    
    test_api
    echo
    
    check_logs
    echo
    
    show_results
    
    log_success "🚀 修复完成！"
}

# 执行主函数
main "$@"
