#!/bin/bash

# ========================================
# 管理员密码快速重置脚本
# 使用方法: bash quick-reset-admin.sh
# ========================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_USER="root"
DB_PASSWORD="ZCaini10000nian!"
DB_NAME="xinjie_mall"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查MySQL是否可用
check_mysql() {
    log_info "检查MySQL连接..."
    
    if ! command -v mysql &> /dev/null; then
        log_error "MySQL客户端未安装"
        exit 1
    fi
    
    if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
        log_error "无法连接到数据库，请检查配置"
        exit 1
    fi
    
    log_success "数据库连接正常"
}

# 列出所有管理员
list_admins() {
    log_info "查询管理员列表..."
    
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -e "
    SELECT 
        id as 'ID',
        username as '用户名',
        COALESCE(real_name, '未设置') as '真实姓名',
        COALESCE(email, '未设置') as '邮箱',
        CASE status WHEN 1 THEN '正常' ELSE '禁用' END as '状态',
        CASE 
            WHEN password_expires_at < NOW() THEN '已过期'
            WHEN password_expires_at < DATE_ADD(NOW(), INTERVAL 7 DAY) THEN '即将过期'
            ELSE '正常'
        END as '密码状态',
        COALESCE(DATE_FORMAT(last_login_at, '%Y-%m-%d %H:%i'), '从未登录') as '最后登录'
    FROM admin_users 
    ORDER BY id ASC;
    "
}

# 生成随机密码
generate_password() {
    # 生成包含大小写字母、数字和特殊字符的12位密码
    local password=$(openssl rand -base64 12 | tr -d "=+/" | cut -c1-8)
    # 确保包含大写字母、小写字母、数字和特殊字符
    echo "${password}A1!"
}

# 重置密码
reset_password() {
    local username="$1"
    local new_password="$2"
    
    if [ -z "$username" ]; then
        log_error "用户名不能为空"
        return 1
    fi
    
    if [ -z "$new_password" ]; then
        log_info "未指定密码，将生成随机密码"
        new_password=$(generate_password)
        log_info "生成的随机密码: $new_password"
    fi
    
    log_info "开始重置用户 $username 的密码..."
    
    # 检查用户是否存在
    local user_exists=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -sN -e "
    SELECT COUNT(*) FROM admin_users WHERE username = '$username';
    ")
    
    if [ "$user_exists" -eq 0 ]; then
        log_error "用户 $username 不存在"
        return 1
    fi
    
    # 使用Node.js生成bcrypt哈希
    local hashed_password=$(node -e "
    const bcrypt = require('bcryptjs');
    const hash = bcrypt.hashSync('$new_password', 12);
    console.log(hash);
    " 2>/dev/null)
    
    if [ -z "$hashed_password" ]; then
        log_error "密码加密失败，请确保已安装bcryptjs"
        log_info "安装命令: npm install -g bcryptjs"
        return 1
    fi
    
    # 更新密码
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -e "
    UPDATE admin_users SET 
        password = '$hashed_password',
        password_changed_at = NOW(),
        password_expires_at = DATE_ADD(NOW(), INTERVAL 90 DAY),
        login_attempts = 0,
        updated_at = NOW()
    WHERE username = '$username';
    "
    
    if [ $? -eq 0 ]; then
        log_success "密码重置成功！"
        echo "=================================="
        echo "账户信息:"
        echo "  用户名: $username"
        echo "  新密码: $new_password"
        echo "  有效期: 90天"
        echo "=================================="
        log_warning "请妥善保管新密码，建议首次登录后立即修改"
    else
        log_error "密码重置失败"
        return 1
    fi
}

# 创建新管理员
create_admin() {
    local username="$1"
    local password="$2"
    local real_name="$3"
    local email="$4"
    
    if [ -z "$username" ]; then
        log_error "用户名不能为空"
        return 1
    fi
    
    if [ -z "$password" ]; then
        password=$(generate_password)
        log_info "生成的随机密码: $password"
    fi
    
    log_info "开始创建管理员 $username..."
    
    # 检查用户名是否已存在
    local user_exists=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -sN -e "
    SELECT COUNT(*) FROM admin_users WHERE username = '$username';
    ")
    
    if [ "$user_exists" -gt 0 ]; then
        log_error "用户名 $username 已存在"
        return 1
    fi
    
    # 生成密码哈希
    local hashed_password=$(node -e "
    const bcrypt = require('bcryptjs');
    const hash = bcrypt.hashSync('$password', 12);
    console.log(hash);
    " 2>/dev/null)
    
    if [ -z "$hashed_password" ]; then
        log_error "密码加密失败，请确保已安装bcryptjs"
        return 1
    fi
    
    # 创建管理员
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -e "
    INSERT INTO admin_users (
        username, password, real_name, email, role_id, status,
        password_changed_at, password_expires_at, created_at, updated_at
    ) VALUES (
        '$username', '$hashed_password', 
        $([ -n "$real_name" ] && echo "'$real_name'" || echo "NULL"), 
        $([ -n "$email" ] && echo "'$email'" || echo "NULL"), 
        1, 1, NOW(), DATE_ADD(NOW(), INTERVAL 90 DAY), NOW(), NOW()
    );
    "
    
    if [ $? -eq 0 ]; then
        log_success "管理员创建成功！"
        echo "=================================="
        echo "账户信息:"
        echo "  用户名: $username"
        echo "  密码: $password"
        echo "  真实姓名: ${real_name:-未设置}"
        echo "  邮箱: ${email:-未设置}"
        echo "  角色: 超级管理员"
        echo "  状态: 正常"
        echo "=================================="
    else
        log_error "管理员创建失败"
        return 1
    fi
}

# 显示菜单
show_menu() {
    echo "=================================="
    echo "    管理员密码重置工具"
    echo "=================================="
    echo "1. 列出所有管理员"
    echo "2. 重置管理员密码"
    echo "3. 创建新管理员"
    echo "4. 退出"
    echo "=================================="
}

# 主函数
main() {
    log_info "启动管理员密码重置工具..."
    
    # 检查MySQL连接
    check_mysql
    
    while true; do
        echo
        show_menu
        read -p "请选择操作 (1-4): " choice
        
        case $choice in
            1)
                echo
                list_admins
                ;;
            2)
                echo
                read -p "请输入要重置密码的用户名: " username
                read -p "请输入新密码 (留空自动生成): " password
                echo
                reset_password "$username" "$password"
                ;;
            3)
                echo
                read -p "请输入新用户名: " username
                read -p "请输入密码 (留空自动生成): " password
                read -p "请输入真实姓名 (可选): " real_name
                read -p "请输入邮箱 (可选): " email
                echo
                create_admin "$username" "$password" "$real_name" "$email"
                ;;
            4)
                log_info "退出程序"
                exit 0
                ;;
            *)
                log_error "无效选择，请输入 1-4"
                ;;
        esac
        
        echo
        read -p "按回车键继续..." -r
    done
}

# 命令行参数处理
if [ $# -gt 0 ]; then
    case "$1" in
        "reset")
            check_mysql
            reset_password "$2" "$3"
            ;;
        "create")
            check_mysql
            create_admin "$2" "$3" "$4" "$5"
            ;;
        "list")
            check_mysql
            list_admins
            ;;
        *)
            echo "用法:"
            echo "  交互模式: bash quick-reset-admin.sh"
            echo "  重置密码: bash quick-reset-admin.sh reset <username> [password]"
            echo "  创建管理员: bash quick-reset-admin.sh create <username> [password] [real_name] [email]"
            echo "  列出管理员: bash quick-reset-admin.sh list"
            ;;
    esac
else
    main
fi
