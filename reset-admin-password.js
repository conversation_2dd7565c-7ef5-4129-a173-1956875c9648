#!/usr/bin/env node

/**
 * 管理员密码重置工具
 * 使用方法：
 * node reset-admin-password.js <username> <new-password>
 * 
 * 示例：
 * node reset-admin-password.js admin NewPassword123!
 */

const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
const path = require('path');

class AdminPasswordReset {
  constructor() {
    // 数据库配置
    this.dbConfig = {
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'ZCaini10000nian!',
      database: 'xinjie_mall',
      charset: 'utf8mb4'
    };
  }

  async createConnection() {
    try {
      const connection = await mysql.createConnection(this.dbConfig);
      console.log('✅ 数据库连接成功');
      return connection;
    } catch (error) {
      console.error('❌ 数据库连接失败:', error.message);
      throw error;
    }
  }

  // 验证密码强度
  validatePassword(password) {
    const errors = [];
    
    if (password.length < 8) {
      errors.push('密码长度至少8位');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('密码必须包含小写字母');
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('密码必须包含大写字母');
    }
    
    if (!/\d/.test(password)) {
      errors.push('密码必须包含数字');
    }
    
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('密码必须包含特殊字符');
    }
    
    return errors;
  }

  // 重置管理员密码
  async resetPassword(username, newPassword) {
    const connection = await this.createConnection();

    try {
      console.log(`🔧 开始重置用户 ${username} 的密码...`);

      // 1. 验证密码强度
      const passwordErrors = this.validatePassword(newPassword);
      if (passwordErrors.length > 0) {
        console.error('❌ 密码不符合要求:');
        passwordErrors.forEach(error => console.error(`   - ${error}`));
        return false;
      }

      // 2. 检查用户是否存在
      const [users] = await connection.execute(
        'SELECT id, username, real_name FROM admin_users WHERE username = ?',
        [username]
      );

      if (users.length === 0) {
        console.error(`❌ 用户 ${username} 不存在`);
        return false;
      }

      const user = users[0];
      console.log(`📋 找到用户: ${user.username} (${user.real_name || '未设置姓名'})`);

      // 3. 加密新密码
      console.log('🔐 正在加密新密码...');
      const hashedPassword = await bcrypt.hash(newPassword, 12);

      // 4. 更新密码
      const [result] = await connection.execute(
        `UPDATE admin_users SET 
         password = ?, 
         password_changed_at = NOW(),
         password_expires_at = DATE_ADD(NOW(), INTERVAL 90 DAY),
         login_attempts = 0,
         updated_at = NOW()
         WHERE username = ?`,
        [hashedPassword, username]
      );

      if (result.affectedRows > 0) {
        console.log('✅ 密码重置成功！');
        console.log('📋 更新信息:');
        console.log(`   - 用户名: ${username}`);
        console.log(`   - 密码已更新`);
        console.log(`   - 密码有效期: 90天`);
        console.log(`   - 登录失败次数已重置`);
        return true;
      } else {
        console.error('❌ 密码更新失败');
        return false;
      }

    } catch (error) {
      console.error('❌ 重置密码时发生错误:', error.message);
      return false;
    } finally {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }

  // 列出所有管理员
  async listAdmins() {
    const connection = await this.createConnection();

    try {
      console.log('📋 查询所有管理员账户...');

      const [admins] = await connection.execute(`
        SELECT 
          id, username, real_name, email, phone, 
          status, created_at, last_login_at,
          CASE 
            WHEN password_expires_at < NOW() THEN '已过期'
            WHEN password_expires_at < DATE_ADD(NOW(), INTERVAL 7 DAY) THEN '即将过期'
            ELSE '正常'
          END as password_status
        FROM admin_users 
        ORDER BY id ASC
      `);

      if (admins.length === 0) {
        console.log('❌ 没有找到管理员账户');
        return;
      }

      console.log('\n📊 管理员账户列表:');
      console.log('─'.repeat(100));
      console.log('ID\t用户名\t\t真实姓名\t\t邮箱\t\t\t状态\t密码状态\t最后登录');
      console.log('─'.repeat(100));

      admins.forEach(admin => {
        const status = admin.status === 1 ? '正常' : '禁用';
        const lastLogin = admin.last_login_at ? 
          new Date(admin.last_login_at).toLocaleString('zh-CN') : '从未登录';
        
        console.log(
          `${admin.id}\t${admin.username.padEnd(12)}\t${(admin.real_name || '未设置').padEnd(12)}\t` +
          `${(admin.email || '未设置').padEnd(20)}\t${status}\t${admin.password_status.padEnd(8)}\t${lastLogin}`
        );
      });

      console.log('─'.repeat(100));

    } catch (error) {
      console.error('❌ 查询管理员列表时发生错误:', error.message);
    } finally {
      await connection.end();
    }
  }

  // 创建新管理员
  async createAdmin(username, password, realName, email) {
    const connection = await this.createConnection();

    try {
      console.log(`🔧 开始创建管理员 ${username}...`);

      // 1. 验证密码强度
      const passwordErrors = this.validatePassword(password);
      if (passwordErrors.length > 0) {
        console.error('❌ 密码不符合要求:');
        passwordErrors.forEach(error => console.error(`   - ${error}`));
        return false;
      }

      // 2. 检查用户名是否已存在
      const [existingUsers] = await connection.execute(
        'SELECT username FROM admin_users WHERE username = ?',
        [username]
      );

      if (existingUsers.length > 0) {
        console.error(`❌ 用户名 ${username} 已存在`);
        return false;
      }

      // 3. 检查邮箱是否已存在
      if (email) {
        const [existingEmails] = await connection.execute(
          'SELECT email FROM admin_users WHERE email = ?',
          [email]
        );

        if (existingEmails.length > 0) {
          console.error(`❌ 邮箱 ${email} 已存在`);
          return false;
        }
      }

      // 4. 加密密码
      console.log('🔐 正在加密密码...');
      const hashedPassword = await bcrypt.hash(password, 12);

      // 5. 创建管理员
      const [result] = await connection.execute(
        `INSERT INTO admin_users (
          username, password, real_name, email, role_id, status,
          password_changed_at, password_expires_at, created_at, updated_at
        ) VALUES (?, ?, ?, ?, 1, 1, NOW(), DATE_ADD(NOW(), INTERVAL 90 DAY), NOW(), NOW())`,
        [username, hashedPassword, realName, email]
      );

      if (result.insertId) {
        console.log('✅ 管理员创建成功！');
        console.log('📋 账户信息:');
        console.log(`   - ID: ${result.insertId}`);
        console.log(`   - 用户名: ${username}`);
        console.log(`   - 真实姓名: ${realName || '未设置'}`);
        console.log(`   - 邮箱: ${email || '未设置'}`);
        console.log(`   - 角色: 超级管理员`);
        console.log(`   - 状态: 正常`);
        return true;
      } else {
        console.error('❌ 管理员创建失败');
        return false;
      }

    } catch (error) {
      console.error('❌ 创建管理员时发生错误:', error.message);
      return false;
    } finally {
      await connection.end();
    }
  }

  // 显示帮助信息
  showHelp() {
    console.log('🔐 管理员密码重置工具');
    console.log('');
    console.log('使用方法:');
    console.log('  重置密码:   node reset-admin-password.js reset <username> <new-password>');
    console.log('  列出管理员: node reset-admin-password.js list');
    console.log('  创建管理员: node reset-admin-password.js create <username> <password> [real-name] [email]');
    console.log('  显示帮助:   node reset-admin-password.js help');
    console.log('');
    console.log('密码要求:');
    console.log('  - 长度至少8位');
    console.log('  - 包含大小写字母');
    console.log('  - 包含数字');
    console.log('  - 包含特殊字符 (!@#$%^&*(),.?":{}|<>)');
    console.log('');
    console.log('示例:');
    console.log('  node reset-admin-password.js reset admin NewPassword123!');
    console.log('  node reset-admin-password.js create newadmin Password123! "新管理员" "<EMAIL>"');
    console.log('  node reset-admin-password.js list');
  }
}

// 命令行参数处理
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  const resetTool = new AdminPasswordReset();

  try {
    switch (command) {
      case 'reset':
        const username = args[1];
        const password = args[2];
        if (!username || !password) {
          console.error('❌ 缺少参数');
          console.log('用法: node reset-admin-password.js reset <username> <new-password>');
          process.exit(1);
        }
        await resetTool.resetPassword(username, password);
        break;

      case 'list':
        await resetTool.listAdmins();
        break;

      case 'create':
        const newUsername = args[1];
        const newPassword = args[2];
        const realName = args[3];
        const email = args[4];
        if (!newUsername || !newPassword) {
          console.error('❌ 缺少参数');
          console.log('用法: node reset-admin-password.js create <username> <password> [real-name] [email]');
          process.exit(1);
        }
        await resetTool.createAdmin(newUsername, newPassword, realName, email);
        break;

      case 'help':
      default:
        resetTool.showHelp();
        break;
    }
  } catch (error) {
    console.error('❌ 执行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = AdminPasswordReset;
