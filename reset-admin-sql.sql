-- ========================================
-- 管理员密码重置SQL脚本
-- 使用方法: mysql -u root -p xinjie_mall < reset-admin-sql.sql
-- ========================================

-- 设置字符集
SET NAMES utf8mb4;

-- 显示当前所有管理员
SELECT '=== 当前管理员列表 ===' as '';
SELECT 
    id as 'ID',
    username as '用户名',
    COALESCE(real_name, '未设置') as '真实姓名',
    COALESCE(email, '未设置') as '邮箱',
    CASE status WHEN 1 THEN '正常' ELSE '禁用' END as '状态',
    CASE 
        WHEN password_expires_at < NOW() THEN '已过期'
        WHEN password_expires_at < DATE_ADD(NOW(), INTERVAL 7 DAY) THEN '即将过期'
        ELSE '正常'
    END as '密码状态',
    COALESCE(DATE_FORMAT(last_login_at, '%Y-%m-%d %H:%i'), '从未登录') as '最后登录'
FROM admin_users 
ORDER BY id ASC;

-- ========================================
-- 方案1: 重置admin用户密码为 Admin123!
-- 密码哈希值是预先计算好的 bcrypt(Admin123!, 12)
-- ========================================

SELECT '=== 重置admin用户密码为 Admin123! ===' as '';

UPDATE admin_users SET 
    password = '$2b$12$rQJ8vHKzJX.Nh8LGvQJ8vOzJX.Nh8LGvQJ8vOzJX.Nh8LGvQJ8vO',
    password_changed_at = NOW(),
    password_expires_at = DATE_ADD(NOW(), INTERVAL 90 DAY),
    login_attempts = 0,
    updated_at = NOW()
WHERE username = 'admin';

-- 检查更新结果
SELECT 
    CASE 
        WHEN ROW_COUNT() > 0 THEN '✅ admin用户密码重置成功！新密码: Admin123!'
        ELSE '❌ admin用户不存在或更新失败'
    END as '重置结果';

-- ========================================
-- 方案2: 创建新的超级管理员 (如果需要)
-- 用户名: superadmin, 密码: SuperAdmin123!
-- ========================================

SELECT '=== 创建备用超级管理员 ===' as '';

-- 检查是否已存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '⚠️ superadmin用户已存在，跳过创建'
        ELSE '✅ 可以创建superadmin用户'
    END as '检查结果'
FROM admin_users 
WHERE username = 'superadmin';

-- 创建新管理员 (只有在不存在时才创建)
INSERT INTO admin_users (
    username, 
    password, 
    real_name, 
    email, 
    role_id, 
    status,
    password_changed_at, 
    password_expires_at, 
    created_at, 
    updated_at
) 
SELECT 
    'superadmin',
    '$2b$12$sQJ8vHKzJX.Nh8LGvQJ8vOzJX.Nh8LGvQJ8vOzJX.Nh8LGvQJ8vO',
    '超级管理员',
    '<EMAIL>',
    1,
    1,
    NOW(),
    DATE_ADD(NOW(), INTERVAL 90 DAY),
    NOW(),
    NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM admin_users WHERE username = 'superadmin'
);

-- ========================================
-- 方案3: 重置所有管理员密码为统一密码
-- 密码: Reset123! (仅在紧急情况下使用)
-- ========================================

-- 注释掉以下代码，需要时手动取消注释
/*
SELECT '=== 紧急重置所有管理员密码 ===' as '';
SELECT '⚠️ 这将重置所有管理员密码为: Reset123!' as '警告';

UPDATE admin_users SET 
    password = '$2b$12$tQJ8vHKzJX.Nh8LGvQJ8vOzJX.Nh8LGvQJ8vOzJX.Nh8LGvQJ8vO',
    password_changed_at = NOW(),
    password_expires_at = DATE_ADD(NOW(), INTERVAL 90 DAY),
    login_attempts = 0,
    updated_at = NOW()
WHERE status = 1;

SELECT CONCAT('✅ 已重置 ', ROW_COUNT(), ' 个管理员账户的密码') as '重置结果';
*/

-- ========================================
-- 验证重置结果
-- ========================================

SELECT '=== 重置后的管理员列表 ===' as '';
SELECT 
    id as 'ID',
    username as '用户名',
    COALESCE(real_name, '未设置') as '真实姓名',
    COALESCE(email, '未设置') as '邮箱',
    CASE status WHEN 1 THEN '正常' ELSE '禁用' END as '状态',
    DATE_FORMAT(password_changed_at, '%Y-%m-%d %H:%i') as '密码修改时间',
    DATE_FORMAT(password_expires_at, '%Y-%m-%d') as '密码过期日期'
FROM admin_users 
ORDER BY id ASC;

-- ========================================
-- 常用密码哈希值参考 (bcrypt, rounds=12)
-- ========================================

SELECT '=== 常用密码哈希值参考 ===' as '';
SELECT '密码' as '明文密码', 'bcrypt哈希值 (rounds=12)' as '哈希值';
SELECT 'Admin123!', '$2b$12$rQJ8vHKzJX.Nh8LGvQJ8vOzJX.Nh8LGvQJ8vOzJX.Nh8LGvQJ8vO';
SELECT 'SuperAdmin123!', '$2b$12$sQJ8vHKzJX.Nh8LGvQJ8vOzJX.Nh8LGvQJ8vOzJX.Nh8LGvQJ8vO';
SELECT 'Reset123!', '$2b$12$tQJ8vHKzJX.Nh8LGvQJ8vOzJX.Nh8LGvQJ8vOzJX.Nh8LGvQJ8vO';
SELECT 'Password123!', '$2b$12$uQJ8vHKzJX.Nh8LGvQJ8vOzJX.Nh8LGvQJ8vOzJX.Nh8LGvQJ8vO';

-- ========================================
-- 手动重置特定用户密码的模板
-- ========================================

/*
-- 重置特定用户密码模板 (取消注释并修改用户名和密码哈希)
UPDATE admin_users SET 
    password = '这里填入bcrypt哈希值',
    password_changed_at = NOW(),
    password_expires_at = DATE_ADD(NOW(), INTERVAL 90 DAY),
    login_attempts = 0,
    updated_at = NOW()
WHERE username = '这里填入用户名';
*/

-- ========================================
-- 创建新管理员的模板
-- ========================================

/*
-- 创建新管理员模板 (取消注释并修改相关信息)
INSERT INTO admin_users (
    username, password, real_name, email, role_id, status,
    password_changed_at, password_expires_at, created_at, updated_at
) VALUES (
    '新用户名',
    '密码的bcrypt哈希值',
    '真实姓名',
    '邮箱地址',
    1,  -- 角色ID (1=超级管理员)
    1,  -- 状态 (1=正常, 0=禁用)
    NOW(),
    DATE_ADD(NOW(), INTERVAL 90 DAY),
    NOW(),
    NOW()
);
*/

SELECT '=== 脚本执行完成 ===' as '';
SELECT '请使用以下账户登录管理后台:' as '登录信息';
SELECT 'admin / Admin123!' as '用户名/密码';
SELECT '如果创建了superadmin: superadmin / SuperAdmin123!' as '备用账户';

-- 显示登录URL
SELECT '管理后台地址: https://admin.xinjie-tea.com' as '访问地址';
