# 🔒 SSL证书配置指南

## 📋 SSL证书文件准备

### 1. 确认您的SSL证书文件
您购买的SSL证书通常包含以下文件：
- **证书文件** (通常命名为 `domain.crt` 或 `domain.pem`)
- **私钥文件** (通常命名为 `domain.key` 或 `private.key`)
- **证书链文件** (可选，通常命名为 `ca-bundle.crt` 或 `fullchain.pem`)

### 2. 文件格式要求
- 证书文件格式：`.pem` 或 `.crt`
- 私钥文件格式：`.key` 或 `.pem`
- 如果是其他格式，需要转换为PEM格式

## 🚀 上传SSL证书到服务器

### 方法一：使用SCP命令上传（推荐）

#### Windows用户（PowerShell）：
```bash
# 上传证书文件
scp C:\path\to\your-cert.pem root@121.199.72.228:/etc/ssl/xinjie-tea/cert.pem

# 上传私钥文件
scp C:\path\to\your-key.key root@121.199.72.228:/etc/ssl/xinjie-tea/key.pem

# 如果有证书链文件
scp C:\path\to\ca-bundle.crt root@121.199.72.228:/etc/ssl/xinjie-tea/fullchain.pem
```

#### Mac/Linux用户：
```bash
# 上传证书文件
scp /path/to/your-cert.pem root@121.199.72.228:/etc/ssl/xinjie-tea/cert.pem

# 上传私钥文件
scp /path/to/your-key.key root@121.199.72.228:/etc/ssl/xinjie-tea/key.pem

# 如果有证书链文件
scp /path/to/ca-bundle.crt root@121.199.72.228:/etc/ssl/xinjie-tea/fullchain.pem
```

### 方法二：使用FTP工具上传

#### 推荐工具：
- **Windows**: WinSCP、FileZilla
- **Mac**: FileZilla、Cyberduck

#### 连接信息：
- **主机**: 121.199.72.228
- **用户名**: root
- **端口**: 22
- **协议**: SFTP

#### 上传路径：
将证书文件上传到服务器的 `/etc/ssl/xinjie-tea/` 目录：
- 证书文件 → `/etc/ssl/xinjie-tea/cert.pem`
- 私钥文件 → `/etc/ssl/xinjie-tea/key.pem`
- 证书链文件 → `/etc/ssl/xinjie-tea/fullchain.pem`

### 方法三：直接在服务器上创建文件

```bash
# 连接到服务器
ssh root@121.199.72.228

# 创建SSL目录
mkdir -p /etc/ssl/xinjie-tea

# 创建证书文件
nano /etc/ssl/xinjie-tea/cert.pem
# 复制粘贴您的证书内容

# 创建私钥文件
nano /etc/ssl/xinjie-tea/key.pem
# 复制粘贴您的私钥内容

# 设置文件权限
chmod 600 /etc/ssl/xinjie-tea/*.pem
chown root:root /etc/ssl/xinjie-tea/*.pem
```

## 🔍 证书文件验证

### 1. 检查文件是否存在
```bash
ls -la /etc/ssl/xinjie-tea/
```

应该看到：
```
-rw------- 1 <USER> <GROUP> cert.pem
-rw------- 1 <USER> <GROUP> key.pem
```

### 2. 验证证书内容
```bash
# 查看证书信息
openssl x509 -in /etc/ssl/xinjie-tea/cert.pem -text -noout

# 验证私钥
openssl rsa -in /etc/ssl/xinjie-tea/key.pem -check

# 验证证书和私钥匹配
openssl x509 -noout -modulus -in /etc/ssl/xinjie-tea/cert.pem | openssl md5
openssl rsa -noout -modulus -in /etc/ssl/xinjie-tea/key.pem | openssl md5
# 两个命令的输出应该相同
```

## 🌐 HTTPS网关配置

### 如果您使用的是云服务商的HTTPS网关：

#### 阿里云SLB/CLB：
1. 在负载均衡控制台上传SSL证书
2. 配置HTTPS监听器
3. 后端服务器使用HTTP协议
4. 修改Nginx配置，移除SSL配置

#### 腾讯云CLB：
1. 在证书管理中上传SSL证书
2. 在CLB中配置HTTPS监听器
3. 后端转发使用HTTP协议

#### 华为云ELB：
1. 在证书管理服务中上传证书
2. 在ELB中配置HTTPS监听器
3. 后端服务器组使用HTTP协议

### 如果使用网关，需要修改部署脚本：

```bash
# 在auto-deploy.sh中找到configure_nginx函数
# 将HTTPS配置改为HTTP配置，因为SSL在网关层处理

# 修改后的server配置示例：
server {
    listen 80;
    server_name api.xinjie-tea.com;
    
    # 信任来自网关的X-Forwarded-Proto头
    set $forwarded_proto $http_x_forwarded_proto;
    if ($forwarded_proto = '') {
        set $forwarded_proto $scheme;
    }
    
    location /api/ {
        proxy_pass http://xinjie_api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $forwarded_proto;
    }
}
```

## ⚠️ 重要提醒

### 1. 证书域名匹配
确保SSL证书包含以下域名：
- `api.xinjie-tea.com`
- `admin.xinjie-tea.com`

### 2. 证书有效期
- 检查证书有效期，设置到期提醒
- 商业证书通常有效期1-2年

### 3. 安全注意事项
- 私钥文件权限必须是600
- 不要将私钥文件提交到代码仓库
- 定期备份证书文件

### 4. 测试SSL配置
部署完成后，使用以下工具测试：
- SSL Labs: https://www.ssllabs.com/ssltest/
- 浏览器访问检查绿锁图标

## 🚀 继续部署

SSL证书配置完成后，您可以继续执行部署脚本：

```bash
# 确保证书文件已上传
ls -la /etc/ssl/xinjie-tea/

# 继续执行部署脚本
sudo bash auto-deploy.sh
```

部署脚本会在SSL配置阶段暂停，等待您上传证书文件，上传完成后按回车继续。

## 📞 需要帮助？

如果在SSL证书配置过程中遇到问题：
1. 检查证书文件格式是否正确
2. 确认域名是否匹配
3. 验证文件权限设置
4. 查看Nginx错误日志：`tail -f /var/log/nginx/error.log`
