@echo off
title Xinjie Tea Mall - Start All Services

echo.
echo ========================================
echo    Xinjie Tea Mall - Start All Services
echo ========================================
echo.
echo Admin Backend: http://localhost:8081
echo MiniProgram API: http://localhost:4000
echo HTTPS API: https://localhost:4443
echo.

echo [INFO] Checking project directories...
if not exist "mall-server" (
    echo [ERROR] mall-server directory not found
    pause
    exit /b 1
)

if not exist "xinjie.mall-admin" (
    echo [ERROR] xinjie.mall-admin directory not found
    pause
    exit /b 1
)

echo [SUCCESS] Project directories found
echo.

echo [INFO] Starting MiniProgram API service...
start "MiniProgram API" cmd /k "cd mall-server && echo Starting MiniProgram API... && node app-https.js"

echo [INFO] Waiting 3 seconds before starting admin backend...
timeout /t 3 /nobreak >nul

echo [INFO] Starting Admin Backend service...
start "Admin Backend" cmd /k "cd xinjie.mall-admin && echo Starting Admin Backend... && npm run dev"

echo.
echo ========================================
echo           Services Started!
echo ========================================
echo.
echo MiniProgram Dev: http://localhost:4000
echo MiniProgram Prod: https://localhost:4443
echo Admin Backend: http://localhost:8081
echo Admin Frontend: http://localhost:3000 (auto-open)
echo.
echo [SUCCESS] All services started!
echo [INFO] MiniProgram API: http://localhost:4000/api/
echo [INFO] Admin Backend: http://localhost:8081
echo [WARNING] Close service windows to stop services
echo.

echo Press any key to close this window...
pause >nul
