// app.js
import { autoLogin } from './utils/auth.js';

App({
  globalData: {
    userInfo: null,
    isLoggedIn: false,
    isNewUser: false,
    systemInfo: null,
  },

  onLaunch() {
    console.log('小程序启动');

    // 获取系统信息
    this.getSystemInfo();
    
    // 执行自动登录
    this.performAutoLogin();
  },

  /**
   * 获取系统信息
   */
  getSystemInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      this.globalData.systemInfo = systemInfo;
      console.log('系统信息:', systemInfo);
    } catch (error) {
      console.error('获取系统信息失败:', error);
    }
  },

  /**
   * 执行自动登录
   */
  async performAutoLogin() {
    try {
      console.log('开始自动登录...');
      
      const loginResult = await autoLogin();

      this.globalData.isLoggedIn = loginResult.isLoggedIn;
      this.globalData.userInfo = loginResult.userInfo;
      this.globalData.isNewUser = loginResult.isNewUser;
      
      console.log('自动登录结果:', loginResult);
      
      if (loginResult.isLoggedIn) {
        if (loginResult.isNewUser) {
          console.log('新用户登录成功');
          // 可以在这里显示新用户引导
        } else {
          console.log('老用户登录成功');
        }
      } else {
        console.log('自动登录失败，用户未登录');
      }
    } catch (error) {
      console.error('自动登录异常:', error);
      this.globalData.isLoggedIn = false;
    }
  },

  /**
   * 更新用户信息
   */
  updateUserInfo(userInfo) {
    this.globalData.userInfo = userInfo;
    this.globalData.isNewUser = false;
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    return this.globalData.isLoggedIn;
  },

  /**
   * 获取用户信息
   */
  getUserInfo() {
    return this.globalData.userInfo;
  },

  /**
   * 检查是否为新用户
   */
  isNewUser() {
    return this.globalData.isNewUser;
  },
});
