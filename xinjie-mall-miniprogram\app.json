{"pages": ["pages/index/index", "pages/category/category", "pages/product-list/product-list", "pages/product-detail/product-detail", "pages/cart/cart", "pages/order-confirm/order-confirm", "pages/order-list/order-list", "pages/order-detail/order-detail", "pages/address-list/address-list", "pages/address-edit/address-edit", "pages/user/user", "pages/login/login", "pages/search/search", "pages/payment/payment", "pages/recharge/recharge", "pages/balance-history/balance-history", "pages/member-center/member-center", "pages/return-apply/return-apply", "pages/return-list/return-list"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#86efac", "navigationBarTitleText": "心洁茗茶商城", "navigationBarTextStyle": "white", "backgroundColor": "#f0fdf4"}, "tabBar": {"color": "#999999", "selectedColor": "#059669", "backgroundColor": "#ffffff", "borderStyle": "black", "list": [{"pagePath": "pages/index/index", "text": "首页🏠"}, {"pagePath": "pages/category/category", "text": "分类📂"}, {"pagePath": "pages/cart/cart", "text": "购物车🛒"}, {"pagePath": "pages/user/user", "text": "我的👤"}]}, "networkTimeout": {"request": 10000, "downloadFile": 10000}, "debug": true, "sitemapLocation": "sitemap.json", "style": "v2", "useExtendedLib": {"kbone": true, "weui": true}, "lazyCodeLoading": "requiredComponents"}