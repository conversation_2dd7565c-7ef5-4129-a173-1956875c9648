/* 引入全局样式 */
@import "./styles/global.wxss";
@import "./styles/variables.wxss";
@import "./styles/common.wxss";
@import "./styles/modern-buttons.wxss";

/* 全局样式重置 */
page {
  background: var(--background-gradient);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  color: #1f2937;
  line-height: 1.6;
}

/* 去除默认边距 */
view, text, image {
  box-sizing: border-box;
}

/* 去除按钮默认样式 */
button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  line-height: 1;
}

button::after {
  border: none;
}