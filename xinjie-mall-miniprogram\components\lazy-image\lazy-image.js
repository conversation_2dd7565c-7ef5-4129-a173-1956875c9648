Component({
  properties: {
    // 图片URL
    src: {
      type: String,
      value: ''
    },
    // 占位图URL
    placeholder: {
      type: String,
      value: '/images/placeholder.png'
    },
    // 图片模式
    mode: {
      type: String,
      value: 'aspectFill'
    },
    // 图片尺寸
    size: {
      type: String,
      value: 'medium' // thumbnail, small, medium, large
    },
    // 是否启用CDN
    useCdn: {
      type: Boolean,
      value: true
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    },
    // 图片宽度
    width: {
      type: String,
      value: '100%'
    },
    // 图片高度
    height: {
      type: String,
      value: 'auto'
    }
  },

  data: {
    imageUrl: '',
    isLoading: true,
    loadError: false,
    isInView: false,
    observer: null
  },

  lifetimes: {
    attached() {
      this.initImageUrl();
      this.initIntersectionObserver();
    },

    detached() {
      if (this.data.observer) {
        this.data.observer.disconnect();
      }
    }
  },

  methods: {
    /**
     * 初始化图片URL
     */
    initImageUrl() {
      const { src, size, useCdn } = this.properties;
      
      if (!src) {
        this.setData({
          imageUrl: this.properties.placeholder,
          isLoading: false
        });
        return;
      }

      // 如果启用CDN，转换图片URL
      if (useCdn) {
        this.setData({
          imageUrl: this.getCdnUrl(src, size)
        });
      } else {
        this.setData({
          imageUrl: src
        });
      }
    },

    /**
     * 获取CDN图片URL
     */
    getCdnUrl(originalUrl, size = 'medium') {
      if (!originalUrl) return '';
      
      // CDN配置
      const cdnConfig = {
        domain: 'https://cdn.xinjie-tea.com',
        imagePath: '/images',
        sizes: {
          thumbnail: '150x150',
          small: '300x300',
          medium: '600x600',
          large: '1200x1200'
        }
      };

      // 如果已经是CDN URL，直接返回
      if (originalUrl.startsWith(cdnConfig.domain)) {
        return originalUrl;
      }

      // 提取文件名
      const urlParts = originalUrl.split('/');
      const fileName = urlParts[urlParts.length - 1];
      
      // 获取尺寸配置
      const imageSize = cdnConfig.sizes[size] || cdnConfig.sizes.medium;
      
      // 构建CDN URL
      return `${cdnConfig.domain}${cdnConfig.imagePath}/${imageSize}/${fileName}`;
    },

    /**
     * 初始化交叉观察器
     */
    initIntersectionObserver() {
      if (typeof wx.createIntersectionObserver === 'function') {
        const observer = wx.createIntersectionObserver(this, {
          thresholds: [0.1] // 当元素10%进入视口时触发
        });

        observer.relativeToViewport().observe('.lazy-image', (res) => {
          if (res.intersectionRatio > 0 && !this.data.isInView) {
            this.setData({ isInView: true });
            this.loadImage();
          }
        });

        this.setData({ observer });
      } else {
        // 降级处理：直接加载图片
        this.setData({ isInView: true });
        this.loadImage();
      }
    },

    /**
     * 加载图片
     */
    loadImage() {
      const { imageUrl } = this.data;
      
      if (!imageUrl || imageUrl === this.properties.placeholder) {
        this.setData({ isLoading: false });
        return;
      }

      // 预加载图片
      wx.getImageInfo({
        src: imageUrl,
        success: () => {
          this.setData({
            isLoading: false,
            loadError: false
          });
          this.triggerEvent('load', { url: imageUrl });
        },
        fail: (error) => {
          console.error('图片加载失败:', error);
          this.setData({
            isLoading: false,
            loadError: true,
            imageUrl: this.properties.placeholder
          });
          this.triggerEvent('error', { error, url: imageUrl });
        }
      });
    },

    /**
     * 图片点击事件
     */
    onImageTap() {
      this.triggerEvent('tap', { url: this.data.imageUrl });
    },

    /**
     * 图片长按事件
     */
    onImageLongTap() {
      this.triggerEvent('longtap', { url: this.data.imageUrl });
    }
  }
}); 