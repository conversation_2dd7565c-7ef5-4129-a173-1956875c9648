<view class="lazy-image-container" style="width: {{width}}; height: {{height}}; {{customStyle}}">
  <!-- 加载中状态 -->
  <view wx:if="{{isLoading}}" class="lazy-image-loading">
    <view class="loading-spinner"></view>
  </view>
  
  <!-- 图片 -->
  <image 
    wx:if="{{!isLoading && !loadError}}"
    class="lazy-image {{isInView ? 'lazy-image-loaded' : ''}}"
    src="{{imageUrl}}"
    mode="{{mode}}"
    lazy-load="{{true}}"
    bind:tap="onImageTap"
    bind:longtap="onImageLongTap"
    bind:load="onImageLoad"
    bind:error="onImageError"
  />
  
  <!-- 加载失败状态 -->
  <view wx:if="{{loadError}}" class="lazy-image-error">
    <image 
      src="{{placeholder}}" 
      mode="{{mode}}"
      class="error-placeholder"
    />
  </view>
</view> 