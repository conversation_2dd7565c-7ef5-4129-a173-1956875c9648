.lazy-image-container {
  position: relative;
  overflow: hidden;
  background-color: #f5f5f5;
}

.lazy-image {
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.lazy-image-loaded {
  opacity: 1;
}

.lazy-image-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.loading-spinner {
  width: 30rpx;
  height: 30rpx;
  border: 3rpx solid #e0e0e0;
  border-top: 3rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.lazy-image-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.error-placeholder {
  width: 100%;
  height: 100%;
  opacity: 0.6;
}

/* 响应式图片尺寸 */
.lazy-image-thumbnail {
  width: 150rpx;
  height: 150rpx;
}

.lazy-image-small {
  width: 300rpx;
  height: 300rpx;
}

.lazy-image-medium {
  width: 600rpx;
  height: 600rpx;
}

.lazy-image-large {
  width: 1200rpx;
  height: 1200rpx;
} 