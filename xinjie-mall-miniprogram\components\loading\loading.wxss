/* components/loading/loading.wxss */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-spinner {
  border: 4rpx solid #f0f0f0;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
}

/* 不同尺寸 */
.size-small .loading-spinner {
  width: 30rpx;
  height: 30rpx;
  border-width: 3rpx;
}

.size-small .loading-text {
  font-size: 24rpx;
}

.size-normal .loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border-width: 4rpx;
}

.size-normal .loading-text {
  font-size: 28rpx;
}

.size-large .loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border-width: 6rpx;
}

.size-large .loading-text {
  font-size: 32rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
