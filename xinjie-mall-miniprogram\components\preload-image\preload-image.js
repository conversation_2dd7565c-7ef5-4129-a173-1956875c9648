Component({
  properties: {
    // 图片URL
    src: {
      type: String,
      value: ''
    },
    // 占位图URL
    placeholder: {
      type: String,
      value: '/images/common/placeholder.png'
    },
    // 图片模式
    mode: {
      type: String,
      value: 'aspectFill'
    },
    // 图片尺寸
    size: {
      type: String,
      value: 'medium'
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    },
    // 图片宽度
    width: {
      type: String,
      value: '100%'
    },
    // 图片高度
    height: {
      type: String,
      value: 'auto'
    }
  },

  data: {
    imageUrl: '',
    isLoading: true,
    loadError: false
  },

  lifetimes: {
    attached() {
      this.initImage();
    }
  },

  observers: {
    'src': function(newSrc) {
      if (newSrc !== this.data.imageUrl) {
        this.initImage();
      }
    }
  },

  methods: {
    /**
     * 初始化图片
     */
    initImage() {
      const { src, placeholder } = this.properties;

      console.log('🔍 preload-image 初始化:', { src, placeholder });

      if (!src) {
        console.log('❌ 没有图片URL，使用占位图');
        this.setData({
          imageUrl: placeholder,
          isLoading: false,
          loadError: false
        });
        return;
      }

      console.log('✅ 开始加载图片:', src);
      this.setData({
        imageUrl: src,
        isLoading: true,
        loadError: false
      });
    },

    /**
     * 图片加载成功
     */
    onImageLoad(e) {
      console.log('✅ preload-image 加载成功:', this.properties.src, e.detail);
      this.setData({
        isLoading: false,
        loadError: false
      });

      this.triggerEvent('load', e.detail);
      this.triggerEvent('success', {
        url: this.properties.src,
        localPath: this.data.imageUrl,
        detail: e.detail
      });
    },

    /**
     * 图片加载失败
     */
    onImageError(e) {
      console.error('❌ preload-image 加载失败:', this.properties.src, e.detail);

      // 尝试使用占位图
      const { placeholder } = this.properties;
      console.log('🔄 尝试使用占位图:', placeholder);

      this.setData({
        loadError: true,
        isLoading: false,
        imageUrl: placeholder
      });

      this.triggerEvent('error', {
        url: this.properties.src,
        error: e.detail.errMsg || e.detail,
        placeholder: placeholder
      });
    },

    /**
     * 点击图片
     */
    onImageTap() {
      this.triggerEvent('tap', {
        url: this.properties.src,
        localPath: this.data.imageUrl
      });
    }
  }
}); 