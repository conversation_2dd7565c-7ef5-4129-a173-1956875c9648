<view class="preload-image-container" style="width: {{width}}; height: {{height}}; {{customStyle}}">
  <!-- 加载状态 -->
  <view wx:if="{{isLoading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
  
  <!-- 错误状态 -->
  <view wx:elif="{{loadError}}" class="error-container">
    <view class="error-icon">❌</view>
    <text class="error-text">加载失败</text>
  </view>
  
  <!-- 图片显示 -->
  <image 
    wx:else
    class="preload-image"
    src="{{imageUrl}}"
    mode="{{mode}}"
    lazy-load="{{true}}"
    binderror="onImageError"
    bindload="onImageLoad"
    bindtap="onImageTap"
  ></image>
</view> 