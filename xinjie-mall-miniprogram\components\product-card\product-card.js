// components/product-card/product-card.js
Component({
  properties: {
    // 商品数据
    product: {
      type: Object,
      value: {},
    },
    // 显示模式 (grid: 网格模式, list: 列表模式)
    mode: {
      type: String,
      value: "grid",
    },
    // 是否显示销量
    showSales: {
      type: Boolean,
      value: true,
    },
    // 是否显示添加到购物车按钮
    showAddCart: {
      type: Boolean,
      value: false,
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: "",
    },
  },

  data: {
    // 默认图片
    defaultImage: "/images/common/default-product.png",
  },

  methods: {
    // 点击商品
    onProductTap: function () {
      const { product } = this.properties;
      this.triggerEvent("productTap", { product });
    },

    // 添加到购物车
    onAddToCart: function (e) {
      e.stopPropagation();
      const { product } = this.properties;
      this.triggerEvent("addToCart", { product });
    },

    // 图片加载失败
    onImageError: function () {
      this.setData({
        "product.image": this.data.defaultImage,
      });
    },

    // 收藏商品
    onFavorite: function (e) {
      e.stopPropagation();
      const { product } = this.properties;
      this.triggerEvent("favorite", { product });
    },
  },
});
