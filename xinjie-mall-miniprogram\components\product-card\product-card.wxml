<!--components/product-card/product-card.wxml-->
<view class="product-card {{mode}} {{customClass}}" bindtap="onProductTap">
  <!-- 网格模式 -->
  <view class="grid-mode" wx:if="{{mode === 'grid'}}">
    <view class="product-image-container">
      <image 
        class="product-image" 
        src="{{product.image || defaultImage}}" 
        mode="aspectFill"
        binderror="onImageError"
      />
      <view class="product-badges" wx:if="{{product.badges}}">
        <text class="badge" wx:for="{{product.badges}}" wx:key="*this">{{item}}</text>
      </view>
      <view class="favorite-btn" bindtap="onFavorite" wx:if="{{product.canFavorite}}">
        <text class="favorite-icon">{{product.isFavorite ? '❤️' : '🤍'}}</text>
      </view>
    </view>
    
    <view class="product-info">
      <text class="product-name">{{product.name}}</text>
      <text class="product-desc" wx:if="{{product.description}}">{{product.description}}</text>
      
      <view class="price-section">
        <text class="price-symbol">¥</text>
        <text class="price-integer">{{product.priceInteger}}</text>
        <text class="price-decimal" wx:if="{{product.priceDecimal}}">{{product.priceDecimal}}</text>
        <text class="original-price" wx:if="{{product.originalPrice}}">¥{{product.originalPrice}}</text>
      </view>
      
      <view class="product-footer">
        <text class="sales-text" wx:if="{{showSales && product.sales}}">销量{{product.sales}}</text>
        <button 
          class="add-cart-btn" 
          wx:if="{{showAddCart}}"
          bindtap="onAddToCart"
          size="mini"
        >
          加入购物车
        </button>
      </view>
    </view>
  </view>

  <!-- 列表模式 -->
  <view class="list-mode" wx:elif="{{mode === 'list'}}">
    <view class="product-image-container">
      <image 
        class="product-image" 
        src="{{product.image || defaultImage}}" 
        mode="aspectFill"
        binderror="onImageError"
      />
      <view class="product-badges" wx:if="{{product.badges}}">
        <text class="badge" wx:for="{{product.badges}}" wx:key="*this">{{item}}</text>
      </view>
    </view>
    
    <view class="product-info">
      <text class="product-name">{{product.name}}</text>
      <text class="product-desc" wx:if="{{product.description}}">{{product.description}}</text>
      
      <view class="price-section">
        <text class="price-symbol">¥</text>
        <text class="price-integer">{{product.priceInteger}}</text>
        <text class="price-decimal" wx:if="{{product.priceDecimal}}">{{product.priceDecimal}}</text>
        <text class="original-price" wx:if="{{product.originalPrice}}">¥{{product.originalPrice}}</text>
        <text class="sales-text" wx:if="{{showSales && product.sales}}">销量{{product.sales}}</text>
      </view>
    </view>
    
    <view class="product-actions">
      <button 
        class="favorite-btn" 
        wx:if="{{product.canFavorite}}"
        bindtap="onFavorite"
        size="mini"
      >
        {{product.isFavorite ? '❤️' : '🤍'}}
      </button>
      <button 
        class="add-cart-btn" 
        wx:if="{{showAddCart}}"
        bindtap="onAddToCart"
        size="mini"
      >
        加入购物车
      </button>
    </view>
  </view>
</view> 