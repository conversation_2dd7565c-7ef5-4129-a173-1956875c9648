/* components/product-card/product-card.wxss */
.product-card {
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.product-card:active {
  transform: scale(0.98);
}

/* 网格模式 */
.grid-mode {
  display: flex;
  flex-direction: column;
}

.grid-mode .product-image-container {
  position: relative;
  width: 100%;
  height: 200rpx;
}

.grid-mode .product-image {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
}

.grid-mode .product-info {
  padding: 20rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.grid-mode .product-name {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
}

.grid-mode .product-desc {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.grid-mode .price-section {
  display: flex;
  align-items: baseline;
  margin-bottom: 10rpx;
}

.grid-mode .product-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
}

/* 列表模式 */
.list-mode {
  display: flex;
  align-items: center;
  padding: 20rpx;
}

.list-mode .product-image-container {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.list-mode .product-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  background-color: #f5f5f5;
}

.list-mode .product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 160rpx;
}

.list-mode .product-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
}

.list-mode .product-desc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.list-mode .price-section {
  display: flex;
  align-items: baseline;
  flex-wrap: wrap;
  margin-bottom: 10rpx;
}

.list-mode .product-actions {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  margin-left: 20rpx;
}

/* 通用样式 */
.product-image-container {
  overflow: hidden;
}

.product-badges {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  display: flex;
  gap: 5rpx;
}

.badge {
  background-color: #ff4444;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-weight: bold;
}

.favorite-btn {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  border: none;
  margin: 0;
  padding: 0;
}

.favorite-icon {
  font-size: 24rpx;
}

.price-section {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}

.price-symbol {
  font-size: 22rpx;
  color: #ff4444;
  font-weight: bold;
}

.price-integer {
  font-size: 32rpx;
  color: #ff4444;
  font-weight: bold;
}

.price-decimal {
  font-size: 24rpx;
  color: #ff4444;
  font-weight: bold;
}

.original-price {
  font-size: 22rpx;
  color: #999;
  text-decoration: line-through;
  margin-left: 10rpx;
}

.sales-text {
  font-size: 20rpx;
  color: #999;
  margin-left: auto;
}

.add-cart-btn {
  background-color: #4caf50;
  color: white;
  font-size: 22rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: none;
  margin: 0;
  line-height: 1.2;
}

.add-cart-btn:active {
  opacity: 0.8;
}

/* 列表模式特殊样式 */
.list-mode .favorite-btn {
  position: static;
  background-color: #f5f5f5;
  border: 2rpx solid #ddd;
  color: #666;
  font-size: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  margin: 0;
  line-height: 1.2;
}

.list-mode .add-cart-btn {
  font-size: 20rpx;
  padding: 8rpx 16rpx;
}

.list-mode .sales-text {
  font-size: 22rpx;
  margin-left: 20rpx;
}

/* 响应式设计 */
@media (max-width: 600rpx) {
  .grid-mode .product-image-container {
    height: 180rpx;
  }

  .grid-mode .product-info {
    padding: 16rpx;
  }

  .grid-mode .product-name {
    font-size: 24rpx;
  }

  .list-mode .product-image-container {
    width: 120rpx;
    height: 120rpx;
  }

  .list-mode .product-info {
    height: 120rpx;
  }

  .list-mode .product-name {
    font-size: 26rpx;
  }
}
