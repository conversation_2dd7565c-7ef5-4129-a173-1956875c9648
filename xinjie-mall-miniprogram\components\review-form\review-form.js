// components/review-form/review-form.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 商品信息
    product: {
      type: Object,
      value: {}
    },
    // 订单ID
    orderId: {
      type: Number,
      value: 0
    },
    // 是否显示弹窗
    show: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    rating: 5, // 评分
    content: '', // 评价内容
    images: [], // 评价图片
    isAnonymous: false, // 是否匿名
    submitting: false // 是否提交中
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 关闭弹窗
    onClose() {
      this.triggerEvent('close');
    },

    // 评分改变
    onRatingChange(e) {
      const rating = e.detail.value;
      this.setData({ rating });
    },

    // 评价内容输入
    onContentInput(e) {
      const content = e.detail.value;
      this.setData({ content });
    },

    // 匿名开关
    onAnonymousChange(e) {
      const isAnonymous = e.detail.value;
      this.setData({ isAnonymous });
    },

    // 选择图片
    onChooseImage() {
      const { images } = this.data;
      const remainCount = 5 - images.length;
      
      if (remainCount <= 0) {
        wx.showToast({
          title: '最多只能上传5张图片',
          icon: 'none'
        });
        return;
      }

      wx.chooseMedia({
        count: remainCount,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFiles = res.tempFiles;
          this.uploadImages(tempFiles);
        }
      });
    },

    // 上传图片
    uploadImages(tempFiles) {
      wx.showLoading({ title: '上传中...' });
      
      const uploadPromises = tempFiles.map(file => {
        return new Promise((resolve, reject) => {
          wx.uploadFile({
            url: getApp().globalData.baseURL + '/upload/image',
            filePath: file.tempFilePath,
            name: 'file',
            header: {
              'Authorization': 'Bearer ' + wx.getStorageSync('token')
            },
            success: (res) => {
              const data = JSON.parse(res.data);
              if (data.code === 200) {
                resolve(data.data.url);
              } else {
                reject(data.message);
              }
            },
            fail: reject
          });
        });
      });

      Promise.all(uploadPromises)
        .then(urls => {
          const newImages = [...this.data.images, ...urls];
          this.setData({ images: newImages });
          wx.hideLoading();
        })
        .catch(error => {
          wx.hideLoading();
          wx.showToast({
            title: error || '上传失败',
            icon: 'none'
          });
        });
    },

    // 删除图片
    onDeleteImage(e) {
      const index = e.currentTarget.dataset.index;
      const images = [...this.data.images];
      images.splice(index, 1);
      this.setData({ images });
    },

    // 预览图片
    onPreviewImage(e) {
      const index = e.currentTarget.dataset.index;
      const { images } = this.data;
      
      wx.previewImage({
        current: images[index],
        urls: images
      });
    },

    // 提交评价
    onSubmit() {
      const { rating, content, images, isAnonymous } = this.data;
      const { product, orderId } = this.properties;

      // 验证评分
      if (rating < 1 || rating > 5) {
        wx.showToast({
          title: '请选择评分',
          icon: 'none'
        });
        return;
      }

      // 验证内容长度
      if (content.length > 500) {
        wx.showToast({
          title: '评价内容不能超过500字',
          icon: 'none'
        });
        return;
      }

      this.setData({ submitting: true });

      // 调用API提交评价
      wx.request({
        url: getApp().globalData.baseURL + '/api/front/review',
        method: 'POST',
        header: {
          'Authorization': 'Bearer ' + wx.getStorageSync('token'),
          'Content-Type': 'application/json'
        },
        data: {
          orderId: orderId,
          productId: product.id,
          rating: rating,
          content: content,
          images: images,
          isAnonymous: isAnonymous
        },
        success: (res) => {
          if (res.data.code === 200) {
            wx.showToast({
              title: '评价成功',
              icon: 'success'
            });
            
            // 重置表单
            this.setData({
              rating: 5,
              content: '',
              images: [],
              isAnonymous: false
            });
            
            // 触发成功事件
            this.triggerEvent('success', res.data.data);
            
            // 关闭弹窗
            this.onClose();
          } else {
            wx.showToast({
              title: res.data.message || '评价失败',
              icon: 'none'
            });
          }
        },
        fail: (error) => {
          wx.showToast({
            title: '网络错误',
            icon: 'none'
          });
        },
        complete: () => {
          this.setData({ submitting: false });
        }
      });
    },

    // 获取评分文字描述
    getRatingText(rating) {
      const texts = {
        1: '很不满意',
        2: '不满意', 
        3: '一般',
        4: '满意',
        5: '非常满意'
      };
      return texts[rating] || '请评分';
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例被放入页面节点树后执行
    },
    
    detached() {
      // 组件实例被从页面节点树移除后执行
    }
  }
});
