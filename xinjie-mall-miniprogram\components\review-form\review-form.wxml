<!-- components/review-form/review-form.wxml -->
<view class="review-form-overlay" wx:if="{{show}}" bindtap="onClose">
  <view class="review-form-container" catchtap="">
    <!-- 头部 -->
    <view class="review-form-header">
      <text class="title">商品评价</text>
      <view class="close-btn" bindtap="onClose">
        <text class="iconfont icon-close"></text>
      </view>
    </view>

    <!-- 商品信息 -->
    <view class="product-info">
      <image class="product-image" src="{{product.main_image}}" mode="aspectFill"></image>
      <view class="product-details">
        <text class="product-name">{{product.name}}</text>
        <text class="product-price">¥{{product.price}}</text>
      </view>
    </view>

    <!-- 评分区域 -->
    <view class="rating-section">
      <text class="section-title">商品评分</text>
      <view class="rating-container">
        <view class="stars">
          <block wx:for="{{5}}" wx:key="index">
            <text 
              class="star {{rating > index ? 'active' : ''}}"
              data-rating="{{index + 1}}"
              bindtap="onRatingChange"
            >★</text>
          </block>
        </view>
        <text class="rating-text">{{getRatingText(rating)}}</text>
      </view>
    </view>

    <!-- 评价内容 -->
    <view class="content-section">
      <text class="section-title">评价内容</text>
      <textarea
        class="content-input"
        placeholder="分享您的使用感受，帮助其他买家了解商品..."
        value="{{content}}"
        maxlength="500"
        bindinput="onContentInput"
        show-confirm-bar="{{false}}"
        auto-height
      ></textarea>
      <view class="content-count">{{content.length}}/500</view>
    </view>

    <!-- 图片上传 -->
    <view class="images-section">
      <text class="section-title">上传图片 (最多5张)</text>
      <view class="images-container">
        <block wx:for="{{images}}" wx:key="index">
          <view class="image-item">
            <image 
              src="{{item}}" 
              mode="aspectFill"
              data-index="{{index}}"
              bindtap="onPreviewImage"
            ></image>
            <view 
              class="delete-btn"
              data-index="{{index}}"
              bindtap="onDeleteImage"
            >
              <text class="iconfont icon-close"></text>
            </view>
          </view>
        </block>
        
        <view 
          class="add-image-btn" 
          wx:if="{{images.length < 5}}"
          bindtap="onChooseImage"
        >
          <text class="iconfont icon-camera"></text>
          <text class="add-text">添加图片</text>
        </view>
      </view>
    </view>

    <!-- 匿名选项 -->
    <view class="anonymous-section">
      <view class="anonymous-item">
        <text class="anonymous-label">匿名评价</text>
        <switch 
          checked="{{isAnonymous}}"
          bindchange="onAnonymousChange"
          color="#ff6b35"
        ></switch>
      </view>
      <text class="anonymous-tip">开启后，您的昵称和头像将不会显示</text>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button 
        class="submit-btn {{submitting ? 'disabled' : ''}}"
        disabled="{{submitting}}"
        bindtap="onSubmit"
      >
        {{submitting ? '提交中...' : '发表评价'}}
      </button>
    </view>
  </view>
</view>
