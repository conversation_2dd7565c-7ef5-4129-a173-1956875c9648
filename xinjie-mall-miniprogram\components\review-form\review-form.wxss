/* components/review-form/review-form.wxss */
.review-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.review-form-container {
  width: 100%;
  max-height: 80vh;
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx 30rpx;
  box-sizing: border-box;
  overflow-y: auto;
}

/* 头部样式 */
.review-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.review-form-header .title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 32rpx;
}

/* 商品信息样式 */
.product-info {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.product-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
}

/* 评分区域样式 */
.rating-section {
  margin-bottom: 40rpx;
}

.section-title {
  display: block;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.rating-container {
  display: flex;
  align-items: center;
}

.stars {
  display: flex;
  margin-right: 20rpx;
}

.star {
  font-size: 48rpx;
  color: #ddd;
  margin-right: 10rpx;
  transition: color 0.2s;
}

.star.active {
  color: #ffb400;
}

.rating-text {
  font-size: 28rpx;
  color: #666;
}

/* 评价内容样式 */
.content-section {
  margin-bottom: 40rpx;
}

.content-input {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  margin-bottom: 10rpx;
}

.content-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
}

/* 图片上传样式 */
.images-section {
  margin-bottom: 40rpx;
}

.images-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
}

.image-item image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.delete-btn {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background: #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 20rpx;
}

.add-image-btn {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.add-image-btn .iconfont {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.add-text {
  font-size: 24rpx;
}

/* 匿名选项样式 */
.anonymous-section {
  margin-bottom: 40rpx;
}

.anonymous-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.anonymous-label {
  font-size: 28rpx;
  color: #333;
}

.anonymous-tip {
  font-size: 24rpx;
  color: #999;
}

/* 提交按钮样式 */
.submit-section {
  padding-top: 20rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: #ff6b35;
  color: #fff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn.disabled {
  background: #ccc;
  color: #999;
}

.submit-btn::after {
  border: none;
}
