// API 接口配置
const { getCurrentEnv } = require('./env');
const currentEnv = getCurrentEnv();
const API_BASE_URL = currentEnv.apiUrl;

const API = {
  // 认证相关
  auth: {
    silentLogin: `${API_BASE_URL}/front/auth/silent-login`,      // 静默登录
    updateUserInfo: `${API_BASE_URL}/front/auth/update-user-info`, // 更新用户信息
    logout: `${API_BASE_URL}/front/auth/logout`,                // 退出登录
  },
  
  // 用户相关
  user: {
    profile: `${API_BASE_URL}/front/user/profile`,              // 获取用户资料
    update: `${API_BASE_URL}/front/user/update`,                // 更新用户资料
  },
  
  // 商品相关
  product: {
    list: `${API_BASE_URL}/front/product/list`,           // 商品列表
    detail: `${API_BASE_URL}/front/product/detail`,       // 商品详情
    search: `${API_BASE_URL}/front/product/search`,       // 商品搜索
    hot: `${API_BASE_URL}/front/product/hot`,             // 热门商品
    recommend: `${API_BASE_URL}/front/product/recommend`, // 推荐商品
  },
  
  // 分类相关
  category: {
    list: `${API_BASE_URL}/front/category/list`,          // 分类列表
    products: `${API_BASE_URL}/front/category/products`,  // 分类商品
  },
  
  // 购物车相关
  cart: {
    list: `${API_BASE_URL}/front/cart/list`,              // 购物车列表
    add: `${API_BASE_URL}/front/cart/add`,                // 添加到购物车
    update: `${API_BASE_URL}/front/cart/update`,          // 更新购物车
    delete: `${API_BASE_URL}/front/cart/delete`,          // 删除购物车商品
    clear: `${API_BASE_URL}/front/cart/clear`,            // 清空购物车
  },
  
  // 订单相关
  order: {
    create: `${API_BASE_URL}/front/order/create`,               // 创建订单
    list: `${API_BASE_URL}/front/order/list`,                   // 订单列表
    detail: `${API_BASE_URL}/front/order/detail`,               // 订单详情
    cancel: `${API_BASE_URL}/front/order/cancel`,               // 取消订单
    pay: `${API_BASE_URL}/front/order/pay`,                     // 支付订单
    confirm: `${API_BASE_URL}/front/order/confirm`,             // 确认收货
    logistics: `${API_BASE_URL}/front/order/logistics`,         // 物流信息
  },
  
  // 地址相关
  address: {
    list: `${API_BASE_URL}/front/address/list`,                 // 地址列表
    add: `${API_BASE_URL}/front/address/add`,                   // 添加地址
    update: `${API_BASE_URL}/front/address/update`,             // 更新地址
    delete: `${API_BASE_URL}/front/address/delete`,             // 删除地址
    default: `${API_BASE_URL}/front/address/default`,           // 设置默认地址
  },
  
  // 轮播图相关
  banner: {
    list: `${API_BASE_URL}/front/banner/list`,            // 轮播图列表
  },
  
  // 支付相关
  payment: {
    create: `${API_BASE_URL}/front/payment/create`,              // 创建支付订单
    status: `${API_BASE_URL}/front/payment/status`,              // 查询支付状态
    callback: `${API_BASE_URL}/front/payment/callback`,          // 支付回调
    refund: `${API_BASE_URL}/front/payment/refund`,              // 申请退款
    options: `${API_BASE_URL}/front/payment/options`,            // 获取支付选项
  },

  // 余额相关
  balance: {
    info: `${API_BASE_URL}/front/balance/info`,                  // 获取余额信息
    recharge: `${API_BASE_URL}/front/balance/recharge`,          // 用户充值
    history: `${API_BASE_URL}/front/balance/history`,            // 余额变动记录
    rechargeHistory: `${API_BASE_URL}/front/balance/recharge/history`, // 充值记录
  },

  // 会员相关
  member: {
    info: `${API_BASE_URL}/front/member/info`,                   // 获取会员信息
    levels: `${API_BASE_URL}/front/member/levels`,               // 获取会员等级列表
    benefits: `${API_BASE_URL}/front/member/benefits`,           // 获取会员权益
    pointsHistory: `${API_BASE_URL}/front/member/points/history`, // 积分记录
    checkIn: `${API_BASE_URL}/front/member/checkin`,             // 每日签到
  },

  // 评论相关
  review: {
    create: `${API_BASE_URL}/front/review`,                      // 发表评论
    product: `${API_BASE_URL}/front/review/product`,             // 获取商品评论
    user: `${API_BASE_URL}/front/review/user`,                   // 获取用户评论
    delete: `${API_BASE_URL}/front/review`,                      // 删除评论
    check: `${API_BASE_URL}/front/review/check`,                 // 检查是否可以评论
  },
};

module.exports = {
  API,
  BASE_URL: currentEnv.imageUrl, // 图片基础URL（配合 /uploads 使用）
  API_BASE_URL,
};
