// 环境配置
const env = {
  // 开发环境
  development: {
    apiUrl: "http://localhost:4000/api", // HTTP API地址
    imageUrl: "http://localhost:4000", // HTTP图片地址
    debug: true,
    logLevel: "debug",
  },

  // 测试环境
  testing: {
    apiUrl: "https://test-api.xinjie-tea.com/api",
    imageUrl: "https://test-api.xinjie-tea.com",
    debug: true,
    logLevel: "info",
  },

  // 生产环境
  production: {
    apiUrl: "https://api.xinjie-tea.com/api",
    imageUrl: "https://api.xinjie-tea.com",
    debug: false,
    logLevel: "error",
    timeout: 10000,
    retryTimes: 3,
  },
};

// 强制环境开关：'production' | 'testing' | 'development' | ''(自动)
const FORCE_ENV = 'production';

// 获取当前环境
const getCurrentEnv = () => {
  // 若强制指定环境，直接返回对应配置
  if (FORCE_ENV && env[FORCE_ENV]) return env[FORCE_ENV];

  // 兼容本地开发和小程序环境
  if (typeof wx === 'undefined' || !wx.getAccountInfoSync) {
    // 非小程序环境默认开发
    return env.development;
  }
  try {
    const accountInfo = wx.getAccountInfoSync();
    const envType = accountInfo.miniProgram.envVersion;
    switch (envType) {
      case "develop":
        return env.development;
      case "trial":
        return env.testing;
      case "release":
        return env.production;
      default:
        return env.development;
    }
  } catch (e) {
    return env.development;
  }
};

module.exports = {
  env,
  getCurrentEnv,
};
