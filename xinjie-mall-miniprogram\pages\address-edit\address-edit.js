// pages/address-edit/address-edit.js
const { get, post, put } = require("../../utils/request");
const { API } = require("../../config/api");
const { validatePhone, validateRequired } = require("../../utils/validate");
const { requireLogin } = require("../../utils/auth");

Page({
  data: {
    // 地址ID（编辑模式）
    addressId: null,

    // 表单数据
    form: {
      receiverName: "",
      receiverPhone: "",
      province: "",
      city: "",
      district: "",
      detail: "",
      isDefault: false,
    },

    // 地区选择器
    regions: [],
    regionIndex: [0, 0, 0],

    // 加载状态
    loading: false,
    submitting: false,

    // 是否编辑模式
    isEdit: false,
  },

  // 页面加载
  onLoad: function (options) {
    // 初始化地区数据
    this.initRegions();

    // 检查是否编辑模式
    if (options.id) {
      this.setData({
        addressId: options.id,
        isEdit: true,
      });
      this.loadAddressDetail();
    }
  },

  // 初始化地区数据
  initRegions: function () {
    // 这里使用模拟数据，实际项目中应该从API获取
    const regions = [
      [
        "北京市",
        "上海市",
        "广东省",
        "江苏省",
        "浙江省",
        "山东省",
        "河南省",
        "四川省",
        "湖南省",
        "湖北省",
      ],
      [
        "市辖区",
        "市辖区",
        "广州市",
        "深圳市",
        "南京市",
        "杭州市",
        "济南市",
        "郑州市",
        "长沙市",
        "武汉市",
      ],
      [
        "东城区",
        "西城区",
        "黄浦区",
        "徐汇区",
        "越秀区",
        "海珠区",
        "罗湖区",
        "福田区",
        "玄武区",
        "秦淮区",
      ],
    ];

    this.setData({
      regions,
    });
  },

  // 加载地址详情
  loadAddressDetail: function () {
    this.setData({
      loading: true,
    });

    return requireLogin()
      .then(() => {
        return get(API.address.detail, { id: this.data.addressId });
      })
      .then((res) => {
        if (res.success) {
          const address = res.data;
          this.setData({
            form: {
              receiverName: address.receiverName,
              receiverPhone: address.receiverPhone,
              province: address.province,
              city: address.city,
              district: address.district,
              detail: address.detail,
              isDefault: address.isDefault,
            },
          });

          // 更新地区选择器
          this.updateRegionIndex();
        }
      })
      .catch((error) => {
        console.error("加载地址详情失败:", error);
        wx.showToast({
          title: "加载失败",
          icon: "none",
        });
      })
      .finally(() => {
        this.setData({
          loading: false,
        });
      });
  },

  // 更新地区选择器索引
  updateRegionIndex: function () {
    const { form, regions } = this.data;
    const provinceIndex = regions[0].indexOf(form.province);
    const cityIndex = regions[1].indexOf(form.city);
    const districtIndex = regions[2].indexOf(form.district);

    this.setData({
      regionIndex: [
        provinceIndex >= 0 ? provinceIndex : 0,
        cityIndex >= 0 ? cityIndex : 0,
        districtIndex >= 0 ? districtIndex : 0,
      ],
    });
  },

  // 输入框变化
  onInputChange: function (e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;

    this.setData({
      [`form.${field}`]: value,
    });
  },

  // 地区选择变化
  onRegionChange: function (e) {
    const { regions } = this.data;
    const regionIndex = e.detail.value;

    this.setData({
      regionIndex,
      "form.province": regions[0][regionIndex[0]],
      "form.city": regions[1][regionIndex[1]],
      "form.district": regions[2][regionIndex[2]],
    });
  },

  // 默认地址开关
  onDefaultSwitch: function (e) {
    this.setData({
      "form.isDefault": e.detail.value,
    });
  },

  // 表单验证
  validateForm: function () {
    const { form } = this.data;

    // 验证收货人姓名
    if (!validateRequired(form.receiverName)) {
      wx.showToast({
        title: "请输入收货人姓名",
        icon: "none",
      });
      return false;
    }

    // 验证手机号码
    if (!validatePhone(form.receiverPhone)) {
      wx.showToast({
        title: "请输入正确的手机号码",
        icon: "none",
      });
      return false;
    }

    // 验证地区
    if (!form.province || !form.city || !form.district) {
      wx.showToast({
        title: "请选择所在地区",
        icon: "none",
      });
      return false;
    }

    // 验证详细地址
    if (!validateRequired(form.detail)) {
      wx.showToast({
        title: "请输入详细地址",
        icon: "none",
      });
      return false;
    }

    return true;
  },

  // 提交表单
  onSubmit: function () {
    if (!this.validateForm()) {
      return;
    }

    this.setData({
      submitting: true,
    });

    const { form, addressId, isEdit } = this.data;
    const apiCall = isEdit
      ? put(API.address.update, { id: addressId, ...form })
      : post(API.address.add, form);

    return requireLogin()
      .then(() => apiCall)
      .then((res) => {
        if (res.success) {
          wx.showToast({
            title: isEdit ? "修改成功" : "添加成功",
            icon: "success",
          });

          // 返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      })
      .catch((error) => {
        console.error("提交失败:", error);
        wx.showToast({
          title: "提交失败",
          icon: "none",
        });
      })
      .finally(() => {
        this.setData({
          submitting: false,
        });
      });
  },

  // 获取用户位置
  onGetLocation: function () {
    wx.showModal({
      title: "获取位置",
      content: "需要获取您的位置信息来自动填写地址",
      success: (res) => {
        if (res.confirm) {
          this.getUserLocation();
        }
      },
    });
  },

  // 获取用户位置
  getUserLocation: function () {
    wx.showLoading({
      title: "定位中...",
    });

    wx.getLocation({
      type: "gcj02",
      success: (res) => {
        // 根据经纬度获取地址信息
        this.reverseGeocode(res.latitude, res.longitude);
      },
      fail: (error) => {
        console.error("获取位置失败:", error);
        wx.showToast({
          title: "获取位置失败",
          icon: "none",
        });
      },
      complete: () => {
        wx.hideLoading();
      },
    });
  },

  // 逆地理编码
  reverseGeocode: function (latitude, longitude) {
    // 这里应该调用地图API进行逆地理编码
    // 简单模拟处理
    wx.showToast({
      title: "定位功能开发中",
      icon: "none",
    });
  },
});
