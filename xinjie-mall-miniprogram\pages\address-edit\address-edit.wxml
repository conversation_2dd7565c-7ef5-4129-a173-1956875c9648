<!--pages/address-edit/address-edit.wxml-->
<view class="container">
  <!-- 表单内容 -->
  <view class="form-content">
    <!-- 收货人信息 -->
    <view class="form-section">
      <view class="form-item">
        <text class="form-label">收货人</text>
        <input 
          class="form-input"
          type="text"
          placeholder="请输入收货人姓名"
          value="{{form.receiverName}}"
          bindinput="onInputChange"
          data-field="receiverName"
          maxlength="20"
        />
      </view>
      
      <view class="form-item">
        <text class="form-label">手机号码</text>
        <input 
          class="form-input"
          type="number"
          placeholder="请输入手机号码"
          value="{{form.receiverPhone}}"
          bindinput="onInputChange"
          data-field="receiverPhone"
          maxlength="11"
        />
      </view>
    </view>

    <!-- 地区选择 -->
    <view class="form-section">
      <view class="form-item">
        <text class="form-label">所在地区</text>
        <picker 
          class="form-picker"
          mode="multiSelector"
          bindchange="onRegionChange"
          value="{{regionIndex}}"
          range="{{regions}}"
        >
          <view class="picker-content">
            <text class="picker-text" wx:if="{{form.province}}">
              {{form.province}} {{form.city}} {{form.district}}
            </text>
            <text class="picker-placeholder" wx:else>请选择所在地区</text>
            <text class="picker-arrow">></text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 详细地址 -->
    <view class="form-section">
      <view class="form-item">
        <text class="form-label">详细地址</text>
        <view class="form-input-row">
          <textarea 
            class="form-textarea"
            placeholder="请输入详细地址（如街道、门牌号等）"
            value="{{form.detail}}"
            bindinput="onInputChange"
            data-field="detail"
            maxlength="200"
            auto-height
          />
          <button class="location-btn" bindtap="onGetLocation">
            <text>📍</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 默认地址设置 -->
    <view class="form-section">
      <view class="form-item">
        <text class="form-label">设为默认地址</text>
        <switch 
          class="form-switch"
          checked="{{form.isDefault}}"
          bindchange="onDefaultSwitch"
          color="#4CAF50"
        />
      </view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button 
      class="submit-btn {{submitting ? 'submitting' : ''}}"
      bindtap="onSubmit"
      disabled="{{submitting}}"
    >
      <text wx:if="{{submitting}}">提交中...</text>
      <text wx:else>{{isEdit ? '保存修改' : '保存地址'}}</text>
    </button>
  </view>
</view>

<!-- 加载状态 -->
<loading wx:if="{{loading}}" text="加载中..." /> 