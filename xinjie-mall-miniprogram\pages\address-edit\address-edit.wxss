/* pages/address-edit/address-edit.wxss */
page {
  background-color: #f5f5f5;
}

.container {
  padding: 20rpx;
  padding-bottom: 120rpx;
}

/* 表单内容 */
.form-content {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.form-section {
  margin-bottom: 30rpx;
}

.form-section:last-child {
  margin-bottom: 0;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
  position: relative;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  width: 180rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  flex-shrink: 0;
}

.form-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  padding: 0 20rpx;
  height: 60rpx;
  line-height: 60rpx;
}

.form-input::placeholder {
  color: #999;
}

.form-picker {
  flex: 1;
  padding: 0 20rpx;
}

.picker-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60rpx;
  line-height: 60rpx;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

.picker-placeholder {
  font-size: 28rpx;
  color: #999;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999;
}

.form-input-row {
  display: flex;
  align-items: flex-start;
  flex: 1;
  padding: 0 20rpx;
}

.form-textarea {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  min-height: 120rpx;
  padding: 20rpx 0;
  line-height: 1.6;
}

.form-textarea::placeholder {
  color: #999;
}

.location-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #f0f0f0;
  border: none;
  border-radius: 50%;
  margin-left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #666;
  margin: 0;
  padding: 0;
}

.location-btn:active {
  background-color: #e0e0e0;
}

.form-switch {
  margin-left: 20rpx;
}

/* 提交按钮 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #f0f0f0;
  z-index: 1000;
}

.submit-btn {
  width: 100%;
  height: 80rpx;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  box-shadow: 0 4rpx 20rpx rgba(76, 175, 80, 0.3);
}

.submit-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.submit-btn.submitting {
  background-color: #ccc;
  box-shadow: none;
}

.submit-btn.submitting:active {
  opacity: 1;
  transform: none;
}

/* 响应式设计 */
@media (max-width: 600rpx) {
  .container {
    padding: 16rpx;
  }

  .form-content {
    padding: 20rpx;
  }

  .form-item {
    padding: 20rpx 0;
  }

  .form-label {
    width: 140rpx;
    font-size: 26rpx;
  }

  .form-input {
    font-size: 26rpx;
  }

  .picker-text,
  .picker-placeholder {
    font-size: 26rpx;
  }

  .form-textarea {
    font-size: 26rpx;
  }
}

/* 聚焦状态 */
.form-input:focus {
  outline: none;
}

.form-textarea:focus {
  outline: none;
}

/* 动画效果 */
.form-content {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表单验证提示 */
.form-item.error {
  border-bottom-color: #f44336;
}

.form-item.error .form-label {
  color: #f44336;
}

/* 禁用状态 */
.form-input:disabled {
  background-color: #f5f5f5;
  color: #999;
}

.form-textarea:disabled {
  background-color: #f5f5f5;
  color: #999;
}
