// pages/address-list/address-list.js
const { get, post, put, del } = require("../../utils/request");
const { API } = require("../../config/api");
const { requireLogin } = require("../../utils/auth");

Page({
  data: {
    // 地址列表
    addressList: [],

    // 是否选择模式
    isSelectMode: false,

    // 选择的地址ID
    selectedAddressId: null,

    // 加载状态
    loading: false,

    // 编辑模式
    editMode: false,
  },

  // 页面加载
  onLoad: function (options) {
    // 检查是否是选择地址模式
    if (options.select === "true") {
      this.setData({
        isSelectMode: true,
        selectedAddressId: options.selectedId || null,
      });
    }

    this.loadAddressList();
  },

  // 页面显示
  onShow: function () {
    this.loadAddressList();
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.loadAddressList().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载地址列表
  loadAddressList: function () {
    this.setData({
      loading: true,
    });

    return requireLogin()
      .then(() => {
        return get(API.address.list);
      })
      .then((res) => {
        if (res.success) {
          this.setData({
            addressList: res.data || [],
          });
        }
      })
      .catch((error) => {
        console.error("加载地址列表失败:", error);
        wx.showToast({
          title: "加载失败",
          icon: "none",
        });
      })
      .finally(() => {
        this.setData({
          loading: false,
        });
      });
  },

  // 选择地址
  onSelectAddress: function (e) {
    const addressId = e.currentTarget.dataset.id;
    const address = this.data.addressList.find((item) => item.id === addressId);

    if (this.data.isSelectMode) {
      // 返回选中的地址
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2];

      if (prevPage) {
        prevPage.setData({
          selectedAddress: address,
        });
      }

      wx.navigateBack();
    } else {
      // 进入编辑模式
      this.editAddress(addressId);
    }
  },

  // 编辑地址
  editAddress: function (addressId) {
    wx.navigateTo({
      url: `/pages/address-edit/address-edit?id=${addressId}`,
    });
  },

  // 新增地址
  onAddAddress: function () {
    wx.navigateTo({
      url: "/pages/address-edit/address-edit",
    });
  },

  // 设置默认地址
  onSetDefault: function (e) {
    e.stopPropagation();
    const addressId = e.currentTarget.dataset.id;

    wx.showLoading({
      title: "设置中...",
    });

    put(API.address.setDefault, { id: addressId })
      .then((res) => {
        if (res.success) {
          wx.showToast({
            title: "设置成功",
            icon: "success",
          });
          this.loadAddressList();
        }
      })
      .catch((error) => {
        console.error("设置默认地址失败:", error);
        wx.showToast({
          title: "设置失败",
          icon: "none",
        });
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  // 删除地址
  onDeleteAddress: function (e) {
    e.stopPropagation();
    const addressId = e.currentTarget.dataset.id;

    wx.showModal({
      title: "确认删除",
      content: "确定要删除这个地址吗？",
      success: (res) => {
        if (res.confirm) {
          this.deleteAddress(addressId);
        }
      },
    });
  },

  // 删除地址请求
  deleteAddress: function (addressId) {
    wx.showLoading({
      title: "删除中...",
    });

    del(API.address.delete, { id: addressId })
      .then((res) => {
        if (res.success) {
          wx.showToast({
            title: "删除成功",
            icon: "success",
          });
          this.loadAddressList();
        }
      })
      .catch((error) => {
        console.error("删除地址失败:", error);
        wx.showToast({
          title: "删除失败",
          icon: "none",
        });
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  // 切换编辑模式
  onToggleEdit: function () {
    this.setData({
      editMode: !this.data.editMode,
    });
  },

  // 格式化地址文本
  formatAddress: function (address) {
    return `${address.province}${address.city}${address.district}${address.detail}`;
  },

  // 处理长按事件
  onLongPress: function (e) {
    const addressId = e.currentTarget.dataset.id;

    wx.showActionSheet({
      itemList: ["编辑", "删除"],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.editAddress(addressId);
        } else if (res.tapIndex === 1) {
          this.onDeleteAddress(e);
        }
      },
    });
  },
});
