<!--pages/address-list/address-list.wxml-->
<view class="container">
  <!-- 地址列表 -->
  <view class="address-list">
    <view 
      class="address-item {{item.isDefault ? 'default' : ''}} {{isSelectMode && selectedAddressId === item.id ? 'selected' : ''}}"
      wx:for="{{addressList}}" 
      wx:key="id"
      bindtap="onSelectAddress"
      bindlongpress="onLongPress"
      data-id="{{item.id}}"
    >
      <!-- 地址信息 -->
      <view class="address-info">
        <view class="address-header">
          <view class="user-info">
            <text class="name">{{item.receiverName}}</text>
            <text class="phone">{{item.receiverPhone}}</text>
          </view>
          <view class="address-tags">
            <text class="tag default-tag" wx:if="{{item.isDefault}}">默认</text>
            <text class="tag selected-tag" wx:if="{{isSelectMode && selectedAddressId === item.id}}">已选</text>
          </view>
        </view>
        
        <view class="address-content">
          <text class="address-text">{{item.province}}{{item.city}}{{item.district}}{{item.detail}}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="address-actions" wx:if="{{!isSelectMode}}">
        <view class="action-buttons">
          <button 
            class="action-btn default-btn {{item.isDefault ? 'disabled' : ''}}"
            bindtap="onSetDefault"
            data-id="{{item.id}}"
            disabled="{{item.isDefault}}"
          >
            {{item.isDefault ? '默认地址' : '设为默认'}}
          </button>
          <button 
            class="action-btn edit-btn"
            bindtap="onSelectAddress"
            data-id="{{item.id}}"
          >
            编辑
          </button>
          <button 
            class="action-btn delete-btn"
            bindtap="onDeleteAddress"
            data-id="{{item.id}}"
          >
            删除
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <empty 
    wx:if="{{addressList.length === 0 && !loading}}"
    icon="📍"
    text="还没有收货地址"
    buttonText="添加地址"
    bind:onButtonTap="onAddAddress"
  />

  <!-- 加载状态 -->
  <loading 
    wx:if="{{loading}}"
    text="加载中..."
    size="normal"
  />

  <!-- 底部新增按钮 -->
  <view class="bottom-action">
    <button class="add-btn" bindtap="onAddAddress">
      <text class="btn-text">新增收货地址</text>
    </button>
  </view>
</view> 