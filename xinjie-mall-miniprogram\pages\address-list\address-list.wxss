/* pages/address-list/address-list.wxss */
page {
  background-color: #f5f5f5;
}

.container {
  padding-bottom: 120rpx;
}

/* 地址列表 */
.address-list {
  margin-top: 20rpx;
}

.address-item {
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  position: relative;
  transition: all 0.3s;
}

.address-item.default {
  border: 2rpx solid #4caf50;
}

.address-item.selected {
  border: 2rpx solid #2196f3;
  background-color: #f8f9ff;
}

.address-item:active {
  transform: scale(0.98);
}

/* 地址信息 */
.address-info {
  margin-bottom: 20rpx;
}

.address-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.user-info {
  display: flex;
  align-items: center;
}

.name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}

.phone {
  font-size: 26rpx;
  color: #666;
}

.address-tags {
  display: flex;
  gap: 10rpx;
}

.tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: bold;
}

.default-tag {
  background-color: #4caf50;
  color: white;
}

.selected-tag {
  background-color: #2196f3;
  color: white;
}

.address-content {
  margin-bottom: 20rpx;
}

.address-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 操作按钮 */
.address-actions {
  border-top: 2rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}

.action-btn {
  padding: 12rpx 30rpx;
  border-radius: 50rpx;
  font-size: 24rpx;
  border: 2rpx solid #ddd;
  background-color: #fff;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

.action-btn.default-btn {
  border-color: #4caf50;
  color: #4caf50;
}

.action-btn.default-btn.disabled {
  border-color: #ccc;
  color: #ccc;
  background-color: #f5f5f5;
}

.action-btn.edit-btn {
  border-color: #2196f3;
  color: #2196f3;
}

.action-btn.delete-btn {
  border-color: #f44336;
  color: #f44336;
}

.action-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 底部新增按钮 */
.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #f0f0f0;
  z-index: 1000;
}

.add-btn {
  width: 100%;
  height: 80rpx;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  box-shadow: 0 4rpx 20rpx rgba(76, 175, 80, 0.3);
}

.add-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.btn-text {
  color: white;
}

/* 响应式设计 */
@media (max-width: 600rpx) {
  .address-item {
    padding: 20rpx;
    margin-bottom: 16rpx;
  }

  .address-header {
    margin-bottom: 16rpx;
  }

  .name {
    font-size: 28rpx;
  }

  .phone {
    font-size: 24rpx;
  }

  .address-text {
    font-size: 24rpx;
  }

  .action-buttons {
    flex-direction: column;
    gap: 10rpx;
  }

  .action-btn {
    width: 100%;
    text-align: center;
  }
}

/* 动画效果 */
.address-item {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 长按选中效果 */
.address-item.longpress {
  background-color: #f0f0f0;
}
