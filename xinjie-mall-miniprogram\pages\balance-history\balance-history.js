// pages/balance-history/balance-history.js
const { request } = require("../../utils/request");
const { API } = require("../../config/api");

Page({
  data: {
    currentBalance: '0.00',
    activeTab: 'balance', // balance: 余额记录, recharge: 充值记录
    balanceList: [],
    rechargeList: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10
  },

  onLoad: function (options) {
    this.loadCurrentBalance();
    this.loadBalanceHistory();
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.setData({
      page: 1,
      hasMore: true
    });
    
    if (this.data.activeTab === 'balance') {
      this.loadBalanceHistory(true);
    } else {
      this.loadRechargeHistory(true);
    }
  },

  // 上拉加载更多
  onReachBottom: function () {
    if (this.data.hasMore && !this.data.loading) {
      if (this.data.activeTab === 'balance') {
        this.loadBalanceHistory();
      } else {
        this.loadRechargeHistory();
      }
    }
  },

  // 加载当前余额
  loadCurrentBalance: async function () {
    try {
      const response = await request({
        url: API.balance.info,
        method: 'GET'
      });

      if (response.success) {
        this.setData({
          currentBalance: response.data.balance
        });
      }
    } catch (error) {
      console.error('加载余额失败:', error);
    }
  },

  // 切换标签
  onTabChange: function (e) {
    const tab = e.currentTarget.dataset.tab;
    
    if (tab === this.data.activeTab) return;

    this.setData({
      activeTab: tab,
      page: 1,
      hasMore: true
    });

    if (tab === 'balance') {
      this.loadBalanceHistory(true);
    } else {
      this.loadRechargeHistory(true);
    }
  },

  // 加载余额记录
  loadBalanceHistory: async function (refresh = false) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const response = await request({
        url: API.balance.history,
        method: 'GET',
        data: {
          page: refresh ? 1 : this.data.page,
          pageSize: this.data.pageSize
        }
      });

      if (response.success) {
        const newList = response.data.list || [];
        
        this.setData({
          balanceList: refresh ? newList : [...this.data.balanceList, ...newList],
          hasMore: newList.length === this.data.pageSize,
          page: refresh ? 2 : this.data.page + 1
        });
      }
    } catch (error) {
      console.error('加载余额记录失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
      if (refresh) {
        wx.stopPullDownRefresh();
      }
    }
  },

  // 加载充值记录
  loadRechargeHistory: async function (refresh = false) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const response = await request({
        url: API.balance.rechargeHistory,
        method: 'GET',
        data: {
          page: refresh ? 1 : this.data.page,
          pageSize: this.data.pageSize
        }
      });

      if (response.success) {
        const newList = response.data.list || [];
        
        this.setData({
          rechargeList: refresh ? newList : [...this.data.rechargeList, ...newList],
          hasMore: newList.length === this.data.pageSize,
          page: refresh ? 2 : this.data.page + 1
        });
      }
    } catch (error) {
      console.error('加载充值记录失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
      if (refresh) {
        wx.stopPullDownRefresh();
      }
    }
  },

  // 格式化时间
  formatTime: function (timeStr) {
    const date = new Date(timeStr);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) { // 1天内
      return Math.floor(diff / 3600000) + '小时前';
    } else {
      return date.toLocaleDateString();
    }
  },

  // 获取记录类型文本
  getRecordTypeText: function (type, source) {
    if (type === 1) { // 增加
      switch (source) {
        case 1: return '充值';
        case 3: return '退款';
        case 4: return '调整';
        default: return '收入';
      }
    } else { // 减少
      switch (source) {
        case 2: return '消费';
        case 4: return '调整';
        default: return '支出';
      }
    }
  },

  // 获取支付方式文本
  getPaymentMethodText: function (method) {
    switch (method) {
      case 1: return '微信支付';
      case 2: return '支付宝';
      case 3: return '余额支付';
      case 4: return '后台充值';
      default: return '其他';
    }
  }
});
