<!--pages/balance-history/balance-history.wxml-->
<view class="container">
  <!-- 当前余额显示 -->
  <view class="balance-header">
    <view class="balance-card">
      <text class="balance-label">当前余额</text>
      <text class="balance-amount">¥{{currentBalance}}</text>
    </view>
  </view>

  <!-- 标签切换 -->
  <view class="tab-section">
    <view class="tab-container">
      <view 
        class="tab-item {{activeTab === 'balance' ? 'active' : ''}}"
        bindtap="onTabChange"
        data-tab="balance"
      >
        <text class="tab-text">余额记录</text>
      </view>
      <view 
        class="tab-item {{activeTab === 'recharge' ? 'active' : ''}}"
        bindtap="onTabChange"
        data-tab="recharge"
      >
        <text class="tab-text">充值记录</text>
      </view>
    </view>
  </view>

  <!-- 余额记录列表 -->
  <view class="record-list" wx:if="{{activeTab === 'balance'}}">
    <view class="empty-state" wx:if="{{!balanceList.length && !loading}}">
      <text class="empty-icon">📊</text>
      <text class="empty-text">暂无余额记录</text>
    </view>
    
    <view 
      class="record-item"
      wx:for="{{balanceList}}" 
      wx:key="id"
    >
      <view class="record-left">
        <view class="record-type">
          <text class="type-icon">{{item.type === 1 ? '💰' : '💸'}}</text>
          <text class="type-text">{{getRecordTypeText(item.type, item.source)}}</text>
        </view>
        <text class="record-time">{{formatTime(item.created_at)}}</text>
      </view>
      
      <view class="record-right">
        <text class="amount {{item.type === 1 ? 'income' : 'expense'}}">
          {{item.type === 1 ? '+' : '-'}}¥{{item.amount}}
        </text>
        <text class="balance-after">余额: ¥{{item.balance_after}}</text>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="loading-more" wx:if="{{loading}}">
      <text class="loading-text">加载中...</text>
    </view>
    
    <view class="no-more" wx:if="{{!hasMore && balanceList.length > 0}}">
      <text class="no-more-text">没有更多记录了</text>
    </view>
  </view>

  <!-- 充值记录列表 -->
  <view class="record-list" wx:if="{{activeTab === 'recharge'}}">
    <view class="empty-state" wx:if="{{!rechargeList.length && !loading}}">
      <text class="empty-icon">💳</text>
      <text class="empty-text">暂无充值记录</text>
    </view>
    
    <view 
      class="record-item"
      wx:for="{{rechargeList}}" 
      wx:key="id"
    >
      <view class="record-left">
        <view class="record-type">
          <text class="type-icon">💰</text>
          <text class="type-text">账户充值</text>
        </view>
        <text class="record-time">{{formatTime(item.created_at)}}</text>
        <text class="payment-method">{{getPaymentMethodText(item.payment_method)}}</text>
      </view>
      
      <view class="record-right">
        <text class="amount income">+¥{{item.total_amount}}</text>
        <text class="status {{item.payment_status === 1 ? 'success' : 'pending'}}">
          {{item.payment_status === 1 ? '已完成' : '处理中'}}
        </text>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="loading-more" wx:if="{{loading}}">
      <text class="loading-text">加载中...</text>
    </view>
    
    <view class="no-more" wx:if="{{!hasMore && rechargeList.length > 0}}">
      <text class="no-more-text">没有更多记录了</text>
    </view>
  </view>
</view>
