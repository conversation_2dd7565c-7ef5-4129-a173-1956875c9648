/* pages/balance-history/balance-history.wxss */
.container {
  background: linear-gradient(180deg, #f0fdf4 0%, #ecfdf5 50%, #f7fee7 100%);
  min-height: 100vh;
}

/* 余额头部 */
.balance-header {
  padding: 30rpx;
}

.balance-card {
  background: linear-gradient(135deg, #86efac, #6ee7b7, #34d399);
  border-radius: 24rpx;
  padding: 40rpx;
  text-align: center;
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(52, 211, 153, 0.2);
}

.balance-label {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 10rpx;
  font-weight: 500;
}

.balance-amount {
  display: block;
  font-size: 56rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 标签切换 */
.tab-section {
  padding: 0 30rpx 20rpx;
}

.tab-container {
  background: white;
  border-radius: 20rpx;
  padding: 8rpx;
  display: flex;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: linear-gradient(135deg, #86efac, #6ee7b7);
  box-shadow: 0 4rpx 12rpx rgba(52, 211, 153, 0.3);
}

.tab-text {
  font-size: 30rpx;
  color: #666;
  font-weight: 500;
  transition: color 0.3s ease;
}

.tab-item.active .tab-text {
  color: white;
  font-weight: 600;
}

/* 记录列表 */
.record-list {
  padding: 0 30rpx;
}

.record-item {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(134, 239, 172, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.record-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.record-left {
  flex: 1;
}

.record-type {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.type-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.type-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
}

.record-time {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 4rpx;
}

.payment-method {
  display: block;
  font-size: 24rpx;
  color: #059669;
  background: rgba(134, 239, 172, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
  margin-top: 8rpx;
}

.record-right {
  text-align: right;
}

.amount {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.amount.income {
  color: #059669;
}

.amount.expense {
  color: #dc2626;
}

.balance-after {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.status {
  display: block;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-top: 8rpx;
}

.status.success {
  color: #059669;
  background: rgba(134, 239, 172, 0.1);
}

.status.pending {
  color: #d97706;
  background: rgba(251, 191, 36, 0.1);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 0;
}

.empty-icon {
  display: block;
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading-more {
  text-align: center;
  padding: 40rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.no-more {
  text-align: center;
  padding: 40rpx 0;
}

.no-more-text {
  font-size: 24rpx;
  color: #ccc;
}

/* 动画效果 */
.tab-item:active {
  transform: scale(0.95);
}

.record-item {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
