// pages/cart/cart.js
const { get, post, put, del } = require("../../utils/request");
const { API } = require("../../config/api");
const { formatPrice } = require("../../utils/format");
const { requireLogin } = require("../../utils/auth");

Page({
  data: {
    // 购物车商品列表
    cartItems: [],

    // 全选状态
    selectAll: false,

    // 选中的商品ID列表
    selectedItems: [],

    // 总价格
    totalPrice: 0,

    // 总数量
    totalCount: 0,

    // 加载状态
    loading: false,

    // 编辑状态
    editMode: false,

    // 默认图片
    defaultImage: "/images/common/default-product.png",
  },

  // 页面加载时执行
  onLoad: function (options) {
    this.loadCartItems();
  },

  // 页面显示时执行
  onShow: function () {
    this.loadCartItems();
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.loadCartItems().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载购物车商品
  loadCartItems: function () {
    this.setData({
      loading: true,
    });

    return requireLogin()
      .then(() => {
        return get(API.cart.list);
      })
      .then((res) => {
        if (res.success) {
          const cartItems = (res.data.items || []).map((item) => ({
            ...item,
            priceText: formatPrice(item.discountPrice || item.price),
            originalPriceText: item.hasDiscount ? formatPrice(item.originalPrice) : null,
            subtotalText: formatPrice(item.itemTotal),
            originalSubtotalText: item.hasDiscount ? formatPrice(item.originalTotal) : null,
            discountText: item.hasDiscount ? formatPrice(item.discountAmount * item.quantity) : null,
            selected: false,
          }));

          this.setData({
            cartItems,
            selectedItems: [],
            selectAll: false,
          });

          this.calculateTotal();
        }
      })
      .catch((error) => {
        console.error("加载购物车失败:", error);
      })
      .finally(() => {
        this.setData({
          loading: false,
        });
      });
  },

  // 切换商品选中状态
  onItemSelect: function (e) {
    const id = e.currentTarget.dataset.id;
    const index = e.currentTarget.dataset.index;
    const selected = !this.data.cartItems[index].selected;

    this.setData({
      [`cartItems[${index}].selected`]: selected,
    });

    // 更新选中的商品列表
    let selectedItems = [...this.data.selectedItems];
    if (selected) {
      selectedItems.push(id);
    } else {
      selectedItems = selectedItems.filter((itemId) => itemId !== id);
    }

    this.setData({
      selectedItems,
      selectAll: selectedItems.length === this.data.cartItems.length,
    });

    this.calculateTotal();
  },

  // 全选/取消全选
  onSelectAll: function () {
    const selectAll = !this.data.selectAll;
    const cartItems = this.data.cartItems.map((item) => ({
      ...item,
      selected: selectAll,
    }));

    const selectedItems = selectAll ? cartItems.map((item) => item.id) : [];

    this.setData({
      selectAll,
      cartItems,
      selectedItems,
    });

    this.calculateTotal();
  },

  // 减少商品数量
  onDecrease: function (e) {
    const index = e.currentTarget.dataset.index;
    const item = this.data.cartItems[index];

    if (item.quantity <= 1) {
      return;
    }

    const newQuantity = item.quantity - 1;
    this.updateQuantity(index, newQuantity);
  },

  // 增加商品数量
  onIncrease: function (e) {
    const index = e.currentTarget.dataset.index;
    const item = this.data.cartItems[index];

    if (item.quantity >= item.stock) {
      wx.showToast({
        title: "库存不足",
        icon: "none",
      });
      return;
    }

    const newQuantity = item.quantity + 1;
    this.updateQuantity(index, newQuantity);
  },

  // 更新商品数量
  updateQuantity: function (index, quantity) {
    const item = this.data.cartItems[index];

    // 更新本地数据
    this.setData({
      [`cartItems[${index}].quantity`]: quantity,
      [`cartItems[${index}].subtotalText`]: formatPrice(item.price * quantity),
    });

    // 更新服务器数据
    put(API.cart.update, {
      id: item.id,
      quantity,
    })
      .then((res) => {
        if (res.success) {
          this.calculateTotal();
        }
      })
      .catch((error) => {
        console.error("更新数量失败:", error);
        // 恢复原数量
        this.setData({
          [`cartItems[${index}].quantity`]: item.quantity,
          [`cartItems[${index}].subtotalText`]: formatPrice(
            item.price * item.quantity,
          ),
        });
      });
  },

  // 删除商品
  onDelete: function (e) {
    const index = e.currentTarget.dataset.index;
    const item = this.data.cartItems[index];

    wx.showModal({
      title: "提示",
      content: "确定要删除这个商品吗？",
      success: (res) => {
        if (res.confirm) {
          this.deleteItem(item.id, index);
        }
      },
    });
  },

  // 删除商品
  deleteItem: function (id, index) {
    del(API.cart.delete, { id })
      .then((res) => {
        if (res.success) {
          // 删除本地数据
          const cartItems = [...this.data.cartItems];
          cartItems.splice(index, 1);

          // 更新选中状态
          const selectedItems = this.data.selectedItems.filter(
            (itemId) => itemId !== id,
          );
          const selectAll =
            selectedItems.length === cartItems.length && cartItems.length > 0;

          this.setData({
            cartItems,
            selectedItems,
            selectAll,
          });

          this.calculateTotal();

          wx.showToast({
            title: "删除成功",
            icon: "success",
          });
        }
      })
      .catch((error) => {
        console.error("删除失败:", error);
      });
  },

  // 计算总价
  calculateTotal: function () {
    const selectedItems = this.data.cartItems.filter((item) => item.selected);

    // 计算原价总额
    const originalTotal = selectedItems.reduce(
      (sum, item) => sum + (item.originalPrice || item.price) * item.quantity,
      0,
    );

    // 计算折扣价总额
    const totalPrice = selectedItems.reduce(
      (sum, item) => sum + (item.discountPrice || item.price) * item.quantity,
      0,
    );

    const totalCount = selectedItems.reduce(
      (sum, item) => sum + item.quantity,
      0,
    );

    // 计算总折扣金额
    const totalDiscount = originalTotal - totalPrice;

    this.setData({
      totalPrice,
      totalCount,
      originalTotal,
      totalDiscount,
      totalPriceText: formatPrice(totalPrice),
      originalTotalText: formatPrice(originalTotal),
      totalDiscountText: formatPrice(totalDiscount),
      hasDiscount: totalDiscount > 0
    });
  },

  // 结算
  onCheckout: function () {
    if (this.data.selectedItems.length === 0) {
      wx.showToast({
        title: "请选择要结算的商品",
        icon: "none",
      });
      return;
    }

    // 获取选中的商品
    const selectedProducts = this.data.cartItems.filter(
      (item) => item.selected,
    );

    // 跳转到订单确认页面
    wx.navigateTo({
      url: "/pages/order-confirm/order-confirm",
      success: function (res) {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit("acceptDataFromOpenerPage", {
          products: selectedProducts,
          totalPrice: this.data.totalPrice,
        });
      }.bind(this),
    });
  },

  // 图片加载失败
  onImageError: function (e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      [`cartItems[${index}].image`]: this.data.defaultImage,
    });
  },
});
