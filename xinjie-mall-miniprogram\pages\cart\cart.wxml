<!--pages/cart/cart.wxml-->
<view class="container">
  <!-- 购物车商品列表 -->
  <view class="cart-content" wx:if="{{cartItems.length > 0}}">
    <!-- 全选区域 -->
    <view class="select-all-section">
      <view class="select-all-checkbox" bindtap="onSelectAll">
        <icon type="{{selectAll ? 'success' : 'circle'}}" size="20" color="{{selectAll ? '#4CAF50' : '#ccc'}}"></icon>
        <text class="select-all-text">全选</text>
      </view>
    </view>

    <!-- 商品列表 -->
    <view class="cart-list">
      <view 
        class="cart-item" 
        wx:for="{{cartItems}}" 
        wx:key="id"
      >
        <!-- 选择框 -->
        <view class="item-checkbox" bindtap="onItemSelect" data-id="{{item.id}}" data-index="{{index}}">
          <icon type="{{item.selected ? 'success' : 'circle'}}" size="20" color="{{item.selected ? '#4CAF50' : '#ccc'}}"></icon>
        </view>

        <!-- 商品信息 -->
        <view class="item-content">
          <image 
            class="item-image" 
            src="{{item.image || defaultImage}}" 
            mode="aspectFill"
            binderror="onImageError"
            data-index="{{index}}"
          ></image>
          
          <view class="item-info">
            <text class="item-name">{{item.name}}</text>
            <text class="item-spec" wx:if="{{item.spec}}">{{item.spec}}</text>
            <view class="item-price">
              <text class="price-text" wx:if="{{!item.hasDiscount}}">{{item.priceText}}</text>
              <view wx:if="{{item.hasDiscount}}" class="discount-price">
                <text class="discount-price-text">¥{{item.discountPrice}}</text>
                <text class="original-price-text">¥{{item.originalPrice}}</text>
                <text class="discount-tag">折扣</text>
              </view>
            </view>
          </view>

          <!-- 数量控制 -->
          <view class="quantity-control">
            <view class="quantity-btn" bindtap="onDecrease" data-index="{{index}}">-</view>
            <text class="quantity-text">{{item.quantity}}</text>
            <view class="quantity-btn" bindtap="onIncrease" data-index="{{index}}">+</view>
          </view>

          <!-- 删除按钮 -->
          <view class="delete-btn" bindtap="onDelete" data-index="{{index}}">
            <text>删除</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空购物车状态 -->
  <view class="empty-cart" wx:if="{{!loading && cartItems.length === 0}}">
    <empty text="购物车空空如也~" actionText="去逛逛" bindaction="onGoShopping"></empty>
  </view>

  <!-- 加载状态 -->
  <loading wx:if="{{loading}}"></loading>
</view>

<!-- 底部结算栏 -->
<view class="cart-footer" wx:if="{{cartItems.length > 0}}">
  <view class="footer-info">
    <view class="price-info">
      <view wx:if="{{hasDiscount}}" class="discount-info">
        <text class="original-total">原价：{{originalTotalText}}</text>
        <text class="discount-amount">优惠：-{{totalDiscountText}}</text>
      </view>
      <view class="total-info">
        <text class="total-text">合计：</text>
        <text class="total-price">{{totalPriceText}}</text>
      </view>
    </view>
  </view>
  <view class="footer-action">
    <text class="selected-count">已选择{{totalCount}}件商品</text>
    <button class="checkout-btn" bindtap="onCheckout">结算({{selectedItems.length}})</button>
  </view>
</view>