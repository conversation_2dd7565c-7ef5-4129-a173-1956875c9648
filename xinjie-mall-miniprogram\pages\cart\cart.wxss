/* pages/cart/cart.wxss */
.container {
  background: linear-gradient(180deg, #f0fdf4 0%, #ecfdf5 30%, #f7fee7 70%, #ffffff 100%);
  min-height: 100vh;
  padding-bottom: 120rpx;
  position: relative;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 300rpx;
  background: linear-gradient(135deg, #86efac 0%, #6ee7b7 50%, #34d399 100%);
  opacity: 0.05;
  z-index: 0;
}

/* 全选区域 */
.select-all-section {
  background: rgba(255, 255, 255, 0.98);
  padding: 35rpx 30rpx;
  border-bottom: 1rpx solid rgba(134, 239, 172, 0.1);
  margin: 20rpx 30rpx;
  border-radius: 20rpx 20rpx 0 0;
  box-shadow: 0 4rpx 20rpx rgba(52, 211, 153, 0.06);
  position: relative;
  z-index: 1;
}

.select-all-checkbox {
  display: flex;
  align-items: center;
}

.select-all-text {
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #333;
}

/* 购物车列表 */
.cart-list {
  background: rgba(255, 255, 255, 0.98);
  margin: 0 30rpx;
  border-radius: 0 0 20rpx 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(52, 211, 153, 0.06);
  position: relative;
  z-index: 1;
}

.cart-item {
  display: flex;
  align-items: flex-start;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.cart-item:last-child {
  border-bottom: none;
}

.item-checkbox {
  margin-right: 20rpx;
  margin-top: 10rpx;
}

.item-content {
  flex: 1;
  display: flex;
  position: relative;
}

.item-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.item-name {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-spec {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.item-price {
  margin-top: auto;
}

.price-text {
  font-size: 28rpx;
  color: #ff4444;
  font-weight: bold;
}

/* 折扣价格样式 */
.discount-price {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10rpx;
}

.discount-price-text {
  font-size: 28rpx;
  color: #ff4444;
  font-weight: bold;
}

.original-price-text {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.discount-tag {
  font-size: 20rpx;
  color: #fff;
  background-color: #ff4444;
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
}

/* 数量控制 */
.quantity-control {
  display: flex;
  align-items: center;
  margin-top: auto;
  margin-left: 20rpx;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  border: 1px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #666;
  background-color: #fff;
}

.quantity-btn:first-child {
  border-radius: 8rpx 0 0 8rpx;
}

.quantity-btn:last-child {
  border-radius: 0 8rpx 8rpx 0;
}

.quantity-text {
  width: 80rpx;
  height: 60rpx;
  border-top: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #333;
  background-color: #fff;
}

/* 删除按钮 */
.delete-btn {
  position: absolute;
  top: 0;
  right: 0;
  padding: 10rpx;
  font-size: 24rpx;
  color: #ff4444;
}

/* 空购物车状态 */
.empty-cart {
  padding: 120rpx 0;
}

/* 底部结算栏 */
.cart-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 1000;
}

.footer-info {
  display: flex;
  align-items: center;
}

.price-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.discount-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-bottom: 10rpx;
}

.original-total {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.discount-amount {
  font-size: 24rpx;
  color: #ff4444;
}

.total-info {
  display: flex;
  align-items: center;
}

.total-text {
  font-size: 28rpx;
  color: #333;
}

.total-price {
  font-size: 36rpx;
  color: #ff4444;
  font-weight: bold;
}

.footer-action {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.selected-count {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.checkout-btn {
  background-color: #4caf50;
  color: #fff;
  font-size: 28rpx;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  border: none;
  min-width: 160rpx;
}

.checkout-btn:disabled {
  background-color: #ccc;
}

/* 动画效果 */
.cart-item {
  transition: background-color 0.3s ease;
}

.cart-item:active {
  background-color: #f8f8f8;
}

.quantity-btn {
  transition: background-color 0.3s ease;
}

.quantity-btn:active {
  background-color: #f0f0f0;
}

.checkout-btn {
  transition: background-color 0.3s ease;
}

.checkout-btn:active {
  background-color: #45a049;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .cart-item {
    flex-direction: column;
    align-items: stretch;
  }

  .item-content {
    flex-direction: column;
  }

  .quantity-control {
    margin-top: 20rpx;
    margin-left: 0;
    align-self: flex-start;
  }
}
