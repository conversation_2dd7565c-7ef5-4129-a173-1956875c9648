// pages/category/category.js
const { get, post, put, del } = require("../../utils/request");
const { API } = require("../../config/api");
const { formatPrice } = require("../../utils/format");
const { processImageUrls, getCategoryImageUrl, getProductImageUrl } = require("../../utils/image");
const { requireUserInfo, requireLogin } = require("../../utils/auth");

Page({
  data: {
    // 分类列表
    categories: [],

    // 当前选中的分类
    selectedCategory: null,
    selectedCategoryIndex: 0,

    // 商品列表
    products: [],

    // 搜索关键词
    searchKeyword: "",

    // 分页信息
    pagination: {
      page: 1,
      pageSize: 10,
      hasMore: true,
    },

    // 加载状态
    loading: {
      categories: false,
      products: false,
      more: false,
    },

    // 购物车浮层
    cartDrawer: {
      visible: false,
      items: [],
      totalPrice: 0,
      totalCount: 0,
    },

    // 默认图片
    defaultImage: "/images/common/default-product.png",
    defaultCategoryImage: "/images/common/default-category.png",
    
    // 规格选择弹窗
    specModal: {
      visible: false,
      product: {},
      quantity: 1,
      totalPrice: "0.00", // 新增总价字段
    },
  },

  // 页面加载时执行
  onLoad: function (options) {
    console.log("页面加载，确保购物车浮层隐藏");
    console.log("接收到的参数:", options);
    
    // 确保购物车浮层初始状态为隐藏
    this.setData({
      "cartDrawer.visible": false
    });
    
    // 保存传入的分类ID参数
    if (options.categoryId) {
      this.setData({
        targetCategoryId: parseInt(options.categoryId)
      });
    }
    
    this.loadCategories();
    this.loadCartItems();
  },

  // 页面显示时执行
  onShow: function () {
    // 确保页面显示时购物车浮层也是隐藏的
    this.setData({
      "cartDrawer.visible": false
    });
    this.loadCartItems();
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.refreshData().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onReachBottom: function () {
    this.loadMoreProducts();
  },

  // 刷新数据
  refreshData: function () {
    this.setData({
      "pagination.page": 1,
      "pagination.hasMore": true,
      products: [],
    });

    return Promise.all([this.loadCategories(), this.loadProducts()]);
  },

  // 加载分类列表
  loadCategories: function () {
    this.setData({
      "loading.categories": true,
    });

    return get(API.category.list)
      .then((res) => {
        console.log("分类API响应:", res);
        // 检查API响应是否成功 (兼容code:200和success字段)
        if (res.code === 200 || res.success) {
          const categories = (res.data || []).map(category => {
            // 优先使用icon字段，如果没有则使用image字段，都没有则使用默认图片
            const imagePath = category.icon || category.image;
            const processedCategory = {
              ...category,
              icon: imagePath ? getCategoryImageUrl(imagePath) : this.data.defaultCategoryImage
            };
            console.log(`处理分类 ${category.name}:`, {
              originalIcon: category.icon,
              originalImage: category.image,
              processedIcon: processedCategory.icon
            });
            return processedCategory;
          });
          
          console.log("处理后的分类数据:", categories);
          this.setData({
            categories,
          });

          // 如果有目标分类ID，自动选中对应分类
          if (this.data.targetCategoryId) {
            const targetIndex = categories.findIndex(cat => cat.id === this.data.targetCategoryId);
            if (targetIndex !== -1) {
              this.selectCategory(categories[targetIndex], targetIndex);
              console.log(`自动选中分类: ${categories[targetIndex].name} (ID: ${this.data.targetCategoryId})`);
            } else {
              console.warn(`未找到目标分类ID: ${this.data.targetCategoryId}`);
              // 如果没找到目标分类，选择第一个
              if (categories.length > 0) {
                this.selectCategory(categories[0], 0);
              }
            }
          } else {
            // 如果还没有选中分类，选择第一个
            if (!this.data.selectedCategory && categories.length > 0) {
              this.selectCategory(categories[0], 0);
            }
          }
        } else {
          console.error("分类API返回失败:", res);
          wx.showToast({
            title: res.message || "加载分类失败",
            icon: "none"
          });
        }
      })
      .catch((error) => {
        console.error("加载分类失败:", error);
        wx.showToast({
          title: "网络连接失败",
          icon: "none"
        });
      })
      .finally(() => {
        this.setData({
          "loading.categories": false,
        });
      });
  },

  // 加载商品列表
  loadProducts: function () {
    if (!this.data.selectedCategory) {
      return Promise.resolve();
    }

    this.setData({
      "loading.products": true,
    });

    const params = {
      categoryId: this.data.selectedCategory.id,
      page: this.data.pagination.page,
      limit: this.data.pagination.pageSize, // 使用 limit 参数名，兼容后端
    };

    if (this.data.searchKeyword) {
      params.keyword = this.data.searchKeyword;
    }

    return get(API.product.list, params)
      .then((res) => {
        if (res.code === 200 || res.success) {
          const products = (res.data.list || []).map((product) => ({
            ...product,
            image: getProductImageUrl(product.main_image || product.image),
            priceText: formatPrice(product.price),
            // 保留原始价格数字用于计算
            originalPrice: parseFloat(product.price) || 0,
            // 格式化价格显示
            price: formatPrice(product.price),
            // 格式化规格信息 - 使用商品描述字段
            description: product.description || '',
            weight: product.weight ? `${product.weight}克` : '',
            unit: product.unit || '件'
          }));

          this.setData({
            products:
              this.data.pagination.page === 1
                ? products
                : [...this.data.products, ...products],
            "pagination.hasMore":
              products.length === this.data.pagination.pageSize,
          });
        } else {
          console.error("商品API返回失败:", res);
          wx.showToast({
            title: res.message || "加载商品失败",
            icon: "none"
          });
        }
      })
      .catch((error) => {
        console.error("加载商品失败:", error);
        wx.showToast({
          title: "网络连接失败",
          icon: "none"
        });
      })
      .finally(() => {
        this.setData({
          "loading.products": false,
        });
      });
  },

  // 加载更多商品
  loadMoreProducts: function () {
    if (this.data.loading.more || !this.data.pagination.hasMore) {
      return;
    }

    this.setData({
      "loading.more": true,
      "pagination.page": this.data.pagination.page + 1,
    });

    this.loadProducts().then(() => {
      this.setData({
        "loading.more": false,
      });
    });
  },

  // 选择分类
  selectCategory: function (category, index) {
    this.setData({
      selectedCategory: category,
      selectedCategoryIndex: index,
      "pagination.page": 1,
      "pagination.hasMore": true,
      products: [],
    });

    this.loadProducts();
  },

  // 分类点击事件
  onCategoryTap: function (e) {
    const { category, index } = e.currentTarget.dataset;
    this.selectCategory(category, index);
  },

  // 商品点击事件
  onProductTap: function (e) {
    const { product } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?id=${product.id}`,
    });
  },

  // 显示规格选择弹窗
  onShowSpecModal: function (e) {
    // 移除 stopPropagation，因为微信小程序的事件对象可能没有这个方法
    const { product } = e.currentTarget.dataset;
    
    // 处理商品图片URL
    const productWithImage = {
      ...product,
      image: product.main_image || product.image || this.data.defaultImage
    };
    
    // 使用原始价格数字进行计算
    const price = product.originalPrice || parseFloat(product.price) || 0;
    
    this.setData({
      'specModal.visible': true,
      'specModal.product': productWithImage,
      'specModal.quantity': 1,
      'specModal.totalPrice': (price * 1).toFixed(2),
    });
  },

  // 隐藏规格选择弹窗
  onHideSpecModal: function () {
    this.setData({
      'specModal.visible': false,
    });
  },

  // 减少数量
  onQuantityDecrease: function () {
    if (this.data.specModal.quantity > 1) {
      const newQuantity = this.data.specModal.quantity - 1;
      const price = this.data.specModal.product.originalPrice || parseFloat(this.data.specModal.product.price) || 0;
      const totalPrice = (price * newQuantity).toFixed(2);
      this.setData({
        'specModal.quantity': newQuantity,
        'specModal.totalPrice': totalPrice,
      });
    }
  },

  // 增加数量
  onQuantityIncrease: function () {
    const maxQuantity = this.data.specModal.product.stock || 999;
    const currentQuantity = parseInt(this.data.specModal.quantity) || 1;
    
    if (currentQuantity < maxQuantity) {
      const newQuantity = currentQuantity + 1;
      const price = this.data.specModal.product.originalPrice || parseFloat(this.data.specModal.product.price) || 0;
      const totalPrice = (price * newQuantity).toFixed(2);
      this.setData({
        'specModal.quantity': newQuantity.toString(),
        'specModal.totalPrice': totalPrice,
      });
    } else {
      wx.showToast({
        title: "库存不足",
        icon: "none",
      });
    }
  },

  // 长按快速增加数量
  onQuantityIncreaseLongPress: function () {
    this.startQuantityAutoChange('increase');
  },

  // 长按快速减少数量
  onQuantityDecreaseLongPress: function () {
    this.startQuantityAutoChange('decrease');
  },

  // 开始自动改变数量
  startQuantityAutoChange: function (direction) {
    const maxQuantity = this.data.specModal.product.stock || 999;
    const minQuantity = 1;
    let currentQuantity = parseInt(this.data.specModal.quantity) || 1;
    
    const changeQuantity = () => {
      if (direction === 'increase' && currentQuantity < maxQuantity) {
        currentQuantity++;
      } else if (direction === 'decrease' && currentQuantity > minQuantity) {
        currentQuantity--;
      } else {
        // 达到限制，停止自动改变
        this.stopQuantityAutoChange();
        return;
      }
      
      const price = this.data.specModal.product.originalPrice || parseFloat(this.data.specModal.product.price) || 0;
      const totalPrice = (price * currentQuantity).toFixed(2);
      
      this.setData({
        'specModal.quantity': currentQuantity.toString(),
        'specModal.totalPrice': totalPrice,
      });
      
      // 继续自动改变
      this.quantityAutoChangeTimer = setTimeout(changeQuantity, 100);
    };
    
    // 开始自动改变
    changeQuantity();
  },

  // 停止自动改变数量
  stopQuantityAutoChange: function () {
    if (this.quantityAutoChangeTimer) {
      clearTimeout(this.quantityAutoChangeTimer);
      this.quantityAutoChangeTimer = null;
    }
  },

  // 数量输入
  onQuantityInput: function (e) {
    const inputValue = e.detail.value;
    const cursor = e.detail.cursor || 0;
    
    // 如果输入为空，显示占位符
    if (!inputValue || inputValue === '') {
      this.setData({
        'specModal.quantity': '',
        'specModal.totalPrice': '0.00',
      });
      return;
    }
    
    // 解析输入值
    const value = parseInt(inputValue) || 1;
    const maxQuantity = this.data.specModal.product.stock || 999;
    const minQuantity = 1;
    
    // 验证数量范围
    let validQuantity = value;
    let showWarning = false;
    let warningMessage = '';
    
    if (value < minQuantity) {
      validQuantity = minQuantity;
      showWarning = true;
      warningMessage = `最少购买${minQuantity}件`;
    } else if (value > maxQuantity) {
      validQuantity = maxQuantity;
      showWarning = true;
      warningMessage = `库存不足，最多购买${maxQuantity}件`;
    }
    
    // 计算总价
    const price = this.data.specModal.product.originalPrice || parseFloat(this.data.specModal.product.price) || 0;
    const totalPrice = (price * validQuantity).toFixed(2);
    
    // 更新数据
    this.setData({
      'specModal.quantity': inputValue, // 保持用户输入的原值
      'specModal.validQuantity': validQuantity, // 存储有效值
      'specModal.totalPrice': totalPrice,
      'specModal.cursor': cursor
    });
    
    // 显示警告信息（但不阻止输入）
    if (showWarning) {
      wx.showToast({
        title: warningMessage,
        icon: 'none',
        duration: 1500
      });
    }
  },

  // 数量输入框获得焦点时的处理
  onQuantityFocus: function (e) {
    // 记录当前输入框的值，用于后续比较
    this.setData({
      'specModal.focusValue': e.detail.value
    });
  },

  // 数量输入框点击时的处理
  onQuantityTap: function (e) {
    // 记录点击时间，用于检测双击
    const now = Date.now();
    const lastTapTime = this.data.specModal.lastTapTime || 0;
    
    if (now - lastTapTime < 300) {
      // 双击：快速重置为1
      const price = this.data.specModal.product.originalPrice || parseFloat(this.data.specModal.product.price) || 0;
      this.setData({
        'specModal.quantity': '1',
        'specModal.validQuantity': 1,
        'specModal.totalPrice': (price * 1).toFixed(2),
        'specModal.selectionStart': 0,
        'specModal.selectionEnd': 1
      });
      return;
    }
    
    // 单击：自动全选文本，方便用户直接输入新值
    const inputValue = e.detail.value;
    if (inputValue && inputValue !== '1') {
      // 延迟执行，确保点击事件完成后再设置选择
      setTimeout(() => {
        this.setData({
          'specModal.quantity': inputValue,
          'specModal.selectionStart': 0,
          'specModal.selectionEnd': inputValue.length
        });
      }, 50);
    }
    
    // 记录点击时间
    this.setData({
      'specModal.lastTapTime': now
    });
  },



  // 数量输入框失去焦点时的验证
  onQuantityBlur: function (e) {
    const value = parseInt(e.detail.value) || 1;
    const maxQuantity = this.data.specModal.product.stock || 999;
    const minQuantity = 1;
    
    let validQuantity = value;
    
    // 验证数量范围
    if (value < minQuantity) {
      validQuantity = minQuantity;
      wx.showToast({
        title: `最少购买${minQuantity}件`,
        icon: 'none'
      });
    } else if (value > maxQuantity) {
      validQuantity = maxQuantity;
      wx.showToast({
        title: `库存不足，最多购买${maxQuantity}件`,
        icon: 'none'
      });
    }
    
    // 更新数量和总价
    const price = this.data.specModal.product.originalPrice || parseFloat(this.data.specModal.product.price) || 0;
    const totalPrice = (price * validQuantity).toFixed(2);
    
    this.setData({
      'specModal.quantity': validQuantity,
      'specModal.totalPrice': totalPrice,
      'specModal.focusValue': null // 清除焦点值
    });
  },

  // 确认免费获取
  onConfirmAddToCart: function () {
    const { product, quantity } = this.data.specModal;
    
    requireLogin().then(() => {
      this.addToCartWithQuantity(product, quantity);
    }).catch(() => {
      // 用户未登录，跳转到登录页面
      wx.navigateTo({
        url: '/pages/login/login'
      });
    });
  },

  // 添加到购物车（带数量）
  addToCartWithQuantity: function (product, quantity) {
    const cartData = {
      productId: product.id,
      quantity: quantity,
    };

    post(API.cart.add, cartData)
      .then((res) => {
        if (res.code === 200 || res.success) {
          wx.showToast({
            title: "已添加至购物车",
            icon: "success",
          });
          this.loadCartItems(); // 刷新购物车数据
          this.onHideSpecModal(); // 隐藏弹窗
        } else {
          wx.showToast({
            title: res.message || "添加失败",
            icon: "none",
          });
        }
      })
      .catch((error) => {
        console.error("添加到购物车失败:", error);
        wx.showToast({
          title: "网络连接失败",
          icon: "none",
        });
      });
  },

  // 添加到购物车（原有方法，保持兼容）
  onAddToCart: function (e) {
    e.stopPropagation(); // 阻止事件冒泡
    const { product } = e.currentTarget.dataset;
    
    requireLogin().then(() => {
      this.addToCart(product);
    }).catch(() => {
      // 用户未登录，跳转到登录页面
      wx.navigateTo({
        url: '/pages/login/login'
      });
    });
  },

  // 添加商品到购物车
  addToCart: function (product) {
    const cartData = {
      productId: product.id,
      quantity: 1,
    };

    post(API.cart.add, cartData)
      .then((res) => {
        if (res.code === 200 || res.success) {
          wx.showToast({
            title: "已加入购物车",
            icon: "success",
          });
          this.loadCartItems(); // 刷新购物车数据
        } else {
          wx.showToast({
            title: res.message || "添加失败",
            icon: "none",
          });
        }
      })
      .catch((error) => {
        console.error("添加到购物车失败:", error);
        wx.showToast({
          title: "网络连接失败",
          icon: "none",
        });
      });
  },

  // 加载购物车商品
  loadCartItems: function () {
    return requireUserInfo()
      .then(() => {
        return get(API.cart.list);
      })
      .then((res) => {
        if (res.code === 200 || res.success) {
          const cartItems = (res.data.items || []).map((item) => {
            // 处理包含product关联的数据结构
            const product = item.product || item;
            // 确保价格是数字类型
            const price = parseFloat(product.price) || 0;
            return {
              id: item.id,
              quantity: item.quantity,
              selected: item.selected,
              // 商品信息
              name: product.name,
              price: price, // 使用数字价格
              image: getProductImageUrl(product.main_image || product.image),
              stock: product.stock,
              // 格式化价格
              priceText: formatPrice(price),
              subtotalText: formatPrice(price * item.quantity),
            };
          });

          const totalPrice = cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
          const totalCount = cartItems.reduce((sum, item) => sum + item.quantity, 0);

          this.setData({
            "cartDrawer.items": cartItems,
            "cartDrawer.totalPrice": formatPrice(totalPrice),
            "cartDrawer.totalCount": totalCount,
          });
        }
      })
      .catch((error) => {
        console.error("加载购物车失败:", error);
        // 如果用户未登录，设置空的购物车数据
        this.setData({
          "cartDrawer.items": [],
          "cartDrawer.totalPrice": "¥0.00",
          "cartDrawer.totalCount": 0,
        });
      });
  },

  // 显示购物车浮层
  onShowCartDrawer: function () {
    console.log("显示购物车浮层");
    // 确保购物车数据已加载
    this.loadCartItems().then(() => {
      this.setData({
        "cartDrawer.visible": true
      });
      // 禁止页面滚动
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
    });
  },

  // 隐藏购物车浮层
  onHideCartDrawer: function () {
    console.log("隐藏购物车浮层");
    this.setData({
      "cartDrawer.visible": false
    });
    // 恢复页面滚动
  },

  // 购物车商品数量减少
  onCartItemDecrease: function (e) {
    const index = e.currentTarget.dataset.index;
    const item = this.data.cartDrawer.items[index];

    if (item.quantity <= 1) {
      return;
    }

    this.updateCartItemQuantity(item.id, item.quantity - 1, index);
  },

  // 购物车商品数量增加
  onCartItemIncrease: function (e) {
    const index = e.currentTarget.dataset.index;
    const item = this.data.cartDrawer.items[index];

    if (item.quantity >= item.stock) {
      wx.showToast({
        title: "库存不足",
        icon: "none"
      });
      return;
    }

    this.updateCartItemQuantity(item.id, item.quantity + 1, index);
  },

  // 更新购物车商品数量
  updateCartItemQuantity: function (cartItemId, quantity, index) {
    put(API.cart.update, {
      cartItemId: cartItemId,
      quantity: quantity
    })
      .then((res) => {
        if (res.code === 200 || res.success) {
          // 更新本地数据
          const cartItems = [...this.data.cartDrawer.items];
          cartItems[index].quantity = quantity;
          cartItems[index].subtotalText = formatPrice(cartItems[index].price * quantity);

          const totalPrice = cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
          const totalCount = cartItems.reduce((sum, item) => sum + item.quantity, 0);

          this.setData({
            "cartDrawer.items": cartItems,
            "cartDrawer.totalPrice": formatPrice(totalPrice),
            "cartDrawer.totalCount": totalCount,
          });
        } else {
          wx.showToast({
            title: res.message || "更新失败",
            icon: "none"
          });
        }
      })
      .catch((error) => {
        console.error("更新购物车失败:", error);
        wx.showToast({
          title: "更新失败",
          icon: "none"
        });
      });
  },

  // 删除购物车商品
  onCartItemDelete: function (e) {
    const index = e.currentTarget.dataset.index;
    const item = this.data.cartDrawer.items[index];

    wx.showModal({
      title: "提示",
      content: "确定要删除这个商品吗？",
      success: (res) => {
        if (res.confirm) {
          this.deleteCartItem(item.id, index);
        }
      },
    });
  },

  // 删除购物车商品
  deleteCartItem: function (cartItemId, index) {
    del(`${API.cart.delete}/${cartItemId}`)
      .then((res) => {
        if (res.code === 200 || res.success) {
          // 删除本地数据
          const cartItems = [...this.data.cartDrawer.items];
          cartItems.splice(index, 1);

          const totalPrice = cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
          const totalCount = cartItems.reduce((sum, item) => sum + item.quantity, 0);

          this.setData({
            "cartDrawer.items": cartItems,
            "cartDrawer.totalPrice": formatPrice(totalPrice),
            "cartDrawer.totalCount": totalCount,
          });

          wx.showToast({
            title: "删除成功",
            icon: "success"
          });
        } else {
          wx.showToast({
            title: res.message || "删除失败",
            icon: "none"
          });
        }
      })
      .catch((error) => {
        console.error("删除购物车商品失败:", error);
        wx.showToast({
          title: "删除失败",
          icon: "none"
        });
      });
  },

  // 去购物车页面
  onGoToCart: function () {
    this.onHideCartDrawer();
    wx.navigateTo({
      url: "/pages/cart/cart",
    });
  },

  // 搜索输入
  onSearchInput: function (e) {
    this.setData({
      searchKeyword: e.detail.value,
    });
  },

  // 搜索提交
  onSearchSubmit: function () {
    this.setData({
      "pagination.page": 1,
      "pagination.hasMore": true,
      products: [],
    });

    this.loadProducts();
  },

  // 搜索点击事件
  onSearchTap: function () {
    wx.navigateTo({
      url: "/pages/search/search",
    });
  },

  // 查看其他分类
  onViewOther: function () {
    // 跳转到首页
    wx.switchTab({
      url: "/pages/index/index",
    });
  },

  // 图片加载错误处理
  onImageError: function (e) {
    const { index } = e.currentTarget.dataset;
    this.setData({
      [`products[${index}].image`]: this.data.defaultImage,
    });
  },

  // 分类图标加载错误处理
  onCategoryIconError: function (e) {
    const { index } = e.currentTarget.dataset;
    this.setData({
      [`categories[${index}].icon`]: this.data.defaultCategoryImage,
    });
  },

  // 购物车商品图片加载失败
  onCartItemImageError: function (e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      [`cartDrawer.items[${index}].image`]: this.data.defaultImage,
    });
  },

  // 分类图标加载成功
  onCategoryIconSuccess: function (e) {
    console.log('分类图标加载成功:', e.detail);
  },

  // 商品图片加载成功
  onProductImageSuccess: function (e) {
    console.log('商品图片加载成功:', e.detail);
  },

  // 商品图片加载失败
  onProductImageError: function (e) {
    const { index } = e.currentTarget.dataset;
    console.error('商品图片加载失败:', e.detail);
    this.setData({
      [`products[${index}].image`]: this.data.defaultImage,
    });
  },

  // 购物车商品图片加载成功
  onCartItemImageSuccess: function (e) {
    console.log('购物车商品图片加载成功:', e.detail);
  },

  // 规格选择弹窗商品图片加载成功
  onSpecProductImageSuccess: function (e) {
    console.log('规格选择弹窗商品图片加载成功:', e.detail);
  },

  // 规格选择弹窗商品图片加载失败
  onSpecProductImageError: function (e) {
    console.error('规格选择弹窗商品图片加载失败:', e.detail);
    this.setData({
      'specModal.product.image': this.data.defaultImage,
    });
  },
});
