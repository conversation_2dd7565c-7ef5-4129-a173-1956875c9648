<!--pages/category/category.wxml-->
<view class="container">
  <!-- 现代化搜索框 -->
  <view class="modern-search">
    <view class="modern-search-input" bindtap="onSearchTap">
      <text class="modern-search-placeholder">搜索商品...</text>
      <text class="modern-search-icon">🔍</text>
    </view>
  </view>

  <!-- 主体内容 -->
  <view class="main-content">
    <!-- 左侧分类导航 -->
    <scroll-view class="category-nav" scroll-y="{{true}}">
      <view 
        class="category-item {{selectedCategoryIndex === index ? 'active' : ''}}" 
        wx:for="{{categories}}" 
        wx:key="id"
        bindtap="onCategoryTap"
        data-category="{{item}}"
        data-index="{{index}}"
      >
        <preload-image 
          class="category-icon" 
          src="{{item.icon}}" 
          mode="aspectFit" 
          width="40px"
          height="40px"
          bindsuccess="onCategoryIconSuccess"
          binderror="onCategoryIconError" 
          data-index="{{index}}"
        />
        <text class="category-name">{{item.name}}</text>
      </view>
      
      <!-- 加载状态 -->
      <view class="category-loading" wx:if="{{loading.categories}}">
        <text>加载中...</text>
      </view>
    </scroll-view>

    <!-- 右侧商品列表 -->
    <scroll-view class="product-content" scroll-y="{{true}}" bindscrolltolower="onReachBottom">
      <!-- 商品列表 -->
      <view class="products-list" wx:if="{{products.length > 0}}">
        <view 
          class="product-item" 
          wx:for="{{products}}" 
          wx:key="id"
          bindtap="onProductTap"
          data-product="{{item}}"
        >
          <!-- 商品图片 -->
          <preload-image 
            class="product-image" 
            src="{{item.main_image || item.image || defaultImage}}" 
            mode="aspectFill"
            width="100%"
            height="200px"
            bindsuccess="onProductImageSuccess"
            binderror="onProductImageError"
            data-index="{{index}}"
          />
          
          <!-- 商品信息 -->
          <view class="product-info">
            <!-- 商品名称 -->
            <text class="product-name">{{item.name}}</text>
            
            <!-- 建议零售价 -->
            <text class="suggested-price">建议零售价: {{item.price}}</text>
            
            <!-- 商品描述 -->
            <text class="product-description" wx:if="{{item.description}}">{{item.description}}</text>
            
            <!-- 规格信息 -->
            <text class="product-spec" wx:if="{{item.weight || item.unit}}">规格型号: {{item.weight || ''}} {{item.unit || ''}}</text>
            
            <!-- 实际价格 -->
            <view class="price-section">
              <text class="actual-price">{{item.price}} /{{item.unit || '件'}}</text>
            </view>
            
            <!-- 选规格按钮 -->
            <view class="select-spec-btn" catchtap="onShowSpecModal" data-product="{{item}}">
              <text class="select-spec-text">选规格</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" wx:if="{{loading.more}}">
        <text>加载更多...</text>
      </view>

      <!-- 没有更多数据 -->
      <view class="no-more" wx:if="{{!pagination.hasMore && products.length > 0}}">
        <text>已经到底了~</text>
      </view>

      <!-- 加载状态 -->
      <loading wx:if="{{loading.products && products.length === 0}}"></loading>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{!loading.products && products.length === 0 && selectedCategory}}">
        <empty text="该分类暂无商品" actionText="看看其他" bindaction="onViewOther"></empty>
      </view>
    </scroll-view>
  </view>

  <!-- 底部购物车小按钮 -->
  <view class="bottom-cart-btn" bindtap="onShowCartDrawer">
    <text class="cart-btn-icon">🛒</text>
    <view class="cart-btn-badge" wx:if="{{cartDrawer.totalCount > 0}}">{{cartDrawer.totalCount}}</view>
  </view>

  <!-- 购物车浮层 -->
  <view class="cart-drawer-mask {{cartDrawer.visible ? 'visible' : ''}}" bindtap="onHideCartDrawer"></view>
  <view class="cart-drawer {{cartDrawer.visible ? 'visible' : ''}}">
    <!-- 购物车头部 -->
    <view class="cart-drawer-header">
      <text class="cart-drawer-title">购物车</text>
      <text class="cart-drawer-close" bindtap="onHideCartDrawer">✕</text>
    </view>

    <!-- 购物车商品列表 -->
    <scroll-view class="cart-drawer-content" scroll-y="{{true}}">
      <view wx:if="{{cartDrawer.items.length === 0}}" class="cart-empty">
        <text class="cart-empty-text">购物车是空的</text>
        <text class="cart-empty-tip">快去添加一些商品吧~</text>
      </view>
      
      <view wx:else class="cart-items">
        <view 
          class="cart-item" 
          wx:for="{{cartDrawer.items}}" 
          wx:key="id"
        >
          <preload-image 
            class="cart-item-image" 
            src="{{item.image || defaultImage}}" 
            mode="aspectFill"
            width="60px"
            height="60px"
            bindsuccess="onCartItemImageSuccess"
            binderror="onCartItemImageError"
            data-index="{{index}}"
          />
          <view class="cart-item-info">
            <text class="cart-item-name">{{item.name}}</text>
            <text class="cart-item-price">{{item.priceText}}</text>
            <view class="cart-item-actions">
              <view class="quantity-control">
                <text class="quantity-btn" bindtap="onCartItemDecrease" data-index="{{index}}">-</text>
                <text class="quantity-text">{{item.quantity}}</text>
                <text class="quantity-btn" bindtap="onCartItemIncrease" data-index="{{index}}">+</text>
              </view>
              <text class="delete-btn" bindtap="onCartItemDelete" data-index="{{index}}">删除</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 购物车底部 -->
    <view class="cart-drawer-footer" wx:if="{{cartDrawer.items.length > 0}}">
      <view class="cart-total-info">
        <text class="cart-total-label">总计:</text>
        <text class="cart-total-price">{{cartDrawer.totalPrice}}</text>
        <text class="cart-total-count">({{cartDrawer.totalCount}}件)</text>
      </view>
      <view class="cart-actions">
        <view class="cart-action-btn secondary" bindtap="onHideCartDrawer">
          <text>继续购物</text>
        </view>
        <view class="cart-action-btn primary" bindtap="onGoToCart">
          <text>去结算</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 规格选择弹窗 -->
  <view class="spec-modal-mask {{specModal.visible ? 'visible' : ''}}" bindtap="onHideSpecModal"></view>
  <view class="spec-modal {{specModal.visible ? 'visible' : ''}}">
    <view class="spec-modal-header">
      <text class="spec-modal-close" bindtap="onHideSpecModal">✕</text>
    </view>
    
    <view class="spec-modal-content">
      <!-- 商品信息 -->
      <view class="spec-product-info">
        <view class="spec-product-header">
          <preload-image 
            class="spec-product-image" 
            src="{{specModal.product.image || defaultImage}}" 
            mode="aspectFill"
            width="80px"
            height="80px"
            bindsuccess="onSpecProductImageSuccess"
            binderror="onSpecProductImageError"
          />
          <view class="spec-product-basic">
          <text class="spec-product-name">{{specModal.product.name}}</text>
            <text class="spec-product-price">{{specModal.product.price}}</text>
          </view>
        </view>
        
        <view class="spec-product-extra">
          <text class="spec-product-code" wx:if="{{specModal.product.id}}">商品编码: {{specModal.product.id}}</text>
          <!-- 购买单位 -->
          <view class="purchase-unit-section">
            <text class="purchase-unit-label">购买单位</text>
            <view class="purchase-unit-btn">
              <text class="purchase-unit-text">{{specModal.product.unit || '件'}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 数量选择 -->
      <view class="quantity-section">
        <view class="quantity-control">
          <text class="quantity-btn" bindtap="onQuantityDecrease" bindlongpress="onQuantityDecreaseLongPress" bindtouchend="stopQuantityAutoChange">-</text>
          <view class="quantity-input-wrapper">
            <input 
              class="quantity-input" 
              type="number" 
              value="{{specModal.quantity}}" 
              bindinput="onQuantityInput"
              bindblur="onQuantityBlur"
              bindfocus="onQuantityFocus"
              bindtap="onQuantityTap"
              placeholder="1"
              min="1"
              max="{{specModal.product.stock || 999}}"
              cursor-spacing="10"
              selection-start="{{specModal.selectionStart || -1}}"
              selection-end="{{specModal.selectionEnd || -1}}"
            />

          </view>
          <text class="quantity-btn" bindtap="onQuantityIncrease" bindlongpress="onQuantityIncreaseLongPress" bindtouchend="stopQuantityAutoChange">+</text>
        </view>
        <text class="quantity-label">数量</text>
        <text class="stock-info" wx:if="{{specModal.product.stock !== undefined}}">库存: {{specModal.product.stock}}</text>
      </view>
      
      <!-- 总价 -->
      <view class="total-price-section">
        <text class="total-price-label">总价:</text>
        <text class="total-price-value">{{specModal.totalPrice}}</text>
      </view>
    </view>
    
    <!-- 规格选择弹窗底部 -->
    <view class="spec-modal-footer">
              <view class="spec-modal-actions">
          <view class="spec-modal-btn secondary" bindtap="onHideSpecModal">
            <text class="btn-text">取消</text>
      </view>
          <view class="spec-modal-btn primary" bindtap="onConfirmAddToCart">
            <text class="btn-text">添加至购物车</text>
            <text class="btn-icon">🛒</text>
        </view>
      </view>
    </view>
  </view>
</view> 