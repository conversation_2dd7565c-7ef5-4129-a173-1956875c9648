/* pages/category/category.wxss */
@import "../../styles/modern-search.wxss";

.container {
  height: 100vh;
  background: linear-gradient(180deg, #f0fdf4 0%, #ecfdf5 30%, #f7fee7 70%, #ffffff 100%);
  display: flex;
  flex-direction: column;
  position: relative;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 400rpx;
  background: linear-gradient(135deg, #86efac 0%, #6ee7b7 50%, #34d399 100%);
  opacity: 0.05;
  z-index: 0;
}

/* 分类页面搜索框样式覆盖 */
.container .modern-search {
  position: relative;
  top: auto;
  z-index: 10;
  flex-shrink: 0;
}

/* 主体内容 */
.main-content {
  flex: 1;
  display: flex;
  height: 0;
  margin: 0;
  overflow: hidden;
  padding-bottom: 120rpx; /* 为底部购物车按钮预留空间 */
  margin-top: 0; /* 确保不被搜索框遮挡 */
}

/* 左侧分类导航 - 贴合左侧 */
.category-nav {
  width: 170rpx;
  background: rgba(255, 255, 255, 0.98);
  flex-shrink: 0;
  border-right: 2rpx solid rgba(134, 239, 172, 0.15);
  margin-left: 0; /* 确保贴合左侧 */
  box-shadow: 2rpx 0 12rpx rgba(52, 211, 153, 0.08);
  position: relative;
  z-index: 1;
}

.category-item {
  padding: 28rpx 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-bottom: 1rpx solid rgba(134, 239, 172, 0.1);
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  margin: 0 8rpx;
  border-radius: 12rpx;
  border-radius: 12rpx;
  margin-bottom: 4rpx;
}

.category-item:last-child {
  border-bottom: none;
}

.category-item.active {
  background: linear-gradient(135deg, rgba(74, 124, 89, 0.12), rgba(74, 124, 89, 0.08));
  border-left: 3rpx solid #4a7c59;
  box-shadow: 0 2rpx 8rpx rgba(74, 124, 89, 0.15);
}

.category-item.active::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: linear-gradient(180deg, #4a7c59, #6b9a7a);
  border-radius: 0 2rpx 2rpx 0;
}

.category-icon {
  width: 50rpx;
  height: 50rpx;
  margin-bottom: 10rpx;
  border-radius: 50%;
  box-shadow: 0 3rpx 12rpx rgba(74, 124, 89, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2rpx solid rgba(74, 124, 89, 0.1);
}

.category-item.active .category-icon {
  transform: scale(1.05);
  box-shadow: 0 4rpx 16rpx rgba(74, 124, 89, 0.2);
  border-color: rgba(74, 124, 89, 0.3);
}

.category-name {
  font-size: 22rpx;
  color: #666;
  text-align: center;
  line-height: 1.3;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  word-break: break-all;
  min-height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6rpx;
  letter-spacing: 0.5rpx;
}

.category-item.active .category-name {
  color: #4a7c59;
  font-weight: 700;
  transform: scale(1.02);
}

.category-item:active {
  background: rgba(74, 124, 89, 0.05);
  transform: scale(0.98);
}

.category-loading {
  padding: 40rpx 0;
  text-align: center;
  color: #7f8c8d;
  font-size: 24rpx;
  font-weight: 500;
}

/* 右侧商品列表 */
.product-content {
  flex: 1;
  background: rgba(255, 255, 255, 0.98);
  padding: 15rpx;
  overflow-y: auto;
  padding-bottom: 140rpx; /* 为底部购物车按钮预留更多空间 */
}

.products-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.product-item {
  display: flex;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(74, 124, 89, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1rpx solid rgba(74, 124, 89, 0.06);
  padding: 18rpx;
}

.product-item:active {
  transform: scale(0.98) translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(74, 124, 89, 0.12);
}

.product-image {
  width: 140rpx;
  height: 140rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
  margin-right: 20rpx;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 140rpx;
}

.product-name {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 78rpx;
}

.suggested-price {
  font-size: 22rpx;
  color: #95a5a6;
  margin-bottom: 4rpx;
  font-weight: 400;
}

.product-description {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 6rpx;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-spec {
  font-size: 22rpx;
  color: #7f8c8d;
  margin-bottom: 8rpx;
  font-weight: 400;
}

.price-section {
  margin-bottom: 12rpx;
  margin-top: auto;
}

.actual-price {
  font-size: 30rpx;
  color: #4a7c59;
  font-weight: 700;
  letter-spacing: 0.5rpx;
}

/* 选规格按钮 */
.select-spec-btn {
  background: #4a7c59;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  text-align: center;
  align-self: flex-start;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(74, 124, 89, 0.3);
}

.select-spec-btn:active {
  transform: scale(0.95);
  background: #3d6b4a;
}

.select-spec-text {
  color: white;
  font-size: 24rpx;
  font-weight: 600;
}

/* 规格选择弹窗 */
.spec-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.spec-modal-mask.visible {
  opacity: 1;
  visibility: visible;
}

.spec-modal {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  z-index: 1001;
  transform: translateY(100%);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10rpx);
}

.spec-modal.visible {
  transform: translateY(0);
  box-shadow: 0 -12rpx 40rpx rgba(0, 0, 0, 0.2);
}

.spec-modal-header {
  padding: 30rpx;
  text-align: right;
  border-bottom: 1rpx solid rgba(74, 124, 89, 0.1);
  background: white;
  border-radius: 24rpx 24rpx 0 0;
}

.spec-modal-close {
  font-size: 40rpx;
  color: #95a5a6;
  padding: 10rpx;
  border-radius: 50%;
  transition: all 0.3s ease;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
}

.spec-modal-close:active {
  background: rgba(149, 165, 166, 0.1);
  transform: scale(0.9);
}

.spec-modal-content {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
}

.spec-product-info {
  margin-bottom: 40rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.spec-product-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  position: relative;
}

.spec-product-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 40rpx;
  flex-shrink: 0;
}

.spec-product-basic {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  margin-left: 50rpx;
}

.spec-product-extra {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  margin-left: 150rpx;
}

.spec-product-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 10rpx;
  line-height: 1.4;
  word-break: break-all;
  min-width: 200rpx;
}

.spec-product-code {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 0;
  min-width: 200rpx;
}

.spec-product-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #4a7c59;
  margin-bottom: 0;
  min-width: 200rpx;
}

.purchase-unit-section {
  display: flex;
  align-items: center;
  margin-bottom: 0;
}

.purchase-unit-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 15rpx;
}

.purchase-unit-btn {
  background: #e8f4fd;
  border: 1rpx solid #b3d9f2;
  border-radius: 6rpx;
  padding: 6rpx 12rpx;
}

.purchase-unit-text {
  font-size: 22rpx;
  color: #1890ff;
}

.quantity-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 25rpx;
  flex-wrap: wrap;
  gap: 15rpx;
}

.quantity-control {
  display: flex;
  align-items: center;
  border: 2rpx solid rgba(74, 124, 89, 0.2);
  border-radius: 10rpx;
  overflow: hidden;
  background: white;
  box-shadow: 0 2rpx 8rpx rgba(74, 124, 89, 0.1);
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  color: #4a7c59;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.quantity-btn:active {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  transform: scale(0.95);
}

.quantity-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-input {
  width: 120rpx;
  height: 60rpx;
  text-align: center;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  background: white;
  font-size: 26rpx;
  font-weight: 600;
  color: #2c3e50;
  padding: 0 10rpx;
  transition: all 0.3s ease;
  position: relative;
}

.quantity-input:focus {
  border-color: #4a7c59;
  box-shadow: 0 0 0 2rpx rgba(74, 124, 89, 0.1);
  outline: none;
}



.stock-info {
  font-size: 22rpx;
  color: #999;
  margin-left: auto;
}

.spec-modal-footer {
  padding: 30rpx;
  border-top: 1rpx solid rgba(74, 124, 89, 0.1);
  background: white;
  border-radius: 0 0 24rpx 24rpx;
}

.total-price-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 25rpx;
  padding: 15rpx 20rpx;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 10rpx;
  border: 1rpx solid rgba(74, 124, 89, 0.1);
}

.total-price-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
}

.total-price-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #4a7c59;
  text-shadow: 0 1rpx 2rpx rgba(74, 124, 89, 0.1);
}

.spec-modal-actions {
  display: flex;
  gap: 20rpx;
}

.spec-modal-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.spec-modal-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.spec-modal-btn:active::before {
  transform: translateX(100%);
}

.spec-modal-btn.secondary {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  color: #6c757d;
  border: 2rpx solid #dee2e6;
}

.spec-modal-btn.secondary:active {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  transform: scale(0.98);
}

.spec-modal-btn.primary {
  background: linear-gradient(135deg, #4a7c59, #6b9a7a);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(74, 124, 89, 0.3);
}

.spec-modal-btn.primary:active {
  background: linear-gradient(135deg, #3d6b4a, #5a8a6a);
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(74, 124, 89, 0.4);
}

.btn-text {
  font-size: 28rpx;
  font-weight: 600;
  margin-right: 8rpx;
}

.btn-icon {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 底部购物车小按钮 */
.bottom-cart-btn {
  position: fixed;
  bottom: 120rpx; /* 位于导航栏上方 */
  right: 30rpx;
  width: 100rpx;
  height: 100rpx;
  background: var(--primary-gradient);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(74, 124, 89, 0.3);
  z-index: 100;
  cursor: pointer;
  transition: all 0.3s ease;
}

.bottom-cart-btn:active {
  transform: scale(0.95);
  box-shadow: 0 6rpx 20rpx rgba(74, 124, 89, 0.4);
}

.cart-btn-icon {
  font-size: 40rpx;
  color: white;
  position: relative;
}

.cart-btn-badge {
  position: absolute;
  top: -12rpx;
  right: -12rpx;
  background: #e74c3c;
  color: white;
  font-size: 20rpx;
  font-weight: bold;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6rpx;
  box-shadow: 0 2rpx 8rpx rgba(231, 76, 60, 0.4);
  animation: badgeBounce 0.5s ease;
}

@keyframes badgeBounce {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* 购物车浮层 */
.cart-drawer-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(0rpx);
}

.cart-drawer-mask.visible {
  opacity: 1;
  visibility: visible;
  backdrop-filter: blur(4rpx);
}

.cart-drawer {
  position: fixed;
  bottom: 100rpx; /* 调整到导航栏上方 */
  left: 0;
  right: 0;
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  z-index: 1000;
  transform: translateY(100%);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  max-height: 60vh; /* 限制最大高度，避免遮挡过多内容 */
  display: flex;
  flex-direction: column;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10rpx);
}

.cart-drawer.visible {
  transform: translateY(0);
  box-shadow: 0 -12rpx 40rpx rgba(0, 0, 0, 0.2);
}

/* 购物车头部 */
.cart-drawer-header {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid rgba(74, 124, 89, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  background: white;
  border-radius: 24rpx 24rpx 0 0;
}

.cart-drawer-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

.cart-drawer-close {
  font-size: 36rpx;
  color: #95a5a6;
  cursor: pointer;
  padding: 8rpx;
  border-radius: 50%;
  transition: all 0.3s ease;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cart-drawer-close:active {
  background: rgba(149, 165, 166, 0.1);
  transform: scale(0.9);
}

/* 购物车内容 */
.cart-drawer-content {
  flex: 1;
  overflow-y: auto;
  padding: 20rpx;
  background: #f8f9fa;
}

/* 购物车空状态 */
.cart-empty {
  text-align: center;
  padding: 60rpx 40rpx;
}

.cart-empty-text {
  display: block;
  font-size: 28rpx;
  color: #7f8c8d;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.cart-empty-tip {
  display: block;
  font-size: 24rpx;
  color: #bdc3c7;
}

/* 购物车商品列表 */
.cart-items {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.cart-item {
  display: flex;
  background: white;
  border-radius: 12rpx;
  padding: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(74, 124, 89, 0.05);
}

.cart-item-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.cart-item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 0; /* 防止文字溢出 */
}

.cart-item-name {
  font-size: 26rpx;
  color: #2c3e50;
  font-weight: 500;
  margin-bottom: 6rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
}

.cart-item-price {
  font-size: 28rpx;
  color: #e74c3c;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.cart-item-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12rpx;
}

/* 数量控制 */
.quantity-control {
  display: flex;
  align-items: center;
  background: rgba(74, 124, 89, 0.1);
  border-radius: 16rpx;
  padding: 2rpx;
}

.quantity-btn {
  width: 44rpx;
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
  font-size: 24rpx;
  color: #4a7c59;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.quantity-btn:active {
  transform: scale(0.9);
  background: rgba(74, 124, 89, 0.1);
}

.quantity-text {
  min-width: 50rpx;
  text-align: center;
  font-size: 26rpx;
  color: #2c3e50;
  font-weight: 600;
  margin: 0 8rpx;
}

/* 删除按钮 */
.delete-btn {
  color: #e74c3c;
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
  background: rgba(231, 76, 60, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.delete-btn:active {
  background: rgba(231, 76, 60, 0.2);
  transform: scale(0.95);
}

/* 购物车底部 */
.cart-drawer-footer {
  padding: 24rpx 30rpx;
  border-top: 1rpx solid rgba(74, 124, 89, 0.1);
  background: white;
  flex-shrink: 0;
}

.cart-total-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.cart-total-label {
  font-size: 26rpx;
  color: #7f8c8d;
}

.cart-total-price {
  font-size: 32rpx;
  color: #e74c3c;
  font-weight: bold;
}

.cart-total-count {
  font-size: 22rpx;
  color: #95a5a6;
}

.cart-actions {
  display: flex;
  gap: 16rpx;
}

.cart-action-btn {
  flex: 1;
  padding: 18rpx;
  border-radius: 12rpx;
  text-align: center;
  font-size: 26rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cart-action-btn.secondary {
  background: rgba(74, 124, 89, 0.1);
  color: #4a7c59;
}

.cart-action-btn.secondary:active {
  background: rgba(74, 124, 89, 0.2);
  transform: scale(0.98);
}

.cart-action-btn.primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-primary);
}

.cart-action-btn.primary:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-medium);
}

/* 加载更多和无更多数据 */
.load-more,
.no-more {
  padding: 40rpx 0;
  text-align: center;
  color: #7f8c8d;
  font-size: 28rpx;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  margin: 20rpx 0;
}

/* 空状态 */
.empty-state {
  padding: 120rpx 40rpx;
  text-align: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  margin: 20rpx 0;
}

.empty-state text {
  color: #7f8c8d;
  font-size: 28rpx;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .main-content {
    margin: 0; /* 移除边距，让分类栏贴合左侧 */
  }
  
  .category-nav {
    width: 140rpx;
  }

  .category-name {
    font-size: 22rpx;
  }
  
  .product-item {
    width: 100%;
  }
  
  .product-content {
    padding: 20rpx;
  }

  .bottom-cart-btn {
    right: 20rpx;
    width: 90rpx;
    height: 90rpx;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6rpx;
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.3);
  border-radius: 3rpx;
}

::-webkit-scrollbar-track {
  background: transparent;
}
