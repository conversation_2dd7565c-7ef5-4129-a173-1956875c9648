<!-- 客服聊天页面 -->
<view class="chat-container">
  <!-- 聊天消息列表 -->
  <scroll-view class="message-list" scroll-y="true" scroll-top="{{scrollTop}}" scroll-into-view="{{scrollIntoView}}">
    <!-- 系统消息 -->
    <view class="message-item system-message" wx:if="{{session.session_status === 'waiting'}}">
      <view class="system-content">
        <text>正在为您接入客服，请稍候...</text>
      </view>
    </view>

    <!-- 消息列表 -->
    <view class="message-item {{message.sender_type === 'user' ? 'user-message' : 'service-message'}}" 
          wx:for="{{messages}}" wx:key="id" id="msg-{{message.id}}">
      
      <!-- 用户消息 -->
      <view class="user-msg" wx:if="{{message.sender_type === 'user'}}">
        <view class="message-content">
          <!-- 文本消息 -->
          <text class="text-content" wx:if="{{message.message_type === 'text'}}">{{message.content}}</text>
          
          <!-- 图片消息 -->
          <image class="image-content" wx:if="{{message.message_type === 'image'}}" 
                 src="{{message.extra_data.fileUrl}}" mode="widthFix" bindtap="previewImage" data-url="{{message.extra_data.fileUrl}}"/>
          
          <!-- 商品消息 -->
          <view class="product-content" wx:if="{{message.message_type === 'product'}}" bindtap="goToProduct" data-id="{{message.extra_data.productId}}">
            <image class="product-image" src="{{message.extra_data.productImage}}" mode="aspectFill"/>
            <view class="product-info">
              <text class="product-name">{{message.extra_data.productName}}</text>
              <text class="product-price">¥{{message.extra_data.productPrice}}</text>
            </view>
          </view>
        </view>
        <image class="avatar" src="{{userInfo.avatarUrl || '/static/images/default-avatar.png'}}" mode="aspectFill"/>
      </view>

      <!-- 客服消息 -->
      <view class="service-msg" wx:else>
        <image class="avatar" src="/static/images/service-avatar.png" mode="aspectFill"/>
        <view class="message-content">
          <!-- 系统消息 -->
          <view class="system-content" wx:if="{{message.sender_type === 'system'}}">
            <text>{{message.content}}</text>
          </view>
          
          <!-- 客服消息 -->
          <view class="service-content" wx:else>
            <text class="text-content">{{message.content}}</text>
          </view>
        </view>
      </view>

      <!-- 消息时间 -->
      <view class="message-time">{{message.created_at}}</view>
    </view>

    <!-- 正在输入提示 -->
    <view class="typing-indicator" wx:if="{{isTyping}}">
      <image class="avatar" src="/static/images/service-avatar.png" mode="aspectFill"/>
      <view class="typing-content">
        <text>客服正在输入...</text>
        <view class="typing-dots">
          <view class="dot"></view>
          <view class="dot"></view>
          <view class="dot"></view>
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 快捷回复 -->
  <view class="quick-replies" wx:if="{{quickReplies.length > 0 && !showInput}}">
    <scroll-view class="quick-reply-list" scroll-x="true">
      <view class="quick-reply-item" wx:for="{{quickReplies}}" wx:key="index" bindtap="sendQuickReply" data-content="{{item}}">
        <text>{{item}}</text>
      </view>
    </scroll-view>
  </view>

  <!-- 输入区域 -->
  <view class="input-area">
    <!-- 功能按钮 -->
    <view class="function-buttons">
      <view class="func-btn" bindtap="chooseImage">
        <image class="func-icon" src="/static/images/camera.png"/>
      </view>
      <view class="func-btn" bindtap="showProductSelector">
        <image class="func-icon" src="/static/images/product.png"/>
      </view>
      <view class="func-btn" bindtap="showFAQ">
        <image class="func-icon" src="/static/images/faq.png"/>
      </view>
    </view>

    <!-- 输入框 -->
    <view class="input-wrapper">
      <textarea class="message-input" 
                placeholder="请输入您的问题..." 
                value="{{inputText}}" 
                bindinput="onInput"
                bindconfirm="sendMessage"
                confirm-type="send"
                auto-height
                maxlength="500"/>
      <button class="send-btn" bindtap="sendMessage" disabled="{{!inputText.trim()}}">发送</button>
    </view>
  </view>

  <!-- 会话状态栏 -->
  <view class="session-status" wx:if="{{session.session_status === 'closed'}}">
    <text>会话已结束</text>
    <button class="rate-btn" bindtap="showRating" wx:if="{{!session.satisfaction}}">评价服务</button>
  </view>
</view>

<!-- 常见问题弹窗 -->
<view class="faq-modal" wx:if="{{showFAQModal}}">
  <view class="modal-mask" bindtap="hideFAQ"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">常见问题</text>
      <icon class="close-icon" type="clear" size="20" bindtap="hideFAQ"/>
    </view>
    <scroll-view class="faq-list" scroll-y="true">
      <view class="faq-item" wx:for="{{faqList}}" wx:key="id" bindtap="selectFAQ" data-question="{{item.question}}" data-answer="{{item.answer}}">
        <text class="faq-question">{{item.question}}</text>
        <text class="faq-category">{{item.category}}</text>
      </view>
    </scroll-view>
  </view>
</view>

<!-- 评价弹窗 -->
<view class="rating-modal" wx:if="{{showRatingModal}}">
  <view class="modal-mask" bindtap="hideRating"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">服务评价</text>
    </view>
    <view class="rating-content">
      <text class="rating-label">请为本次服务打分：</text>
      <view class="star-rating">
        <icon wx:for="{{[1,2,3,4,5]}}" wx:key="*this" 
              type="{{item <= rating ? 'success' : 'success_no_circle'}}" 
              size="30" 
              color="{{item <= rating ? '#ff6b35' : '#ccc'}}"
              bindtap="setRating" 
              data-rating="{{item}}"/>
      </view>
      <textarea class="feedback-input" 
                placeholder="请输入您的建议或意见（可选）" 
                value="{{feedback}}" 
                bindinput="onFeedbackInput"
                maxlength="200"/>
      <view class="rating-buttons">
        <button class="cancel-btn" bindtap="hideRating">取消</button>
        <button class="submit-btn" bindtap="submitRating">提交</button>
      </view>
    </view>
  </view>
</view>
