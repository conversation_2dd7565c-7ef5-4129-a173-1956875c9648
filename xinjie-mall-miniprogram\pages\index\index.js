// index.js
const app = getApp()
const { request } = require('../../utils/request');
const { getBannerImageUrl, getProductImageUrl, getCategoryImageUrl } = require('../../utils/image');
const { debugImageUrl, batchTestImages } = require('../../utils/debug-image');

Page({
  data: {
    banners: [],
    hotProducts: [],
    recommendProducts: [],
    categories: [],
    loading: true
  },

  onLoad() {
    // 强制清除缓存的调试信息
    console.log('🔄 强制清除缓存时间戳:', Date.now());
    console.log('🌍 当前环境检查:', require('../../config/env').getCurrentEnv());

    this.loadPageData()
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadPageData()
  },

  // 加载页面数据
  async loadPageData() {
    this.setData({ loading: true })
    
    try {
      await Promise.all([
        this.loadBanners(),
        this.loadHotProducts(),
        this.loadRecommendProducts(),
        this.loadCategories()
      ])
    } catch (error) {
      console.error('加载页面数据失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载轮播图
  async loadBanners() {
    try {
      console.log('开始加载轮播图...');

      // 调试：检查当前环境配置
      const { getCurrentEnv } = require('../../config/env');
      const currentEnv = getCurrentEnv();
      console.log('🌍 当前环境配置:', currentEnv);
      const res = await request({
        url: '/front/banner/list',
        method: 'GET'
      });
      
      console.log('🚨🚨🚨 轮播图API响应 - 时间戳:', Date.now());
      console.log('🚨🚨🚨 API响应数据:', res);
      const banners = res.data || res.list || res || [];
      console.log('🚨🚨🚨 提取的banners数组:', banners);

      // 处理轮播图图片URL
      const processedBanners = banners.map((banner, index) => {
        console.log(`🚨🚨🚨 处理第${index + 1}个轮播图:`, banner);
        const originalUrl = banner.image_url || banner.image;
        console.log(`🚨🚨🚨 原始URL:`, originalUrl);
        const processedUrl = getBannerImageUrl(originalUrl);
        console.log(`🚨🚨🚨 处理后URL:`, processedUrl);

        // 调试信息
        console.log('🔍 轮播图处理:', {
          original: originalUrl,
          processed: processedUrl,
          banner: banner
        });

        return {
          ...banner,
          image_url: processedUrl
        };
      });

      // 批量测试图片URL（仅在开发环境）- 临时禁用
      if (false && wx.getAccountInfoSync().miniProgram.envVersion === 'develop') {
        batchTestImages(processedBanners, 'image_url').then(results => {
          console.log('📊 轮播图URL测试结果:', results);
        });
      }

      console.log('🎯 最终设置的轮播图数据:', processedBanners);

      this.setData({
        banners: processedBanners
      });

      // 测试：延迟检查数据是否正确设置
      setTimeout(() => {
        console.log('📊 页面数据中的轮播图:', this.data.banners);
      }, 100);
    } catch (error) {
      console.error('加载轮播图失败:', error);
      this.setData({ banners: [] });
    }
  },

  // 加载热门商品
  async loadHotProducts() {
    try {
      console.log('开始加载热门商品...');
      const res = await request({
        url: '/front/product/hot',
        method: 'GET'
      });
      
      console.log('热门商品API响应:', res);
      const products = res.data || res.list || res || [];
      
      // 处理商品图片URL
      const processedHotProducts = products.map(product => ({
        ...product,
        image_url: getProductImageUrl(product.main_image || product.image_url || product.image)
      }));
      
        this.setData({
          hotProducts: processedHotProducts
        });
    } catch (error) {
      console.error('加载热门商品失败:', error);
      this.setData({ hotProducts: [] });
    }
  },

  // 加载推荐商品
  async loadRecommendProducts() {
    try {
      console.log('开始加载推荐商品...');
      const res = await request({
        url: '/front/product/recommend',
        method: 'GET'
      });
      
      console.log('推荐商品API响应:', res);
      const products = res.data || res.list || res || [];
      
      // 处理商品图片URL
      const processedRecommendProducts = products.map(product => ({
        ...product,
        image_url: getProductImageUrl(product.main_image || product.image_url || product.image)
      }));
      
        this.setData({
          recommendProducts: processedRecommendProducts
        });
    } catch (error) {
      console.error('加载推荐商品失败:', error);
      this.setData({ recommendProducts: [] });
    }
  },

  // 加载分类
  async loadCategories() {
    try {
      console.log('开始加载分类...');
      const res = await request({
        url: '/front/category/list',
        method: 'GET'
      });
      
      console.log('分类API响应:', res);
      const categories = res.data || res.list || res || [];
      
      // 处理分类图片URL
      const processedCategories = categories.map(category => ({
        ...category,
        image_url: getCategoryImageUrl(category.image_url || category.icon || category.image)
      }));
      
        this.setData({
          categories: processedCategories
        });
    } catch (error) {
      console.error('加载分类失败:', error);
      this.setData({ categories: [] });
    }
  },

  // 轮播图点击
  onBannerTap(e) {
    const { index } = e.currentTarget.dataset
    const banner = this.data.banners[index]
    
    if (banner.link_url) {
      // 如果有链接，跳转到对应页面
      if (banner.link_url.startsWith('/pages/')) {
        wx.navigateTo({
          url: banner.link_url
        })
      } else if (banner.link_url.startsWith('http')) {
        // 外部链接，可以打开webview
        wx.navigateTo({
          url: `/pages/webview/webview?url=${encodeURIComponent(banner.link_url)}`
        })
      }
    }
  },

  // 商品点击
  onProductTap(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?id=${id}`
    })
  },

  // 分类点击
  onCategoryTap(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/category/category?categoryId=${id}`
    })
  },

  // 搜索点击
  onSearchTap() {
    wx.navigateTo({
      url: '/pages/search/search'
    })
  },

  // 查看更多热门商品
  onMoreHotTap() {
    wx.navigateTo({
      url: '/pages/product-list/product-list?type=hot'
    })
  },

  // 查看更多推荐商品
  onMoreRecommendTap() {
    wx.navigateTo({
      url: '/pages/product-list/product-list?type=recommend'
    })
  },

  // 图片加载成功事件
  onBannerImageSuccess(e) {
    console.log('🎉 轮播图加载成功:', e);
    console.log('图片尺寸:', e.detail);
  },

  onCategoryImageSuccess(e) {
    console.log('分类图片加载成功:', e.detail);
  },

  onHotProductImageSuccess(e) {
    console.log('热门商品图片加载成功:', e.detail);
  },

  onRecommendProductImageSuccess(e) {
    console.log('推荐商品图片加载成功:', e.detail);
  },

  // 图片加载失败事件
  onBannerImageError(e) {
    console.error('💥 轮播图加载失败:', e);
    console.error('错误详情:', e.detail);
    console.error('当前target:', e.currentTarget);
    console.error('图片src:', e.currentTarget?.dataset?.src || e.target?.src);
  },

  onCategoryImageError(e) {
    console.error('分类图片加载失败:', e.detail);
  },

  onHotProductImageError(e) {
    console.error('热门商品图片加载失败:', e.detail);
  },

  onRecommendProductImageError(e) {
    console.error('推荐商品图片加载失败:', e.detail);
  },
  onMoreRecommendTap() {
    wx.navigateTo({
      url: '/pages/product-list/product-list?type=recommend'
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadPageData().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 分享
  onShareAppMessage() {
    return {
      title: '心洁茶叶商城',
      path: '/pages/index/index'
    }
  }
})
