<!--index.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading">加载中...</view>
  </view>

  <!-- 页面内容 -->
  <view wx:else>
    <!-- 现代化搜索框 -->
    <view class="modern-search">
      <view class="modern-search-input" bindtap="onSearchTap">
        <text class="modern-search-placeholder">搜索心洁茗茶...</text>
        <text class="modern-search-icon">🔍</text>
      </view>
    </view>
    
    <!-- 轮播图 -->
    <view class="banner-section" wx:if="{{banners.length > 0}}">
      <swiper class="banner-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="3000" duration="500">
        <swiper-item wx:for="{{banners}}" wx:key="id" bindtap="onBannerTap" data-index="{{index}}">
          <image
            class="banner-image"
            src="{{item.image_url}}"
            mode="aspectFill"
            bindload="onBannerImageSuccess"
            binderror="onBannerImageError"
            lazy-load="{{false}}"
            show-menu-by-longpress="{{false}}"
          />
          <view class="banner-info" wx:if="{{item.title}}">
            <text class="banner-title">{{item.title}}</text>
            <text class="banner-desc" wx:if="{{item.description}}">{{item.description}}</text>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 分类导航 -->
    <view class="category-section" wx:if="{{categories.length > 0}}">
      <view class="section-title">商品分类</view>
      <view class="category-grid">
        <view class="category-item" wx:for="{{categories}}" wx:key="id" bindtap="onCategoryTap" data-id="{{item.id}}">
          <preload-image 
            class="category-image" 
            src="{{item.image_url}}" 
            mode="aspectFill"
            width="60px"
            height="60px"
            bindsuccess="onCategoryImageSuccess"
            binderror="onCategoryImageError"
          />
          <text class="category-name">{{item.name}}</text>
        </view>
      </view>
    </view>

    <!-- 热门商品 -->
    <view class="product-section" wx:if="{{hotProducts.length > 0}}">
      <view class="section-header">
        <text class="section-title">热门商品</text>
        <text class="more-link" bindtap="onMoreHotTap">查看更多</text>
      </view>
      <view class="product-grid">
        <!-- 第一个商品占据整行 -->
        <view class="product-item product-item-featured" wx:if="{{hotProducts[0]}}" bindtap="onProductTap" data-id="{{hotProducts[0].id}}">
          <preload-image 
            class="product-image product-image-featured" 
            src="{{hotProducts[0].image_url}}" 
            mode="aspectFill"
            width="100%"
            height="240px"
            bindsuccess="onHotProductImageSuccess"
            binderror="onHotProductImageError"
          />
          <view class="product-info product-info-featured">
            <text class="product-name product-name-featured">{{hotProducts[0].name}}</text>
            <view class="product-price">
              <text class="current-price">¥{{hotProducts[0].price}}</text>
              <text class="original-price" wx:if="{{hotProducts[0].original_price > hotProducts[0].price}}">¥{{hotProducts[0].original_price}}</text>
            </view>
          </view>
        </view>
        
        <!-- 其他商品按三个一排 -->
        <view class="product-item product-item-small" wx:for="{{hotProducts}}" wx:key="id" wx:if="{{index > 0}}" bindtap="onProductTap" data-id="{{item.id}}">
          <preload-image 
            class="product-image product-image-small" 
            src="{{item.image_url}}" 
            mode="aspectFill"
            width="100%"
            height="120px"
            bindsuccess="onHotProductImageSuccess"
            binderror="onHotProductImageError"
          />
          <view class="product-info product-info-small">
            <text class="product-name product-name-small">{{item.name}}</text>
            <view class="product-price">
              <text class="current-price">¥{{item.price}}</text>
              <text class="original-price" wx:if="{{item.original_price > item.price}}">¥{{item.original_price}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 推荐商品 -->
    <view class="product-section" wx:if="{{recommendProducts.length > 0}}">
      <view class="section-header">
        <text class="section-title">推荐商品</text>
        <text class="more-link" bindtap="onMoreRecommendTap">查看更多</text>
      </view>
      <view class="product-grid">
        <!-- 第一个商品占据整行 -->
        <view class="product-item product-item-featured" wx:if="{{recommendProducts[0]}}" bindtap="onProductTap" data-id="{{recommendProducts[0].id}}">
          <preload-image 
            class="product-image product-image-featured" 
            src="{{recommendProducts[0].image_url}}" 
            mode="aspectFill"
            width="100%"
            height="240px"
            bindsuccess="onRecommendProductImageSuccess"
            binderror="onRecommendProductImageError"
          />
          <view class="product-info product-info-featured">
            <text class="product-name product-name-featured">{{recommendProducts[0].name}}</text>
            <view class="product-price">
              <text class="current-price">¥{{recommendProducts[0].price}}</text>
              <text class="original-price" wx:if="{{recommendProducts[0].original_price > recommendProducts[0].price}}">¥{{recommendProducts[0].original_price}}</text>
            </view>
          </view>
        </view>
        
        <!-- 其他商品按三个一排 -->
        <view class="product-item product-item-small" wx:for="{{recommendProducts}}" wx:key="id" wx:if="{{index > 0}}" bindtap="onProductTap" data-id="{{item.id}}">
          <preload-image 
            class="product-image product-image-small" 
            src="{{item.image_url}}" 
            mode="aspectFill"
            width="100%"
            height="120px"
            bindsuccess="onRecommendProductImageSuccess"
            binderror="onRecommendProductImageError"
          />
          <view class="product-info product-info-small">
            <text class="product-name product-name-small">{{item.name}}</text>
            <view class="product-price">
              <text class="current-price">¥{{item.price}}</text>
              <text class="original-price" wx:if="{{item.original_price > item.price}}">¥{{item.original_price}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{!loading && banners.length === 0 && categories.length === 0 && hotProducts.length === 0 && recommendProducts.length === 0}}" class="empty-state">
      <view class="empty-icon">📦</view>
      <text class="empty-text">暂无数据</text>
    </view>
  </view>
</view> 