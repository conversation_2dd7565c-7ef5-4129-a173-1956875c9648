/* pages/index/index.wxss */
@import "../../styles/modern-search.wxss";

.container {
  background: linear-gradient(180deg, #f0fdf4 0%, #ecfdf5 30%, #f7fee7 70%, #ffffff 100%);
  min-height: 100vh;
  padding-bottom: 30rpx;
  position: relative;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 600rpx;
  background: linear-gradient(135deg, #86efac 0%, #6ee7b7 50%, #34d399 100%);
  opacity: 0.05;
  z-index: 0;
}

/* 搜索框 - 现代设计 */
.search-bar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  padding: 30rpx 20rpx 20rpx;
  position: sticky;
  top: 0;
  z-index: 100;
  border-bottom: 1rpx solid rgba(134, 239, 172, 0.1);
}

.search-input {
  background: rgba(255, 255, 255, 0.95);
  border: 2rpx solid rgba(134, 239, 172, 0.2);
  border-radius: 28rpx;
  padding: 26rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 8rpx 32rpx rgba(52, 211, 153, 0.12);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-input:focus-within {
  border-color: rgba(134, 239, 172, 0.6);
  box-shadow: 0 12rpx 40rpx rgba(52, 211, 153, 0.2);
  transform: translateY(-2rpx);
}

.search-placeholder {
  color: #8e8e93;
  font-size: 28rpx;
  font-weight: 400;
}

.search-icon {
  width: 36rpx;
  height: 36rpx;
  opacity: 0.6;
}

/* 轮播图 - 现代卡片设计 */
.banner-section {
  margin: 20rpx 30rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 12rpx 40rpx rgba(52, 211, 153, 0.15);
  position: relative;
  z-index: 1;
}

.banner-swiper {
  height: 320rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
}

.banner-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 40rpx 30rpx 30rpx;
  color: white;
}

.banner-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
}

.banner-desc {
  font-size: 24rpx;
  opacity: 0.9;
  display: block;
}

/* 分类导航 - 现代网格设计 */
.category-section {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20rpx);
  margin: 20rpx 30rpx;
  padding: 35rpx 30rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(52, 211, 153, 0.08);
  border: 1rpx solid rgba(134, 239, 172, 0.1);
  position: relative;
  z-index: 1;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #059669;
  margin-bottom: 30rpx;
  text-align: center;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #4a7c59, #6b9a7a);
  border-radius: 2rpx;
}

.category-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 20rpx;
  padding: 15rpx 0;
}

.category-item {
  width: calc(25% - 15rpx);
  min-width: 140rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 25rpx 10rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(74, 124, 89, 0.02));
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1rpx solid rgba(74, 124, 89, 0.08);
  box-shadow: 0 4rpx 16rpx rgba(74, 124, 89, 0.08);
}

.category-item:active {
  transform: scale(0.96) translateY(2rpx);
  background: linear-gradient(135deg, rgba(74, 124, 89, 0.08), rgba(74, 124, 89, 0.12));
  box-shadow: 0 2rpx 8rpx rgba(74, 124, 89, 0.15);
}

.category-image {
  width: 70rpx;
  height: 70rpx;
  margin-bottom: 15rpx;
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(74, 124, 89, 0.12);
  border: 2rpx solid rgba(74, 124, 89, 0.1);
}

.category-name {
  font-size: 24rpx;
  color: #4a7c59;
  text-align: center;
  font-weight: 600;
  line-height: 1.3;
  word-break: break-all;
  min-height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  letter-spacing: 0.5rpx;
}

/* 商品区域 - 现代卡片设计 */
.product-section {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20rpx);
  margin: 20rpx 30rpx;
  padding: 35rpx 30rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(52, 211, 153, 0.08);
  border: 1rpx solid rgba(134, 239, 172, 0.1);
  position: relative;
  z-index: 1;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid rgba(102, 126, 234, 0.1);
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  margin: 0;
}

.section-title::after {
  display: none;
}

.more-link {
  font-size: 26rpx;
  color: #059669;
  font-weight: 600;
  padding: 10rpx 20rpx;
  border-radius: 25rpx;
  background: rgba(134, 239, 172, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.more-link:active {
  background: rgba(134, 239, 172, 0.25);
  transform: scale(0.95);
}

.product-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
}

/* 特色商品 - 占据整行 */
.product-item-featured {
  width: 100% !important;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: var(--shadow-light);
  transition: all 0.3s ease;
  border: 1rpx solid rgba(74, 124, 89, 0.05);
  margin-bottom: 20rpx;
}

.product-item-featured:active {
  transform: translateY(-4rpx) scale(0.98);
  box-shadow: var(--shadow-medium);
}

.product-image-featured {
  width: 100%;
  height: 240px;
  border-radius: 16rpx 16rpx 0 0;
}

.product-info-featured {
  padding: 30rpx;
}

.product-name-featured {
  font-size: 32rpx;
  color: #2c3e50;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  margin-bottom: 20rpx;
  font-weight: 600;
  min-height: 88rpx;
}

/* 小商品 - 三个一排 */
.product-item-small {
  width: 30% !important;
  max-width: 200rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: var(--shadow-light);
  transition: all 0.3s ease;
  border: 1rpx solid rgba(74, 124, 89, 0.05);
  flex-shrink: 0;
  margin-right: 5%;
  margin-bottom: 20rpx;
}

.product-item-small:nth-child(3n) {
  margin-right: 0;
}

.product-item-small:active {
  transform: translateY(-4rpx) scale(0.98);
  box-shadow: var(--shadow-medium);
}

.product-image-small {
  width: 100%;
  height: 120px;
  border-radius: 16rpx 16rpx 0 0;
}

.product-info-small {
  padding: 16rpx;
}

.product-name-small {
  font-size: 24rpx;
  color: #2c3e50;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  margin-bottom: 12rpx;
  font-weight: 500;
  min-height: 66rpx;
}

/* 兼容原有的商品样式 */
.product-item {
  width: calc(50% - 10rpx);
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: var(--shadow-light);
  transition: all 0.3s ease;
  border: 1rpx solid rgba(74, 124, 89, 0.05);
}

.product-item:active {
  transform: translateY(-4rpx) scale(0.98);
  box-shadow: var(--shadow-medium);
}

.product-image {
  width: 100%;
  height: 200rpx;
  border-radius: 16rpx 16rpx 0 0;
}

.product-info {
  padding: 24rpx;
}

.product-name {
  font-size: 28rpx;
  color: #2c3e50;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  margin-bottom: 16rpx;
  font-weight: 500;
  min-height: 78rpx;
}

.product-price {
  display: flex;
  align-items: baseline;
  justify-content: flex-start;
  gap: 8rpx;
}

.current-price {
  font-size: 30rpx;
  color: #4a7c59;
  font-weight: 700;
  letter-spacing: 0.5rpx;
}

.original-price {
  font-size: 22rpx;
  color: #999;
  text-decoration: line-through;
  opacity: 0.8;
}

/* 空状态 - 现代设计 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  margin: 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #7f8c8d;
  font-weight: 500;
}

/* 加载状态 - 茶风格设计 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--tea-background);
}

.loading {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  padding: 40rpx 60rpx;
  border-radius: 20rpx;
  color: #2c3e50;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .category-item {
    width: 120rpx;
  }

  .product-item {
    width: 100%;
  }
  
  /* 小屏幕上的特色商品 */
  .product-item-featured {
    width: 100% !important;
  }
  
  /* 小屏幕上的小商品 - 改为两个一排 */
  .product-item-small {
    width: calc(50% - 10rpx);
  }
  
  .product-image-small {
    height: 160rpx;
  }
  
  .product-info-small {
    padding: 20rpx;
  }
  
  .product-name-small {
    font-size: 26rpx;
    min-height: 72rpx;
  }
  
  .container {
    padding: 0 10rpx 40rpx;
  }
  
  .banner-section,
  .category-section,
  .product-section {
    margin: 10rpx;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 0;
  background: transparent;
}
