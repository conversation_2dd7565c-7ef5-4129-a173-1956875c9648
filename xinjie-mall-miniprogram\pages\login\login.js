// pages/login/login.js
const { login } = require("../../utils/auth");

Page({
  data: {
    loginType: "wechat", // wechat, phone
    phone: "",
    verifyCode: "",
    loading: false,
    codeLoading: false,
    countdown: 0,
  },

  onLoad: function (options) {
    // 检查是否已经登录
    const token = wx.getStorageSync("token");
    if (token) {
      wx.navigateBack();
    }
  },

  // 微信登录
  onWechatLogin: function () {
    this.setData({ loading: true });

    // 先执行静默登录
    const { silentLogin, businessLogin } = require("../../utils/auth");

    silentLogin()
      .then(() => {
        // 静默登录成功后，询问用户是否获取详细信息
        wx.showModal({
          title: '完善资料',
          content: '是否获取您的微信头像和昵称，用于完善会员资料？',
          confirmText: '获取',
          cancelText: '跳过',
          success: (res) => {
            if (res.confirm) {
              // 用户同意，获取详细信息
              businessLogin()
                .then(() => {
                  this.loginSuccess();
                })
                .catch((error) => {
                  console.error("获取用户信息失败:", error);
                  // 即使获取用户信息失败，静默登录已成功，仍然算登录成功
                  this.loginSuccess();
                });
            } else {
              // 用户跳过，直接登录成功
              this.loginSuccess();
            }
          }
        });
      })
      .catch((error) => {
        console.error("登录失败:", error);
        wx.showToast({
          title: error || "登录失败",
          icon: "none",
        });
        this.setData({ loading: false });
      });
  },

  // 登录成功处理
  loginSuccess: function() {
    wx.showToast({
      title: "登录成功",
      icon: "success",
    });

    setTimeout(() => {
      wx.navigateBack();
    }, 1500);

    this.setData({ loading: false });
  },

  // 手机号输入
  onPhoneInput: function (e) {
    this.setData({
      phone: e.detail.value,
    });
  },

  // 验证码输入
  onCodeInput: function (e) {
    this.setData({
      verifyCode: e.detail.value,
    });
  },

  // 发送验证码
  onSendCode: function () {
    if (!this.data.phone || !/^1[3-9]\d{9}$/.test(this.data.phone)) {
      wx.showToast({
        title: "请输入正确的手机号",
        icon: "none",
      });
      return;
    }

    this.setData({ codeLoading: true });

    // 模拟发送验证码
    setTimeout(() => {
      this.setData({
        codeLoading: false,
        countdown: 60,
      });

      this.startCountdown();

      wx.showToast({
        title: "验证码已发送",
        icon: "success",
      });
    }, 1000);
  },

  // 倒计时
  startCountdown: function () {
    const timer = setInterval(() => {
      if (this.data.countdown <= 1) {
        clearInterval(timer);
        this.setData({ countdown: 0 });
      } else {
        this.setData({ countdown: this.data.countdown - 1 });
      }
    }, 1000);
  },

  // 手机号登录
  onPhoneLogin: function () {
    if (!this.data.phone || !this.data.verifyCode) {
      wx.showToast({
        title: "请输入手机号和验证码",
        icon: "none",
      });
      return;
    }

    this.setData({ loading: true });

    // 模拟登录
    setTimeout(() => {
      wx.setStorageSync("token", "mock-token");
      wx.setStorageSync("userInfo", {
        nickName: "用户",
        phone: this.data.phone,
        avatarUrl: "/images/common/default-avatar.png",
      });

      wx.showToast({
        title: "登录成功",
        icon: "success",
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 1500);
  },

  // 切换登录方式
  onSwitchLoginType: function () {
    this.setData({
      loginType: this.data.loginType === "wechat" ? "phone" : "wechat",
    });
  },
});
