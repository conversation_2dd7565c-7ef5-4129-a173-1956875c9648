<!--pages/login/login.wxml-->
<view class="login-container">
  <view class="logo-container">
    <text class="logo-text">心洁茶叶商城</text>
  </view>

  <!-- 微信登录 -->
  <view class="login-section" wx:if="{{loginType === 'wechat'}}">
    <button 
      class="wechat-login-btn {{loading ? 'loading' : ''}}"
      bindtap="onWechatLogin"
      disabled="{{loading}}"
    >
      {{loading ? '登录中...' : '微信快速登录'}}
    </button>
    
    <view class="switch-login">
      <text bindtap="onSwitchLoginType">使用手机号登录</text>
    </view>
  </view>

  <!-- 手机号登录 -->
  <view class="login-section" wx:else>
    <view class="input-group">
      <input 
        class="phone-input" 
        type="number" 
        placeholder="请输入手机号"
        value="{{phone}}"
        bindinput="onPhoneInput"
        maxlength="11"
      />
    </view>
    
    <view class="input-group">
      <input 
        class="code-input" 
        type="number" 
        placeholder="请输入验证码"
        value="{{verifyCode}}"
        bindinput="onCodeInput"
        maxlength="6"
      />
      <button 
        class="send-code-btn {{codeLoading ? 'loading' : ''}}"
        bindtap="onSendCode"
        disabled="{{codeLoading || countdown > 0}}"
      >
        {{countdown > 0 ? countdown + 's' : (codeLoading ? '发送中...' : '获取验证码')}}
      </button>
    </view>
    
    <button 
      class="phone-login-btn {{loading ? 'loading' : ''}}"
      bindtap="onPhoneLogin"
      disabled="{{loading}}"
    >
      {{loading ? '登录中...' : '登录'}}
    </button>
    
    <view class="switch-login">
      <text bindtap="onSwitchLoginType">使用微信登录</text>
    </view>
  </view>

  <!-- 协议提示 -->
  <view class="agreement">
    <text>登录即同意</text>
    <text class="link">《用户协议》</text>
    <text>和</text>
    <text class="link">《隐私政策》</text>
  </view>
</view> 