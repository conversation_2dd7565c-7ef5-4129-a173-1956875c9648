/* pages/login/login.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 80rpx 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 头部 */
.header {
  text-align: center;
  margin-bottom: 80rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

/* 登录区域 */
.login-section {
  width: 100%;
  max-width: 500rpx;
}

/* 微信登录 */
.wechat-login-btn {
  width: 100%;
  background-color: #07c160;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 30rpx;
  font-size: 32rpx;
  margin-bottom: 40rpx;
}

.wechat-login-btn.loading {
  background-color: #ccc;
}

.wechat-login-btn:disabled {
  background-color: #ccc;
}

/* 手机号登录 */
.input-group {
  position: relative;
  margin-bottom: 30rpx;
}

.phone-input,
.code-input {
  width: 100%;
  padding: 30rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 32rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.send-code-btn {
  position: absolute;
  right: 15rpx;
  top: 50%;
  transform: translateY(-50%);
  background-color: #4caf50;
  color: #fff;
  border: none;
  border-radius: 25rpx;
  padding: 15rpx 25rpx;
  font-size: 26rpx;
  min-width: 160rpx;
}

.send-code-btn.loading {
  background-color: #ccc;
}

.send-code-btn:disabled {
  background-color: #ccc;
}

.phone-login-btn {
  width: 100%;
  background-color: #4caf50;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 30rpx;
  font-size: 32rpx;
  margin-bottom: 40rpx;
}

.phone-login-btn.loading {
  background-color: #ccc;
}

.phone-login-btn:disabled {
  background-color: #ccc;
}

/* 切换登录方式 */
.switch-login {
  text-align: center;
  margin-bottom: 60rpx;
}

.switch-login text {
  font-size: 28rpx;
  color: #4caf50;
  text-decoration: underline;
}

/* 协议提示 */
.agreement {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
  margin-top: auto;
}

.agreement .link {
  color: #4caf50;
  text-decoration: underline;
}

/* 动画效果 */
.wechat-login-btn,
.phone-login-btn,
.send-code-btn {
  transition: all 0.3s ease;
}

.wechat-login-btn:active,
.phone-login-btn:active {
  transform: scale(0.98);
}

.send-code-btn:active {
  opacity: 0.8;
}

.switch-login text:active {
  opacity: 0.7;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .container {
    padding: 60rpx 40rpx;
  }

  .header {
    margin-bottom: 60rpx;
  }
}
