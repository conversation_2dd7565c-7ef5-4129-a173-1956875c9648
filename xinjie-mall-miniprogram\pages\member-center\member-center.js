// pages/member-center/member-center.js
const { request } = require("../../utils/request");
const { API } = require("../../config/api");

Page({
  data: {
    memberInfo: {
      userId: 0,
      nickname: '',
      points: 0,
      currentLevel: {
        id: 1,
        name: '普通会员',
        discountRate: 1.00,
        description: ''
      },
      nextLevel: null,
      upgradeProgress: {
        progress: 0,
        needPoints: 0,
        progressText: ''
      },
      benefits: [],
      isVip: false
    },
    memberLevels: [],
    pointsHistory: [],
    loading: false,
    activeTab: 'info' // info: 会员信息, levels: 等级说明, points: 积分记录
  },

  onLoad: function (options) {
    this.loadMemberInfo();
    this.loadMemberLevels();
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.loadMemberInfo();
    this.loadMemberLevels();
    if (this.data.activeTab === 'points') {
      this.loadPointsHistory(true);
    }
  },

  // 加载会员信息
  loadMemberInfo: async function () {
    try {
      const response = await request({
        url: API.member.info,
        method: 'GET'
      });

      if (response.success) {
        this.setData({
          memberInfo: response.data
        });
      }
    } catch (error) {
      console.error('加载会员信息失败:', error);
    } finally {
      wx.stopPullDownRefresh();
    }
  },

  // 加载会员等级列表
  loadMemberLevels: async function () {
    try {
      const response = await request({
        url: API.member.levels,
        method: 'GET'
      });

      if (response.success) {
        this.setData({
          memberLevels: response.data
        });
      }
    } catch (error) {
      console.error('加载会员等级失败:', error);
    }
  },

  // 加载积分记录
  loadPointsHistory: async function (refresh = false) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const response = await request({
        url: API.member.pointsHistory,
        method: 'GET',
        data: {
          page: 1,
          pageSize: 20
        }
      });

      if (response.success) {
        this.setData({
          pointsHistory: response.data.list || []
        });
      }
    } catch (error) {
      console.error('加载积分记录失败:', error);
    } finally {
      this.setData({ loading: false });
    }
  },

  // 切换标签
  onTabChange: function (e) {
    const tab = e.currentTarget.dataset.tab;
    
    if (tab === this.data.activeTab) return;

    this.setData({
      activeTab: tab
    });

    if (tab === 'points' && this.data.pointsHistory.length === 0) {
      this.loadPointsHistory();
    }
  },

  // 查看等级权益
  onViewBenefits: function (e) {
    const levelId = e.currentTarget.dataset.levelId;
    
    wx.showModal({
      title: '会员权益',
      content: '会员权益详情功能开发中...',
      showCancel: false
    });
  },

  // 签到获取积分
  onDailyCheckIn: async function () {
    try {
      wx.showLoading({
        title: '签到中...'
      });

      const response = await request({
        url: API.member.checkIn,
        method: 'POST'
      });

      wx.hideLoading();

      if (response.success) {
        wx.showToast({
          title: response.message,
          icon: 'success'
        });
        
        // 刷新会员信息
        this.loadMemberInfo();
      } else {
        wx.showToast({
          title: response.message || '签到失败',
          icon: 'none'
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('签到失败:', error);
      wx.showToast({
        title: '签到失败',
        icon: 'none'
      });
    }
  },

  // 格式化时间
  formatTime: function (timeStr) {
    const date = new Date(timeStr);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  },

  // 获取积分类型文本
  getPointsTypeText: function (type, source) {
    if (type === 1) { // 获得
      switch (source) {
        case 1: return '购物获得';
        case 2: return '签到获得';
        case 3: return '活动获得';
        default: return '积分获得';
      }
    } else { // 消费
      switch (source) {
        case 4: return '积分抵扣';
        default: return '积分消费';
      }
    }
  },

  // 去充值
  onGoRecharge: function () {
    wx.navigateTo({
      url: '/pages/recharge/recharge'
    });
  },

  // 去购物
  onGoShopping: function () {
    wx.switchTab({
      url: '/pages/index/index'
    });
  }
});
