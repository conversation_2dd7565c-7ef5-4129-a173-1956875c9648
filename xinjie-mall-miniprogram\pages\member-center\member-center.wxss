/* pages/member-center/member-center.wxss */
.container {
  background: linear-gradient(180deg, #f0fdf4 0%, #ecfdf5 50%, #f7fee7 100%);
  min-height: 100vh;
}

/* 会员头部 */
.member-header {
  padding: 30rpx;
}

.member-card {
  background: linear-gradient(135deg, #86efac, #6ee7b7, #34d399);
  border-radius: 24rpx;
  padding: 40rpx;
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(52, 211, 153, 0.2);
  position: relative;
  overflow: hidden;
}

.member-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(180deg); }
}

.member-info {
  position: relative;
  z-index: 1;
  margin-bottom: 30rpx;
}

.member-level-badge {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.level-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.level-name {
  font-size: 32rpx;
  font-weight: bold;
  background: rgba(255, 255, 255, 0.9);
  color: #059669;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.member-nickname {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.member-points {
  font-size: 28rpx;
  opacity: 0.95;
  font-weight: 500;
}

/* 升级进度 */
.upgrade-section {
  position: relative;
  z-index: 1;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.progress-label {
  font-size: 26rpx;
  opacity: 0.9;
}

.progress-need {
  font-size: 24rpx;
  opacity: 0.8;
}

.progress-bar {
  height: 12rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 6rpx;
  transition: width 0.5s ease;
  box-shadow: 0 0 10rpx rgba(255, 255, 255, 0.5);
}

.max-level {
  text-align: center;
  position: relative;
  z-index: 1;
}

.max-level-text {
  font-size: 28rpx;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 标签切换 */
.tab-section {
  padding: 0 30rpx 20rpx;
}

.tab-container {
  background: white;
  border-radius: 20rpx;
  padding: 8rpx;
  display: flex;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: linear-gradient(135deg, #86efac, #6ee7b7);
  box-shadow: 0 4rpx 12rpx rgba(52, 211, 153, 0.3);
}

.tab-text {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  transition: color 0.3s ease;
}

.tab-item.active .tab-text {
  color: white;
  font-weight: 600;
}

/* 内容区域 */
.content-section {
  padding: 0 30rpx;
}

/* 签到卡片 */
.checkin-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(134, 239, 172, 0.1);
}

.checkin-info {
  flex: 1;
}

.checkin-title {
  display: block;
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.checkin-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.checkin-btn {
  background: linear-gradient(135deg, #86efac, #6ee7b7);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(52, 211, 153, 0.3);
}

.checkin-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.checkin-text {
  font-size: 28rpx;
  font-weight: 600;
}

/* 权益列表 */
.benefits-section {
  margin-bottom: 30rpx;
}

.section-title {
  margin-bottom: 20rpx;
}

.title-text {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.benefits-list {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.benefit-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.benefit-item:last-child {
  border-bottom: none;
}

.benefit-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
}

.benefit-content {
  flex: 1;
}

.benefit-name {
  display: block;
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 6rpx;
}

.benefit-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 快捷操作 */
.actions-section {
  margin-bottom: 30rpx;
}

.action-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.action-item {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(134, 239, 172, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.action-item:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.action-icon {
  display: block;
  font-size: 48rpx;
  margin-bottom: 15rpx;
}

.action-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

/* 等级列表 */
.levels-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.level-item {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(134, 239, 172, 0.1);
  position: relative;
}

.level-item.current {
  border-color: #86efac;
  background: linear-gradient(135deg, rgba(134, 239, 172, 0.05), rgba(110, 231, 183, 0.05));
}

.level-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.level-info {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.level-name {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.level-discount {
  background: #86efac;
  color: white;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 600;
}

.level-points {
  font-size: 26rpx;
  color: #059669;
  font-weight: 600;
}

.level-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.current-badge {
  position: absolute;
  top: -10rpx;
  right: 20rpx;
  background: #86efac;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 600;
  box-shadow: 0 2rpx 8rpx rgba(52, 211, 153, 0.3);
}

/* 积分记录 */
.points-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.points-item {
  background: white;
  border-radius: 16rpx;
  padding: 25rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.points-left {
  flex: 1;
}

.points-type {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 6rpx;
}

.points-time {
  font-size: 22rpx;
  color: #999;
}

.points-right {
  text-align: right;
}

.points-change {
  font-size: 32rpx;
  font-weight: bold;
}

.points-change.gain {
  color: #059669;
}

.points-change.use {
  color: #dc2626;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 0;
}

.empty-icon {
  display: block;
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: #ccc;
}
