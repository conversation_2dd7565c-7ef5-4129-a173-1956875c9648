// pages/order-confirm/order-confirm.js
const { get, post } = require("../../utils/request");
const { API } = require("../../config/api");
const { formatPrice } = require("../../utils/format");
const { requireLogin } = require("../../utils/auth");

Page({
  data: {
    // 商品列表
    products: [],

    // 收货地址
    selectedAddress: null,

    // 订单信息
    orderInfo: {
      totalPrice: 0,
      shippingFee: 0,
      finalPrice: 0,
    },

    // 支付方式
    paymentMethod: "wechat", // wechat, alipay, cod

    // 订单备注
    remark: "",

    // 加载状态
    loading: false,

    // 支付方式选项
    paymentMethods: [
      { value: "wechat", label: "微信支付", icon: "💰" },
      { value: "alipay", label: "支付宝", icon: "💳" },
      { value: "cod", label: "货到付款", icon: "📦" },
    ],
  },

  // 页面加载时执行
  onLoad: function (options) {
    // 通过eventChannel接收数据
    const eventChannel = this.getOpenerEventChannel();
    eventChannel.on("acceptDataFromOpenerPage", (data) => {
      this.setData({
        products: data.products || [],
        "orderInfo.totalPrice": data.totalPrice || 0,
      });

      this.calculateOrderInfo();
    });

    this.loadDefaultAddress();
  },

  // 页面显示时执行
  onShow: function () {
    // 可能从地址页面返回，重新加载地址
    this.loadDefaultAddress();
  },

  // 加载默认收货地址
  loadDefaultAddress: function () {
    requireLogin()
      .then(() => {
        return get(API.address.list);
      })
      .then((res) => {
        if (res.success) {
          const addresses = res.data || [];
          // 查找默认地址或第一个地址
          const defaultAddress =
            addresses.find((addr) => addr.isDefault) || addresses[0];

          if (defaultAddress) {
            this.setData({
              selectedAddress: defaultAddress,
            });
          }
        }
      })
      .catch((error) => {
        console.error("加载收货地址失败:", error);
      });
  },

  // 计算订单信息
  calculateOrderInfo: function () {
    const products = this.data.products;

    // 计算原价总额和折扣价总额
    let originalTotal = 0;
    let discountTotal = 0;

    products.forEach(product => {
      const originalPrice = product.originalPrice || product.price;
      const discountPrice = product.discountPrice || product.price;

      originalTotal += originalPrice * product.quantity;
      discountTotal += discountPrice * product.quantity;
    });

    const totalDiscount = originalTotal - discountTotal;

    // 计算运费（基于折扣后价格）
    const shippingFee = discountTotal >= 99 ? 0 : 10;
    const finalPrice = discountTotal + shippingFee;

    this.setData({
      "orderInfo.originalTotal": originalTotal,
      "orderInfo.discountTotal": discountTotal,
      "orderInfo.totalDiscount": totalDiscount,
      "orderInfo.totalPrice": discountTotal, // 兼容原有字段
      "orderInfo.shippingFee": shippingFee,
      "orderInfo.finalPrice": finalPrice,
      "orderInfo.originalTotalText": formatPrice(originalTotal),
      "orderInfo.discountTotalText": formatPrice(discountTotal),
      "orderInfo.totalDiscountText": formatPrice(totalDiscount),
      "orderInfo.totalPriceText": formatPrice(discountTotal),
      "orderInfo.shippingFeeText": formatPrice(shippingFee),
      "orderInfo.finalPriceText": formatPrice(finalPrice),
      "orderInfo.hasDiscount": totalDiscount > 0
    });
  },

  // 选择收货地址
  onSelectAddress: function () {
    wx.navigateTo({
      url: "/pages/address-list/address-list?from=order",
    });
  },

  // 支付方式选择
  onPaymentMethodChange: function (e) {
    const paymentMethod = e.currentTarget.dataset.method;
    this.setData({
      paymentMethod,
    });
  },

  // 备注输入
  onRemarkInput: function (e) {
    this.setData({
      remark: e.detail.value,
    });
  },

  // 提交订单
  onSubmitOrder: function () {
    // 验证收货地址
    if (!this.data.selectedAddress) {
      wx.showToast({
        title: "请选择收货地址",
        icon: "none",
      });
      return;
    }

    // 验证商品
    if (!this.data.products || this.data.products.length === 0) {
      wx.showToast({
        title: "请选择商品",
        icon: "none",
      });
      return;
    }

    this.setData({ loading: true });

    requireLogin()
      .then(() => {
        const orderData = {
          items: this.data.products.map((product) => ({
            product_id: product.id,
            product_name: product.name,
            product_image: product.image,
            quantity: product.quantity,
            originalPrice: product.originalPrice || product.price,
            discountPrice: product.discountPrice || product.price,
            specs: product.selectedSpecs || null,
          })),
          receiver_name: this.data.selectedAddress.receiver,
          receiver_phone: this.data.selectedAddress.phone,
          receiver_address: `${this.data.selectedAddress.province}${this.data.selectedAddress.city}${this.data.selectedAddress.district}${this.data.selectedAddress.detail_address}`,
          remark: this.data.remark,
          cart_item_ids: this.data.cartItemIds || [] // 如果是从购物车来的，需要清理购物车
        };

        return post(API.order.create, orderData);
      })
      .then((res) => {
        if (res.success) {
          const orderId = res.data.orderId;

          wx.showToast({
            title: "订单创建成功",
            icon: "success",
          });

          // 根据支付方式跳转
          if (this.data.paymentMethod === "cod") {
            // 货到付款，直接跳转到订单详情
            setTimeout(() => {
              wx.redirectTo({
                url: `/pages/order-detail/order-detail?id=${orderId}`,
              });
            }, 1500);
          } else {
            // 在线支付，跳转到支付页面
            setTimeout(() => {
              wx.redirectTo({
                url: `/pages/payment/payment?orderId=${orderId}`,
              });
            }, 1500);
          }
        }
      })
      .catch((error) => {
        console.error("创建订单失败:", error);
      })
      .finally(() => {
        this.setData({ loading: false });
      });
  },
});
