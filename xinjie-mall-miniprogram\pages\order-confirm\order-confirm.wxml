<!--pages/order-confirm/order-confirm.wxml-->
<view class="container">
  <!-- 收货地址 -->
  <view class="address-section" bindtap="onSelectAddress">
    <view class="section-header">
      <text class="section-title">收货地址</text>
      <text class="section-arrow">></text>
    </view>
    
    <view class="address-content" wx:if="{{selectedAddress}}">
      <view class="address-info">
        <view class="address-line1">
          <text class="consignee">{{selectedAddress.consignee}}</text>
          <text class="phone">{{selectedAddress.phone}}</text>
        </view>
        <view class="address-line2">
          <text class="address-detail">{{selectedAddress.province}}{{selectedAddress.city}}{{selectedAddress.district}}{{selectedAddress.detail}}</text>
        </view>
      </view>
      <view class="address-icon">📍</view>
    </view>
    
    <view class="address-empty" wx:else>
      <text class="empty-text">请选择收货地址</text>
      <text class="empty-icon">+</text>
    </view>
  </view>

  <!-- 商品列表 -->
  <view class="products-section">
    <view class="section-header">
      <text class="section-title">商品信息</text>
    </view>
    
    <view class="product-list">
      <view class="product-item" wx:for="{{products}}" wx:key="id">
        <image class="product-image" src="{{item.image}}" mode="aspectFill"></image>
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <text class="product-spec" wx:if="{{item.selectedSpecs}}">
            <block wx:for="{{Object.keys(item.selectedSpecs)}}" wx:key="*this" wx:for-item="specKey">
              {{specKey}}:{{item.selectedSpecs[specKey]}} 
            </block>
          </text>
          <view class="product-price-quantity">
            <text class="product-price">{{item.priceText}}</text>
            <text class="product-quantity">×{{item.quantity}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 支付方式 -->
  <view class="payment-section">
    <view class="section-header">
      <text class="section-title">支付方式</text>
    </view>
    
    <view class="payment-methods">
      <view 
        class="payment-item {{paymentMethod === item.value ? 'active' : ''}}"
        wx:for="{{paymentMethods}}" 
        wx:key="value"
        bindtap="onPaymentMethodChange"
        data-method="{{item.value}}"
      >
        <view class="payment-info">
          <text class="payment-icon">{{item.icon}}</text>
          <text class="payment-label">{{item.label}}</text>
        </view>
        <view class="payment-radio {{paymentMethod === item.value ? 'checked' : ''}}"></view>
      </view>
    </view>
  </view>

  <!-- 订单备注 -->
  <view class="remark-section">
    <view class="section-header">
      <text class="section-title">订单备注</text>
    </view>
    
    <textarea 
      class="remark-input" 
      placeholder="选填，可以告诉我们您的特殊需求"
      value="{{remark}}"
      bindinput="onRemarkInput"
      maxlength="200"
    ></textarea>
  </view>

  <!-- 订单金额 -->
  <view class="amount-section">
    <view class="amount-item" wx:if="{{orderInfo.hasDiscount}}">
      <text class="amount-label">商品原价</text>
      <text class="amount-value">{{orderInfo.originalTotalText}}</text>
    </view>
    <view class="amount-item" wx:if="{{orderInfo.hasDiscount}}">
      <text class="amount-label">优惠金额</text>
      <text class="amount-value discount-amount">-{{orderInfo.totalDiscountText}}</text>
    </view>
    <view class="amount-item">
      <text class="amount-label">商品金额</text>
      <text class="amount-value">{{orderInfo.totalPriceText}}</text>
    </view>
    <view class="amount-item">
      <text class="amount-label">运费</text>
      <text class="amount-value">{{orderInfo.shippingFeeText}}</text>
    </view>
    <view class="amount-item total">
      <text class="amount-label">实付款</text>
      <text class="amount-value total-price">{{orderInfo.finalPriceText}}</text>
    </view>
  </view>
</view>

<!-- 底部提交栏 -->
<view class="submit-bar">
  <view class="submit-info">
    <text class="submit-total">合计：{{orderInfo.finalPriceText}}</text>
  </view>
  <button 
    class="submit-btn {{loading ? 'loading' : ''}}" 
    bindtap="onSubmitOrder"
    disabled="{{loading}}"
  >
    {{loading ? '提交中...' : '提交订单'}}
  </button>
</view> 