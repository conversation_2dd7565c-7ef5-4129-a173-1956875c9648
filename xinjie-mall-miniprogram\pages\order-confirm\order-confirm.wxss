/* pages/order-confirm/order-confirm.wxss */
.container {
  background: linear-gradient(180deg, #f0fdf4 0%, #ecfdf5 30%, #f7fee7 70%, #ffffff 100%);
  min-height: 100vh;
  padding-bottom: 120rpx;
  position: relative;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 400rpx;
  background: linear-gradient(135deg, #86efac 0%, #6ee7b7 50%, #34d399 100%);
  opacity: 0.05;
  z-index: 0;
}

/* 通用区块样式 */
.address-section,
.products-section,
.payment-section,
.remark-section,
.amount-section {
  background: rgba(255, 255, 255, 0.98);
  margin: 20rpx 30rpx;
  padding: 35rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(52, 211, 153, 0.08);
  border: 1rpx solid rgba(134, 239, 172, 0.1);
  position: relative;
  z-index: 1;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.section-arrow {
  color: #999;
  font-size: 28rpx;
}

/* 收货地址 */
.address-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.address-info {
  flex: 1;
}

.address-line1 {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.consignee {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-right: 30rpx;
}

.phone {
  font-size: 28rpx;
  color: #666;
}

.address-line2 {
  margin-bottom: 0;
}

.address-detail {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.address-icon {
  font-size: 40rpx;
  color: #4caf50;
  margin-left: 20rpx;
}

.address-empty {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 40rpx 0;
  border: 2rpx dashed #e0e0e0;
  border-radius: 8rpx;
  text-align: center;
}

.empty-text {
  flex: 1;
  font-size: 28rpx;
  color: #999;
}

.empty-icon {
  font-size: 40rpx;
  color: #4caf50;
  margin-left: 20rpx;
}

/* 商品列表 */
.product-list {
  margin-top: 20rpx;
}

.product-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-spec {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 15rpx;
}

.product-price-quantity {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.product-price {
  font-size: 30rpx;
  color: #ff4444;
  font-weight: bold;
}

.product-quantity {
  font-size: 28rpx;
  color: #666;
}

/* 支付方式 */
.payment-methods {
  margin-top: 20rpx;
}

.payment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.payment-item:last-child {
  border-bottom: none;
}

.payment-item.active {
  background-color: #f0fff0;
  margin: 0 -30rpx;
  padding-left: 30rpx;
  padding-right: 30rpx;
}

.payment-info {
  display: flex;
  align-items: center;
}

.payment-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.payment-label {
  font-size: 28rpx;
  color: #333;
}

.payment-radio {
  width: 40rpx;
  height: 40rpx;
  border: 2px solid #e0e0e0;
  border-radius: 50%;
  position: relative;
}

.payment-radio.checked {
  border-color: #4caf50;
}

.payment-radio.checked::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20rpx;
  height: 20rpx;
  background-color: #4caf50;
  border-radius: 50%;
}

/* 订单备注 */
.remark-input {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fafafa;
  margin-top: 20rpx;
}

/* 订单金额 */
.amount-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15rpx 0;
}

.amount-item.total {
  border-top: 1px solid #f0f0f0;
  margin-top: 15rpx;
  padding-top: 20rpx;
}

.amount-label {
  font-size: 28rpx;
  color: #666;
}

.amount-value {
  font-size: 28rpx;
  color: #333;
}

.amount-item.total .amount-label {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.total-price {
  font-size: 36rpx;
  color: #ff4444;
  font-weight: bold;
}

.discount-amount {
  color: #27ae60;
  font-weight: bold;
}

/* 底部提交栏 */
.submit-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 1000;
}

.submit-info {
  display: flex;
  flex-direction: column;
}

.submit-total {
  font-size: 32rpx;
  color: #ff4444;
  font-weight: bold;
}

.submit-btn {
  background-color: #4caf50;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 25rpx 50rpx;
  font-size: 32rpx;
  min-width: 200rpx;
}

.submit-btn.loading {
  background-color: #ccc;
}

.submit-btn:disabled {
  background-color: #ccc;
}

/* 动画效果 */
.address-section,
.payment-item {
  transition: all 0.3s ease;
}

.address-section:active,
.payment-item:active {
  background-color: #f8f8f8;
}

.submit-btn {
  transition: background-color 0.3s ease;
}

.submit-btn:active {
  background-color: #45a049;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .address-content,
  .product-item {
    flex-direction: column;
    align-items: stretch;
  }

  .address-icon,
  .product-image {
    align-self: flex-start;
    margin-top: 10rpx;
  }
}
