// pages/order-detail/order-detail.js
const { get, post } = require("../../utils/request");
const { API } = require("../../config/api");
const { formatPrice, formatDate } = require("../../utils/format");
const { requireLogin } = require("../../utils/auth");
const {
  ORDER_STATUS,
  ORDER_STATUS_NUMBER,
  ORDER_STATUS_TEXT,
  ORDER_STATUS_NUMBER_TEXT,
  PAYMENT_METHOD_TEXT,
} = require("../../utils/constants");

Page({
  data: {
    // 订单ID
    orderId: null,

    // 订单详情
    orderDetail: null,

    // 物流信息
    logistics: [],

    // 加载状态
    loading: false,

    // 默认图片
    defaultImage: "/images/common/default-product.png",

    // 显示物流信息
    showLogistics: false,
  },

  // 页面加载
  onLoad: function (options) {
    if (options.id) {
      this.setData({
        orderId: options.id,
      });
      this.loadOrderDetail();
    }
  },

  // 页面显示
  onShow: function () {
    if (this.data.orderId) {
      this.loadOrderDetail();
    }
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.loadOrderDetail().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载订单详情
  loadOrderDetail: function () {
    this.setData({
      loading: true,
    });

    return requireLogin()
      .then(() => {
        return get(API.order.detail, { id: this.data.orderId });
      })
      .then((res) => {
        if (res.success) {
          const orderDetail = this.formatOrderDetail(res.data);
          this.setData({
            orderDetail,
          });

          // 如果是已发货状态，加载物流信息
          if (orderDetail.order_status === ORDER_STATUS_NUMBER.SHIPPED) {
            this.loadLogistics();
          }
        }
      })
      .catch((error) => {
        console.error("加载订单详情失败:", error);
        wx.showToast({
          title: "加载失败",
          icon: "none",
        });
      })
      .finally(() => {
        this.setData({
          loading: false,
        });
      });
  },

  // 格式化订单详情
  formatOrderDetail: function (order) {
    return {
      ...order,
      statusText: ORDER_STATUS_NUMBER_TEXT[order.order_status] || "未知状态",
      paymentMethodText: PAYMENT_METHOD_TEXT[order.pay_type] || "未知支付方式",
      totalPriceText: formatPrice(order.total_amount),
      payAmountText: formatPrice(order.pay_amount),
      freightAmountText: formatPrice(order.freight_amount),
      discountAmountText: formatPrice(order.discount_amount),
      createTimeText: formatDate(order.created_at),
      payTimeText: order.pay_time ? formatDate(order.pay_time) : "",
      deliveryTimeText: order.delivery_time ? formatDate(order.delivery_time) : "",
      receiveTimeText: order.receive_time ? formatDate(order.receive_time) : "",
      items: (order.orderItems || []).map((item) => ({
        ...item,
        priceText: formatPrice(item.price),
        subtotalText: formatPrice(item.total_amount),
      })),
    };
  },

  // 加载物流信息
  loadLogistics: function () {
    get(API.order.logistics, { id: this.data.orderId })
      .then((res) => {
        if (res.success) {
          const logistics = (res.data.traces || []).map((item, index) => ({
            ...item,
            timeText: formatDate(item.time),
            isActive: index === 0, // 第一条为最新状态
          }));

          this.setData({
            logistics,
          });
        }
      })
      .catch((error) => {
        console.error("加载物流信息失败:", error);
      });
  },

  // 切换物流信息显示
  onToggleLogistics: function () {
    this.setData({
      showLogistics: !this.data.showLogistics,
    });
  },

  // 复制订单号
  onCopyOrderNo: function () {
    const { orderDetail } = this.data;
    if (orderDetail && orderDetail.order_no) {
      wx.setClipboardData({
        data: orderDetail.order_no,
        success: () => {
          wx.showToast({
            title: "已复制订单号",
            icon: "success",
          });
        },
      });
    }
  },

  // 联系客服
  onContact: function () {
    wx.showToast({
      title: "客服功能开发中",
      icon: "none",
    });
  },

  // 取消订单
  onCancelOrder: function () {
    wx.showModal({
      title: "确认取消",
      content: "确定要取消这个订单吗？",
      success: (res) => {
        if (res.confirm) {
          this.cancelOrder();
        }
      },
    });
  },

  // 取消订单请求
  cancelOrder: function () {
    wx.showLoading({
      title: "取消中...",
    });

    post(API.order.cancel, { id: this.data.orderId })
      .then((res) => {
        if (res.success) {
          wx.showToast({
            title: "取消成功",
            icon: "success",
          });
          this.loadOrderDetail();
        }
      })
      .catch((error) => {
        console.error("取消订单失败:", error);
        wx.showToast({
          title: "取消失败",
          icon: "none",
        });
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  // 确认收货
  onConfirmOrder: function () {
    wx.showModal({
      title: "确认收货",
      content: "确定已收到商品吗？",
      success: (res) => {
        if (res.confirm) {
          this.confirmOrder();
        }
      },
    });
  },

  // 确认收货请求
  confirmOrder: function () {
    wx.showLoading({
      title: "确认中...",
    });

    post(API.order.confirm, { id: this.data.orderId })
      .then((res) => {
        if (res.success) {
          wx.showToast({
            title: "确认成功",
            icon: "success",
          });
          this.loadOrderDetail();
        }
      })
      .catch((error) => {
        console.error("确认收货失败:", error);
        wx.showToast({
          title: "确认失败",
          icon: "none",
        });
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  // 去支付
  onPay: function () {
    wx.navigateTo({
      url: `/pages/payment/payment?orderId=${this.data.orderId}`,
    });
  },

  // 商品详情
  onProductDetail: function (e) {
    const productId = e.currentTarget.dataset.productId;
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?id=${productId}`,
    });
  },

  // 申请退货
  onApplyReturn: function () {
    if (!this.data.orderDetail) {
      wx.showToast({
        title: '订单信息错误',
        icon: 'error'
      });
      return;
    }

    // 检查订单是否可以退货
    if (this.data.orderDetail.order_status !== 3) {
      wx.showToast({
        title: '当前订单状态不支持退货',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/return-apply/return-apply?orderId=${this.data.orderId}`
    });
  },
});
