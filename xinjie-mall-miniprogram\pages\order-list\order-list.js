// pages/order-list/order-list.js
const { get, post } = require("../../utils/request");
const { API } = require("../../config/api");
const { formatPrice, formatDate } = require("../../utils/format");
const { requireLogin } = require("../../utils/auth");
const { ORDER_STATUS, ORDER_STATUS_TEXT } = require("../../utils/constants");

Page({
  data: {
    // 订单列表
    orders: [],

    // 当前状态筛选
    currentStatus: "all",

    // 状态筛选选项
    statusTabs: [
      { key: "all", text: "全部" },
      { key: ORDER_STATUS.PENDING, text: "待付款" },
      { key: ORDER_STATUS.PAID, text: "待发货" },
      { key: ORDER_STATUS.SHIPPED, text: "待收货" },
      { key: ORDER_STATUS.DELIVERED, text: "待评价" },
    ],

    // 分页信息
    pagination: {
      page: 1,
      size: 10,
      total: 0,
      hasMore: true,
    },

    // 加载状态
    loading: false,
    refreshing: false,

    // 默认图片
    defaultImage: "/images/common/default-product.png",
  },

  // 页面加载
  onLoad: function (options) {
    // 如果有传入状态参数，设置默认状态
    if (options.status) {
      this.setData({
        currentStatus: options.status,
      });
    }

    this.loadOrders();
  },

  // 页面显示
  onShow: function () {
    this.loadOrders();
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.refreshOrders();
  },

  // 上拉加载更多
  onReachBottom: function () {
    if (this.data.pagination.hasMore && !this.data.loading) {
      this.loadMore();
    }
  },

  // 状态筛选切换
  onStatusChange: function (e) {
    const status = e.currentTarget.dataset.status;
    if (status === this.data.currentStatus) return;

    this.setData({
      currentStatus: status,
    });

    this.refreshOrders();
  },

  // 刷新订单列表
  refreshOrders: function () {
    this.setData({
      refreshing: true,
      "pagination.page": 1,
      "pagination.hasMore": true,
    });

    this.loadOrders().finally(() => {
      this.setData({
        refreshing: false,
      });
      wx.stopPullDownRefresh();
    });
  },

  // 加载更多
  loadMore: function () {
    const { page, hasMore } = this.data.pagination;
    if (!hasMore) return;

    this.setData({
      "pagination.page": page + 1,
    });

    this.loadOrders(true);
  },

  // 加载订单列表
  loadOrders: function (append = false) {
    this.setData({
      loading: true,
    });

    return requireLogin()
      .then(() => {
        const { currentStatus, pagination } = this.data;
        const params = {
          page: pagination.page,
          size: pagination.size,
        };

        if (currentStatus !== "all") {
          params.status = currentStatus;
        }

        return get(API.order.list, params);
      })
      .then((res) => {
        if (res.success) {
          const newOrders = (res.data.items || []).map(this.formatOrder);
          const orders = append
            ? [...this.data.orders, ...newOrders]
            : newOrders;

          this.setData({
            orders,
            "pagination.total": res.data.total || 0,
            "pagination.hasMore": newOrders.length >= this.data.pagination.size,
          });
        }
      })
      .catch((error) => {
        console.error("加载订单列表失败:", error);
        wx.showToast({
          title: "加载失败",
          icon: "none",
        });
      })
      .finally(() => {
        this.setData({
          loading: false,
        });
      });
  },

  // 格式化订单数据
  formatOrder: function (order) {
    return {
      ...order,
      statusText: ORDER_STATUS_TEXT[order.status] || "未知状态",
      totalPriceText: formatPrice(order.totalPrice),
      createTimeText: formatDate(order.createTime),
      // 只显示第一个商品的图片
      firstProductImage:
        order.items && order.items.length > 0
          ? order.items[0].productImage
          : this.data.defaultImage,
      // 商品数量提示
      productCountText:
        order.items && order.items.length > 1
          ? `等${order.items.length}件商品`
          : "",
    };
  },

  // 查看订单详情
  onOrderDetail: function (e) {
    const orderId = e.currentTarget.dataset.orderId;
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?id=${orderId}`,
    });
  },

  // 取消订单
  onCancelOrder: function (e) {
    const orderId = e.currentTarget.dataset.orderId;

    wx.showModal({
      title: "确认取消",
      content: "确定要取消这个订单吗？",
      success: (res) => {
        if (res.confirm) {
          this.cancelOrder(orderId);
        }
      },
    });
  },

  // 取消订单请求
  cancelOrder: function (orderId) {
    wx.showLoading({
      title: "取消中...",
    });

    post(API.order.cancel, { orderId })
      .then((res) => {
        if (res.success) {
          wx.showToast({
            title: "取消成功",
            icon: "success",
          });
          this.refreshOrders();
        }
      })
      .catch((error) => {
        console.error("取消订单失败:", error);
        wx.showToast({
          title: "取消失败",
          icon: "none",
        });
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  // 确认收货
  onConfirmOrder: function (e) {
    const orderId = e.currentTarget.dataset.orderId;

    wx.showModal({
      title: "确认收货",
      content: "确定已收到商品吗？",
      success: (res) => {
        if (res.confirm) {
          this.confirmOrder(orderId);
        }
      },
    });
  },

  // 确认收货请求
  confirmOrder: function (orderId) {
    wx.showLoading({
      title: "确认中...",
    });

    post(API.order.confirm, { orderId })
      .then((res) => {
        if (res.success) {
          wx.showToast({
            title: "确认成功",
            icon: "success",
          });
          this.refreshOrders();
        }
      })
      .catch((error) => {
        console.error("确认收货失败:", error);
        wx.showToast({
          title: "确认失败",
          icon: "none",
        });
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  // 去支付
  onPay: function (e) {
    const orderId = e.currentTarget.dataset.orderId;

    wx.navigateTo({
      url: `/pages/payment/payment?orderId=${orderId}`,
    });
  },
});
