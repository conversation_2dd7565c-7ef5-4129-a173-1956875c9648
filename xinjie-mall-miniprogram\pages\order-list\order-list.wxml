<!--pages/order-list/order-list.wxml-->
<view class="container">
  <!-- 状态筛选 -->
  <view class="status-tabs">
    <scroll-view scroll-x="true" class="status-scroll">
      <view class="status-list">
        <view 
          class="status-item {{currentStatus === item.key ? 'active' : ''}}"
          wx:for="{{statusTabs}}" 
          wx:key="key"
          bindtap="onStatusChange"
          data-status="{{item.key}}"
        >
          <text>{{item.text}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 订单列表 -->
  <view class="order-list">
    <view 
      class="order-item"
      wx:for="{{orders}}" 
      wx:key="id"
      bindtap="onOrderDetail"
      data-order-id="{{item.id}}"
    >
      <!-- 订单头部 -->
      <view class="order-header">
        <view class="order-info">
          <text class="order-no">订单号：{{item.orderNo}}</text>
          <text class="order-time">{{item.createTimeText}}</text>
        </view>
        <view class="order-status">
          <text class="status-text">{{item.statusText}}</text>
        </view>
      </view>

      <!-- 商品信息 -->
      <view class="order-products">
        <view class="product-item">
          <image 
            class="product-image" 
            src="{{item.firstProductImage}}" 
            mode="aspectFill"
          ></image>
          <view class="product-info">
            <text class="product-name">{{item.items[0].productName}}</text>
            <text class="product-spec">{{item.items[0].specification}}</text>
            <text class="product-count">{{item.productCountText}}</text>
          </view>
          <view class="product-price">
            <text class="price-text">{{item.totalPriceText}}</text>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="order-actions">
        <view class="action-buttons">
          <!-- 待付款状态 -->
          <block wx:if="{{item.status === 'pending'}}">
            <button 
              class="action-btn cancel-btn" 
              bindtap="onCancelOrder"
              data-order-id="{{item.id}}"
            >
              取消订单
            </button>
            <button 
              class="action-btn pay-btn" 
              bindtap="onPay"
              data-order-id="{{item.id}}"
            >
              立即付款
            </button>
          </block>

          <!-- 待收货状态 -->
          <block wx:elif="{{item.status === 'shipped'}}">
            <button 
              class="action-btn confirm-btn" 
              bindtap="onConfirmOrder"
              data-order-id="{{item.id}}"
            >
              确认收货
            </button>
          </block>

          <!-- 待评价状态 -->
          <block wx:elif="{{item.status === 'delivered'}}">
            <button 
              class="action-btn evaluate-btn" 
              bindtap="onEvaluate"
              data-order-id="{{item.id}}"
            >
              评价商品
            </button>
          </block>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <empty 
    wx:if="{{orders.length === 0 && !loading}}"
    icon="📦"
    text="暂无订单"
    buttonText="去逛逛"
    bind:onButtonTap="onGoShopping"
  />

  <!-- 加载更多 -->
  <loading 
    wx:if="{{loading}}"
    text="加载中..."
    size="normal"
  />

  <!-- 没有更多数据 -->
  <view class="no-more" wx:if="{{!pagination.hasMore && orders.length > 0}}">
    <text>没有更多订单了</text>
  </view>
</view> 