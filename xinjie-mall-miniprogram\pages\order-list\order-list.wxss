/* pages/order-list/order-list.wxss */
page {
  background-color: #f5f5f5;
}

.container {
  padding-bottom: 20rpx;
}

/* 状态筛选 */
.status-tabs {
  background-color: #fff;
  border-bottom: 2rpx solid #f0f0f0;
}

.status-scroll {
  white-space: nowrap;
}

.status-list {
  display: flex;
  padding: 0 20rpx;
}

.status-item {
  flex-shrink: 0;
  padding: 30rpx 40rpx;
  font-size: 28rpx;
  color: #666;
  border-bottom: 4rpx solid transparent;
  transition: all 0.3s;
}

.status-item.active {
  color: #4caf50;
  border-bottom-color: #4caf50;
  font-weight: bold;
}

/* 订单列表 */
.order-list {
  margin-top: 20rpx;
}

.order-item {
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.order-info {
  display: flex;
  flex-direction: column;
}

.order-no {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.order-status {
  display: flex;
  align-items: center;
}

.status-text {
  font-size: 28rpx;
  color: #4caf50;
  font-weight: bold;
}

/* 商品信息 */
.order-products {
  padding: 20rpx 30rpx;
}

.product-item {
  display: flex;
  align-items: center;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-spec {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.product-count {
  font-size: 24rpx;
  color: #999;
}

.product-price {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
}

.price-text {
  font-size: 32rpx;
  color: #e91e63;
  font-weight: bold;
}

/* 操作按钮 */
.order-actions {
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #f0f0f0;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}

.action-btn {
  padding: 12rpx 30rpx;
  border-radius: 50rpx;
  font-size: 24rpx;
  border: 2rpx solid #ddd;
  background-color: #fff;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

.action-btn.cancel-btn {
  border-color: #999;
  color: #999;
}

.action-btn.pay-btn {
  border-color: #4caf50;
  background-color: #4caf50;
  color: #fff;
}

.action-btn.confirm-btn {
  border-color: #4caf50;
  background-color: #4caf50;
  color: #fff;
}

.action-btn.evaluate-btn {
  border-color: #ff9800;
  background-color: #ff9800;
  color: #fff;
}

.action-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 加载更多 */
.no-more {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 24rpx;
}

/* 响应式设计 */
@media (max-width: 600rpx) {
  .status-item {
    padding: 24rpx 30rpx;
  }

  .order-item {
    margin-bottom: 16rpx;
  }

  .order-header {
    padding: 16rpx 24rpx;
  }

  .order-products {
    padding: 16rpx 24rpx;
  }

  .order-actions {
    padding: 16rpx 24rpx;
  }

  .product-image {
    width: 100rpx;
    height: 100rpx;
  }

  .product-name {
    font-size: 26rpx;
  }

  .price-text {
    font-size: 28rpx;
  }
}

/* 动画效果 */
.order-item {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
