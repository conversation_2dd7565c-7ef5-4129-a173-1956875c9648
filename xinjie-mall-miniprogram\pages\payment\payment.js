// pages/payment/payment.js
import { request } from '../../utils/request.js';
const { API } = require('../../config/api.js');

Page({
  data: {
    orderId: '',
    orderInfo: null,
    paymentMethod: 'wechat',
    paymentStatus: 'pending', // pending, paying, success, failed
    countdown: 900, // 15分钟倒计时
    countdownTimer: null,
    loading: false,
    
    paymentMethods: [
      { value: 'wechat', label: '微信支付', icon: '💰', enabled: true },
      { value: 'alipay', label: '支付宝', icon: '💳', enabled: false },
    ]
  },

  onLoad(options) {
    const { orderId } = options;
    if (!orderId) {
      wx.showToast({
        title: '订单ID不能为空',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({ orderId });
    this.loadOrderInfo();
    this.startCountdown();
  },

  onUnload() {
    this.clearCountdown();
  },

  // 加载订单信息
  async loadOrderInfo() {
    try {
      const response = await request({
        url: `${API.order.detail}/${this.data.orderId}`,
        method: 'GET'
      });

      if (response.success) {
        this.setData({
          orderInfo: response.data
        });
      } else {
        wx.showToast({
          title: response.message || '获取订单信息失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载订单信息失败:', error);
      wx.showToast({
        title: '获取订单信息失败',
        icon: 'none'
      });
    }
  },

  // 开始倒计时
  startCountdown() {
    this.data.countdownTimer = setInterval(() => {
      const countdown = this.data.countdown - 1;
      
      if (countdown <= 0) {
        this.clearCountdown();
        this.handlePaymentTimeout();
        return;
      }

      this.setData({ countdown });
    }, 1000);
  },

  // 清除倒计时
  clearCountdown() {
    if (this.data.countdownTimer) {
      clearInterval(this.data.countdownTimer);
      this.setData({ countdownTimer: null });
    }
  },

  // 支付超时处理
  handlePaymentTimeout() {
    wx.showModal({
      title: '支付超时',
      content: '订单支付已超时，请重新下单',
      showCancel: false,
      success: () => {
        wx.navigateTo({
          url: '/pages/order-list/order-list'
        });
      }
    });
  },

  // 格式化倒计时显示
  formatCountdown(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  },

  // 选择支付方式
  onPaymentMethodChange(e) {
    const method = e.currentTarget.dataset.method;
    this.setData({ paymentMethod: method });
  },

  // 发起支付
  async onPay() {
    if (this.data.loading) return;

    this.setData({ 
      loading: true,
      paymentStatus: 'paying'
    });

    try {
      // 创建支付订单
      const paymentResponse = await request({
        url: API.payment.create,
        method: 'POST',
        data: {
          orderId: this.data.orderId,
          paymentMethod: this.data.paymentMethod
        }
      });

      if (!paymentResponse.success) {
        throw new Error(paymentResponse.message || '创建支付订单失败');
      }

      // 调用微信支付
      if (this.data.paymentMethod === 'wechat') {
        await this.callWechatPay(paymentResponse.data.paymentData);
      } else {
        throw new Error('暂不支持该支付方式');
      }

    } catch (error) {
      console.error('支付失败:', error);
      this.setData({ paymentStatus: 'failed' });
      wx.showToast({
        title: error.message || '支付失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 调用微信支付
  async callWechatPay(paymentData) {
    return new Promise((resolve, reject) => {
      wx.requestPayment({
        ...paymentData,
        success: (res) => {
          console.log('支付成功:', res);
          this.handlePaymentSuccess();
          resolve(res);
        },
        fail: (err) => {
          console.error('支付失败:', err);
          if (err.errMsg === 'requestPayment:fail cancel') {
            this.setData({ paymentStatus: 'pending' });
            wx.showToast({
              title: '支付已取消',
              icon: 'none'
            });
          } else {
            this.setData({ paymentStatus: 'failed' });
            reject(new Error('支付失败'));
          }
        }
      });
    });
  },

  // 支付成功处理
  handlePaymentSuccess() {
    this.clearCountdown();
    this.setData({ paymentStatus: 'success' });
    
    wx.showToast({
      title: '支付成功',
      icon: 'success'
    });

    // 延迟跳转到订单详情
    setTimeout(() => {
      wx.redirectTo({
        url: `/pages/order-detail/order-detail?id=${this.data.orderId}`
      });
    }, 2000);
  },

  // 查询支付状态
  async checkPaymentStatus() {
    try {
      const response = await request({
        url: `${API.payment.status}/${this.data.orderId}`,
        method: 'GET'
      });

      if (response.success && response.data.isPaid) {
        this.handlePaymentSuccess();
      }
    } catch (error) {
      console.error('查询支付状态失败:', error);
    }
  },

  // 取消支付
  onCancel() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消支付吗？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  },

  // 重新支付
  onRetry() {
    this.setData({
      paymentStatus: 'pending',
      countdown: 900
    });
    this.startCountdown();
  },

  // 跳转到订单详情
  goToOrderDetail() {
    wx.redirectTo({
      url: `/pages/order-detail/order-detail?id=${this.data.orderId}`
    });
  },

  // 格式化倒计时显示
  formatCountdown(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
});
