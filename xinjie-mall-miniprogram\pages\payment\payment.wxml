<!--pages/payment/payment.wxml-->
<view class="payment-container">
  <!-- 顶部状态栏 -->
  <view class="status-bar">
    <view class="status-info">
      <text class="status-text">{{paymentStatus === 'pending' ? '等待支付' : paymentStatus === 'paying' ? '支付中...' : paymentStatus === 'success' ? '支付成功' : '支付失败'}}</text>
      <view class="countdown" wx:if="{{paymentStatus === 'pending'}}">
        <text class="countdown-text">剩余时间：{{formatCountdown(countdown)}}</text>
      </view>
    </view>
  </view>

  <!-- 订单信息 -->
  <view class="order-section" wx:if="{{orderInfo}}">
    <view class="section-header">
      <text class="section-title">订单信息</text>
    </view>
    
    <view class="order-info">
      <view class="order-row">
        <text class="label">订单号：</text>
        <text class="value">{{orderInfo.orderNo}}</text>
      </view>
      <view class="order-row">
        <text class="label">商品数量：</text>
        <text class="value">{{orderInfo.totalQuantity}}件</text>
      </view>
      <view class="order-row">
        <text class="label">订单金额：</text>
        <text class="value price">¥{{orderInfo.totalAmount}}</text>
      </view>
    </view>

    <!-- 商品列表 -->
    <view class="product-list">
      <view class="product-item" wx:for="{{orderInfo.products}}" wx:key="id">
        <image class="product-image" src="{{item.image}}" mode="aspectFill"></image>
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <view class="product-meta">
            <text class="product-price">¥{{item.price}}</text>
            <text class="product-quantity">x{{item.quantity}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 支付方式选择 -->
  <view class="payment-methods" wx:if="{{paymentStatus === 'pending'}}">
    <view class="section-header">
      <text class="section-title">支付方式</text>
    </view>
    
    <view class="method-list">
      <view 
        class="method-item {{paymentMethod === item.value ? 'active' : ''}} {{!item.enabled ? 'disabled' : ''}}"
        wx:for="{{paymentMethods}}" 
        wx:key="value"
        bindtap="onPaymentMethodChange"
        data-method="{{item.value}}"
      >
        <view class="method-info">
          <text class="method-icon">{{item.icon}}</text>
          <text class="method-label">{{item.label}}</text>
          <text class="method-status" wx:if="{{!item.enabled}}">（暂不支持）</text>
        </view>
        <view class="method-radio {{paymentMethod === item.value ? 'checked' : ''}}"></view>
      </view>
    </view>
  </view>

  <!-- 支付状态显示 -->
  <view class="payment-status" wx:if="{{paymentStatus !== 'pending'}}">
    <view class="status-icon">
      <text wx:if="{{paymentStatus === 'paying'}}">⏳</text>
      <text wx:if="{{paymentStatus === 'success'}}">✅</text>
      <text wx:if="{{paymentStatus === 'failed'}}">❌</text>
    </view>
    <text class="status-message">
      {{paymentStatus === 'paying' ? '正在处理支付...' : 
        paymentStatus === 'success' ? '支付成功，即将跳转到订单详情' : 
        '支付失败，请重试'}}
    </text>
  </view>

  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <!-- 等待支付状态 -->
    <view class="action-buttons" wx:if="{{paymentStatus === 'pending'}}">
      <button class="cancel-btn" bindtap="onCancel">取消支付</button>
      <button 
        class="pay-btn {{loading ? 'loading' : ''}}" 
        bindtap="onPay"
        disabled="{{loading}}"
      >
        {{loading ? '处理中...' : '立即支付'}}
      </button>
    </view>

    <!-- 支付中状态 -->
    <view class="action-buttons" wx:if="{{paymentStatus === 'paying'}}">
      <button class="check-btn" bindtap="checkPaymentStatus">查询支付状态</button>
    </view>

    <!-- 支付失败状态 -->
    <view class="action-buttons" wx:if="{{paymentStatus === 'failed'}}">
      <button class="cancel-btn" bindtap="onCancel">返回订单</button>
      <button class="retry-btn" bindtap="onRetry">重新支付</button>
    </view>

    <!-- 支付成功状态 -->
    <view class="action-buttons" wx:if="{{paymentStatus === 'success'}}">
      <button class="success-btn" bindtap="goToOrderDetail">查看订单</button>
    </view>
  </view>

  <!-- 安全提示 -->
  <view class="security-tips">
    <view class="tips-header">
      <text class="tips-icon">🔒</text>
      <text class="tips-title">安全提示</text>
    </view>
    <view class="tips-content">
      <text class="tips-text">• 请在安全的网络环境下进行支付</text>
      <text class="tips-text">• 支付过程中请勿关闭小程序</text>
      <text class="tips-text">• 如遇问题请联系客服</text>
    </view>
  </view>
</view>
