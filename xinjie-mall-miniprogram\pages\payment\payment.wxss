/* pages/payment/payment.wxss */
.payment-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f0fdf4 0%, #ecfdf5 50%, #f7fee7 100%);
  padding-bottom: 120rpx;
  position: relative;
}

.payment-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 400rpx;
  background: linear-gradient(135deg, #86efac 0%, #6ee7b7 50%, #34d399 100%);
  opacity: 0.08;
  z-index: 0;
}

/* 状态栏 */
.status-bar {
  background: linear-gradient(135deg, #86efac, #6ee7b7, #34d399);
  padding: 60rpx 30rpx 40rpx;
  color: white;
  border-radius: 0 0 40rpx 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(52, 211, 153, 0.15);
  position: relative;
  z-index: 1;
}

.status-info {
  text-align: center;
}

.status-text {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.countdown {
  margin-top: 20rpx;
}

.countdown-text {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 订单信息 */
.order-section {
  background: white;
  margin: 30rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(134, 239, 172, 0.1);
  position: relative;
  z-index: 1;
}

.section-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.order-info {
  padding: 30rpx;
}

.order-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.order-row:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #666;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.price {
  color: #059669;
  font-weight: bold;
  font-size: 32rpx;
}

/* 商品列表 */
.product-list {
  border-top: 1rpx solid #f0f0f0;
  padding: 20rpx 30rpx;
}

.product-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 26rpx;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 24rpx;
  color: #ff4444;
  font-weight: bold;
}

.product-quantity {
  font-size: 24rpx;
  color: #999;
}

/* 支付方式 */
.payment-methods {
  background: white;
  margin: 30rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(134, 239, 172, 0.1);
  position: relative;
  z-index: 1;
}

.method-list {
  padding: 0 30rpx 20rpx;
}

.method-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
  transition: all 0.3s ease;
}

.method-item:last-child {
  border-bottom: none;
}

.method-item.active {
  background-color: #f8fff8;
}

.method-item.disabled {
  opacity: 0.5;
}

.method-info {
  display: flex;
  align-items: center;
}

.method-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.method-label {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.method-status {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}

.method-radio {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  position: relative;
  transition: all 0.3s ease;
}

.method-radio.checked {
  border-color: #4CAF50;
  background-color: #4CAF50;
}

.method-radio.checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20rpx;
  height: 20rpx;
  background-color: white;
  border-radius: 50%;
}

/* 支付状态 */
.payment-status {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 60rpx 30rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.status-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.status-message {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -4rpx 12rpx rgba(0,0,0,0.1);
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-buttons button {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.cancel-btn:active {
  background-color: #e8e8e8;
}

.pay-btn {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.pay-btn:active {
  background: linear-gradient(135deg, #45a049, #3d8b40);
}

.pay-btn.loading {
  opacity: 0.7;
}

.check-btn {
  background-color: #2196F3;
  color: white;
}

.check-btn:active {
  background-color: #1976D2;
}

.retry-btn {
  background: linear-gradient(135deg, #FF9800, #F57C00);
  color: white;
}

.retry-btn:active {
  background: linear-gradient(135deg, #F57C00, #E65100);
}

.success-btn {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.success-btn:active {
  background: linear-gradient(135deg, #45a049, #3d8b40);
}

/* 安全提示 */
.security-tips {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.tips-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.tips-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.tips-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.tips-content {
  padding-left: 42rpx;
}

.tips-text {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 8rpx;
}

.tips-text:last-child {
  margin-bottom: 0;
}
