// pages/product-detail/product-detail.js
const { get, post, del } = require("../../utils/request");
const { API } = require("../../config/api");
const { formatPrice } = require("../../utils/format");
const { requireLogin } = require("../../utils/auth");

Page({
  data: {
    // 商品ID
    productId: null,

    // 商品信息
    product: null,

    // 当前选中的主图索引
    currentImageIndex: 0,

    // 选中的规格
    selectedSpecs: {},

    // 购买数量
    quantity: 1,

    // 加载状态
    loading: true,

    // 默认图片
    defaultImage: "/images/common/default-product.png",

    // 是否显示规格弹窗
    showSpecModal: false,

    // 弹窗类型：cart-加入购物车，buy-立即购买
    modalType: "cart",

    // 规格选择弹窗（照搬分类页面的实现）
    specModal: {
      visible: false,
      product: {},
      quantity: 1,
      totalPrice: "0.00",
      validQuantity: 1,
      cursor: -1,
      selectionStart: -1,
      selectionEnd: -1,
      focusValue: null,
      lastTapTime: 0
    },

    // 评论相关
    reviews: [],
    reviewPagination: {
      page: 1,
      limit: 10,
      hasMore: true
    },
    canReview: false,
    reviewOrderId: null,
    
    // 评论弹窗
    reviewModal: {
      visible: false,
      rating: 5,
      content: '',
      isAnonymous: false
    },
  },

  // 页面加载时执行
  onLoad: function (options) {
    const productId = options.id;
    if (productId) {
      this.setData({ productId });
      this.loadProductDetail();
      this.loadProductReviews();
      this.checkCanReview();
    } else {
      wx.showToast({
        title: "商品不存在",
        icon: "none",
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
    }
  },

  // 页面显示时执行
  onShow: function () {
    // 可以在这里刷新数据
  },

  // 页面分享
  onShareAppMessage: function () {
    return {
      title: this.data.product ? this.data.product.name : "心洁茶叶商城",
      path: `/pages/product-detail/product-detail?id=${this.data.productId}`,
    };
  },

  // 加载商品详情
  loadProductDetail: function () {
    this.setData({ loading: true });

    // 使用正确的API路径格式
    const url = `${API.product.detail}/${this.data.productId}`;
    
    get(url)
      .then((res) => {
        if (res.code === 200 || res.success) {
          const product = res.data;

          // 格式化价格
          product.priceText = formatPrice(product.price);

          // 初始化规格选择
          const selectedSpecs = {};
          if (product.specs && product.specs.length > 0) {
            product.specs.forEach((spec) => {
              if (spec.options && spec.options.length > 0) {
                selectedSpecs[spec.name] = spec.options[0].value;
              }
            });
          }

          this.setData({
            product,
            selectedSpecs,
          });

          // 设置页面标题
          wx.setNavigationBarTitle({
            title: product.name,
          });
        } else {
          wx.showToast({
            title: res.message || "商品不存在",
            icon: "none",
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 2000);
        }
      })
      .catch((error) => {
        console.error("加载商品详情失败:", error);
        wx.showToast({
          title: "加载失败",
          icon: "none",
        });
      })
      .finally(() => {
        this.setData({ loading: false });
      });
  },

  // 切换主图
  onImageChange: function (e) {
    this.setData({
      currentImageIndex: e.detail.current,
    });
  },

  // 点击缩略图
  onThumbTap: function (e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentImageIndex: index,
    });
  },

  // 选择规格
  onSpecSelect: function (e) {
    const { specName, specValue } = e.currentTarget.dataset;

    this.setData({
      [`selectedSpecs.${specName}`]: specValue,
    });
  },

  // 减少数量
  onQuantityDecrease: function () {
    if (this.data.quantity > 1) {
      this.setData({
        quantity: this.data.quantity - 1,
      });
    }
  },

  // 增加数量
  onQuantityIncrease: function () {
    const maxQuantity = this.data.product.stock || 999;
    if (this.data.quantity < maxQuantity) {
      this.setData({
        quantity: this.data.quantity + 1,
      });
    } else {
      wx.showToast({
        title: "库存不足",
        icon: "none",
      });
    }
  },

  // 数量输入
  onQuantityInput: function (e) {
    const value = parseInt(e.detail.value) || 1;
    const maxQuantity = this.data.product.stock || 999;

    this.setData({
      quantity: Math.min(Math.max(value, 1), maxQuantity),
    });
  },

  // 显示规格弹窗
  showSpecModal: function (type) {
    this.setData({
      showSpecModal: true,
      modalType: type,
    });
  },

  // 隐藏规格弹窗
  hideSpecModal: function () {
    this.setData({
      showSpecModal: false,
    });
  },

  // 加入购物车（修改为使用新的规格选择弹窗）
  onAddToCart: function () {
    this.onShowSpecModal();
  },

  // 立即购买（修改为使用新的规格选择弹窗）
  onBuyNow: function () {
    this.onShowSpecModal();
  },

  // 显示规格选择弹窗（照搬分类页面的实现）
  onShowSpecModal: function (e) {
    // 处理商品图片URL
    const productWithImage = {
      ...this.data.product,
      image: this.data.product.main_image || this.data.product.image || this.data.defaultImage
    };
    
    // 使用原始价格数字进行计算
    const price = this.data.product.originalPrice || parseFloat(this.data.product.price) || 0;
    
    this.setData({
      'specModal.visible': true,
      'specModal.product': productWithImage,
      'specModal.quantity': 1,
      'specModal.totalPrice': (price * 1).toFixed(2),
    });
  },

  // 隐藏规格选择弹窗
  onHideSpecModal: function () {
    this.setData({
      'specModal.visible': false,
    });
  },

  // 减少数量（规格弹窗）
  onQuantityDecrease: function () {
    if (this.data.specModal.quantity > 1) {
      const newQuantity = this.data.specModal.quantity - 1;
      const price = this.data.specModal.product.originalPrice || parseFloat(this.data.specModal.product.price) || 0;
      const totalPrice = (price * newQuantity).toFixed(2);
      this.setData({
        'specModal.quantity': newQuantity,
        'specModal.totalPrice': totalPrice,
      });
    }
  },

  // 增加数量（规格弹窗）
  onQuantityIncrease: function () {
    const maxQuantity = this.data.specModal.product.stock || 999;
    const currentQuantity = parseInt(this.data.specModal.quantity) || 1;
    
    if (currentQuantity < maxQuantity) {
      const newQuantity = currentQuantity + 1;
      const price = this.data.specModal.product.originalPrice || parseFloat(this.data.specModal.product.price) || 0;
      const totalPrice = (price * newQuantity).toFixed(2);
      this.setData({
        'specModal.quantity': newQuantity.toString(),
        'specModal.totalPrice': totalPrice,
      });
    } else {
      wx.showToast({
        title: "库存不足",
        icon: "none",
      });
    }
  },

  // 长按快速增加数量
  onQuantityIncreaseLongPress: function () {
    this.startQuantityAutoChange('increase');
  },

  // 长按快速减少数量
  onQuantityDecreaseLongPress: function () {
    this.startQuantityAutoChange('decrease');
  },

  // 开始自动改变数量
  startQuantityAutoChange: function (direction) {
    const maxQuantity = this.data.specModal.product.stock || 999;
    const minQuantity = 1;
    let currentQuantity = parseInt(this.data.specModal.quantity) || 1;
    
    const changeQuantity = () => {
      if (direction === 'increase' && currentQuantity < maxQuantity) {
        currentQuantity++;
      } else if (direction === 'decrease' && currentQuantity > minQuantity) {
        currentQuantity--;
      } else {
        // 达到限制，停止自动改变
        this.stopQuantityAutoChange();
        return;
      }
      
      const price = this.data.specModal.product.originalPrice || parseFloat(this.data.specModal.product.price) || 0;
      const totalPrice = (price * currentQuantity).toFixed(2);
      
      this.setData({
        'specModal.quantity': currentQuantity.toString(),
        'specModal.totalPrice': totalPrice,
      });
      
      // 继续自动改变
      this.quantityAutoChangeTimer = setTimeout(changeQuantity, 100);
    };
    
    // 开始自动改变
    changeQuantity();
  },

  // 停止自动改变数量
  stopQuantityAutoChange: function () {
    if (this.quantityAutoChangeTimer) {
      clearTimeout(this.quantityAutoChangeTimer);
      this.quantityAutoChangeTimer = null;
    }
  },

  // 数量输入（规格弹窗）
  onQuantityInput: function (e) {
    const inputValue = e.detail.value;
    const cursor = e.detail.cursor || 0;
    
    // 如果输入为空，显示占位符
    if (!inputValue || inputValue === '') {
      this.setData({
        'specModal.quantity': '',
        'specModal.totalPrice': '0.00',
      });
      return;
    }
    
    // 解析输入值
    const value = parseInt(inputValue) || 1;
    const maxQuantity = this.data.specModal.product.stock || 999;
    const minQuantity = 1;
    
    // 验证数量范围
    let validQuantity = value;
    let showWarning = false;
    let warningMessage = '';
    
    if (value < minQuantity) {
      validQuantity = minQuantity;
      showWarning = true;
      warningMessage = `最少购买${minQuantity}件`;
    } else if (value > maxQuantity) {
      validQuantity = maxQuantity;
      showWarning = true;
      warningMessage = `库存不足，最多购买${maxQuantity}件`;
    }
    
    // 计算总价
    const price = this.data.specModal.product.originalPrice || parseFloat(this.data.specModal.product.price) || 0;
    const totalPrice = (price * validQuantity).toFixed(2);
    
    // 更新数据
    this.setData({
      'specModal.quantity': inputValue, // 保持用户输入的原值
      'specModal.validQuantity': validQuantity, // 存储有效值
      'specModal.totalPrice': totalPrice,
      'specModal.cursor': cursor
    });
    
    // 显示警告信息（但不阻止输入）
    if (showWarning) {
      wx.showToast({
        title: warningMessage,
        icon: 'none',
        duration: 1500
      });
    }
  },

  // 数量输入框获得焦点时的处理
  onQuantityFocus: function (e) {
    // 记录当前输入框的值，用于后续比较
    this.setData({
      'specModal.focusValue': e.detail.value
    });
  },

  // 数量输入框点击时的处理
  onQuantityTap: function (e) {
    // 记录点击时间，用于检测双击
    const now = Date.now();
    const lastTapTime = this.data.specModal.lastTapTime || 0;
    
    if (now - lastTapTime < 300) {
      // 双击：快速重置为1
      const price = this.data.specModal.product.originalPrice || parseFloat(this.data.specModal.product.price) || 0;
      this.setData({
        'specModal.quantity': '1',
        'specModal.validQuantity': 1,
        'specModal.totalPrice': (price * 1).toFixed(2),
        'specModal.selectionStart': 0,
        'specModal.selectionEnd': 1
      });
      return;
    }
    
    // 单击：自动全选文本，方便用户直接输入新值
    const inputValue = e.detail.value;
    if (inputValue && inputValue !== '1') {
      // 延迟执行，确保点击事件完成后再设置选择
      setTimeout(() => {
        this.setData({
          'specModal.selectionStart': 0,
          'specModal.selectionEnd': inputValue.length
        });
      }, 50);
    }
    
    this.setData({
      'specModal.lastTapTime': now
    });
  },



  // 数量输入框失去焦点时的验证
  onQuantityBlur: function (e) {
    const value = parseInt(e.detail.value) || 1;
    const maxQuantity = this.data.specModal.product.stock || 999;
    const minQuantity = 1;
    
    let validQuantity = value;
    
    // 验证数量范围
    if (value < minQuantity) {
      validQuantity = minQuantity;
      wx.showToast({
        title: `最少购买${minQuantity}件`,
        icon: 'none'
      });
    } else if (value > maxQuantity) {
      validQuantity = maxQuantity;
      wx.showToast({
        title: `库存不足，最多购买${maxQuantity}件`,
        icon: 'none'
      });
    }
    
    // 更新数量和总价
    const price = this.data.specModal.product.originalPrice || parseFloat(this.data.specModal.product.price) || 0;
    const totalPrice = (price * validQuantity).toFixed(2);
    
    this.setData({
      'specModal.quantity': validQuantity,
      'specModal.totalPrice': totalPrice,
      'specModal.focusValue': null // 清除焦点值
    });
  },

  // 确认添加到购物车
  onConfirmAddToCart: function () {
    const { product, quantity } = this.data.specModal;
    
    requireLogin().then(() => {
      this.addToCartWithQuantity(product, quantity);
    }).catch(() => {
      // 用户未登录，跳转到登录页面
      wx.navigateTo({
        url: '/pages/login/login'
      });
    });
  },

  // 添加到购物车（带数量）
  addToCartWithQuantity: function (product, quantity) {
    const cartData = {
      productId: product.id,
      quantity: quantity,
    };

    post(API.cart.add, cartData)
      .then((res) => {
        if (res.code === 200 || res.success) {
          wx.showToast({
            title: "已添加至购物车",
            icon: "success",
          });
          this.onHideSpecModal(); // 隐藏弹窗
        } else {
          wx.showToast({
            title: res.message || "添加失败",
            icon: "none",
          });
        }
      })
      .catch((error) => {
        console.error("添加到购物车失败:", error);
        wx.showToast({
          title: "网络连接失败",
          icon: "none",
        });
      });
  },

  // 确认立即购买
  onConfirmBuyNow: function () {
    const { product, quantity } = this.data.specModal;
    
    requireLogin().then(() => {
      // 构造购买商品数据
      const buyProduct = {
        ...product,
        quantity: quantity,
        selectedSpecs: this.data.selectedSpecs,
      };

      // 跳转到订单确认页面
      wx.navigateTo({
        url: "/pages/order-confirm/order-confirm",
        success: function (res) {
          res.eventChannel.emit("acceptDataFromOpenerPage", {
            products: [buyProduct],
            totalPrice: product.price * quantity,
          });
        }.bind(this),
      });
      
      this.onHideSpecModal(); // 隐藏弹窗
    }).catch(() => {
      // 用户未登录，跳转到登录页面
      wx.navigateTo({
        url: '/pages/login/login'
      });
    });
  },

  // 图片加载失败
  onImageError: function (e) {
    const index = e.currentTarget.dataset.index;
    const type = e.currentTarget.dataset.type;

    if (type === "main") {
      this.setData({
        [`product.images[${index}]`]: this.data.defaultImage,
      });
    }
  },

  // 预览图片
  onImagePreview: function () {
    const images = this.data.product.images || [];
    if (images.length > 0) {
      wx.previewImage({
        current: images[this.data.currentImageIndex],
        urls: images,
      });
    }
  },

  // 联系客服
  onContactService: function () {
    wx.showModal({
      title: "联系客服",
      content: "是否要联系客服？",
      success: (res) => {
        if (res.confirm) {
          // 这里可以跳转到客服页面或者打开客服电话
          wx.showToast({
            title: "功能开发中",
            icon: "none",
          });
        }
      },
    });
  },

  // 加载商品评论
  loadProductReviews: function () {
    const { productId } = this.data;
    const { page, limit } = this.data.reviewPagination;

    get(`${API.review.product}/${productId}?page=${page}&limit=${limit}`)
      .then((res) => {
        if (res.code === 200 || res.success) {
          const newReviews = res.data.list || [];
          const pagination = res.data.pagination || {};
          
          this.setData({
            reviews: page === 1 ? newReviews : [...this.data.reviews, ...newReviews],
            'reviewPagination.hasMore': pagination.page < pagination.pages,
            'reviewPagination.page': pagination.page
          });
        }
      })
      .catch((error) => {
        console.error("加载商品评论失败:", error);
      });
  },

  // 检查是否可以评论
  checkCanReview: function () {
    const { productId } = this.data;
    
    requireLogin().then(() => {
      return get(`${API.review.check}/${productId}`);
    }).then((res) => {
      if (res.code === 200 || res.success) {
        this.setData({
          canReview: res.data.canReview,
          reviewOrderId: res.data.orderId
        });
      }
    }).catch((error) => {
      console.error("检查评论权限失败:", error);
    });
  },

  // 显示评论弹窗
  showReviewModal: function () {
    if (!this.data.canReview) {
      wx.showToast({
        title: "您还没有购买过该商品",
        icon: "none"
      });
      return;
    }

    this.setData({
      'reviewModal.visible': true
    });
  },

  // 隐藏评论弹窗
  hideReviewModal: function () {
    this.setData({
      'reviewModal.visible': false,
      'reviewModal.rating': 5,
      'reviewModal.content': '',
      'reviewModal.isAnonymous': false
    });
  },

  // 设置评分
  setRating: function (e) {
    const rating = e.currentTarget.dataset.rating;
    this.setData({
      'reviewModal.rating': rating
    });
  },

  // 评论内容输入
  onReviewContentInput: function (e) {
    this.setData({
      'reviewModal.content': e.detail.value
    });
  },

  // 切换匿名
  toggleAnonymous: function () {
    this.setData({
      'reviewModal.isAnonymous': !this.data.reviewModal.isAnonymous
    });
  },

  // 提交评论
  submitReview: function () {
    const { rating, content, isAnonymous } = this.data.reviewModal;
    const { productId, reviewOrderId } = this.data;

    if (!content.trim()) {
      wx.showToast({
        title: "请输入评论内容",
        icon: "none"
      });
      return;
    }

    const reviewData = {
      orderId: reviewOrderId,
      productId: productId,
      rating: rating,
      content: content.trim(),
      isAnonymous: isAnonymous
    };

    post(API.review.create, reviewData)
      .then((res) => {
        if (res.code === 200 || res.success) {
          wx.showToast({
            title: "评论发表成功",
            icon: "success"
          });
          this.hideReviewModal();
          this.loadProductReviews(); // 重新加载评论
          this.checkCanReview(); // 重新检查评论权限
        } else {
          wx.showToast({
            title: res.message || "评论发表失败",
            icon: "none"
          });
        }
      })
      .catch((error) => {
        console.error("发表评论失败:", error);
        wx.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      });
  },

  // 删除评论
  deleteReview: function (e) {
    const reviewId = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: "确认删除",
      content: "确定要删除这条评论吗？",
      success: (res) => {
        if (res.confirm) {
          del(`${API.review.delete}/${reviewId}`)
            .then((res) => {
              if (res.code === 200 || res.success) {
                wx.showToast({
                  title: "评论删除成功",
                  icon: "success"
                });
                this.loadProductReviews(); // 重新加载评论
                this.checkCanReview(); // 重新检查评论权限
              } else {
                wx.showToast({
                  title: res.message || "删除失败",
                  icon: "none"
                });
              }
            })
            .catch((error) => {
              console.error("删除评论失败:", error);
              wx.showToast({
                title: "网络错误，请重试",
                icon: "none"
              });
            });
        }
      }
    });
  },

  // 加载更多评论
  loadMoreReviews: function () {
    if (!this.data.reviewPagination.hasMore) {
      return;
    }

    this.setData({
      'reviewPagination.page': this.data.reviewPagination.page + 1
    });

    this.loadProductReviews();
  },
});
