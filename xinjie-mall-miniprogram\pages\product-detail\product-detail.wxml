<!--pages/product-detail/product-detail.wxml-->
<view class="container" wx:if="{{!loading && product}}">
  <!-- 商品图片 -->
  <view class="product-images">
    <swiper 
      class="main-images" 
      indicator-dots="{{true}}" 
      autoplay="{{false}}"
      bindchange="onImageChange"
      current="{{currentImageIndex}}"
    >
      <swiper-item wx:for="{{product.images}}" wx:key="*this">
        <image 
          class="main-image" 
          src="{{item || defaultImage}}" 
          mode="aspectFit"
          bindtap="onImagePreview"
          binderror="onImageError"
          data-index="{{index}}"
          data-type="main"
        ></image>
      </swiper-item>
    </swiper>
    
    <!-- 缩略图 -->
    <scroll-view class="thumb-images" scroll-x="{{true}}" wx:if="{{product.images.length > 1}}">
      <view 
        class="thumb-item {{currentImageIndex === index ? 'active' : ''}}"
        wx:for="{{product.images}}" 
        wx:key="*this"
        bindtap="onThumbTap"
        data-index="{{index}}"
      >
        <image class="thumb-image" src="{{item || defaultImage}}" mode="aspectFill"></image>
      </view>
    </scroll-view>
  </view>

  <!-- 商品信息 -->
  <view class="product-info">
    <view class="product-title">{{product.name}}</view>
    <view class="product-price">
      <text class="current-price">{{product.priceText}}</text>
      <text class="original-price" wx:if="{{product.originalPrice}}">￥{{product.originalPrice}}</text>
    </view>
    <view class="product-stats">
      <text class="sales">销量{{product.sales || 0}}</text>
      <text class="stock">库存{{product.stock || 0}}</text>
    </view>
  </view>

  <!-- 规格选择 -->
  <view class="product-specs" wx:if="{{product.specs && product.specs.length > 0}}">
    <view class="spec-section" wx:for="{{product.specs}}" wx:key="name">
      <view class="spec-title">{{item.name}}</view>
      <view class="spec-options">
        <view 
          class="spec-option {{selectedSpecs[item.name] === option.value ? 'active' : ''}}"
          wx:for="{{item.options}}" 
          wx:key="value"
          wx:for-item="option"
          bindtap="onSpecSelect"
          data-spec-name="{{item.name}}"
          data-spec-value="{{option.value}}"
        >
          {{option.value}}
        </view>
      </view>
    </view>
  </view>

  <!-- 数量选择 -->
  <view class="quantity-section">
    <view class="section-title">数量</view>
    <view class="quantity-control">
      <view class="quantity-btn" bindtap="onQuantityDecrease">-</view>
      <input class="quantity-input" type="number" value="{{quantity}}" bindinput="onQuantityInput" />
      <view class="quantity-btn" bindtap="onQuantityIncrease">+</view>
    </view>
  </view>

  <!-- 商品详情 -->
  <view class="product-detail" wx:if="{{product.detail}}">
    <view class="section-title">商品详情</view>
    <view class="detail-content">
      <rich-text nodes="{{product.detail}}"></rich-text>
    </view>
  </view>

  <!-- 商品评价 -->
  <view class="product-reviews">
    <view class="review-header">
      <view class="section-title">商品评价({{reviews.length}})</view>
      <view class="review-actions">
        <button class="review-btn" bindtap="showReviewModal" wx:if="{{canReview}}">写评价</button>
      </view>
    </view>
    
    <view class="review-list" wx:if="{{reviews.length > 0}}">
      <view class="review-item" wx:for="{{reviews}}" wx:key="id">
        <view class="review-user-info">
          <image class="user-avatar" src="{{item.user.avatar || '/images/common/default-avatar.png'}}" mode="aspectFill"></image>
          <view class="user-details">
            <text class="user-name">{{item.user.nickname}}</text>
            <view class="rating-stars">
              <text class="star" wx:for="{{5}}" wx:key="*this" wx:for-item="starIndex">
                {{starIndex <= item.rating ? '★' : '☆'}}
              </text>
            </view>
            <text class="review-time">{{item.createTime}}</text>
          </view>
          <view class="review-actions" wx:if="{{item.user.id === userInfo.id}}">
            <text class="delete-btn" bindtap="deleteReview" data-id="{{item.id}}">删除</text>
          </view>
        </view>
        <view class="review-content">{{item.content}}</view>
        <view class="review-images" wx:if="{{item.images && item.images.length > 0}}">
          <image 
            class="review-image" 
            wx:for="{{item.images}}" 
            wx:key="*this" 
            wx:for-item="image"
            src="{{image}}" 
            mode="aspectFill"
            bindtap="previewImage"
            data-urls="{{item.images}}"
            data-current="{{image}}"
          ></image>
        </view>
        <view class="review-reply" wx:if="{{item.replyContent}}">
          <text class="reply-label">商家回复：</text>
          <text class="reply-content">{{item.replyContent}}</text>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" wx:if="{{reviewPagination.hasMore}}" bindtap="loadMoreReviews">
        <text>加载更多评论</text>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-reviews" wx:else>
      <text class="empty-text">暂无评价</text>
      <text class="empty-tip">购买后可以发表评价哦~</text>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<loading wx:if="{{loading}}"></loading>

<!-- 底部操作栏 -->
<view class="action-bar" wx:if="{{!loading && product}}">
  <view class="action-buttons">
    <button class="contact-btn" bindtap="onContactService">客服</button>
    <button class="cart-btn" bindtap="onAddToCart">加入购物车</button>
    <button class="buy-btn" bindtap="onBuyNow">立即购买</button>
  </view>
</view>

<!-- 规格选择弹窗（照搬分类页面的实现） -->
<view class="spec-modal-mask {{specModal.visible ? 'visible' : ''}}" bindtap="onHideSpecModal"></view>
<view class="spec-modal {{specModal.visible ? 'visible' : ''}}">
  <view class="spec-modal-header">
    <text class="spec-modal-close" bindtap="onHideSpecModal">✕</text>
  </view>
  
  <view class="spec-modal-content">
    <!-- 商品信息 -->
    <view class="spec-product-info">
      <view class="spec-product-header">
        <image 
          class="spec-product-image" 
          src="{{specModal.product.image || defaultImage}}" 
          mode="aspectFill"
        ></image>
        <view class="spec-product-basic">
          <text class="spec-product-name">{{specModal.product.name}}</text>
          <text class="spec-product-price">{{specModal.product.price}}</text>
        </view>
      </view>
      
      <view class="spec-product-extra">
        <text class="spec-product-code" wx:if="{{specModal.product.id}}">商品编码: {{specModal.product.id}}</text>
        <!-- 购买单位 -->
        <view class="purchase-unit-section">
          <text class="purchase-unit-label">购买单位</text>
          <view class="purchase-unit-btn">
            <text class="purchase-unit-text">{{specModal.product.unit || '件'}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 数量选择 -->
    <view class="quantity-section">
      <view class="quantity-control">
        <text class="quantity-btn" bindtap="onQuantityDecrease" bindlongpress="onQuantityDecreaseLongPress" bindtouchend="stopQuantityAutoChange">-</text>
        <view class="quantity-input-wrapper">
          <input 
            class="quantity-input" 
            type="number" 
            value="{{specModal.quantity}}" 
            bindinput="onQuantityInput"
            bindblur="onQuantityBlur"
            bindfocus="onQuantityFocus"
            bindtap="onQuantityTap"
            placeholder="1"
            min="1"
            max="{{specModal.product.stock || 999}}"
            cursor-spacing="10"
            selection-start="{{specModal.selectionStart || -1}}"
            selection-end="{{specModal.selectionEnd || -1}}"
          />

        </view>
        <text class="quantity-btn" bindtap="onQuantityIncrease" bindlongpress="onQuantityIncreaseLongPress" bindtouchend="stopQuantityAutoChange">+</text>
      </view>
      <text class="quantity-label">数量</text>
      <text class="stock-info" wx:if="{{specModal.product.stock !== undefined}}">库存: {{specModal.product.stock}}</text>
    </view>
    
    <!-- 总价 -->
    <view class="total-price-section">
      <text class="total-price-label">总价:</text>
      <text class="total-price-value">{{specModal.totalPrice}}</text>
    </view>
  </view>
  
  <!-- 规格选择弹窗底部 -->
  <view class="spec-modal-footer">
    <view class="spec-modal-actions">
      <view class="spec-modal-btn secondary" bindtap="onHideSpecModal">
        <text class="btn-text">取消</text>
      </view>
      <view class="spec-modal-btn primary" bindtap="onConfirmAddToCart">
        <text class="btn-text">添加至购物车</text>
        <text class="btn-icon">🛒</text>
      </view>
      <view class="spec-modal-btn buy-now" bindtap="onConfirmBuyNow">
        <text class="btn-text">立即购买</text>
        <text class="btn-icon">💰</text>
      </view>
    </view>
  </view>
</view>

<!-- 评论弹窗 -->
<view class="review-modal-mask {{reviewModal.visible ? 'visible' : ''}}" bindtap="hideReviewModal"></view>
<view class="review-modal {{reviewModal.visible ? 'visible' : ''}}">
  <view class="review-modal-header">
    <text class="review-modal-title">发表评价</text>
    <text class="review-modal-close" bindtap="hideReviewModal">✕</text>
  </view>
  
  <view class="review-modal-content">
    <!-- 评分 -->
    <view class="rating-section">
      <text class="rating-label">评分</text>
      <view class="rating-stars">
        <text 
          class="star {{index < reviewModal.rating ? 'active' : ''}}" 
          wx:for="{{5}}" 
          wx:key="*this" 
          wx:for-item="index"
          bindtap="setRating"
          data-rating="{{index + 1}}"
        >
          {{index < reviewModal.rating ? '★' : '☆'}}
        </text>
      </view>
    </view>
    
    <!-- 评价内容 -->
    <view class="content-section">
      <text class="content-label">评价内容</text>
      <textarea 
        class="content-input" 
        placeholder="请分享您的使用体验..."
        value="{{reviewModal.content}}"
        bindinput="onReviewContentInput"
        maxlength="500"
      ></textarea>
      <text class="content-count">{{reviewModal.content.length}}/500</text>
    </view>
    
    <!-- 匿名选项 -->
    <view class="anonymous-section">
      <view class="anonymous-option" bindtap="toggleAnonymous">
        <text class="checkbox {{reviewModal.isAnonymous ? 'checked' : ''}}">
          {{reviewModal.isAnonymous ? '✓' : ''}}
        </text>
        <text class="anonymous-text">匿名评价</text>
      </view>
    </view>
  </view>
  
  <!-- 评论弹窗底部 -->
  <view class="review-modal-footer">
    <view class="review-modal-actions">
      <view class="review-modal-btn secondary" bindtap="hideReviewModal">
        <text class="btn-text">取消</text>
      </view>
      <view class="review-modal-btn primary" bindtap="submitReview">
        <text class="btn-text">发表评价</text>
      </view>
    </view>
  </view>
</view> 