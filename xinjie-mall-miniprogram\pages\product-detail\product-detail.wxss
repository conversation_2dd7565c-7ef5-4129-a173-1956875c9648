/* pages/product-detail/product-detail.wxss */
@import "../../styles/global.wxss";

.container {
  padding-bottom: 120rpx;
  background: linear-gradient(180deg, #f0fdf4 0%, #ecfdf5 30%, #f7fee7 70%, #ffffff 100%);
  position: relative;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 400rpx;
  background: linear-gradient(135deg, #86efac 0%, #6ee7b7 50%, #34d399 100%);
  opacity: 0.05;
  z-index: 0;
}

/* 商品图片 */
.product-images {
  background: rgba(255, 255, 255, 0.98);
  margin: 20rpx 30rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(52, 211, 153, 0.08);
  position: relative;
  z-index: 1;
}

.main-images {
  height: 500rpx;
  width: 100%;
}

.main-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumb-images {
  padding: 20rpx;
  white-space: nowrap;
  background: white;
}

.thumb-item {
  display: inline-block;
  margin-right: 20rpx;
  border: 2rpx solid transparent;
  border-radius: 8rpx;
  overflow: hidden;
  transition: all 0.3s ease;
}

.thumb-item.active {
  border-color: #86efac;
  box-shadow: 0 4rpx 12rpx rgba(52, 211, 153, 0.3);
}

.thumb-image {
  width: 80rpx;
  height: 80rpx;
  object-fit: cover;
}

/* 商品信息 */
.product-info {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.product-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

.product-price {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.current-price {
  font-size: 36rpx;
  font-weight: bold;
  color: #e74c3c;
  margin-right: 20rpx;
}

.original-price {
  font-size: 24rpx;
  color: #95a5a6;
  text-decoration: line-through;
}

.product-stats {
  display: flex;
  gap: 30rpx;
  font-size: 24rpx;
  color: #7f8c8d;
}

/* 规格选择 */
.product-specs {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.spec-section {
  margin-bottom: 30rpx;
}

.spec-section:last-child {
  margin-bottom: 0;
}

.spec-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20rpx;
}

.spec-options {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.spec-option {
  padding: 15rpx 25rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 25rpx;
  font-size: 24rpx;
  color: #6c757d;
  background: white;
  transition: all 0.3s ease;
}

.spec-option.active {
  border-color: #4a7c59;
  background: #4a7c59;
  color: white;
}

.spec-option:active {
  transform: scale(0.95);
}

/* 数量选择 */
.quantity-section {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20rpx;
}

.quantity-control {
  display: flex;
  align-items: center;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  overflow: hidden;
  width: fit-content;
}

.quantity-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  color: #4a7c59;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.quantity-btn:active {
  background: #e9ecef;
}

.quantity-input {
  width: 120rpx;
  height: 80rpx;
  text-align: center;
  border: none;
  background: white;
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
}

/* 商品详情 */
.product-detail {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.detail-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 商品评价 */
.product-reviews {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.review-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
}

.review-actions {
  display: flex;
  gap: 15rpx;
}

.review-btn {
  padding: 10rpx 20rpx;
  background: linear-gradient(135deg, #4a7c59, #6b9a7a);
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.review-btn:active {
  transform: scale(0.95);
}

.review-list {
  margin-top: 20rpx;
}

.review-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.review-item:last-child {
  border-bottom: none;
}

.review-user-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15rpx;
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 15rpx;
  flex-shrink: 0;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #2c3e50;
  display: block;
  margin-bottom: 5rpx;
}

.rating-stars {
  display: flex;
  gap: 2rpx;
  margin-bottom: 5rpx;
}

.star {
  font-size: 20rpx;
  color: #ffd700;
}

.review-time {
  font-size: 22rpx;
  color: #95a5a6;
  display: block;
}

.review-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 15rpx;
}

.review-images {
  display: flex;
  gap: 10rpx;
  margin-bottom: 15rpx;
  flex-wrap: wrap;
}

.review-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  object-fit: cover;
}

.review-reply {
  background: #f8f9fa;
  padding: 15rpx;
  border-radius: 8rpx;
  margin-top: 10rpx;
}

.reply-label {
  font-size: 22rpx;
  color: #4a7c59;
  font-weight: 600;
  margin-right: 10rpx;
}

.reply-content {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.load-more {
  text-align: center;
  padding: 20rpx;
  color: #4a7c59;
  font-size: 26rpx;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 20rpx;
}

.load-more:active {
  background: #f8f9fa;
}

.empty-reviews {
  text-align: center;
  padding: 60rpx 20rpx;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.empty-tip {
  display: block;
  font-size: 24rpx;
  color: #ccc;
}

.delete-btn {
  color: #e74c3c;
  font-size: 22rpx;
  padding: 5rpx 10rpx;
  border-radius: 10rpx;
  background: #fdf2f2;
  transition: all 0.3s ease;
}

.delete-btn:active {
  background: #fadbd8;
}

/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1rpx solid #e9ecef;
  padding: 20rpx 30rpx;
  z-index: 100;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.contact-btn {
  flex: 1;
  height: 80rpx;
  background: white;
  border: 2rpx solid #4a7c59;
  color: #4a7c59;
  border-radius: 40rpx;
  font-size: 26rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.contact-btn:active {
  background: #4a7c59;
  color: white;
}

.cart-btn {
  flex: 2;
  height: 80rpx;
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 26rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.cart-btn:active {
  transform: scale(0.98);
}

.buy-btn {
  flex: 2;
  height: 80rpx;
  background: linear-gradient(135deg, #4a7c59, #6b9a7a);
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 26rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.buy-btn:active {
  transform: scale(0.98);
}

/* 规格选择弹窗（照搬分类页面的实现） */
.spec-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.spec-modal-mask.visible {
  opacity: 1;
  visibility: visible;
}

.spec-modal {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  z-index: 1001;
  transform: translateY(100%);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10rpx);
}

.spec-modal.visible {
  transform: translateY(0);
  box-shadow: 0 -12rpx 40rpx rgba(0, 0, 0, 0.2);
}

.spec-modal-header {
  padding: 30rpx;
  text-align: right;
  border-bottom: 1rpx solid rgba(74, 124, 89, 0.1);
  background: white;
  border-radius: 24rpx 24rpx 0 0;
}

.spec-modal-close {
  font-size: 40rpx;
  color: #95a5a6;
  padding: 10rpx;
  border-radius: 50%;
  transition: all 0.3s ease;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
}

.spec-modal-close:active {
  background: rgba(149, 165, 166, 0.1);
  transform: scale(0.9);
}

.spec-modal-content {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
}

.spec-product-info {
  margin-bottom: 40rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.spec-product-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  position: relative;
}

.spec-product-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 40rpx;
  flex-shrink: 0;
}

.spec-product-basic {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  margin-left: 50rpx;
}

.spec-product-extra {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  margin-left: 150rpx;
}

.spec-product-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 10rpx;
  line-height: 1.4;
  word-break: break-all;
  min-width: 200rpx;
}

.spec-product-code {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 0;
  min-width: 200rpx;
}

.spec-product-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #4a7c59;
  margin-bottom: 0;
  min-width: 200rpx;
}

.purchase-unit-section {
  display: flex;
  align-items: center;
  margin-bottom: 0;
}

.purchase-unit-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 15rpx;
}

.purchase-unit-btn {
  background: #e8f4fd;
  border: 1rpx solid #b3d9f2;
  border-radius: 6rpx;
  padding: 6rpx 12rpx;
}

.purchase-unit-text {
  font-size: 22rpx;
  color: #1890ff;
}

.quantity-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 25rpx;
  flex-wrap: wrap;
  gap: 15rpx;
}

.quantity-control {
  display: flex;
  align-items: center;
  border: 2rpx solid rgba(74, 124, 89, 0.2);
  border-radius: 10rpx;
  overflow: hidden;
  background: white;
  box-shadow: 0 2rpx 8rpx rgba(74, 124, 89, 0.1);
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  color: #4a7c59;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.quantity-btn:active {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  transform: scale(0.95);
}

.quantity-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-input {
  width: 120rpx;
  height: 60rpx;
  text-align: center;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  background: white;
  font-size: 26rpx;
  font-weight: 600;
  color: #2c3e50;
  padding: 0 10rpx;
  transition: all 0.3s ease;
  position: relative;
}

.quantity-input:focus {
  border-color: #4a7c59;
  box-shadow: 0 0 0 2rpx rgba(74, 124, 89, 0.1);
  outline: none;
}



.quantity-label {
  font-size: 26rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-right: 20rpx;
}

.stock-info {
  font-size: 22rpx;
  color: #999;
  margin-left: auto;
}

.spec-modal-footer {
  padding: 30rpx;
  border-top: 1rpx solid rgba(74, 124, 89, 0.1);
  background: white;
  border-radius: 0 0 24rpx 24rpx;
}

.total-price-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 25rpx;
  padding: 15rpx 20rpx;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 10rpx;
  border: 1rpx solid rgba(74, 124, 89, 0.1);
}

.total-price-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
}

.total-price-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #4a7c59;
  text-shadow: 0 1rpx 2rpx rgba(74, 124, 89, 0.1);
}

.spec-modal-actions {
  display: flex;
  gap: 20rpx;
}

.spec-modal-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.spec-modal-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.spec-modal-btn:active::before {
  transform: translateX(100%);
}

.spec-modal-btn.secondary {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  color: #6c757d;
  border: 2rpx solid #dee2e6;
}

.spec-modal-btn.secondary:active {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  transform: scale(0.98);
}

.spec-modal-btn.primary {
  background: linear-gradient(135deg, #4a7c59, #6b9a7a);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(74, 124, 89, 0.3);
}

.spec-modal-btn.primary:active {
  background: linear-gradient(135deg, #3d6b4a, #5a8a6a);
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(74, 124, 89, 0.4);
}

.spec-modal-btn.buy-now {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(231, 76, 60, 0.3);
}

.spec-modal-btn.buy-now:active {
  background: linear-gradient(135deg, #c0392b, #a93226);
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(231, 76, 60, 0.4);
}

.btn-text {
  font-size: 28rpx;
  font-weight: 600;
  margin-right: 8rpx;
}

.btn-icon {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 评论弹窗样式 */
.review-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.review-modal-mask.visible {
  opacity: 1;
  visibility: visible;
}

.review-modal {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  z-index: 1001;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  max-height: 80vh;
  overflow: hidden;
}

.review-modal.visible {
  transform: translateY(0);
}

.review-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #e9ecef;
  background: white;
}

.review-modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
}

.review-modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 50%;
  font-size: 24rpx;
  color: #6c757d;
  transition: all 0.3s ease;
}

.review-modal-close:active {
  background: #e9ecef;
  transform: scale(0.9);
}

.review-modal-content {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.rating-section {
  margin-bottom: 30rpx;
}

.rating-label {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20rpx;
}

.rating-stars {
  display: flex;
  gap: 10rpx;
}

.star {
  font-size: 40rpx;
  color: #ddd;
  transition: all 0.3s ease;
}

.star.active {
  color: #ffd700;
}

.star:active {
  transform: scale(1.1);
}

.content-section {
  margin-bottom: 30rpx;
}

.content-label {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20rpx;
}

.content-input {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #2c3e50;
  background: #f8f9fa;
  resize: none;
  box-sizing: border-box;
}

.content-input:focus {
  border-color: #4a7c59;
  background: white;
}

.content-count {
  display: block;
  text-align: right;
  font-size: 22rpx;
  color: #999;
  margin-top: 10rpx;
}

.anonymous-section {
  margin-bottom: 30rpx;
}

.anonymous-option {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  color: #4a7c59;
  font-size: 20rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background: #4a7c59;
  border-color: #4a7c59;
  color: white;
}

.anonymous-text {
  font-size: 26rpx;
  color: #2c3e50;
}

.review-modal-footer {
  padding: 30rpx;
  border-top: 1rpx solid #e9ecef;
  background: white;
}

.review-modal-actions {
  display: flex;
  gap: 20rpx;
}

.review-modal-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.review-modal-btn.secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #e9ecef;
}

.review-modal-btn.secondary:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.review-modal-btn.primary {
  background: linear-gradient(135deg, #4a7c59, #6b9a7a);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(74, 124, 89, 0.3);
}

.review-modal-btn.primary:active {
  background: linear-gradient(135deg, #3d6b4a, #5a8a6a);
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(74, 124, 89, 0.4);
}
