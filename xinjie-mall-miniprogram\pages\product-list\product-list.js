// pages/product-list/product-list.js
const { get } = require("../../utils/request");
const { API } = require("../../config/api");
const { formatPrice } = require("../../utils/format");

Page({
  data: {
    // 分类ID
    categoryId: null,

    // 分类信息
    category: null,

    // 商品列表
    products: [],

    // 分页信息
    pagination: {
      page: 1,
      pageSize: 10,
      hasMore: true,
    },

    // 排序方式
    sortType: "default", // default, price_asc, price_desc, sales

    // 筛选条件
    filters: {
      priceRange: null, // [min, max]
      keyword: "",
    },

    // 加载状态
    loading: {
      products: false,
      more: false,
    },

    // 是否显示筛选弹窗
    showFilterModal: false,

    // 价格区间选项
    priceRanges: [
      { label: "全部价格", value: null },
      { label: "0-50元", value: [0, 50] },
      { label: "50-100元", value: [50, 100] },
      { label: "100-200元", value: [100, 200] },
      { label: "200元以上", value: [200, 9999] },
    ],

    // 排序选项
    sortOptions: [
      { label: "默认排序", value: "default" },
      { label: "价格从低到高", value: "price_asc" },
      { label: "价格从高到低", value: "price_desc" },
      { label: "销量优先", value: "sales" },
    ],

    // 默认图片
    defaultImage: "/images/common/default-product.png",
  },

  // 页面加载时执行
  onLoad: function (options) {
    const categoryId = options.categoryId;
    const keyword = options.keyword;

    if (categoryId) {
      this.setData({ categoryId });
      this.loadCategoryInfo();
    }

    if (keyword) {
      this.setData({
        "filters.keyword": keyword,
      });
      wx.setNavigationBarTitle({
        title: `搜索：${keyword}`,
      });
    }

    this.loadProducts();
  },

  // 页面显示时执行
  onShow: function () {
    // 可以在这里刷新数据
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.refreshData().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onReachBottom: function () {
    this.loadMoreProducts();
  },

  // 刷新数据
  refreshData: function () {
    this.setData({
      "pagination.page": 1,
      "pagination.hasMore": true,
      products: [],
    });

    return this.loadProducts();
  },

  // 加载分类信息
  loadCategoryInfo: function () {
    if (!this.data.categoryId) return;

    get(API.category.detail, { id: this.data.categoryId })
      .then((res) => {
        if (res.success) {
          const category = res.data;
          this.setData({ category });

          wx.setNavigationBarTitle({
            title: category.name,
          });
        }
      })
      .catch((error) => {
        console.error("加载分类信息失败:", error);
      });
  },

  // 加载商品列表
  loadProducts: function () {
    this.setData({
      "loading.products": this.data.pagination.page === 1,
    });

    const params = {
      page: this.data.pagination.page,
      limit: this.data.pagination.pageSize, // 使用 limit 参数名，兼容后端
      sortType: this.data.sortType,
    };

    // 添加分类筛选
    if (this.data.categoryId) {
      params.categoryId = this.data.categoryId;
    }

    // 添加关键词搜索
    if (this.data.filters.keyword) {
      params.keyword = this.data.filters.keyword;
    }

    // 添加价格筛选
    if (this.data.filters.priceRange) {
      params.minPrice = this.data.filters.priceRange[0];
      params.maxPrice = this.data.filters.priceRange[1];
    }

    return get(API.product.list, params)
      .then((res) => {
        if (res.success) {
          const products = (res.data.list || []).map((product) => ({
            ...product,
            priceText: formatPrice(product.price),
          }));

          this.setData({
            products:
              this.data.pagination.page === 1
                ? products
                : [...this.data.products, ...products],
            "pagination.hasMore":
              products.length === this.data.pagination.pageSize,
          });
        }
      })
      .catch((error) => {
        console.error("加载商品列表失败:", error);
      })
      .finally(() => {
        this.setData({
          "loading.products": false,
        });
      });
  },

  // 加载更多商品
  loadMoreProducts: function () {
    if (!this.data.pagination.hasMore || this.data.loading.more) {
      return;
    }

    this.setData({
      "loading.more": true,
      "pagination.page": this.data.pagination.page + 1,
    });

    this.loadProducts().then(() => {
      this.setData({
        "loading.more": false,
      });
    });
  },

  // 排序切换
  onSortChange: function (e) {
    const sortType = e.currentTarget.dataset.sort;

    if (sortType !== this.data.sortType) {
      this.setData({
        sortType,
        "pagination.page": 1,
        "pagination.hasMore": true,
        products: [],
      });

      this.loadProducts();
    }
  },

  // 显示筛选弹窗
  showFilterModal: function () {
    this.setData({
      showFilterModal: true,
    });
  },

  // 隐藏筛选弹窗
  hideFilterModal: function () {
    this.setData({
      showFilterModal: false,
    });
  },

  // 价格筛选
  onPriceFilter: function (e) {
    const priceRange = e.currentTarget.dataset.range;

    this.setData({
      "filters.priceRange": priceRange,
      "pagination.page": 1,
      "pagination.hasMore": true,
      products: [],
    });

    this.loadProducts();
    this.hideFilterModal();
  },

  // 清除筛选
  onClearFilter: function () {
    this.setData({
      "filters.priceRange": null,
      "pagination.page": 1,
      "pagination.hasMore": true,
      products: [],
    });

    this.loadProducts();
    this.hideFilterModal();
  },

  // 商品点击
  onProductTap: function (e) {
    const product = e.currentTarget.dataset.product;
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?id=${product.id}`,
    });
  },

  // 图片加载失败
  onImageError: function (e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      [`products[${index}].image`]: this.data.defaultImage,
    });
  },

  // 返回顶部
  onBackToTop: function () {
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 300,
    });
  },
});
