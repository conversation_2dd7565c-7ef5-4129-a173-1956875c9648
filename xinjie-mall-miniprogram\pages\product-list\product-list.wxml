<!--pages/product-list/product-list.wxml-->
<view class="container">
  <!-- 排序和筛选栏 -->
  <view class="filter-bar">
    <view class="sort-options">
      <view 
        class="sort-item {{sortType === 'default' ? 'active' : ''}}"
        bindtap="onSortChange"
        data-sort="default"
      >
        默认
      </view>
      <view 
        class="sort-item {{sortType === 'sales' ? 'active' : ''}}"
        bindtap="onSortChange"
        data-sort="sales"
      >
        销量
      </view>
      <view 
        class="sort-item {{sortType === 'price_asc' ? 'active' : ''}}"
        bindtap="onSortChange"
        data-sort="price_asc"
      >
        价格↑
      </view>
      <view 
        class="sort-item {{sortType === 'price_desc' ? 'active' : ''}}"
        bindtap="onSortChange"
        data-sort="price_desc"
      >
        价格↓
      </view>
    </view>
    <view class="filter-btn" bindtap="showFilterModal">
      <text>筛选</text>
      <text class="filter-icon">⚙</text>
    </view>
  </view>

  <!-- 商品列表 -->
  <view class="products-section">
    <view class="products-grid" wx:if="{{products.length > 0}}">
      <view 
        class="product-item" 
        wx:for="{{products}}" 
        wx:key="id"
        bindtap="onProductTap"
        data-product="{{item}}"
      >
        <image 
          class="product-image" 
          src="{{item.image || defaultImage}}" 
          mode="aspectFill"
          binderror="onImageError"
          data-index="{{index}}"
        ></image>
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <view class="product-price">
            <text class="price-text">{{item.priceText}}</text>
            <text class="sales-text">销量{{item.sales || 0}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{loading.more}}">
      <text>加载更多...</text>
    </view>

    <!-- 没有更多数据 -->
    <view class="no-more" wx:if="{{!pagination.hasMore && products.length > 0}}">
      <text>已经到底了~</text>
    </view>

    <!-- 加载状态 -->
    <loading wx:if="{{loading.products}}"></loading>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading.products && products.length === 0}}">
      <empty text="暂无商品" actionText="返回首页" bindaction="onBackToHome"></empty>
    </view>
  </view>
</view>

<!-- 筛选弹窗 -->
<view class="filter-modal" wx:if="{{showFilterModal}}">
  <view class="modal-mask" bindtap="hideFilterModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">筛选条件</text>
      <view class="close-btn" bindtap="hideFilterModal">×</view>
    </view>
    
    <view class="modal-body">
      <!-- 价格筛选 -->
      <view class="filter-section">
        <view class="filter-title">价格区间</view>
        <view class="filter-options">
          <view 
            class="filter-option {{filters.priceRange === item.value ? 'active' : ''}}"
            wx:for="{{priceRanges}}" 
            wx:key="label"
            bindtap="onPriceFilter"
            data-range="{{item.value}}"
          >
            {{item.label}}
          </view>
        </view>
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="clear-btn" bindtap="onClearFilter">清除</button>
      <button class="confirm-btn" bindtap="hideFilterModal">确定</button>
    </view>
  </view>
</view>

<!-- 返回顶部按钮 -->
<view class="back-to-top" bindtap="onBackToTop" wx:if="{{products.length > 10}}">
  <text>↑</text>
</view> 