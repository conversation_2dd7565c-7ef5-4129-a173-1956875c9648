/* pages/product-list/product-list.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 筛选栏 */
.filter-bar {
  background-color: #fff;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.sort-options {
  display: flex;
  align-items: center;
  gap: 40rpx;
}

.sort-item {
  font-size: 28rpx;
  color: #666;
  padding: 10rpx 0;
  position: relative;
}

.sort-item.active {
  color: #4caf50;
  font-weight: bold;
}

.sort-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background-color: #4caf50;
  border-radius: 2rpx;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 15rpx 25rpx;
  border: 1px solid #e0e0e0;
  border-radius: 50rpx;
  font-size: 26rpx;
  color: #666;
}

.filter-icon {
  font-size: 24rpx;
}

/* 商品列表 */
.products-section {
  padding: 20rpx;
}

.products-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.product-item {
  width: 48%;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
}

.product-image {
  width: 100%;
  height: 220rpx;
}

.product-info {
  padding: 20rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  margin-bottom: 15rpx;
  min-height: 78rpx;
}

.product-price {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.price-text {
  font-size: 30rpx;
  color: #ff4444;
  font-weight: bold;
}

.sales-text {
  font-size: 24rpx;
  color: #999;
}

/* 加载更多和无更多数据 */
.load-more,
.no-more {
  padding: 40rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

/* 空状态 */
.empty-state {
  padding: 120rpx 0;
}

/* 筛选弹窗 */
.filter-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 70vh;
  overflow-y: auto;
}

.modal-header {
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.close-btn {
  font-size: 60rpx;
  color: #999;
  line-height: 1;
}

.modal-body {
  padding: 30rpx;
}

.filter-section {
  margin-bottom: 40rpx;
}

.filter-section:last-child {
  margin-bottom: 0;
}

.filter-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: bold;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.filter-option {
  padding: 15rpx 30rpx;
  border: 2px solid #e0e0e0;
  border-radius: 50rpx;
  font-size: 26rpx;
  color: #666;
  background-color: #fff;
  text-align: center;
  min-width: 120rpx;
}

.filter-option.active {
  border-color: #4caf50;
  color: #4caf50;
  background-color: #f0fff0;
}

.modal-footer {
  padding: 30rpx;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 20rpx;
}

.clear-btn {
  flex: 1;
  background-color: #fff;
  color: #666;
  border: 2px solid #e0e0e0;
  border-radius: 50rpx;
  font-size: 28rpx;
  line-height: 80rpx;
  height: 80rpx;
}

.confirm-btn {
  flex: 2;
  background-color: #4caf50;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  font-size: 28rpx;
  line-height: 80rpx;
  height: 80rpx;
}

/* 返回顶部按钮 */
.back-to-top {
  position: fixed;
  bottom: 150rpx;
  right: 30rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(76, 175, 80, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  z-index: 1000;
}

.back-to-top text {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

/* 动画效果 */
.sort-item {
  transition: color 0.3s ease;
}

.sort-item:active {
  opacity: 0.7;
}

.filter-btn {
  transition: all 0.3s ease;
}

.filter-btn:active {
  background-color: #f0f0f0;
}

.product-item {
  transition: transform 0.3s ease;
}

.product-item:active {
  transform: scale(0.95);
}

.filter-option {
  transition: all 0.3s ease;
}

.filter-option:active {
  opacity: 0.7;
}

.back-to-top {
  transition: opacity 0.3s ease;
}

.back-to-top:active {
  opacity: 0.7;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .product-item {
    width: 100%;
  }

  .sort-options {
    gap: 30rpx;
  }

  .filter-bar {
    padding: 15rpx 20rpx;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 0;
  background: transparent;
}
