// pages/recharge/recharge.js
const { request } = require("../../utils/request");
const { API } = require("../../config/api");

Page({
  data: {
    currentBalance: '0.00',
    selectedAmount: '',
    customAmount: '',
    paymentMethod: 'wechat',
    loading: false,
    
    // 预设充值金额
    presetAmounts: [
      { value: 50, label: '50元' },
      { value: 100, label: '100元' },
      { value: 200, label: '200元' },
      { value: 500, label: '500元' },
      { value: 1000, label: '1000元' },
      { value: 'custom', label: '自定义' }
    ],
    
    // 支付方式
    paymentMethods: [
      { value: 'wechat', label: '微信支付', icon: '💚', enabled: true },
      { value: 'alipay', label: '支付宝', icon: '💙', enabled: false }
    ]
  },

  onLoad: function (options) {
    this.loadCurrentBalance();
  },

  // 加载当前余额
  loadCurrentBalance: async function () {
    try {
      const response = await request({
        url: API.balance.info,
        method: 'GET'
      });

      if (response.success) {
        this.setData({
          currentBalance: response.data.balance
        });
      }
    } catch (error) {
      console.error('加载余额失败:', error);
    }
  },

  // 选择充值金额
  onAmountSelect: function (e) {
    const amount = e.currentTarget.dataset.amount;
    
    if (amount === 'custom') {
      this.setData({
        selectedAmount: 'custom',
        customAmount: ''
      });
    } else {
      this.setData({
        selectedAmount: amount,
        customAmount: ''
      });
    }
  },

  // 自定义金额输入
  onCustomAmountInput: function (e) {
    const value = e.detail.value;
    this.setData({
      customAmount: value,
      selectedAmount: value ? 'custom' : ''
    });
  },

  // 选择支付方式
  onPaymentMethodSelect: function (e) {
    const method = e.currentTarget.dataset.method;
    this.setData({
      paymentMethod: method
    });
  },

  // 获取充值金额
  getRechargeAmount: function () {
    if (this.data.selectedAmount === 'custom') {
      return parseFloat(this.data.customAmount) || 0;
    } else {
      return parseFloat(this.data.selectedAmount) || 0;
    }
  },

  // 验证充值金额
  validateAmount: function (amount) {
    if (!amount || amount <= 0) {
      wx.showToast({
        title: '请输入有效的充值金额',
        icon: 'none'
      });
      return false;
    }

    if (amount < 1) {
      wx.showToast({
        title: '充值金额不能少于1元',
        icon: 'none'
      });
      return false;
    }

    if (amount > 10000) {
      wx.showToast({
        title: '单次充值不能超过10000元',
        icon: 'none'
      });
      return false;
    }

    return true;
  },

  // 确认充值
  onConfirmRecharge: async function () {
    if (this.data.loading) return;

    const amount = this.getRechargeAmount();
    
    if (!this.validateAmount(amount)) {
      return;
    }

    // 显示确认对话框
    wx.showModal({
      title: '确认充值',
      content: `确定要充值 ${amount} 元吗？`,
      success: (res) => {
        if (res.confirm) {
          this.processRecharge(amount);
        }
      }
    });
  },

  // 处理充值
  processRecharge: async function (amount) {
    this.setData({ loading: true });

    try {
      const response = await request({
        url: API.balance.recharge,
        method: 'POST',
        data: {
          amount: amount,
          paymentMethod: this.data.paymentMethod,
          remark: `用户充值${amount}元`
        }
      });

      if (response.success) {
        wx.showToast({
          title: '充值成功',
          icon: 'success'
        });

        // 延迟跳转回用户中心
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        throw new Error(response.message || '充值失败');
      }

    } catch (error) {
      console.error('充值失败:', error);
      wx.showToast({
        title: error.message || '充值失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 查看充值记录
  onViewHistory: function () {
    wx.navigateTo({
      url: '/pages/recharge-history/recharge-history'
    });
  }
});
