<!--pages/recharge/recharge.wxml-->
<view class="container">
  <!-- 当前余额 -->
  <view class="balance-section">
    <view class="balance-card">
      <text class="balance-label">当前余额</text>
      <text class="balance-amount">¥{{currentBalance}}</text>
    </view>
  </view>

  <!-- 充值金额选择 -->
  <view class="amount-section">
    <view class="section-title">选择充值金额</view>
    <view class="amount-grid">
      <view 
        class="amount-item {{selectedAmount == item.value ? 'selected' : ''}}"
        wx:for="{{presetAmounts}}" 
        wx:key="value"
        bindtap="onAmountSelect"
        data-amount="{{item.value}}"
      >
        <text class="amount-text">{{item.label}}</text>
      </view>
    </view>

    <!-- 自定义金额输入 -->
    <view class="custom-amount" wx:if="{{selectedAmount === 'custom'}}">
      <view class="input-wrapper">
        <text class="currency-symbol">¥</text>
        <input 
          class="amount-input"
          type="digit"
          placeholder="请输入充值金额"
          value="{{customAmount}}"
          bindinput="onCustomAmountInput"
          maxlength="8"
        />
      </view>
      <view class="amount-tips">
        <text class="tip-text">充值金额范围：1-10000元</text>
      </view>
    </view>
  </view>

  <!-- 支付方式选择 -->
  <view class="payment-section">
    <view class="section-title">选择支付方式</view>
    <view class="payment-methods">
      <view 
        class="payment-item {{paymentMethod === item.value ? 'selected' : ''}} {{!item.enabled ? 'disabled' : ''}}"
        wx:for="{{paymentMethods}}" 
        wx:key="value"
        bindtap="onPaymentMethodSelect"
        data-method="{{item.value}}"
      >
        <view class="payment-left">
          <text class="payment-icon">{{item.icon}}</text>
          <text class="payment-name">{{item.label}}</text>
        </view>
        <view class="payment-right">
          <text class="payment-status" wx:if="{{!item.enabled}}">暂不支持</text>
          <view class="radio-icon {{paymentMethod === item.value ? 'checked' : ''}}" wx:else></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 充值说明 -->
  <view class="notice-section">
    <view class="section-title">充值说明</view>
    <view class="notice-content">
      <text class="notice-item">• 充值金额将实时到账，可用于购买商品</text>
      <text class="notice-item">• 余额支付享受会员折扣优惠</text>
      <text class="notice-item">• 充值记录可在"余额记录"中查看</text>
      <text class="notice-item">• 如有问题请联系客服</text>
    </view>
  </view>

  <!-- 底部操作区 -->
  <view class="bottom-section">
    <view class="amount-summary" wx:if="{{selectedAmount}}">
      <text class="summary-text">
        充值金额：¥{{selectedAmount === 'custom' ? customAmount : selectedAmount}}
      </text>
    </view>
    
    <view class="action-buttons">
      <button class="history-btn" bindtap="onViewHistory">充值记录</button>
      <button 
        class="confirm-btn {{!selectedAmount || loading ? 'disabled' : ''}}"
        bindtap="onConfirmRecharge"
        disabled="{{!selectedAmount || loading}}"
        loading="{{loading}}"
      >
        {{loading ? '处理中...' : '确认充值'}}
      </button>
    </view>
  </view>
</view>
