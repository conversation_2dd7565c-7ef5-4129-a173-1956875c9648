/* pages/recharge/recharge.wxss */
.container {
  background: linear-gradient(180deg, #f0fdf4 0%, #ecfdf5 50%, #f7fee7 100%);
  min-height: 100vh;
  padding-bottom: 200rpx;
  position: relative;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 400rpx;
  background: linear-gradient(135deg, #86efac 0%, #6ee7b7 50%, #34d399 100%);
  opacity: 0.08;
  z-index: 0;
}

/* 余额显示区域 */
.balance-section {
  padding: 30rpx;
  position: relative;
  z-index: 1;
}

.balance-card {
  background: linear-gradient(135deg, #86efac, #6ee7b7, #34d399);
  border-radius: 24rpx;
  padding: 50rpx 40rpx;
  text-align: center;
  color: white;
  box-shadow: 0 12rpx 40rpx rgba(52, 211, 153, 0.25);
  position: relative;
  overflow: hidden;
}

.balance-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: shimmer 4s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(180deg); }
}

.balance-label {
  display: block;
  font-size: 30rpx;
  opacity: 0.95;
  margin-bottom: 15rpx;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

.balance-amount {
  display: block;
  font-size: 64rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

/* 金额选择区域 */
.amount-section {
  padding: 0 30rpx 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.amount-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.amount-item {
  background: white;
  border: 2rpx solid rgba(134, 239, 172, 0.2);
  border-radius: 20rpx;
  padding: 35rpx 20rpx;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

.amount-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(134, 239, 172, 0.1), transparent);
  transition: left 0.5s ease;
}

.amount-item.selected {
  border-color: #86efac;
  background: linear-gradient(135deg, rgba(134, 239, 172, 0.1), rgba(110, 231, 183, 0.05));
  transform: scale(1.05);
  box-shadow: 0 8rpx 30rpx rgba(52, 211, 153, 0.2);
}

.amount-item.selected::before {
  left: 100%;
}

.amount-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  transition: color 0.3s ease;
}

.amount-item.selected .amount-text {
  color: #059669;
  font-weight: bold;
  text-shadow: 0 1rpx 2rpx rgba(5, 150, 105, 0.2);
}

/* 自定义金额输入 */
.custom-amount {
  margin-top: 20rpx;
}

.input-wrapper {
  display: flex;
  align-items: center;
  background: white;
  border: 2rpx solid #4CAF50;
  border-radius: 16rpx;
  padding: 0 20rpx;
  margin-bottom: 15rpx;
}

.currency-symbol {
  font-size: 36rpx;
  color: #4CAF50;
  font-weight: bold;
  margin-right: 10rpx;
}

.amount-input {
  flex: 1;
  padding: 25rpx 0;
  font-size: 32rpx;
  color: #333;
}

.amount-tips {
  text-align: center;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
}

/* 支付方式选择 */
.payment-section {
  padding: 0 30rpx 30rpx;
}

.payment-methods {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.payment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.payment-item:last-child {
  border-bottom: none;
}

.payment-item.selected {
  background: #f8fff8;
}

.payment-item.disabled {
  opacity: 0.5;
}

.payment-left {
  display: flex;
  align-items: center;
}

.payment-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
}

.payment-name {
  font-size: 32rpx;
  color: #333;
}

.payment-right {
  display: flex;
  align-items: center;
}

.payment-status {
  font-size: 24rpx;
  color: #999;
}

.radio-icon {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  position: relative;
}

.radio-icon.checked {
  border-color: #4CAF50;
}

.radio-icon.checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20rpx;
  height: 20rpx;
  background: #4CAF50;
  border-radius: 50%;
}

/* 充值说明 */
.notice-section {
  padding: 0 30rpx 30rpx;
}

.notice-content {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.notice-item {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 15rpx;
}

.notice-item:last-child {
  margin-bottom: 0;
}

/* 底部操作区 */
.bottom-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.amount-summary {
  text-align: center;
  margin-bottom: 20rpx;
}

.summary-text {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.history-btn {
  flex: 1;
  background: #f0f0f0;
  color: #666;
  border: none;
  border-radius: 50rpx;
  padding: 25rpx;
  font-size: 30rpx;
}

.confirm-btn {
  flex: 2;
  background: linear-gradient(135deg, #86efac, #6ee7b7);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 28rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 6rpx 20rpx rgba(52, 211, 153, 0.3);
  transition: all 0.3s ease;
}

.confirm-btn.disabled {
  background: linear-gradient(135deg, #e5e7eb, #d1d5db);
  color: #9ca3af;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 动画效果 */
.amount-item:active {
  transform: scale(0.95);
}

.payment-item:active {
  background-color: rgba(134, 239, 172, 0.05);
}

.confirm-btn:active:not(.disabled) {
  transform: scale(0.98);
  box-shadow: 0 4rpx 15rpx rgba(52, 211, 153, 0.4);
}

.history-btn:active {
  background: #e5e7eb;
  transform: scale(0.98);
}

/* 页面进入动画 */
.container {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片悬浮效果 */
.amount-item:hover {
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
}

.payment-item:hover {
  background-color: rgba(134, 239, 172, 0.02);
}
