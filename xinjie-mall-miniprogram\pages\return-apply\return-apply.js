// pages/return-apply/return-apply.js
const { request } = require('../../utils/request');

Page({
  data: {
    orderId: '',
    orderInfo: null,
    returnType: 2, // 1:仅退款 2:退货退款 3:换货
    returnReasons: [
      '质量问题',
      '商品描述不符',
      '收到商品破损',
      '商品缺件',
      '不喜欢/不合适',
      '其他原因'
    ],
    selectedReason: '',
    customReason: '',
    returnDescription: '',
    contactPhone: '',
    returnImages: [],
    selectedItems: [],
    loading: false,
    showReasonPicker: false,
    returnSettings: null
  },

  onLoad(options) {
    console.log('退货申请页面参数:', options);
    if (options.orderId) {
      this.setData({ orderId: options.orderId });
      this.checkOrderCanReturn();
      this.getReturnSettings();
    } else {
      wx.showToast({
        title: '订单信息错误',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 检查订单是否可以退货
  async checkOrderCanReturn() {
    try {
      wx.showLoading({ title: '加载中...' });
      
      const response = await request({
        url: `/front/return/check/${this.data.orderId}`,
        method: 'GET'
      });

      if (response.code === 200 && response.data.canReturn) {
        const orderInfo = response.data.order;
        // 初始化选中的商品（默认全选）
        const selectedItems = orderInfo.orderItems.map(item => ({
          orderItemId: item.id,
          productId: item.product_id,
          productName: item.product_name,
          productImage: item.product_image,
          productPrice: item.price,
          quantity: item.quantity,
          returnQuantity: item.quantity,
          reason: ''
        }));

        this.setData({
          orderInfo,
          selectedItems,
          contactPhone: orderInfo.user?.phone || ''
        });
      } else {
        wx.showToast({
          title: response.message || '该订单不能退货',
          icon: 'error'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('检查订单退货状态失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 获取退货配置
  async getReturnSettings() {
    try {
      const response = await request({
        url: '/front/return/settings',
        method: 'GET'
      });

      if (response.code === 200) {
        this.setData({
          returnSettings: response.data,
          returnReasons: response.data.returnReasons || this.data.returnReasons
        });
      }
    } catch (error) {
      console.error('获取退货配置失败:', error);
    }
  },

  // 选择退货原因
  onReasonTap() {
    this.setData({ showReasonPicker: true });
  },

  onReasonChange(e) {
    const index = e.detail.value;
    const selectedReason = this.data.returnReasons[index];
    this.setData({
      selectedReason,
      showReasonPicker: false,
      customReason: selectedReason === '其他原因' ? this.data.customReason : ''
    });
  },

  onReasonCancel() {
    this.setData({ showReasonPicker: false });
  },

  // 自定义原因输入
  onCustomReasonInput(e) {
    this.setData({ customReason: e.detail.value });
  },

  // 退货描述输入
  onDescriptionInput(e) {
    this.setData({ returnDescription: e.detail.value });
  },

  // 联系电话输入
  onPhoneInput(e) {
    this.setData({ contactPhone: e.detail.value });
  },

  // 退货数量变更
  onQuantityChange(e) {
    const { index, quantity } = e.currentTarget.dataset;
    const selectedItems = [...this.data.selectedItems];
    selectedItems[index].returnQuantity = Math.max(1, Math.min(quantity, selectedItems[index].quantity));
    this.setData({ selectedItems });
  },

  // 增加退货数量
  onQuantityIncrease(e) {
    const index = e.currentTarget.dataset.index;
    const selectedItems = [...this.data.selectedItems];
    const item = selectedItems[index];
    if (item.returnQuantity < item.quantity) {
      item.returnQuantity++;
      this.setData({ selectedItems });
    }
  },

  // 减少退货数量
  onQuantityDecrease(e) {
    const index = e.currentTarget.dataset.index;
    const selectedItems = [...this.data.selectedItems];
    const item = selectedItems[index];
    if (item.returnQuantity > 1) {
      item.returnQuantity--;
      this.setData({ selectedItems });
    }
  },

  // 选择退货凭证图片
  onChooseImage() {
    const maxImages = 6;
    const currentCount = this.data.returnImages.length;
    
    if (currentCount >= maxImages) {
      wx.showToast({
        title: `最多上传${maxImages}张图片`,
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: maxImages - currentCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const returnImages = [...this.data.returnImages, ...res.tempFilePaths];
        this.setData({ returnImages });
      }
    });
  },

  // 删除图片
  onDeleteImage(e) {
    const index = e.currentTarget.dataset.index;
    const returnImages = [...this.data.returnImages];
    returnImages.splice(index, 1);
    this.setData({ returnImages });
  },

  // 预览图片
  onPreviewImage(e) {
    const index = e.currentTarget.dataset.index;
    wx.previewImage({
      current: this.data.returnImages[index],
      urls: this.data.returnImages
    });
  },

  // 提交退货申请
  async onSubmit() {
    // 表单验证
    if (!this.validateForm()) {
      return;
    }

    this.setData({ loading: true });

    try {
      wx.showLoading({ title: '提交中...' });

      // 准备提交数据
      const submitData = {
        orderId: this.data.orderId,
        returnType: this.data.returnType,
        returnReason: this.data.selectedReason === '其他原因' ? this.data.customReason : this.data.selectedReason,
        returnDescription: this.data.returnDescription,
        contactPhone: this.data.contactPhone,
        returnImages: this.data.returnImages,
        returnItems: this.data.selectedItems.map(item => ({
          orderItemId: item.orderItemId,
          quantity: item.returnQuantity,
          reason: item.reason || this.data.selectedReason
        }))
      };

      const response = await request({
        url: '/front/return/submit',
        method: 'POST',
        data: submitData
      });

      if (response.code === 200) {
        wx.showToast({
          title: '申请提交成功',
          icon: 'success'
        });

        setTimeout(() => {
          wx.navigateTo({
            url: '/pages/return-list/return-list'
          });
        }, 1500);
      } else {
        wx.showToast({
          title: response.message || '提交失败',
          icon: 'error'
        });
      }
    } catch (error) {
      console.error('提交退货申请失败:', error);
      wx.showToast({
        title: '提交失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
      wx.hideLoading();
    }
  },

  // 表单验证
  validateForm() {
    if (!this.data.selectedReason) {
      wx.showToast({
        title: '请选择退货原因',
        icon: 'none'
      });
      return false;
    }

    if (this.data.selectedReason === '其他原因' && !this.data.customReason.trim()) {
      wx.showToast({
        title: '请填写具体原因',
        icon: 'none'
      });
      return false;
    }

    if (!this.data.contactPhone.trim()) {
      wx.showToast({
        title: '请填写联系电话',
        icon: 'none'
      });
      return false;
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(this.data.contactPhone)) {
      wx.showToast({
        title: '请填写正确的手机号',
        icon: 'none'
      });
      return false;
    }

    if (this.data.selectedItems.length === 0) {
      wx.showToast({
        title: '请选择退货商品',
        icon: 'none'
      });
      return false;
    }

    return true;
  }
});
