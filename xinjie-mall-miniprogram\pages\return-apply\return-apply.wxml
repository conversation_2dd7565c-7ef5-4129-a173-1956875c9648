<!--pages/return-apply/return-apply.wxml-->
<view class="container">
  <!-- 订单信息 -->
  <view class="order-info" wx:if="{{orderInfo}}">
    <view class="section-title">订单信息</view>
    <view class="order-card">
      <view class="order-header">
        <text class="order-no">订单号：{{orderInfo.order_no}}</text>
        <text class="order-amount">¥{{orderInfo.pay_amount}}</text>
      </view>
      <view class="order-time">下单时间：{{orderInfo.created_at}}</view>
    </view>
  </view>

  <!-- 退货商品 -->
  <view class="return-items" wx:if="{{selectedItems.length > 0}}">
    <view class="section-title">退货商品</view>
    <view class="item-list">
      <view class="item-card" wx:for="{{selectedItems}}" wx:key="orderItemId">
        <view class="item-info">
          <image class="item-image" src="{{item.productImage}}" mode="aspectFill"></image>
          <view class="item-details">
            <view class="item-name">{{item.productName}}</view>
            <view class="item-price">¥{{item.productPrice}}</view>
            <view class="item-original">原购买数量：{{item.quantity}}</view>
          </view>
        </view>
        <view class="quantity-control">
          <text class="quantity-label">退货数量：</text>
          <view class="quantity-input">
            <button class="quantity-btn" data-index="{{index}}" bindtap="onQuantityDecrease">-</button>
            <input class="quantity-value" type="number" value="{{item.returnQuantity}}" data-index="{{index}}" bindinput="onQuantityChange" />
            <button class="quantity-btn" data-index="{{index}}" bindtap="onQuantityIncrease">+</button>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 退货原因 -->
  <view class="return-reason">
    <view class="section-title">退货原因</view>
    <view class="form-item">
      <view class="form-label">选择原因</view>
      <view class="reason-selector" bindtap="onReasonTap">
        <text class="reason-text {{selectedReason ? '' : 'placeholder'}}">
          {{selectedReason || '请选择退货原因'}}
        </text>
        <text class="arrow">></text>
      </view>
    </view>

    <!-- 自定义原因输入 -->
    <view class="form-item" wx:if="{{selectedReason === '其他原因'}}">
      <view class="form-label">具体原因</view>
      <textarea 
        class="custom-reason-input" 
        placeholder="请详细说明退货原因" 
        value="{{customReason}}"
        bindinput="onCustomReasonInput"
        maxlength="200"
      ></textarea>
    </view>

    <!-- 详细说明 -->
    <view class="form-item">
      <view class="form-label">详细说明（可选）</view>
      <textarea 
        class="description-input" 
        placeholder="请详细描述商品问题或退货原因" 
        value="{{returnDescription}}"
        bindinput="onDescriptionInput"
        maxlength="500"
      ></textarea>
    </view>
  </view>

  <!-- 联系方式 -->
  <view class="contact-info">
    <view class="section-title">联系方式</view>
    <view class="form-item">
      <view class="form-label">联系电话</view>
      <input 
        class="phone-input" 
        type="number" 
        placeholder="请输入联系电话" 
        value="{{contactPhone}}"
        bindinput="onPhoneInput"
        maxlength="11"
      />
    </view>
  </view>

  <!-- 退货凭证 -->
  <view class="return-evidence">
    <view class="section-title">退货凭证</view>
    <view class="form-item">
      <view class="form-label">上传图片（可选，最多6张）</view>
      <view class="image-upload">
        <view class="image-list">
          <view class="image-item" wx:for="{{returnImages}}" wx:key="*this">
            <image class="uploaded-image" src="{{item}}" mode="aspectFill" data-index="{{index}}" bindtap="onPreviewImage"></image>
            <view class="delete-btn" data-index="{{index}}" bindtap="onDeleteImage">×</view>
          </view>
          <view class="add-image" wx:if="{{returnImages.length < 6}}" bindtap="onChooseImage">
            <text class="add-icon">+</text>
            <text class="add-text">添加图片</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 退货地址提示 -->
  <view class="return-address" wx:if="{{returnSettings}}">
    <view class="section-title">退货地址</view>
    <view class="address-card">
      <view class="address-info">
        <view class="address-name">{{returnSettings.returnAddress.name}}</view>
        <view class="address-phone">{{returnSettings.returnAddress.phone}}</view>
        <view class="address-detail">{{returnSettings.returnAddress.address}}</view>
        <view class="address-zipcode">邮编：{{returnSettings.returnAddress.zipcode}}</view>
      </view>
      <view class="address-note">
        <text class="note-title">温馨提示：</text>
        <text class="note-content">请将商品完好包装后寄回上述地址，运费需您承担</text>
      </view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button 
      class="submit-btn {{loading ? 'loading' : ''}}" 
      bindtap="onSubmit" 
      disabled="{{loading}}"
    >
      {{loading ? '提交中...' : '提交退货申请'}}
    </button>
  </view>
</view>

<!-- 退货原因选择器 -->
<picker 
  wx:if="{{showReasonPicker}}"
  range="{{returnReasons}}" 
  bindchange="onReasonChange" 
  bindcancel="onReasonCancel"
>
  <view></view>
</picker>
