/* pages/return-apply/return-apply.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 通用样式 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
  border-left: 6rpx solid #10b981;
}

/* 订单信息 */
.order-info {
  margin-bottom: 30rpx;
}

.order-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.order-no {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.order-amount {
  font-size: 32rpx;
  color: #10b981;
  font-weight: bold;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

/* 退货商品 */
.return-items {
  margin-bottom: 30rpx;
}

.item-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.item-card {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.item-card:last-child {
  border-bottom: none;
}

.item-info {
  display: flex;
  margin-bottom: 20rpx;
}

.item-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.item-details {
  flex: 1;
}

.item-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  line-height: 1.4;
}

.item-price {
  font-size: 32rpx;
  color: #10b981;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.item-original {
  font-size: 24rpx;
  color: #999;
}

.quantity-control {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.quantity-label {
  font-size: 28rpx;
  color: #333;
}

.quantity-input {
  display: flex;
  align-items: center;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  overflow: hidden;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  background: #f8f8f8;
  border: none;
  font-size: 32rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0;
  border-radius: 0;
}

.quantity-btn:active {
  background: #e8e8e8;
}

.quantity-value {
  width: 80rpx;
  height: 60rpx;
  text-align: center;
  font-size: 28rpx;
  border: none;
  background: white;
}

/* 表单样式 */
.return-reason,
.contact-info,
.return-evidence,
.return-address {
  margin-bottom: 30rpx;
}

.form-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.reason-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e0e0e0;
}

.reason-text {
  font-size: 28rpx;
  color: #333;
}

.reason-text.placeholder {
  color: #999;
}

.arrow {
  font-size: 24rpx;
  color: #999;
}

.custom-reason-input,
.description-input {
  width: 100%;
  min-height: 120rpx;
  font-size: 28rpx;
  color: #333;
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx;
  border: none;
}

.phone-input {
  width: 100%;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 0 20rpx;
  border: none;
}

/* 图片上传 */
.image-upload {
  margin-top: 20rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.delete-btn {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
}

.add-image {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.add-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.add-text {
  font-size: 24rpx;
}

/* 退货地址 */
.address-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.address-info {
  margin-bottom: 20rpx;
}

.address-name {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.address-phone {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.address-detail {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 10rpx;
}

.address-zipcode {
  font-size: 24rpx;
  color: #999;
}

.address-note {
  padding: 20rpx;
  background: #f0fdf4;
  border-radius: 12rpx;
  border-left: 6rpx solid #10b981;
}

.note-title {
  font-size: 26rpx;
  color: #10b981;
  font-weight: bold;
}

.note-content {
  font-size: 24rpx;
  color: #166534;
  line-height: 1.5;
}

/* 提交按钮 */
.submit-section {
  margin-top: 40rpx;
  padding-bottom: 40rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(16, 185, 129, 0.3);
  transition: all 0.3s ease;
}

.submit-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.3);
}

.submit-btn.loading {
  background: #ccc;
  box-shadow: none;
}

.submit-btn[disabled] {
  background: #ccc;
  box-shadow: none;
}
