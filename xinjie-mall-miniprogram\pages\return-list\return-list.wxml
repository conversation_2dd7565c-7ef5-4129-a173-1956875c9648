<!--pages/return-list/return-list.wxml-->
<view class="container">
  <!-- 状态筛选 -->
  <view class="status-filter">
    <scroll-view class="filter-scroll" scroll-x="true">
      <view class="filter-list">
        <view 
          class="filter-item {{statusFilter === item.value ? 'active' : ''}}"
          wx:for="{{statusOptions}}" 
          wx:key="value"
          data-status="{{item.value}}"
          bindtap="onStatusFilter"
        >
          {{item.text}}
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 退货列表 -->
  <view class="return-list" wx:if="{{returnList.length > 0}}">
    <view class="return-item" wx:for="{{returnList}}" wx:key="id">
      <view class="item-header">
        <view class="return-info">
          <text class="return-no">{{item.return_no}}</text>
          <text class="return-time">{{formatTime(item.created_at)}}</text>
        </view>
        <view class="status-tag" style="color: {{getStatusInfo(item.status).color}}">
          {{getStatusInfo(item.status).text}}
        </view>
      </view>

      <view class="item-content" data-id="{{item.id}}" bindtap="onViewDetail">
        <view class="order-info">
          <text class="order-label">关联订单：</text>
          <text class="order-no">{{item.order_no}}</text>
        </view>
        
        <view class="return-details">
          <view class="return-reason">
            <text class="reason-label">退货原因：</text>
            <text class="reason-text">{{item.return_reason}}</text>
          </view>
          <view class="return-amount">
            <text class="amount-label">退货金额：</text>
            <text class="amount-value">¥{{item.return_amount}}</text>
          </view>
        </view>

        <!-- 退货商品预览 -->
        <view class="return-items-preview" wx:if="{{item.returnItems && item.returnItems.length > 0}}">
          <view class="items-label">退货商品：</view>
          <view class="items-list">
            <image 
              class="item-image" 
              src="{{subItem.product_image}}" 
              mode="aspectFill"
              wx:for="{{item.returnItems}}" 
              wx:for-item="subItem"
              wx:key="id"
            ></image>
            <view class="more-items" wx:if="{{item.returnItems.length > 3}}">
              +{{item.returnItems.length - 3}}
            </view>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="item-actions" wx:if="{{canFillExpress(item.status) || canCancel(item.status)}}">
        <button 
          class="action-btn secondary" 
          wx:if="{{canCancel(item.status)}}"
          data-id="{{item.id}}" 
          bindtap="onCancelReturn"
        >
          取消申请
        </button>
        <button 
          class="action-btn primary" 
          wx:if="{{canFillExpress(item.status)}}"
          data-id="{{item.id}}" 
          bindtap="onFillExpress"
        >
          填写物流
        </button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && returnList.length === 0}}">
    <image class="empty-image" src="/images/empty-return.png" mode="aspectFit"></image>
    <text class="empty-text">暂无退货申请</text>
    <text class="empty-desc">您还没有提交过退货申请</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-more" wx:if="{{loading && returnList.length > 0}}">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 没有更多数据 -->
  <view class="no-more" wx:if="{{!hasMore && returnList.length > 0}}">
    <text class="no-more-text">没有更多数据了</text>
  </view>
</view>
