/* pages/return-list/return-list.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 状态筛选 */
.status-filter {
  background: white;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-list {
  display: flex;
  padding: 0 20rpx;
}

.filter-item {
  flex-shrink: 0;
  padding: 12rpx 24rpx;
  margin-right: 20rpx;
  font-size: 26rpx;
  color: #666;
  background: #f8f8f8;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.filter-item.active {
  color: white;
  background: #10b981;
}

/* 退货列表 */
.return-list {
  padding: 20rpx;
}

.return-item {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.return-info {
  flex: 1;
}

.return-no {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.return-time {
  font-size: 24rpx;
  color: #999;
}

.status-tag {
  font-size: 26rpx;
  font-weight: 500;
  padding: 8rpx 16rpx;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 12rpx;
}

.item-content {
  padding: 20rpx 30rpx;
}

.order-info {
  margin-bottom: 15rpx;
}

.order-label {
  font-size: 26rpx;
  color: #666;
}

.order-no {
  font-size: 26rpx;
  color: #333;
  font-family: monospace;
}

.return-details {
  margin-bottom: 20rpx;
}

.return-reason {
  display: flex;
  margin-bottom: 10rpx;
}

.reason-label {
  font-size: 26rpx;
  color: #666;
  width: 140rpx;
  flex-shrink: 0;
}

.reason-text {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  line-height: 1.4;
}

.return-amount {
  display: flex;
  align-items: center;
}

.amount-label {
  font-size: 26rpx;
  color: #666;
  width: 140rpx;
  flex-shrink: 0;
}

.amount-value {
  font-size: 28rpx;
  color: #10b981;
  font-weight: bold;
}

/* 退货商品预览 */
.return-items-preview {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.items-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.items-list {
  display: flex;
  align-items: center;
}

.item-image {
  width: 60rpx;
  height: 60rpx;
  border-radius: 8rpx;
  margin-right: 10rpx;
}

.more-items {
  width: 60rpx;
  height: 60rpx;
  background: #f0f0f0;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #999;
}

/* 操作按钮 */
.item-actions {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
  padding: 20rpx 30rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-btn {
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  border-radius: 20rpx;
  border: none;
  margin: 0;
}

.action-btn.primary {
  background: #10b981;
  color: white;
}

.action-btn.secondary {
  background: #f8f8f8;
  color: #666;
  border: 1rpx solid #e0e0e0;
}

.action-btn:active {
  opacity: 0.8;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
}

/* 加载状态 */
.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}

.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.no-more-text {
  font-size: 26rpx;
  color: #ccc;
}

/* 加载动画 */
.loading-more::before {
  content: '';
  width: 30rpx;
  height: 30rpx;
  border: 3rpx solid #f0f0f0;
  border-top: 3rpx solid #10b981;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 15rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
