// pages/search/search.js
const { get } = require("../../utils/request");
const { API } = require("../../config/api");
const { formatPrice } = require("../../utils/format");
const { getProductImageUrl } = require("../../utils/image");

Page({
  data: {
    // 搜索关键词
    keyword: "",

    // 搜索历史
    searchHistory: [],

    // 热门搜索
    hotKeywords: [
      "绿茶",
      "红茶",
      "乌龙茶",
      "白茶",
      "黄茶",
      "黑茶",
      "花茶",
      "茶具",
    ],

    // 推荐商品
    recommendProducts: [],

    // 搜索结果
    searchResults: [],

    // 是否显示搜索结果
    showSearchResults: false,

    // 加载状态
    loading: {
      search: false,
      recommend: false,
    },

    // 默认图片
    defaultImage: "/images/common/default-product.png",
  },

  onLoad: function (options) {
    this.loadSearchHistory();
    this.loadRecommendProducts();
  },

  // 加载搜索历史
  loadSearchHistory: function () {
    const history = wx.getStorageSync("searchHistory") || [];
    this.setData({
      searchHistory: history,
    });
  },

  // 加载推荐商品
  loadRecommendProducts: function () {
    this.setData({
      "loading.recommend": true,
    });

    get(API.product.recommend, { limit: 6 })
      .then((res) => {
        if (res.code === 200 || res.success) {
          const products = (res.data || []).map((product) => ({
            ...product,
            image_url: getProductImageUrl(product.main_image || product.image_url || product.image),
            priceText: formatPrice(product.price),
          }));

          this.setData({
            recommendProducts: products,
          });
        }
      })
      .catch((error) => {
        console.error("加载推荐商品失败:", error);
      })
      .finally(() => {
        this.setData({
          "loading.recommend": false,
        });
      });
  },

  // 搜索输入
  onSearchInput: function (e) {
    const keyword = e.detail.value.trim();
    this.setData({
      keyword,
      showSearchResults: false,
    });

    // 如果关键词为空，隐藏搜索结果
    if (!keyword) {
      this.setData({
        searchResults: [],
        showSearchResults: false,
      });
      return;
    }

    // 延迟搜索，避免频繁请求
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }

    this.searchTimer = setTimeout(() => {
      this.performSearch(keyword);
    }, 500);
  },

  // 执行搜索
  performSearch: function (keyword) {
    if (!keyword.trim()) {
      return;
    }

    this.setData({
      "loading.search": true,
    });

    get(API.product.search, { keyword, page: 1, limit: 10 })
      .then((res) => {
        if (res.code === 200 || res.success) {
          // 使用正确的数据结构
          const products = (res.data.products || res.data.list || []).map((product) => ({
            ...product,
            image_url: getProductImageUrl(product.main_image || product.image_url || product.image),
            priceText: formatPrice(product.price),
          }));

          this.setData({
            searchResults: products,
            showSearchResults: true,
          });
        } else {
          this.setData({
            searchResults: [],
            showSearchResults: true,
          });
        }
      })
      .catch((error) => {
        console.error("搜索失败:", error);
        this.setData({
          searchResults: [],
          showSearchResults: true,
        });
      })
      .finally(() => {
        this.setData({
          "loading.search": false,
        });
      });
  },

  // 搜索提交
  onSearchSubmit: function () {
    const keyword = this.data.keyword.trim();
    if (!keyword) {
      return;
    }

    // 保存搜索历史
    this.saveSearchHistory(keyword);

    // 跳转到商品列表页面
    wx.navigateTo({
      url: `/pages/product-list/product-list?keyword=${encodeURIComponent(keyword)}`,
    });
  },

  // 点击热门搜索
  onHotKeywordTap: function (e) {
    const keyword = e.currentTarget.dataset.keyword;
    this.setData({
      keyword,
    });
    this.performSearch(keyword);
  },

  // 点击历史搜索
  onHistoryTap: function (e) {
    const keyword = e.currentTarget.dataset.keyword;
    this.setData({
      keyword,
    });
    this.performSearch(keyword);
  },

  // 点击推荐商品
  onRecommendProductTap: function (e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?id=${id}`,
    });
  },

  // 点击搜索结果商品
  onSearchResultTap: function (e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?id=${id}`,
    });
  },

  // 保存搜索历史
  saveSearchHistory: function (keyword) {
    let history = this.data.searchHistory;

    // 移除重复项
    history = history.filter((item) => item !== keyword);

    // 添加到开头
    history.unshift(keyword);

    // 限制数量
    if (history.length > 10) {
      history = history.slice(0, 10);
    }

    this.setData({
      searchHistory: history,
    });

    wx.setStorageSync("searchHistory", history);
  },

  // 清空搜索历史
  onClearHistory: function () {
    wx.showModal({
      title: "提示",
      content: "确定要清空搜索历史吗？",
      success: (res) => {
        if (res.confirm) {
          this.setData({
            searchHistory: [],
          });
          wx.removeStorageSync("searchHistory");
        }
      },
    });
  },

  // 清空搜索
  onClearSearch: function () {
    this.setData({
      keyword: "",
      searchResults: [],
      showSearchResults: false,
    });
  },
});
