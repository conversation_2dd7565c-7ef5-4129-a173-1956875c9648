<!--pages/search/search.wxml-->
<view class="container">
  <!-- 搜索框 -->
  <view class="search-header">
    <view class="search-box">
      <input 
        class="search-input" 
        type="text" 
        placeholder="请输入商品名称"
        value="{{keyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearchSubmit"
        focus="{{true}}"
      />
      <button class="search-btn" bindtap="onSearchSubmit">搜索</button>
      <button class="clear-btn" bindtap="onClearSearch" wx:if="{{keyword}}">清空</button>
    </view>
  </view>

  <!-- 搜索结果 -->
  <view class="search-results" wx:if="{{showSearchResults}}">
    <view class="section-header">
      <text class="section-title">搜索结果 ({{searchResults.length}})</text>
    </view>
    
    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{loading.search}}">
      <view class="loading">搜索中...</view>
    </view>
    
    <!-- 搜索结果列表 -->
    <view class="product-list" wx:elif="{{searchResults.length > 0}}">
      <view 
        class="product-item" 
        wx:for="{{searchResults}}" 
        wx:key="id"
        bindtap="onSearchResultTap"
        data-id="{{item.id}}"
      >
        <image 
          class="product-image" 
          src="{{item.image_url || defaultImage}}" 
          mode="aspectFill"
        />
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <text class="product-price">{{item.priceText}}</text>
        </view>
      </view>
    </view>
    
    <!-- 无搜索结果 -->
    <view class="empty-results" wx:else>
      <view class="empty-icon">🔍</view>
      <text class="empty-text">未找到相关商品</text>
      <text class="empty-tip">试试其他关键词吧</text>
    </view>
  </view>

  <!-- 搜索历史 -->
  <view class="history-section" wx:if="{{searchHistory.length > 0 && !showSearchResults}}">
    <view class="section-header">
      <text class="section-title">搜索历史</text>
      <text class="clear-btn" bindtap="onClearHistory">清空</text>
    </view>
    <view class="keyword-list">
      <view 
        class="keyword-item"
        wx:for="{{searchHistory}}" 
        wx:key="*this"
        bindtap="onHistoryTap"
        data-keyword="{{item}}"
      >
        {{item}}
      </view>
    </view>
  </view>

  <!-- 热门搜索 -->
  <view class="hot-section" wx:if="{{!showSearchResults}}">
    <view class="section-header">
      <text class="section-title">热门搜索</text>
    </view>
    <view class="keyword-list">
      <view 
        class="keyword-item hot"
        wx:for="{{hotKeywords}}" 
        wx:key="*this"
        bindtap="onHotKeywordTap"
        data-keyword="{{item}}"
      >
        {{item}}
      </view>
    </view>
  </view>

  <!-- 推荐商品 -->
  <view class="recommend-section" wx:if="{{!showSearchResults}}">
    <view class="section-header">
      <text class="section-title">推荐商品</text>
    </view>
    
    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{loading.recommend}}">
      <view class="loading">加载中...</view>
    </view>
    
    <!-- 推荐商品列表 -->
    <view class="product-grid" wx:elif="{{recommendProducts.length > 0}}">
      <view 
        class="product-item" 
        wx:for="{{recommendProducts}}" 
        wx:key="id"
        bindtap="onRecommendProductTap"
        data-id="{{item.id}}"
      >
        <image 
          class="product-image" 
          src="{{item.image_url || defaultImage}}" 
          mode="aspectFill"
        />
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <text class="product-price">{{item.priceText}}</text>
        </view>
      </view>
    </view>
    
    <!-- 无推荐商品 -->
    <view class="empty-recommend" wx:else>
      <text class="empty-text">暂无推荐商品</text>
    </view>
  </view>
</view> 