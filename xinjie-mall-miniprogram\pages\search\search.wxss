/* pages/search/search.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索头部 */
.search-header {
  background-color: #4caf50;
  padding: 20rpx;
}

.search-box {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.search-input {
  flex: 1;
  background-color: #fff;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #333;
}

.search-btn {
  background-color: #45a049;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  min-width: 120rpx;
}

.clear-btn {
  background-color: #ff6b6b;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  min-width: 100rpx;
}

/* 搜索结果 */
.search-results {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

/* 区块通用样式 */
.history-section,
.hot-section,
.recommend-section {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.clear-btn {
  font-size: 26rpx;
  color: #999;
  padding: 10rpx 20rpx;
  border: 1px solid #e0e0e0;
  border-radius: 20rpx;
}

/* 关键词列表 */
.keyword-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.keyword-item {
  padding: 15rpx 30rpx;
  background-color: #f0f0f0;
  border-radius: 50rpx;
  font-size: 26rpx;
  color: #666;
  border: 1px solid #e0e0e0;
}

.keyword-item.hot {
  background-color: #fff3e0;
  border-color: #ffc107;
  color: #f57c00;
}

/* 商品列表 */
.product-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.product-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  border: 1px solid #e0e0e0;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  line-height: 1.4;
}

.product-price {
  font-size: 32rpx;
  color: #ff4444;
  font-weight: bold;
}

/* 商品网格 */
.product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.product-grid .product-item {
  flex-direction: column;
  align-items: flex-start;
  padding: 15rpx;
}

.product-grid .product-image {
  width: 100%;
  height: 200rpx;
  margin-right: 0;
  margin-bottom: 15rpx;
}

.product-grid .product-info {
  width: 100%;
}

.product-grid .product-name {
  font-size: 26rpx;
  margin-bottom: 10rpx;
}

.product-grid .product-price {
  font-size: 28rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.loading {
  font-size: 28rpx;
  color: #999;
}

/* 空状态 */
.empty-results,
.empty-recommend {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 20rpx;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: #999;
}

/* 动画效果 */
.search-btn,
.clear-btn,
.keyword-item,
.product-item {
  transition: all 0.3s ease;
}

.search-btn:active {
  background-color: #3e8e41;
}

.clear-btn:active {
  background-color: #e74c3c;
}

.keyword-item:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.product-item:active {
  transform: scale(0.98);
  opacity: 0.8;
}
