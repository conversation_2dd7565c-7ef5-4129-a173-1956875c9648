<!-- 浏览历史页面 -->
<view class="browse-history-container">
  <!-- 头部 -->
  <view class="header">
    <view class="title">浏览历史</view>
    <view class="actions">
      <text class="clear-btn" bindtap="clearHistory" wx:if="{{historyList.length > 0}}">清空</text>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats" wx:if="{{historyList.length > 0}}">
    <text>共浏览 {{totalCount}} 件商品</text>
  </view>

  <!-- 历史列表 -->
  <view class="history-list" wx:if="{{historyList.length > 0}}">
    <view class="date-group" wx:for="{{groupedHistory}}" wx:key="date">
      <view class="date-header">{{item.date}}</view>
      <view class="history-item" wx:for="{{item.items}}" wx:key="id" wx:for-item="historyItem">
        <view class="product-info" bindtap="goToProduct" data-id="{{historyItem.product.id}}">
          <image class="product-image" src="{{historyItem.product.main_image}}" mode="aspectFill"/>
          <view class="product-details">
            <view class="product-name">{{historyItem.product.name}}</view>
            <view class="product-category">{{historyItem.product.category.name}}</view>
            <view class="product-price">
              <text class="current-price">¥{{historyItem.product.price}}</text>
              <text class="original-price" wx:if="{{historyItem.product.original_price > historyItem.product.price}}">¥{{historyItem.product.original_price}}</text>
            </view>
            <view class="product-meta">
              <text class="sales">已售{{historyItem.product.sales}}件</text>
              <text class="rating">{{historyItem.product.rating}}分</text>
              <text class="browse-source">来源：{{sourceMap[historyItem.source]}}</text>
            </view>
            <view class="browse-info">
              <text class="browse-time">{{historyItem.browse_time}}</text>
              <text class="browse-duration" wx:if="{{historyItem.browse_duration > 0}}">浏览{{historyItem.browse_duration}}秒</text>
            </view>
          </view>
        </view>
        
        <!-- 快捷操作 -->
        <view class="quick-actions">
          <view class="action-btn favorite-btn" bindtap="toggleFavorite" data-id="{{historyItem.product.id}}" data-favorited="{{historyItem.product.is_favorited}}">
            <icon type="{{historyItem.product.is_favorited ? 'success' : 'success_no_circle'}}" size="20" color="{{historyItem.product.is_favorited ? '#ff6b35' : '#ccc'}}"/>
          </view>
          <view class="action-btn compare-btn" bindtap="addToCompare" data-id="{{historyItem.product.id}}">
            <image class="compare-icon" src="/static/images/compare.png"/>
          </view>
          <view class="action-btn remove-btn" bindtap="removeHistory" data-id="{{historyItem.product.id}}">
            <icon type="clear" size="18" color="#999"/>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{historyList.length === 0 && !loading}}">
    <image class="empty-icon" src="/static/images/empty-history.png"/>
    <text class="empty-text">暂无浏览历史</text>
    <text class="empty-desc">快去发现心仪的茶叶吧</text>
    <button class="go-shopping-btn" bindtap="goShopping">去逛逛</button>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>

  <!-- 基于历史的推荐 -->
  <view class="recommendations" wx:if="{{recommendations.length > 0}}">
    <view class="section-title">
      <text>基于浏览历史为你推荐</text>
    </view>
    <scroll-view class="recommend-list" scroll-x="true">
      <view class="recommend-item" wx:for="{{recommendations}}" wx:key="id" bindtap="goToProduct" data-id="{{item.id}}">
        <image class="recommend-image" src="{{item.main_image}}" mode="aspectFill"/>
        <view class="recommend-name">{{item.name}}</view>
        <view class="recommend-price">¥{{item.price}}</view>
        <view class="recommend-sales">已售{{item.sales}}件</view>
      </view>
    </scroll-view>
  </view>

  <!-- 浏览统计 -->
  <view class="browse-stats" wx:if="{{showStats && stats}}">
    <view class="section-title">浏览统计</view>
    <view class="stats-content">
      <view class="stat-item">
        <text class="stat-label">总浏览量</text>
        <text class="stat-value">{{stats.totalCount}}</text>
      </view>
      <view class="stat-item" wx:for="{{stats.sourceStats}}" wx:key="source">
        <text class="stat-label">{{sourceMap[item.source]}}</text>
        <text class="stat-value">{{item.count}}</text>
      </view>
    </view>
  </view>
</view>

<!-- 确认清空弹窗 -->
<modal title="确认清空" confirm-text="清空" cancel-text="取消" show="{{showClearModal}}" bindconfirm="confirmClear" bindcancel="cancelClear">
  <text>确定要清空所有浏览历史吗？此操作不可恢复。</text>
</modal>
