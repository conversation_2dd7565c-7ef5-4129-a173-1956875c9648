// 商品收藏页面逻辑
const api = require('../../../utils/api');
const util = require('../../../utils/util');

Page({
  data: {
    favorites: [],
    recommendations: [],
    totalCount: 0,
    currentPage: 1,
    totalPages: 1,
    loading: false,
    isEdit: false,
    selectedIds: [],
    showDeleteModal: false,
    isAllSelected: false
  },

  onLoad() {
    this.loadFavorites();
    this.loadRecommendations();
  },

  onShow() {
    // 从其他页面返回时刷新数据
    this.loadFavorites();
  },

  onPullDownRefresh() {
    this.data.currentPage = 1;
    this.loadFavorites(true);
  },

  onReachBottom() {
    if (this.data.currentPage < this.data.totalPages && !this.data.loading) {
      this.data.currentPage++;
      this.loadFavorites();
    }
  },

  // 加载收藏列表
  async loadFavorites(refresh = false) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const res = await api.get('/user-features/favorites', {
        page: this.data.currentPage,
        limit: 20
      });

      if (res.code === 200) {
        const favorites = res.data.favorites.map(item => ({
          ...item,
          created_at: util.formatTime(new Date(item.created_at))
        }));

        this.setData({
          favorites: refresh ? favorites : [...this.data.favorites, ...favorites],
          totalCount: res.data.total,
          totalPages: res.data.totalPages,
          loading: false
        });

        if (refresh) {
          wx.stopPullDownRefresh();
        }
      }
    } catch (error) {
      console.error('加载收藏列表失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  // 加载推荐商品
  async loadRecommendations() {
    try {
      const res = await api.get('/user-features/browse-history/recommendations', {
        limit: 10
      });

      if (res.code === 200) {
        this.setData({
          recommendations: res.data.slice(0, 6) // 只显示6个推荐
        });
      }
    } catch (error) {
      console.error('加载推荐商品失败:', error);
    }
  },

  // 切换编辑模式
  toggleEdit() {
    this.setData({
      isEdit: !this.data.isEdit,
      selectedIds: [],
      isAllSelected: false
    });
  },

  // 切换选择状态
  toggleSelect(e) {
    const productId = e.currentTarget.dataset.id;
    const selectedIds = [...this.data.selectedIds];
    const index = selectedIds.indexOf(productId);

    if (index > -1) {
      selectedIds.splice(index, 1);
    } else {
      selectedIds.push(productId);
    }

    this.setData({
      selectedIds,
      isAllSelected: selectedIds.length === this.data.favorites.length
    });
  },

  // 全选/取消全选
  toggleSelectAll() {
    const isAllSelected = !this.data.isAllSelected;
    const selectedIds = isAllSelected 
      ? this.data.favorites.map(item => item.product.id)
      : [];

    this.setData({
      isAllSelected,
      selectedIds
    });
  },

  // 单个删除
  async removeSingle(e) {
    const productId = e.currentTarget.dataset.id;
    
    const res = await wx.showModal({
      title: '确认删除',
      content: '确定要取消收藏这件商品吗？'
    });

    if (res.confirm) {
      await this.removeFromFavorites([productId]);
    }
  },

  // 批量删除
  batchRemove() {
    if (this.data.selectedIds.length === 0) {
      wx.showToast({
        title: '请选择要删除的商品',
        icon: 'none'
      });
      return;
    }

    this.setData({ showDeleteModal: true });
  },

  // 确认删除
  async confirmDelete() {
    this.setData({ showDeleteModal: false });
    await this.removeFromFavorites(this.data.selectedIds);
    this.toggleEdit(); // 退出编辑模式
  },

  // 取消删除
  cancelDelete() {
    this.setData({ showDeleteModal: false });
  },

  // 从收藏中移除商品
  async removeFromFavorites(productIds) {
    wx.showLoading({ title: '删除中...' });

    try {
      // 如果是单个删除
      if (productIds.length === 1) {
        const res = await api.delete(`/user-features/favorites/${productIds[0]}`);
        if (res.code === 200) {
          wx.showToast({
            title: '取消收藏成功',
            icon: 'success'
          });
        }
      } else {
        // 批量删除 - 这里需要循环调用单个删除接口
        for (const productId of productIds) {
          await api.delete(`/user-features/favorites/${productId}`);
        }
        wx.showToast({
          title: `成功删除${productIds.length}件商品`,
          icon: 'success'
        });
      }

      // 刷新列表
      this.data.currentPage = 1;
      this.loadFavorites(true);

    } catch (error) {
      console.error('删除收藏失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 跳转到商品详情
  goToProduct(e) {
    if (this.data.isEdit) return; // 编辑模式下不跳转

    const productId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/product/detail/detail?id=${productId}`
    });
  },

  // 去购物
  goShopping() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 分享收藏夹
  onShareAppMessage() {
    return {
      title: '我的茶叶收藏夹',
      path: '/pages/index/index',
      imageUrl: '/static/images/share-favorite.png'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '发现了好多优质茶叶，快来看看吧！',
      imageUrl: '/static/images/share-favorite.png'
    };
  }
});
