<!-- 商品收藏页面 -->
<view class="favorites-container">
  <!-- 头部 -->
  <view class="header">
    <view class="title">我的收藏</view>
    <view class="actions">
      <text class="edit-btn" wx:if="{{!isEdit}}" bindtap="toggleEdit">编辑</text>
      <text class="done-btn" wx:else bindtap="toggleEdit">完成</text>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats" wx:if="{{favorites.length > 0}}">
    <text>共收藏 {{totalCount}} 件商品</text>
  </view>

  <!-- 收藏列表 -->
  <view class="favorites-list" wx:if="{{favorites.length > 0}}">
    <view class="favorite-item" wx:for="{{favorites}}" wx:key="id">
      <!-- 编辑模式选择框 -->
      <view class="checkbox" wx:if="{{isEdit}}" bindtap="toggleSelect" data-id="{{item.product.id}}">
        <icon type="{{selectedIds.includes(item.product.id) ? 'success' : 'circle'}}" size="20" color="{{selectedIds.includes(item.product.id) ? '#ff6b35' : '#ccc'}}"/>
      </view>

      <!-- 商品信息 -->
      <view class="product-info" bindtap="goToProduct" data-id="{{item.product.id}}">
        <image class="product-image" src="{{item.product.main_image}}" mode="aspectFill"/>
        <view class="product-details">
          <view class="product-name">{{item.product.name}}</view>
          <view class="product-category">{{item.product.category.name}}</view>
          <view class="product-price">
            <text class="current-price">¥{{item.product.price}}</text>
            <text class="original-price" wx:if="{{item.product.original_price > item.product.price}}">¥{{item.product.original_price}}</text>
          </view>
          <view class="product-meta">
            <text class="sales">已售{{item.product.sales}}件</text>
            <text class="rating">{{item.product.rating}}分</text>
            <text class="stock" wx:if="{{item.product.stock <= 10}}">仅剩{{item.product.stock}}件</text>
          </view>
          <view class="favorite-time">收藏于 {{item.created_at}}</view>
        </view>
      </view>

      <!-- 单个删除按钮 -->
      <view class="remove-btn" wx:if="{{!isEdit}}" bindtap="removeSingle" data-id="{{item.product.id}}">
        <icon type="clear" size="20" color="#999"/>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{favorites.length === 0 && !loading}}">
    <image class="empty-icon" src="/static/images/empty-favorite.png"/>
    <text class="empty-text">还没有收藏任何商品</text>
    <text class="empty-desc">快去收藏心仪的茶叶吧</text>
    <button class="go-shopping-btn" bindtap="goShopping">去逛逛</button>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions" wx:if="{{isEdit && favorites.length > 0}}">
    <view class="select-all" bindtap="toggleSelectAll">
      <icon type="{{isAllSelected ? 'success' : 'circle'}}" size="20" color="{{isAllSelected ? '#ff6b35' : '#ccc'}}"/>
      <text>全选</text>
    </view>
    <view class="action-buttons">
      <button class="batch-remove-btn" bindtap="batchRemove" disabled="{{selectedIds.length === 0}}">
        删除({{selectedIds.length}})
      </button>
    </view>
  </view>

  <!-- 推荐商品 -->
  <view class="recommendations" wx:if="{{recommendations.length > 0}}">
    <view class="section-title">为你推荐</view>
    <scroll-view class="recommend-list" scroll-x="true">
      <view class="recommend-item" wx:for="{{recommendations}}" wx:key="id" bindtap="goToProduct" data-id="{{item.id}}">
        <image class="recommend-image" src="{{item.main_image}}" mode="aspectFill"/>
        <view class="recommend-name">{{item.name}}</view>
        <view class="recommend-price">¥{{item.price}}</view>
      </view>
    </scroll-view>
  </view>
</view>

<!-- 确认删除弹窗 -->
<modal title="确认删除" confirm-text="删除" cancel-text="取消" show="{{showDeleteModal}}" bindconfirm="confirmDelete" bindcancel="cancelDelete">
  <text>确定要删除选中的收藏商品吗？</text>
</modal>
