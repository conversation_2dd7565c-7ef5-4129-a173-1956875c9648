/* 商品收藏页面样式 */
.favorites-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 头部 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.edit-btn, .done-btn {
  font-size: 28rpx;
  color: #ff6b35;
}

/* 统计信息 */
.stats {
  padding: 20rpx 30rpx;
  background-color: #fff;
  font-size: 24rpx;
  color: #666;
  border-bottom: 1rpx solid #eee;
}

/* 收藏列表 */
.favorites-list {
  background-color: #fff;
}

.favorite-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  position: relative;
}

.checkbox {
  margin-right: 20rpx;
}

.product-info {
  display: flex;
  flex: 1;
  align-items: flex-start;
}

.product-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.product-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 160rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.product-category {
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
}

.product-price {
  margin-top: 16rpx;
}

.current-price {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
  margin-left: 16rpx;
}

.product-meta {
  display: flex;
  align-items: center;
  margin-top: 12rpx;
  font-size: 22rpx;
  color: #666;
}

.product-meta text {
  margin-right: 20rpx;
}

.stock {
  color: #ff6b35 !important;
}

.favorite-time {
  font-size: 20rpx;
  color: #999;
  margin-top: 8rpx;
}

.remove-btn {
  padding: 10rpx;
  margin-left: 20rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  background-color: #fff;
  margin-top: 20rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 60rpx;
}

.go-shopping-btn {
  background-color: #ff6b35;
  color: #fff;
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 60rpx;
  color: #666;
  font-size: 26rpx;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  z-index: 100;
}

.select-all {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}

.select-all icon {
  margin-right: 12rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.batch-remove-btn {
  background-color: #ff6b35;
  color: #fff;
  border-radius: 50rpx;
  padding: 16rpx 40rpx;
  font-size: 26rpx;
  border: none;
}

.batch-remove-btn[disabled] {
  background-color: #ccc;
}

/* 推荐商品 */
.recommendations {
  margin-top: 20rpx;
  background-color: #fff;
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.recommend-list {
  white-space: nowrap;
}

.recommend-item {
  display: inline-block;
  width: 200rpx;
  margin-right: 20rpx;
  vertical-align: top;
}

.recommend-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
}

.recommend-name {
  font-size: 24rpx;
  color: #333;
  margin-top: 12rpx;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.recommend-price {
  font-size: 26rpx;
  color: #ff6b35;
  font-weight: bold;
  margin-top: 8rpx;
}
