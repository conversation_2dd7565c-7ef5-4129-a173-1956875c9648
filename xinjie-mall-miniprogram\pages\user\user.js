// pages/user/user.js
const { logout } = require("../../utils/auth");
const { request } = require("../../utils/request");
const { API } = require("../../config/api");

Page({
  data: {
    userInfo: null,
    isLogin: false,
    balanceInfo: {
      balance: '0.00',
      canPay: false
    },
    memberInfo: {
      currentLevel: {
        name: '普通会员',
        discountRate: 1.00
      },
      points: 0,
      upgradeProgress: {
        progress: 0,
        progressText: '暂无积分'
      }
    },
    loading: false,
    menuItems: [
      {
        title: "我的订单",
        icon: "📋",
        url: "/pages/order-list/order-list",
      },
      {
        title: "退货记录",
        icon: "🔄",
        url: "/pages/return-list/return-list",
      },
      {
        title: "账户充值",
        icon: "💰",
        url: "/pages/recharge/recharge",
      },
      {
        title: "余额记录",
        icon: "📊",
        url: "/pages/balance-history/balance-history",
      },
      {
        title: "会员中心",
        icon: "👑",
        url: "/pages/member-center/member-center",
      },
      {
        title: "收货地址",
        icon: "📍",
        url: "/pages/address-list/address-list",
      },
      {
        title: "联系客服",
        icon: "💬",
        action: "contact",
      },
      {
        title: "关于我们",
        icon: "ℹ️",
        action: "about",
      },
    ],
  },

  onLoad: function (options) {
    this.loadUserInfo();
  },

  onShow: function () {
    this.loadUserInfo();
  },

  // 加载用户信息
  loadUserInfo: function () {
    const userInfo = wx.getStorageSync("userInfo");
    const token = wx.getStorageSync("token");

    this.setData({
      userInfo,
      isLogin: !!token,
    });

    // 如果已登录，加载余额和会员信息
    if (token) {
      this.loadBalanceInfo();
      this.loadMemberInfo();
    }
  },

  // 加载余额信息
  loadBalanceInfo: async function () {
    try {
      const response = await request({
        url: API.balance.info,
        method: 'GET'
      });

      if (response.success) {
        this.setData({
          balanceInfo: response.data
        });
      }
    } catch (error) {
      console.error('加载余额信息失败:', error);
    }
  },

  // 加载会员信息
  loadMemberInfo: async function () {
    try {
      const response = await request({
        url: API.member.info,
        method: 'GET'
      });

      if (response.success) {
        this.setData({
          memberInfo: response.data
        });
      }
    } catch (error) {
      console.error('加载会员信息失败:', error);
    }
  },

  // 登录
  onLogin: function () {
    wx.navigateTo({
      url: "/pages/login/login",
    });
  },

  // 退出登录
  onLogout: function () {
    wx.showModal({
      title: "提示",
      content: "确定要退出登录吗？",
      success: (res) => {
        if (res.confirm) {
          logout();
          this.setData({
            userInfo: null,
            isLogin: false,
          });
        }
      },
    });
  },

  // 菜单点击
  onMenuTap: function (e) {
    const item = e.currentTarget.dataset.item;

    if (item.url) {
      wx.navigateTo({
        url: item.url,
      });
    } else if (item.action) {
      this.handleAction(item.action);
    }
  },

  // 处理特殊操作
  handleAction: function (action) {
    switch (action) {
      case "contact":
        wx.showToast({
          title: "客服功能开发中",
          icon: "none",
        });
        break;
      case "about":
        wx.showModal({
          title: "关于我们",
          content: "心洁茶叶商城\n专注于优质茶叶的销售\n版本：1.0.0",
          showCancel: false,
        });
        break;
    }
  },

  // 充值按钮点击
  onRecharge: function () {
    wx.navigateTo({
      url: '/pages/recharge/recharge'
    });
  },

  // 查看余额记录
  onViewBalanceHistory: function () {
    wx.navigateTo({
      url: '/pages/balance-history/balance-history'
    });
  },
});
