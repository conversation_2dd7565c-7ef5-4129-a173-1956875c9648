<!--pages/user/user.wxml-->
<view class="container">
  <!-- 用户信息 -->
  <view class="user-header">
    <view class="user-info" wx:if="{{isLogin}}">
      <view class="user-main">
        <image class="user-avatar" src="{{userInfo.avatarUrl || '/images/common/default-avatar.png'}}" mode="aspectFill"></image>
        <view class="user-details">
          <view class="user-name-section">
            <text class="user-name">{{userInfo.nickName || '用户'}}</text>
            <text class="member-level">{{memberInfo.currentLevel.name}}</text>
          </view>
          <text class="user-phone">{{userInfo.phone || '未绑定手机'}}</text>
          <view class="user-stats">
            <view class="stat-item">
              <text class="stat-value">{{memberInfo.points}}</text>
              <text class="stat-label">积分</text>
            </view>
            <view class="stat-divider"></view>
            <view class="stat-item">
              <text class="stat-value">{{balanceInfo.balance}}</text>
              <text class="stat-label">余额(元)</text>
            </view>
          </view>
        </view>
      </view>
      <button class="logout-btn" bindtap="onLogout">退出</button>
    </view>

    <view class="user-info" wx:else>
      <view class="user-main">
        <image class="user-avatar" src="/images/common/default-avatar.png" mode="aspectFill"></image>
        <view class="user-details">
          <text class="user-name">未登录</text>
          <text class="user-phone">点击登录享受更多服务</text>
        </view>
      </view>
      <button class="login-btn" bindtap="onLogin">登录</button>
    </view>
  </view>

  <!-- 余额和会员信息卡片 -->
  <view class="info-cards" wx:if="{{isLogin}}">
    <view class="balance-card">
      <view class="card-header">
        <text class="card-title">账户余额</text>
        <text class="balance-amount">¥{{balanceInfo.balance}}</text>
      </view>
      <view class="card-actions">
        <button class="action-btn primary" bindtap="onRecharge">立即充值</button>
        <button class="action-btn secondary" bindtap="onViewBalanceHistory">余额记录</button>
      </view>
    </view>

    <view class="member-card">
      <view class="card-header">
        <text class="card-title">会员等级</text>
        <text class="member-level-text">{{memberInfo.currentLevel.name}}</text>
      </view>
      <view class="progress-section" wx:if="{{memberInfo.upgradeProgress.progress < 100}}">
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{memberInfo.upgradeProgress.progress}}%"></view>
        </view>
        <text class="progress-text">{{memberInfo.upgradeProgress.progressText}}</text>
      </view>
      <view class="max-level-text" wx:else>
        <text>已达最高等级</text>
      </view>
    </view>
  </view>

  <!-- 菜单列表 -->
  <view class="menu-section">
    <view 
      class="menu-item"
      wx:for="{{menuItems}}" 
      wx:key="title"
      bindtap="onMenuTap"
      data-item="{{item}}"
    >
      <view class="menu-left">
        <text class="menu-icon">{{item.icon}}</text>
        <text class="menu-title">{{item.title}}</text>
      </view>
      <text class="menu-arrow">></text>
    </view>
  </view>
</view> 