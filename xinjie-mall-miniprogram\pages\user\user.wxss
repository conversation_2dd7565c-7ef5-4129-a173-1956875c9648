/* pages/user/user.wxss */
.container {
  background: linear-gradient(180deg, #f0fdf4 0%, #ecfdf5 50%, #f7fee7 100%);
  min-height: 100vh;
  position: relative;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 400rpx;
  background: linear-gradient(135deg, #86efac 0%, #6ee7b7 50%, #34d399 100%);
  opacity: 0.1;
  z-index: 0;
}

/* 用户头部 */
.user-header {
  background: linear-gradient(135deg, #86efac, #6ee7b7, #34d399);
  padding: 60rpx 30rpx 40rpx;
  margin-bottom: 30rpx;
  border-radius: 0 0 40rpx 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(52, 211, 153, 0.15);
  position: relative;
  z-index: 1;
}

.user-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #fff;
}

.user-avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  margin-right: 30rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.user-phone {
  font-size: 28rpx;
  opacity: 0.8;
  margin-bottom: 15rpx;
}

.member-badge {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.member-level {
  background: rgba(255, 255, 255, 0.9);
  color: #059669;
  padding: 10rpx 20rpx;
  border-radius: 25rpx;
  font-size: 24rpx;
  font-weight: 600;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.member-points {
  font-size: 26rpx;
  opacity: 0.95;
  font-weight: 500;
}

.login-btn,
.logout-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50rpx;
  padding: 15rpx 30rpx;
  font-size: 28rpx;
  min-width: 120rpx;
}

.login-btn:active,
.logout-btn:active {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 信息卡片区域 */
.info-cards {
  margin: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.balance-card, .member-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.balance-card {
  background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
}

.member-card {
  background: linear-gradient(135deg, #fef7cd 0%, #fef3c7 100%);
  border: 1px solid #f59e0b;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.balance-amount {
  font-size: 48rpx;
  color: #4CAF50;
  font-weight: bold;
}

.member-level-text {
  font-size: 32rpx;
  color: #FF9800;
  font-weight: bold;
}

.card-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  text-align: center;
}

.action-btn.primary {
  background: #4CAF50;
  color: white;
}

.action-btn.secondary {
  background: #f0f0f0;
  color: #666;
}

/* 进度条 */
.progress-section {
  margin-top: 15rpx;
}

.progress-bar {
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #45a049);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
}

.max-level-text {
  text-align: center;
  color: #FF9800;
  font-size: 28rpx;
  margin-top: 15rpx;
}

/* 菜单列表 */
.menu-section {
  background-color: #fff;
  margin: 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 35rpx 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-left {
  display: flex;
  align-items: center;
}

.menu-icon {
  font-size: 36rpx;
  margin-right: 30rpx;
}

.menu-title {
  font-size: 32rpx;
  color: #333;
}

.menu-arrow {
  font-size: 28rpx;
  color: #999;
}

/* 动画效果 */
.menu-item {
  transition: background-color 0.3s ease;
}

.menu-item:active {
  background-color: #f8f8f8;
}

.user-avatar {
  transition: transform 0.3s ease;
}

.user-avatar:active {
  transform: scale(0.95);
}
