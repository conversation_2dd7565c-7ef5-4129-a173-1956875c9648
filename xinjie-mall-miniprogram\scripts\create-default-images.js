const fs = require('fs');
const path = require('path');

// 创建默认图片目录
const commonDir = path.join(__dirname, '../images/common');
if (!fs.existsSync(commonDir)) {
  fs.mkdirSync(commonDir, { recursive: true });
}

// 创建默认图片的占位符文件
const defaultImages = [
  'default-category.png',
  'default-product.png',
  'default-banner.png'
];

defaultImages.forEach(imageName => {
  const imagePath = path.join(commonDir, imageName);
  if (!fs.existsSync(imagePath)) {
    // 创建一个简单的占位符文件
    fs.writeFileSync(imagePath, '# 默认图片占位符\n# 请替换为实际的PNG图片文件');
    console.log(`创建默认图片: ${imageName}`);
  }
});

console.log('默认图片创建完成！');
console.log('请将实际的PNG图片文件替换这些占位符文件。'); 