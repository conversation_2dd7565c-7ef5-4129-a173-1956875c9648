const fs = require('fs');
const path = require('path');

// 创建简单的PNG图片数据（16x16像素的透明图片）
const createSimplePNG = () => {
  return Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
    0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
    0x49, 0x48, 0x44, 0x52, // IHDR
    0x00, 0x00, 0x00, 0x10, // width: 16
    0x00, 0x00, 0x00, 0x10, // height: 16
    0x08, 0x06, 0x00, 0x00, 0x00, // bit depth, color type, compression, filter, interlace
    0x1F, 0xF3, 0xFF, 0x61, // CRC
    0x00, 0x00, 0x00, 0x0C, // IDAT chunk length
    0x49, 0x44, 0x41, 0x54, // IDAT
    0x78, 0x9C, 0x63, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, // compressed data
    0xE5, 0x27, 0x7E, 0xFC, // CRC
    0x00, 0x00, 0x00, 0x00, // IEND chunk length
    0x49, 0x45, 0x4E, 0x44, // IEND
    0xAE, 0x42, 0x60, 0x82  // CRC
  ]);
};

// 创建目录和默认图片
const createDefaultImages = () => {
  const directories = [
    'images/common',
    'images/categories', 
    'images/products',
    'images/banners'
  ];

  const defaultImages = [
    {
      path: 'images/common/default-category.png',
      description: '默认分类图标'
    },
    {
      path: 'images/common/default-product.png',
      description: '默认商品图片'
    },
    {
      path: 'images/common/default-banner.png', 
      description: '默认轮播图'
    },
    {
      path: 'images/common/empty.png',
      description: '空状态图片'
    }
  ];

  console.log('📁 创建目录结构...');
  directories.forEach(dir => {
    const fullPath = path.join(__dirname, '..', dir);
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
      console.log(`✅ 创建目录: ${dir}`);
    }
  });

  console.log('\n🖼️ 创建默认图片...');
  defaultImages.forEach(img => {
    const fullPath = path.join(__dirname, '..', img.path);
    const dir = path.dirname(fullPath);
    
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    if (!fs.existsSync(fullPath)) {
      const pngData = createSimplePNG();
      fs.writeFileSync(fullPath, pngData);
      console.log(`✅ 创建图片: ${img.path} (${img.description})`);
    } else {
      console.log(`⏭️ 图片已存在: ${img.path}`);
    }
  });

  console.log('\n✨ 默认图片创建完成！');
  console.log('📝 说明:');
  console.log('1. 这些是16x16像素的透明PNG图片');
  console.log('2. 可以替换为实际的图片文件');
  console.log('3. 建议尺寸: 分类图标64x64px, 商品图片200x200px, 轮播图750x300px');
};

createDefaultImages(); 