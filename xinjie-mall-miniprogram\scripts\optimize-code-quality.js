const fs = require('fs');
const path = require('path');

// 代码质量优化脚本
class CodeQualityOptimizer {
  constructor() {
    this.projectRoot = path.join(__dirname, '..');
    this.issues = [];
  }

  // 检查未使用的组件
  checkUnusedComponents() {
    console.log('🔍 检查未使用的组件...');
    
    const componentsDir = path.join(this.projectRoot, 'components');
    if (!fs.existsSync(componentsDir)) {
      console.log('✅ 没有components目录');
      return;
    }

    const components = fs.readdirSync(componentsDir, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name);

    const pagesDir = path.join(this.projectRoot, 'pages');
    const pages = fs.readdirSync(pagesDir, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name);

    components.forEach(component => {
      let isUsed = false;
      
      // 检查是否在页面中被引用
      pages.forEach(page => {
        const jsonPath = path.join(pagesDir, page, `${page}.json`);
        if (fs.existsSync(jsonPath)) {
          try {
            const jsonContent = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));
            const usingComponents = jsonContent.usingComponents || {};
            
            if (Object.values(usingComponents).some(path => path.includes(component))) {
              isUsed = true;
            }
          } catch (error) {
            console.error(`解析 ${jsonPath} 失败:`, error.message);
          }
        }
      });

      if (!isUsed) {
        this.issues.push({
          type: 'unused_component',
          component: component,
          suggestion: `建议移除未使用的组件: components/${component}/`
        });
      }
    });
  }

  // 检查大文件
  checkLargeFiles() {
    console.log('🔍 检查大文件...');
    
    const maxSize = 200 * 1024; // 200KB
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    
    const checkDirectory = (dir) => {
      if (!fs.existsSync(dir)) return;
      
      const files = fs.readdirSync(dir, { withFileTypes: true });
      
      files.forEach(file => {
        const fullPath = path.join(dir, file.name);
        
        if (file.isDirectory()) {
          checkDirectory(fullPath);
        } else if (file.isFile()) {
          const ext = path.extname(file.name).toLowerCase();
          if (imageExtensions.includes(ext)) {
            try {
              const stats = fs.statSync(fullPath);
              if (stats.size > maxSize) {
                this.issues.push({
                  type: 'large_file',
                  file: fullPath.replace(this.projectRoot, ''),
                  size: (stats.size / 1024).toFixed(2) + 'KB',
                  suggestion: '建议压缩图片或使用CDN'
                });
              }
            } catch (error) {
              console.error(`检查文件 ${fullPath} 失败:`, error.message);
            }
          }
        }
      });
    };

    checkDirectory(path.join(this.projectRoot, 'images'));
  }

  // 检查重复代码
  checkDuplicateCode() {
    console.log('🔍 检查重复代码...');
    
    const jsFiles = [];
    const scanDirectory = (dir) => {
      if (!fs.existsSync(dir)) return;
      
      const files = fs.readdirSync(dir, { withFileTypes: true });
      
      files.forEach(file => {
        const fullPath = path.join(dir, file.name);
        
        if (file.isDirectory()) {
          scanDirectory(fullPath);
        } else if (file.isFile() && file.name.endsWith('.js')) {
          jsFiles.push(fullPath);
        }
      });
    };

    scanDirectory(path.join(this.projectRoot, 'pages'));
    scanDirectory(path.join(this.projectRoot, 'components'));
    scanDirectory(path.join(this.projectRoot, 'utils'));

    // 简单的重复代码检测
    const codeSnippets = new Map();
    
    jsFiles.forEach(filePath => {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n');
        
        // 检查重复的函数定义
        const functionPattern = /function\s+(\w+)\s*\(/g;
        let match;
        
        while ((match = functionPattern.exec(content)) !== null) {
          const funcName = match[1];
          const key = `function_${funcName}`;
          
          if (codeSnippets.has(key)) {
            this.issues.push({
              type: 'duplicate_function',
              function: funcName,
              files: [codeSnippets.get(key), filePath.replace(this.projectRoot, '')],
              suggestion: '建议将重复函数提取到公共模块'
            });
          } else {
            codeSnippets.set(key, filePath.replace(this.projectRoot, ''));
          }
        }
      } catch (error) {
        console.error(`检查文件 ${filePath} 失败:`, error.message);
      }
    });
  }

  // 检查未使用的样式
  checkUnusedStyles() {
    console.log('🔍 检查未使用的样式...');
    
    const pagesDir = path.join(this.projectRoot, 'pages');
    const pages = fs.readdirSync(pagesDir, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name);

    pages.forEach(page => {
      const wxssPath = path.join(pagesDir, page, `${page}.wxss`);
      const wxmlPath = path.join(pagesDir, page, `${page}.wxml`);
      
      if (fs.existsSync(wxssPath) && fs.existsSync(wxmlPath)) {
        try {
          const wxssContent = fs.readFileSync(wxssPath, 'utf8');
          const wxmlContent = fs.readFileSync(wxmlPath, 'utf8');
          
          // 提取CSS类名
          const classPattern = /\.([a-zA-Z][a-zA-Z0-9_-]*)/g;
          const cssClasses = new Set();
          let match;
          
          while ((match = classPattern.exec(wxssContent)) !== null) {
            cssClasses.add(match[1]);
          }
          
          // 检查哪些类名在WXML中没有使用
          cssClasses.forEach(className => {
            const classPattern = new RegExp(`class="[^"]*${className}[^"]*"`, 'g');
            if (!classPattern.test(wxmlContent)) {
              this.issues.push({
                type: 'unused_style',
                file: `pages/${page}/${page}.wxss`,
                class: className,
                suggestion: '建议移除未使用的样式类'
              });
            }
          });
        } catch (error) {
          console.error(`检查页面 ${page} 样式失败:`, error.message);
        }
      }
    });
  }

  // 生成优化建议
  generateSuggestions() {
    console.log('\n📊 代码质量分析报告');
    console.log('='.repeat(50));
    
    if (this.issues.length === 0) {
      console.log('✅ 没有发现需要优化的问题');
      return;
    }

    const groupedIssues = {};
    this.issues.forEach(issue => {
      if (!groupedIssues[issue.type]) {
        groupedIssues[issue.type] = [];
      }
      groupedIssues[issue.type].push(issue);
    });

    Object.keys(groupedIssues).forEach(type => {
      const issues = groupedIssues[type];
      console.log(`\n🔴 ${this.getIssueTypeName(type)} (${issues.length}项):`);
      
      issues.forEach(issue => {
        console.log(`  - ${issue.suggestion}`);
        if (issue.file) {
          console.log(`    文件: ${issue.file}`);
        }
        if (issue.size) {
          console.log(`    大小: ${issue.size}`);
        }
        if (issue.files) {
          console.log(`    涉及文件: ${issue.files.join(', ')}`);
        }
      });
    });

    console.log('\n💡 优化建议:');
    console.log('1. 移除未使用的组件和样式');
    console.log('2. 压缩大图片文件');
    console.log('3. 提取重复代码到公共模块');
    console.log('4. 使用CDN加载图片资源');
    console.log('5. 启用代码压缩和混淆');
  }

  getIssueTypeName(type) {
    const names = {
      'unused_component': '未使用的组件',
      'large_file': '大文件',
      'duplicate_function': '重复函数',
      'unused_style': '未使用的样式'
    };
    return names[type] || type;
  }

  // 执行所有检查
  run() {
    console.log('🚀 开始代码质量优化检查...\n');
    
    this.checkUnusedComponents();
    this.checkLargeFiles();
    this.checkDuplicateCode();
    this.checkUnusedStyles();
    
    this.generateSuggestions();
  }
}

// 运行优化器
const optimizer = new CodeQualityOptimizer();
optimizer.run(); 