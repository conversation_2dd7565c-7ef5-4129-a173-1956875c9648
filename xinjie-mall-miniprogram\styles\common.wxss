/* 按钮样式 */
.btn {
  display: inline-block;
  padding: 20rpx 40rpx;
  border-radius: var(--border-radius);
  font-size: var(--font-size-normal);
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  outline: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-outline {
  background-color: transparent;
  border: 2rpx solid var(--primary-color);
  color: var(--primary-color);
}

.btn-small {
  padding: 12rpx 24rpx;
  font-size: var(--font-size-small);
}

.btn-large {
  padding: 28rpx 56rpx;
  font-size: var(--font-size-large);
}

.btn-block {
  display: block;
  width: 100%;
}

.btn-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 卡片样式 */
.card {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  padding: var(--spacing-normal);
  margin-bottom: var(--spacing-normal);
}

.card-header {
  padding-bottom: var(--spacing-normal);
  border-bottom: 1px solid var(--border-color);
  margin-bottom: var(--spacing-normal);
}

.card-title {
  font-size: var(--font-size-large);
  font-weight: bold;
  color: var(--text-color);
}

.card-content {
  color: var(--text-light);
}

/* 列表样式 */
.list {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.list-item {
  padding: var(--spacing-normal);
  border-bottom: 1px solid var(--border-light);
  position: relative;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item-title {
  font-size: var(--font-size-normal);
  color: var(--text-color);
  margin-bottom: var(--spacing-small);
}

.list-item-desc {
  font-size: var(--font-size-small);
  color: var(--text-light);
}

.list-item-arrow {
  position: absolute;
  right: var(--spacing-normal);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-lighter);
}

/* 表单样式 */
.form-group {
  margin-bottom: var(--spacing-normal);
}

.form-label {
  display: block;
  font-size: var(--font-size-normal);
  color: var(--text-color);
  margin-bottom: var(--spacing-small);
}

.form-input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-normal);
  color: var(--text-color);
  background-color: var(--bg-white);
}

.form-input:focus {
  border-color: var(--primary-color);
  outline: none;
}

.form-input-error {
  border-color: var(--danger-color);
}

.form-error-text {
  color: var(--danger-color);
  font-size: var(--font-size-small);
  margin-top: var(--spacing-small);
}

/* 徽章样式 */
.badge {
  display: inline-block;
  padding: 4rpx 12rpx;
  font-size: var(--font-size-small);
  border-radius: 20rpx;
  color: white;
  background-color: var(--danger-color);
  min-width: 32rpx;
  text-align: center;
}

.badge-primary {
  background-color: var(--primary-color);
}

.badge-secondary {
  background-color: var(--secondary-color);
}

.badge-success {
  background-color: var(--success-color);
}

.badge-info {
  background-color: var(--info-color);
}

.badge-warning {
  background-color: var(--warning-color);
}

/* 分割线 */
.divider {
  height: 1px;
  background-color: var(--border-color);
  margin: var(--spacing-normal) 0;
}

.divider-thick {
  height: 20rpx;
  background-color: var(--bg-color);
  margin: 0;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-large);
  color: var(--text-lighter);
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid var(--border-color);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: var(--spacing-small);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 空状态 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xlarge);
  color: var(--text-lighter);
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: var(--spacing-normal);
  opacity: 0.6;
}

.empty-text {
  font-size: var(--font-size-normal);
}

/* 固定底部 */
.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: var(--bg-white);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-normal);
}
