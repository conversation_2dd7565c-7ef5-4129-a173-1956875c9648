/* styles/global-compatible.wxss */
/* 全局样式文件 - 兼容版本 */

/* 基础重置 */
view,
text,
image,
button,
input,
textarea,
scroll-view,
swiper,
picker {
  box-sizing: border-box;
}

page {
  background-color: #f5f5f5;
  color: #333333;
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu",
    "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  line-height: 1.6;
}

/* 通用容器 */
.container {
  padding: 20rpx;
}

.container-full {
  padding: 0;
}

.container-small {
  padding: 10rpx;
}

.container-large {
  padding: 30rpx;
}

/* 卡片样式 */
.card {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.card-small {
  padding: 20rpx;
  margin-bottom: 16rpx;
}

.card-large {
  padding: 40rpx;
  margin-bottom: 24rpx;
}

/* 按钮样式 */
.btn {
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  text-align: center;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.btn-primary {
  background-color: #4caf50;
  color: white;
}

.btn-secondary {
  background-color: #ffc107;
  color: white;
}

.btn-outline {
  background-color: transparent;
  border: 2rpx solid #4caf50;
  color: #4caf50;
}

.btn-small {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.btn-large {
  padding: 24rpx 48rpx;
  font-size: 32rpx;
}

.btn-disabled {
  background-color: #ccc;
  color: #999;
  opacity: 0.6;
}

/* 文本样式 */
.text-primary {
  color: #4caf50;
}

.text-secondary {
  color: #666666;
}

.text-disabled {
  color: #999999;
}

.text-error {
  color: #f44336;
}

.text-warning {
  color: #ff9800;
}

.text-info {
  color: #2196f3;
}

.text-success {
  color: #4caf50;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-bold {
  font-weight: bold;
}

.text-normal {
  font-weight: normal;
}

.text-small {
  font-size: 22rpx;
}

.text-medium {
  font-size: 26rpx;
}

.text-large {
  font-size: 30rpx;
}

.text-xl {
  font-size: 34rpx;
}

.text-xxl {
  font-size: 38rpx;
}

/* 布局样式 */
.flex {
  display: flex;
}

.flex-row {
  flex-direction: row;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

.align-center {
  align-items: center;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

/* 边距样式 */
.m-0 {
  margin: 0;
}
.mt-0 {
  margin-top: 0;
}
.mr-0 {
  margin-right: 0;
}
.mb-0 {
  margin-bottom: 0;
}
.ml-0 {
  margin-left: 0;
}

.m-1 {
  margin: 10rpx;
}
.mt-1 {
  margin-top: 10rpx;
}
.mr-1 {
  margin-right: 10rpx;
}
.mb-1 {
  margin-bottom: 10rpx;
}
.ml-1 {
  margin-left: 10rpx;
}

.m-2 {
  margin: 20rpx;
}
.mt-2 {
  margin-top: 20rpx;
}
.mr-2 {
  margin-right: 20rpx;
}
.mb-2 {
  margin-bottom: 20rpx;
}
.ml-2 {
  margin-left: 20rpx;
}

.m-3 {
  margin: 30rpx;
}
.mt-3 {
  margin-top: 30rpx;
}
.mr-3 {
  margin-right: 30rpx;
}
.mb-3 {
  margin-bottom: 30rpx;
}
.ml-3 {
  margin-left: 30rpx;
}

.p-0 {
  padding: 0;
}
.pt-0 {
  padding-top: 0;
}
.pr-0 {
  padding-right: 0;
}
.pb-0 {
  padding-bottom: 0;
}
.pl-0 {
  padding-left: 0;
}

.p-1 {
  padding: 10rpx;
}
.pt-1 {
  padding-top: 10rpx;
}
.pr-1 {
  padding-right: 10rpx;
}
.pb-1 {
  padding-bottom: 10rpx;
}
.pl-1 {
  padding-left: 10rpx;
}

.p-2 {
  padding: 20rpx;
}
.pt-2 {
  padding-top: 20rpx;
}
.pr-2 {
  padding-right: 20rpx;
}
.pb-2 {
  padding-bottom: 20rpx;
}
.pl-2 {
  padding-left: 20rpx;
}

.p-3 {
  padding: 30rpx;
}
.pt-3 {
  padding-top: 30rpx;
}
.pr-3 {
  padding-right: 30rpx;
}
.pb-3 {
  padding-bottom: 30rpx;
}
.pl-3 {
  padding-left: 30rpx;
}

/* 边框样式 */
.border {
  border: 1rpx solid #e0e0e0;
}

.border-top {
  border-top: 1rpx solid #e0e0e0;
}

.border-bottom {
  border-bottom: 1rpx solid #e0e0e0;
}

.border-left {
  border-left: 1rpx solid #e0e0e0;
}

.border-right {
  border-right: 1rpx solid #e0e0e0;
}

.border-radius {
  border-radius: 8rpx;
}

.border-radius-small {
  border-radius: 4rpx;
}

.border-radius-large {
  border-radius: 16rpx;
}

.border-radius-round {
  border-radius: 50%;
}

/* 阴影样式 */
.shadow {
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.shadow-small {
  box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.1);
}

.shadow-large {
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 显示控制 */
.show {
  display: block;
}

.hide {
  display: none;
}

.invisible {
  visibility: hidden;
}

.visible {
  visibility: visible;
}

/* 位置样式 */
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

/* 宽度高度 */
.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}

.w-auto {
  width: auto;
}

.h-auto {
  height: auto;
}

/* 表单样式 */
.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  margin-bottom: 10rpx;
  font-size: 26rpx;
  color: #333333;
  font-weight: bold;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.form-input.error {
  border-color: #f44336;
}

.form-error {
  color: #f44336;
  font-size: 22rpx;
  margin-top: 10rpx;
}

/* 列表样式 */
.list-item {
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.list-item:last-child {
  border-bottom: none;
}

/* 徽章样式 */
.badge {
  display: inline-block;
  padding: 4rpx 12rpx;
  font-size: 20rpx;
  font-weight: bold;
  border-radius: 20rpx;
  background-color: #f44336;
  color: white;
  min-width: 30rpx;
  text-align: center;
  line-height: 1.2;
}

.badge-primary {
  background-color: #4caf50;
}

.badge-secondary {
  background-color: #ffc107;
}

.badge-success {
  background-color: #4caf50;
}

.badge-warning {
  background-color: #ff9800;
}

.badge-info {
  background-color: #2196f3;
}

/* 分割线 */
.divider {
  height: 1rpx;
  background-color: #f0f0f0;
  margin: 20rpx 0;
}

.divider-thick {
  height: 2rpx;
  background-color: #e0e0e0;
}

/* 价格样式 */
.price {
  color: #f44336;
  font-weight: bold;
}

.price-symbol {
  font-size: 22rpx;
}

.price-integer {
  font-size: 32rpx;
}

.price-decimal {
  font-size: 24rpx;
}

.price-original {
  color: #999999;
  text-decoration: line-through;
  font-size: 24rpx;
  margin-left: 10rpx;
}

/* 动画样式 */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.bounce {
  animation: bounce 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}

/* 响应式设计 - 小屏幕样式类 */
.container-small-screen {
  padding: 16rpx;
}

.card-small-screen {
  padding: 20rpx;
}

.btn-small-screen {
  padding: 16rpx 32rpx;
  font-size: 26rpx;
}
