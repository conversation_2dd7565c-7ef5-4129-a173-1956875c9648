/* styles/modern-buttons.wxss */
/* 现代化按钮样式 */

/* 主要按钮 - 淡绿色渐变 */
.btn-primary {
  background: linear-gradient(135deg, #86efac, #6ee7b7);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 28rpx 40rpx;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 6rpx 20rpx rgba(52, 211, 153, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-primary:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 15rpx rgba(52, 211, 153, 0.4);
}

.btn-primary:active::before {
  left: 100%;
}

/* 次要按钮 - 淡绿色边框 */
.btn-secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #059669;
  border: 2rpx solid #86efac;
  border-radius: 50rpx;
  padding: 26rpx 38rpx;
  font-size: 30rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 15rpx rgba(52, 211, 153, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-secondary:active {
  background: rgba(134, 239, 172, 0.1);
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(52, 211, 153, 0.2);
}

/* 小按钮 */
.btn-small {
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  border-radius: 30rpx;
}

/* 大按钮 */
.btn-large {
  padding: 36rpx 50rpx;
  font-size: 36rpx;
  border-radius: 60rpx;
}

/* 禁用状态 */
.btn-disabled {
  background: linear-gradient(135deg, #e5e7eb, #d1d5db) !important;
  color: #9ca3af !important;
  border-color: #e5e7eb !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1) !important;
  transform: none !important;
}

/* 成功按钮 */
.btn-success {
  background: linear-gradient(135deg, #34d399, #10b981);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 28rpx 40rpx;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 6rpx 20rpx rgba(16, 185, 129, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-success:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 15rpx rgba(16, 185, 129, 0.4);
}

/* 警告按钮 */
.btn-warning {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 28rpx 40rpx;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 6rpx 20rpx rgba(245, 158, 11, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-warning:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 15rpx rgba(245, 158, 11, 0.4);
}

/* 危险按钮 */
.btn-danger {
  background: linear-gradient(135deg, #f87171, #ef4444);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 28rpx 40rpx;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 6rpx 20rpx rgba(239, 68, 68, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-danger:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 15rpx rgba(239, 68, 68, 0.4);
}

/* 幽灵按钮 */
.btn-ghost {
  background: transparent;
  color: #059669;
  border: 2rpx solid rgba(134, 239, 172, 0.5);
  border-radius: 50rpx;
  padding: 26rpx 38rpx;
  font-size: 30rpx;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-ghost:active {
  background: rgba(134, 239, 172, 0.1);
  border-color: #86efac;
  transform: scale(0.98);
}

/* 圆形按钮 */
.btn-circle {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

/* 浮动操作按钮 */
.btn-fab {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #86efac, #6ee7b7);
  color: white;
  border: none;
  font-size: 40rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 25rpx rgba(52, 211, 153, 0.4);
  position: fixed;
  bottom: 200rpx;
  right: 60rpx;
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-fab:active {
  transform: scale(0.95);
  box-shadow: 0 6rpx 20rpx rgba(52, 211, 153, 0.5);
}

/* 按钮组 */
.btn-group {
  display: flex;
  gap: 20rpx;
  align-items: center;
}

.btn-group .btn-primary,
.btn-group .btn-secondary {
  flex: 1;
}

/* 加载状态 */
.btn-loading {
  position: relative;
  color: transparent !important;
}

.btn-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: btn-spin 1s linear infinite;
}

@keyframes btn-spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* 按钮悬浮效果 */
.btn-hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-hover-lift:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 30rpx rgba(52, 211, 153, 0.3);
}

/* 按钮波纹效果 */
.btn-ripple {
  position: relative;
  overflow: hidden;
}

.btn-ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn-ripple:active::before {
  width: 300rpx;
  height: 300rpx;
}

/* 响应式按钮 */
@media (max-width: 750rpx) {
  .btn-primary,
  .btn-secondary {
    padding: 24rpx 32rpx;
    font-size: 28rpx;
  }
  
  .btn-large {
    padding: 32rpx 40rpx;
    font-size: 32rpx;
  }
}
