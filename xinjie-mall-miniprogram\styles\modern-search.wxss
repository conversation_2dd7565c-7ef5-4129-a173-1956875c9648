/* 现代化搜索框样式 */
.modern-search {
  background: linear-gradient(135deg, rgba(74, 124, 89, 0.05), rgba(255, 255, 255, 0.98));
  backdrop-filter: blur(20rpx);
  padding: 20rpx 20rpx 15rpx;
  position: sticky;
  top: 0;
  z-index: 100;
  border-bottom: 1rpx solid rgba(74, 124, 89, 0.1);
  box-shadow: 0 2rpx 20rpx rgba(74, 124, 89, 0.08);
}

.modern-search-input {
  background: rgba(255, 255, 255, 0.98);
  border: 2rpx solid rgba(74, 124, 89, 0.2);
  border-radius: 25rpx;
  padding: 22rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 6rpx 24rpx rgba(74, 124, 89, 0.12);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.modern-search-input::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(74, 124, 89, 0.1), transparent);
  transition: left 0.6s;
}

.modern-search-input:focus-within::before {
  left: 100%;
}

.modern-search-input:focus-within {
  border-color: rgba(74, 124, 89, 0.6);
  box-shadow: 0 8rpx 32rpx rgba(74, 124, 89, 0.25);
  transform: translateY(-2rpx);
}

.modern-search-placeholder {
  color: #999;
  font-size: 28rpx;
  font-weight: 400;
  flex: 1;
  letter-spacing: 0.5rpx;
}

.modern-search-icon {
  width: 36rpx;
  height: 36rpx;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.modern-search-input:focus-within .modern-search-icon {
  opacity: 1;
  transform: scale(1.1);
}

/* 搜索建议框 */
.search-suggestions {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20rpx);
  border-radius: 16rpx;
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
  margin-top: 10rpx;
  max-height: 400rpx;
  overflow-y: auto;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
}

.search-suggestion-item {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
}

.search-suggestion-item:last-child {
  border-bottom: none;
}

.search-suggestion-item:active {
  background: rgba(102, 126, 234, 0.05);
  transform: scale(0.98);
}

.search-suggestion-text {
  color: #2c3e50;
  font-size: 28rpx;
  font-weight: 500;
}

/* 搜索历史 */
.search-history {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid rgba(102, 126, 234, 0.1);
}

.search-history-title {
  color: #7f8c8d;
  font-size: 24rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.search-history-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.search-history-tag {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.search-history-tag:active {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(0.95);
}

/* 热门搜索 */
.hot-search {
  padding: 20rpx 30rpx;
}

.hot-search-title {
  color: #7f8c8d;
  font-size: 24rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.hot-search-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.hot-search-item {
  display: flex;
  align-items: center;
  padding: 12rpx 0;
  transition: all 0.3s ease;
}

.hot-search-rank {
  width: 40rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 20rpx;
}

.hot-search-rank.top3 {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.hot-search-text {
  color: #2c3e50;
  font-size: 28rpx;
  font-weight: 500;
  flex: 1;
}

.hot-search-count {
  color: #95a5a6;
  font-size: 24rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .modern-search {
    padding: 20rpx 10rpx 15rpx;
  }
  
  .modern-search-input {
    padding: 20rpx 25rpx;
  }
  
  .modern-search-placeholder {
    font-size: 26rpx;
  }
} 