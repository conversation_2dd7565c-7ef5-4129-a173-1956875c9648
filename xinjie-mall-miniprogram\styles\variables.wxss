/* 颜色变量 - 茶叶主题配色 */
:root {
  /* 主色调 - 茶绿色系 */
  --primary-color: #4a7c59;
  --primary-light: #6b9a7a;
  --primary-dark: #3d6b4a;

  /* 辅助色 - 温暖茶色系 */
  --secondary-color: #d4a574;
  --secondary-light: #e6c19a;
  --secondary-dark: #b8925f;

  /* 状态色 */
  --success-color: #4a7c59;
  --info-color: #5a9fd4;
  --warning-color: #d4a574;
  --danger-color: #d45a5a;

  /* 文字颜色 */
  --text-color: #2c3e50;
  --text-light: #666;
  --text-lighter: #999;
  --text-disabled: #ccc;

  /* 背景色 - 茶叶主题 */
  --bg-color: #f8f9fa;
  --bg-white: #ffffff;
  --bg-light: #fafbfc;
  --bg-dark: #2c3e50;

  /* 边框颜色 */
  --border-color: rgba(74, 124, 89, 0.1);
  --border-light: rgba(74, 124, 89, 0.05);
  --border-dark: rgba(74, 124, 89, 0.2);

  /* 阴影 - 茶叶主题 */
  --shadow-light: 0 4rpx 16rpx rgba(74, 124, 89, 0.08);
  --shadow-normal: 0 6rpx 24rpx rgba(74, 124, 89, 0.12);
  --shadow-dark: 0 8rpx 32rpx rgba(74, 124, 89, 0.16);

  /* 圆角 */
  --border-radius: 8rpx;
  --border-radius-small: 4rpx;
  --border-radius-large: 16rpx;

  /* 字体大小 */
  --font-size-small: 24rpx;
  --font-size-normal: 28rpx;
  --font-size-large: 32rpx;
  --font-size-xlarge: 36rpx;
  --font-size-xxlarge: 40rpx;

  /* 间距 */
  --spacing-small: 8rpx;
  --spacing-normal: 16rpx;
  --spacing-large: 24rpx;
  --spacing-xlarge: 32rpx;

  /* 高度 */
  --height-small: 60rpx;
  --height-normal: 80rpx;
  --height-large: 100rpx;

  /* 宽度 */
  --width-small: 120rpx;
  --width-normal: 160rpx;
  --width-large: 200rpx;
}
