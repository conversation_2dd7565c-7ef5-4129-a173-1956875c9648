/**
 * 微信小程序登录工具类
 * 实现静默登录 + 业务登录的组合方式
 */

const { API } = require('../config/api.js');

/**
 * 静默登录 - 获取用户身份标识
 * 在用户打开小程序时自动执行，无需用户授权
 */
const silentLogin = () => {
  return new Promise((resolve, reject) => {
    wx.login({
      success: (res) => {
        if (res.code) {
          console.log('静默登录开始，code:', res.code);
          
          // 发送 code 到后端换取 openid
          wx.request({
            url: API.auth.silentLogin,
            method: 'POST',
            data: { 
              code: res.code,
              type: 'silent'
            },
            success: (loginRes) => {
              if (loginRes.data.code === 200) {
                const { token, openid, isNewUser } = loginRes.data.data;
                
                // 保存登录信息
                wx.setStorageSync('token', token);
                wx.setStorageSync('openid', openid);
                wx.setStorageSync('isNewUser', isNewUser);
                wx.setStorageSync('loginTime', Date.now());
                
                console.log('静默登录成功，openid:', openid);
                resolve(loginRes.data.data);
              } else {
                console.error('静默登录失败:', loginRes.data.message);
                reject(loginRes.data.message);
              }
            },
            fail: (error) => {
              console.error('静默登录请求失败:', error);
              reject('网络请求失败');
            }
          });
        } else {
          console.error('获取 code 失败');
          reject('获取登录凭证失败');
        }
      },
      fail: (error) => {
        console.error('wx.login 失败:', error);
        reject('微信登录失败');
      }
    });
  });
};

/**
 * 业务登录 - 获取用户详细信息
 * 需要用户主动授权，用于完善用户资料
 * 注意：此函数必须在用户点击事件中调用
 */
const businessLogin = () => {
  return new Promise((resolve, reject) => {
    // 先检查是否已经登录
    const token = wx.getStorageSync('token');
    if (!token) {
      reject('用户未登录，请先执行静默登录');
      return;
    }

    // 如果已经登录，获取用户信息
    getUserProfile().then(resolve).catch(reject);
  });
};

/**
 * 获取用户信息
 */
const getUserProfile = () => {
  return new Promise((resolve, reject) => {
    wx.getUserProfile({
      desc: '用于完善会员资料和订单信息',
      success: (res) => {
        const userInfo = res.userInfo;
        console.log('获取用户信息成功:', userInfo);
        
        // 将用户信息发送到后端
        wx.request({
          url: API.auth.updateUserInfo,
          method: 'POST',
          header: {
            'Authorization': `Bearer ${wx.getStorageSync('token')}`
          },
          data: {
            userInfo: userInfo
          },
          success: (updateRes) => {
            if (updateRes.data.code === 200) {
              // 保存用户信息
              wx.setStorageSync('userInfo', userInfo);
              wx.setStorageSync('isNewUser', false);
              
              console.log('用户信息更新成功');
              resolve(updateRes.data.data);
            } else {
              console.error('用户信息更新失败:', updateRes.data.message);
              reject(updateRes.data.message);
            }
          },
          fail: (error) => {
            console.error('用户信息更新请求失败:', error);
            reject('网络请求失败');
          }
        });
      },
      fail: (error) => {
        console.error('获取用户信息失败:', error);
        reject('用户拒绝授权');
      }
    });
  });
};

/**
 * 检查登录状态
 */
const checkLoginStatus = () => {
  const token = wx.getStorageSync('token');
  const loginTime = wx.getStorageSync('loginTime');
  const now = Date.now();
  
  // 检查 token 是否存在且未过期（7天）
  if (token && loginTime && (now - loginTime) < 7 * 24 * 60 * 60 * 1000) {
    return true;
  }
  
  // 清除过期信息
  wx.removeStorageSync('token');
  wx.removeStorageSync('openid');
  wx.removeStorageSync('userInfo');
  wx.removeStorageSync('loginTime');

  return false;
};

/**
 * 获取用户信息（已登录状态）
 */
const getUserInfo = () => {
  return wx.getStorageSync('userInfo') || null;
};

/**
 * 检查是否为新用户
 */
const isNewUser = () => {
  return wx.getStorageSync('isNewUser') || false;
};

/**
 * 退出登录
 */
const logout = () => {
  wx.removeStorageSync('token');
  wx.removeStorageSync('openid');
  wx.removeStorageSync('userInfo');
  wx.removeStorageSync('loginTime');
  wx.removeStorageSync('isNewUser');
  
  console.log('用户已退出登录');
};

/**
 * 自动登录（在 app.js 中调用）
 */
const autoLogin = () => {
  return new Promise((resolve, reject) => {
    // 检查登录状态
    if (checkLoginStatus()) {
      console.log('用户已登录，无需重新登录');
      resolve({
        isLoggedIn: true,
        userInfo: getUserInfo(),
        isNewUser: isNewUser()
      });
      return;
    }
    
    // 执行静默登录
    silentLogin().then((data) => {
      resolve({
        isLoggedIn: true,
        userInfo: null,
        isNewUser: data.isNewUser
      });
    }).catch((error) => {
      console.error('自动登录失败:', error);
      resolve({
        isLoggedIn: false,
        userInfo: null,
        isNewUser: false
      });
    });
  });
};

/**
 * 需要用户信息的操作（如下单、查看订单等）
 */
const requireUserInfo = () => {
  return new Promise((resolve, reject) => {
    const userInfo = getUserInfo();
    
    if (userInfo) {
      // 已有用户信息
      resolve(userInfo);
    } else {
      // 需要获取用户信息
      wx.showModal({
        title: '完善资料',
        content: '为了更好地为您服务，需要获取您的头像和昵称信息',
        confirmText: '确定',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            businessLogin().then(resolve).catch(reject);
          } else {
            reject('用户拒绝授权');
          }
        }
      });
    }
  });
};

/**
 * 需要登录的操作（如加入购物车、下单等）
 */
const requireLogin = () => {
  return new Promise((resolve, reject) => {
    // 检查登录状态
    if (checkLoginStatus()) {
      resolve();
    } else {
      // 未登录，执行静默登录
      silentLogin().then(() => {
        resolve();
      }).catch((error) => {
        console.error('登录失败:', error);
        reject('登录失败');
      });
    }
  });
};

// 导出所有函数
module.exports = {
  silentLogin,
  businessLogin,
  login: businessLogin, // 为了兼容性，添加 login 别名
  checkLoginStatus,
  getUserInfo,
  isNewUser,
  logout,
  autoLogin,
  requireUserInfo,
  requireLogin
};
