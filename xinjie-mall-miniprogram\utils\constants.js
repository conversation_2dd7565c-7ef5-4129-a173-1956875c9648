// 常量定义

// 订单状态
const ORDER_STATUS = {
  PENDING: "pending", // 待付款
  PAID: "paid", // 待发货
  SHIPPED: "shipped", // 待收货
  DELIVERED: "delivered", // 待评价
  COMPLETED: "completed", // 已完成
  CANCELLED: "cancelled", // 已取消
  REFUNDED: "refunded", // 已退款
};

// 数字订单状态（与后端对应）
const ORDER_STATUS_NUMBER = {
  PENDING: 0, // 待付款
  PAID: 1, // 待发货
  SHIPPED: 2, // 待收货
  COMPLETED: 3, // 已完成
  CANCELLED: 4, // 已取消
};

// 订单状态文本
const ORDER_STATUS_TEXT = {
  [ORDER_STATUS.PENDING]: "待付款",
  [ORDER_STATUS.PAID]: "待发货",
  [ORDER_STATUS.SHIPPED]: "待收货",
  [ORDER_STATUS.DELIVERED]: "待评价",
  [ORDER_STATUS.COMPLETED]: "已完成",
  [ORDER_STATUS.CANCELLED]: "已取消",
  [ORDER_STATUS.REFUNDED]: "已退款",
};

// 数字订单状态文本
const ORDER_STATUS_NUMBER_TEXT = {
  [ORDER_STATUS_NUMBER.PENDING]: "待付款",
  [ORDER_STATUS_NUMBER.PAID]: "待发货",
  [ORDER_STATUS_NUMBER.SHIPPED]: "待收货",
  [ORDER_STATUS_NUMBER.COMPLETED]: "已完成",
  [ORDER_STATUS_NUMBER.CANCELLED]: "已取消",
};

// 支付方式
const PAYMENT_METHOD = {
  WECHAT: "wechat", // 微信支付
  ALIPAY: "alipay", // 支付宝
  BALANCE: "balance", // 余额支付
  COD: "cod", // 货到付款
};

// 支付方式文本
const PAYMENT_METHOD_TEXT = {
  [PAYMENT_METHOD.WECHAT]: "微信支付",
  [PAYMENT_METHOD.ALIPAY]: "支付宝",
  [PAYMENT_METHOD.BALANCE]: "余额支付",
  [PAYMENT_METHOD.COD]: "货到付款",
};

// 商品状态
const PRODUCT_STATUS = {
  ACTIVE: "active", // 上架
  INACTIVE: "inactive", // 下架
  DRAFT: "draft", // 草稿
  DELETED: "deleted", // 已删除
};

// 商品状态文本
const PRODUCT_STATUS_TEXT = {
  [PRODUCT_STATUS.ACTIVE]: "上架",
  [PRODUCT_STATUS.INACTIVE]: "下架",
  [PRODUCT_STATUS.DRAFT]: "草稿",
  [PRODUCT_STATUS.DELETED]: "已删除",
};

// 用户类型
const USER_TYPE = {
  CUSTOMER: "customer", // 客户
  ADMIN: "admin", // 管理员
};

// 性别
const GENDER = {
  MALE: "male", // 男
  FEMALE: "female", // 女
  UNKNOWN: "unknown", // 未知
};

// 性别文本
const GENDER_TEXT = {
  [GENDER.MALE]: "男",
  [GENDER.FEMALE]: "女",
  [GENDER.UNKNOWN]: "未知",
};

// 优惠券类型
const COUPON_TYPE = {
  FIXED: "fixed", // 固定金额
  PERCENT: "percent", // 百分比
  SHIPPING: "shipping", // 免邮费
};

// 优惠券类型文本
const COUPON_TYPE_TEXT = {
  [COUPON_TYPE.FIXED]: "固定金额",
  [COUPON_TYPE.PERCENT]: "百分比",
  [COUPON_TYPE.SHIPPING]: "免邮费",
};

// 活动状态
const ACTIVITY_STATUS = {
  PENDING: "pending", // 待开始
  ACTIVE: "active", // 进行中
  ENDED: "ended", // 已结束
  CANCELLED: "cancelled", // 已取消
};

// 分页配置
const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 50,
};

// 图片尺寸
const IMAGE_SIZE = {
  THUMB: "thumb", // 缩略图
  SMALL: "small", // 小图
  MEDIUM: "medium", // 中图
  LARGE: "large", // 大图
};

// 图片尺寸配置
const IMAGE_SIZE_CONFIG = {
  [IMAGE_SIZE.THUMB]: { width: 100, height: 100 },
  [IMAGE_SIZE.SMALL]: { width: 200, height: 200 },
  [IMAGE_SIZE.MEDIUM]: { width: 400, height: 400 },
  [IMAGE_SIZE.LARGE]: { width: 800, height: 800 },
};

// 存储键名
const STORAGE_KEY = {
  TOKEN: "token",
  USER_INFO: "userInfo",
  CART: "cart",
  RECENT_SEARCH: "recentSearch",
  SETTINGS: "settings",
};

// 事件类型
const EVENT_TYPE = {
  LOGIN: "login",
  LOGOUT: "logout",
  CART_UPDATE: "cartUpdate",
  ORDER_CREATE: "orderCreate",
  ORDER_UPDATE: "orderUpdate",
};

// 错误码
const ERROR_CODE = {
  NETWORK_ERROR: "NETWORK_ERROR",
  TOKEN_EXPIRED: "TOKEN_EXPIRED",
  PERMISSION_DENIED: "PERMISSION_DENIED",
  VALIDATION_ERROR: "VALIDATION_ERROR",
  UNKNOWN_ERROR: "UNKNOWN_ERROR",
};

// 错误信息
const ERROR_MESSAGE = {
  [ERROR_CODE.NETWORK_ERROR]: "网络连接失败",
  [ERROR_CODE.TOKEN_EXPIRED]: "登录已过期",
  [ERROR_CODE.PERMISSION_DENIED]: "没有权限",
  [ERROR_CODE.VALIDATION_ERROR]: "数据验证失败",
  [ERROR_CODE.UNKNOWN_ERROR]: "未知错误",
};

// 颜色配置
const COLORS = {
  PRIMARY: "#4CAF50",
  SECONDARY: "#FFC107",
  SUCCESS: "#4CAF50",
  INFO: "#2196F3",
  WARNING: "#FF9800",
  DANGER: "#F44336",
  LIGHT: "#F5F5F5",
  DARK: "#333333",
};

// 字体大小
const FONT_SIZE = {
  SMALL: 24,
  NORMAL: 28,
  LARGE: 32,
  XLARGE: 36,
  XXLARGE: 40,
};

// 间距
const SPACING = {
  SMALL: 8,
  NORMAL: 16,
  LARGE: 24,
  XLARGE: 32,
};

// 动画时长
const ANIMATION_DURATION = {
  FAST: 200,
  NORMAL: 300,
  SLOW: 500,
};

// 默认头像
const DEFAULT_AVATAR = "/images/common/default-avatar.png";

// 默认商品图片
const DEFAULT_PRODUCT_IMAGE = "/images/common/default-product.png";

// 默认轮播图
const DEFAULT_BANNER = "/images/common/default-banner.png";

// 分类图标
const CATEGORY_ICONS = {
  GREEN_TEA: "/images/icons/green-tea.png",
  BLACK_TEA: "/images/icons/black-tea.png",
  FLOWER_TEA: "/images/icons/flower-tea.png",
  OOLONG_TEA: "/images/icons/oolong-tea.png",
  PUER_TEA: "/images/icons/puer-tea.png",
};

// 正则表达式
const REGEX = {
  PHONE: /^1[3-9]\d{9}$/,
  EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  PASSWORD: /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]{6,20}$/,
  USERNAME: /^[\u4e00-\u9fa5a-zA-Z0-9_]{2,20}$/,
  ID_CARD:
    /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
  CHINESE_NAME: /^[\u4e00-\u9fa5]{2,4}$/,
  PRICE: /^\d+(\.\d{1,2})?$/,
  INTEGER: /^[1-9]\d*$/,
  POSITIVE_INTEGER: /^\d+$/,
};

module.exports = {
  ORDER_STATUS,
  ORDER_STATUS_NUMBER,
  ORDER_STATUS_TEXT,
  ORDER_STATUS_NUMBER_TEXT,
  PAYMENT_METHOD,
  PAYMENT_METHOD_TEXT,
  PRODUCT_STATUS,
  PRODUCT_STATUS_TEXT,
  USER_TYPE,
  GENDER,
  GENDER_TEXT,
  COUPON_TYPE,
  COUPON_TYPE_TEXT,
  ACTIVITY_STATUS,
  PAGINATION,
  IMAGE_SIZE,
  IMAGE_SIZE_CONFIG,
  STORAGE_KEY,
  EVENT_TYPE,
  ERROR_CODE,
  ERROR_MESSAGE,
  COLORS,
  FONT_SIZE,
  SPACING,
  ANIMATION_DURATION,
  DEFAULT_AVATAR,
  DEFAULT_PRODUCT_IMAGE,
  DEFAULT_BANNER,
  CATEGORY_ICONS,
  REGEX,
};
