// 图片URL调试工具
const { getCurrentEnv } = require('../config/env');

/**
 * 调试图片URL生成
 * @param {string} imagePath - 图片路径
 * @param {string} type - 图片类型 (banner, product, category)
 * @returns {object} 调试信息
 */
const debugImageUrl = (imagePath, type = 'unknown') => {
  const env = getCurrentEnv();
  const debugInfo = {
    originalPath: imagePath,
    type: type,
    env: env,
    timestamp: new Date().toISOString()
  };

  console.log('🔍 图片URL调试信息:', debugInfo);

  // 检查图片路径格式
  if (!imagePath) {
    console.warn('⚠️ 图片路径为空');
    return { ...debugInfo, status: 'empty_path' };
  }

  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    console.log('✅ 已经是完整URL:', imagePath);
    return { ...debugInfo, finalUrl: imagePath, status: 'complete_url' };
  }

  if (imagePath.startsWith('/uploads/')) {
    // 🚨 临时强制使用正确的URL
    const FORCE_BASE_URL = 'http://localhost:4000';
    const finalUrl = `${FORCE_BASE_URL}${imagePath}`;
    console.log('🔧 强制拼接uploads路径:', finalUrl);
    return { ...debugInfo, finalUrl: finalUrl, status: 'uploads_path' };
  }

  if (imagePath.startsWith('/images/')) {
    console.log('✅ 本地图片路径:', imagePath);
    return { ...debugInfo, finalUrl: imagePath, status: 'local_image' };
  }

  // 默认处理
  // 🚨 临时强制使用正确的URL
  const FORCE_BASE_URL = 'http://localhost:4000';
  const finalUrl = `${FORCE_BASE_URL}/uploads/${type}/${imagePath}`;
  console.log('🔧 强制默认拼接路径:', finalUrl);
  return { ...debugInfo, finalUrl: finalUrl, status: 'default_concat' };
};

/**
 * 测试图片URL是否可访问
 * @param {string} imageUrl - 图片URL
 * @returns {Promise<boolean>} 是否可访问
 */
const testImageUrl = (imageUrl) => {
  return new Promise((resolve) => {
    // 🚨 临时禁用图片测试功能，避免4443端口错误
    console.log('🔧 图片测试已禁用:', imageUrl);
    resolve(true);
    return;

    // 原始代码（暂时禁用）
    /*
    if (!imageUrl) {
      resolve(false);
      return;
    }

    // 在小程序中测试图片是否可访问
    if (typeof wx !== 'undefined') {
      wx.getImageInfo({
        src: imageUrl,
        success: () => {
          console.log('✅ 图片可访问:', imageUrl);
          resolve(true);
        },
        fail: (err) => {
          console.error('❌ 图片无法访问:', imageUrl, err);
          resolve(false);
        }
      });
    } else {
      // 非小程序环境，直接返回true
      resolve(true);
    }
    */
  });
};

/**
 * 批量测试图片URL
 * @param {Array} images - 图片数组
 * @param {string} pathKey - 图片路径字段名
 * @returns {Promise<Array>} 测试结果
 */
const batchTestImages = async (images, pathKey = 'image_url') => {
  const results = [];
  
  for (const image of images) {
    const imagePath = image[pathKey];
    const debugInfo = debugImageUrl(imagePath, 'batch_test');
    const isAccessible = await testImageUrl(debugInfo.finalUrl);
    
    results.push({
      ...image,
      debugInfo,
      isAccessible,
      testedUrl: debugInfo.finalUrl
    });
  }
  
  console.log('📊 批量测试结果:', results);
  return results;
};

module.exports = {
  debugImageUrl,
  testImageUrl,
  batchTestImages
};
