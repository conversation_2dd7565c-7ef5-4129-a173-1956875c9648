// 格式化工具函数

// 格式化价格
const formatPrice = (price) => {
  if (price === null || price === undefined) {
    return "¥0.00";
  }

  const num = parseFloat(price);
  if (isNaN(num)) {
    return "¥0.00";
  }

  return "¥" + num.toFixed(2);
};

// 格式化数量
const formatQuantity = (quantity) => {
  if (quantity === null || quantity === undefined) {
    return "0";
  }

  const num = parseInt(quantity);
  if (isNaN(num) || num < 0) {
    return "0";
  }

  return num.toString();
};

// 格式化日期
const formatDate = (date, format = "YYYY-MM-DD") => {
  if (!date) return "";

  const d = new Date(date);
  if (isNaN(d.getTime())) return "";

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, "0");
  const day = String(d.getDate()).padStart(2, "0");
  const hours = String(d.getHours()).padStart(2, "0");
  const minutes = String(d.getMinutes()).padStart(2, "0");
  const seconds = String(d.getSeconds()).padStart(2, "0");

  const formatMap = {
    YYYY: year,
    MM: month,
    DD: day,
    HH: hours,
    mm: minutes,
    ss: seconds,
  };

  return format.replace(/YYYY|MM|DD|HH|mm|ss/g, (match) => formatMap[match]);
};

// 格式化时间（相对时间）
const formatRelativeTime = (date) => {
  if (!date) return "";

  const d = new Date(date);
  if (isNaN(d.getTime())) return "";

  const now = new Date();
  const diff = now.getTime() - d.getTime();

  const minute = 60 * 1000;
  const hour = 60 * minute;
  const day = 24 * hour;
  const week = 7 * day;
  const month = 30 * day;
  const year = 365 * day;

  if (diff < minute) {
    return "刚刚";
  } else if (diff < hour) {
    return Math.floor(diff / minute) + "分钟前";
  } else if (diff < day) {
    return Math.floor(diff / hour) + "小时前";
  } else if (diff < week) {
    return Math.floor(diff / day) + "天前";
  } else if (diff < month) {
    return Math.floor(diff / week) + "周前";
  } else if (diff < year) {
    return Math.floor(diff / month) + "个月前";
  } else {
    return Math.floor(diff / year) + "年前";
  }
};

// 格式化手机号
const formatPhone = (phone) => {
  if (!phone) return "";

  const cleanPhone = phone.replace(/\D/g, "");
  if (cleanPhone.length === 11) {
    return cleanPhone.replace(/(\d{3})(\d{4})(\d{4})/, "$1 $2 $3");
  }

  return phone;
};

// 隐藏手机号中间四位
const hidePhoneMiddle = (phone) => {
  if (!phone) return "";

  const cleanPhone = phone.replace(/\D/g, "");
  if (cleanPhone.length === 11) {
    return cleanPhone.replace(/(\d{3})(\d{4})(\d{4})/, "$1****$3");
  }

  return phone;
};

// 格式化订单号
const formatOrderNo = (orderNo) => {
  if (!orderNo) return "";

  return orderNo.replace(/(.{4})/g, "$1 ").trim();
};

// 格式化文件大小
const formatFileSize = (size) => {
  if (size === 0) return "0 B";

  const units = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(size) / Math.log(1024));

  return (size / Math.pow(1024, i)).toFixed(2) + " " + units[i];
};

// 格式化数字（添加千分位分隔符）
const formatNumber = (number) => {
  if (number === null || number === undefined) {
    return "0";
  }

  const num = parseFloat(number);
  if (isNaN(num)) {
    return "0";
  }

  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

// 格式化百分比
const formatPercent = (value, total) => {
  if (total === 0) return "0%";

  const percent = (value / total) * 100;
  return percent.toFixed(1) + "%";
};

// 文本截断
const truncateText = (text, maxLength = 50) => {
  if (!text) return "";

  if (text.length <= maxLength) {
    return text;
  }

  return text.substring(0, maxLength) + "...";
};

// 格式化地址
const formatAddress = (address) => {
  if (!address) return "";

  const { province, city, district, detail } = address;
  return [province, city, district, detail].filter(Boolean).join("");
};

// 格式化订单状态
const formatOrderStatus = (status) => {
  const statusMap = {
    pending: "待付款",
    paid: "待发货",
    shipped: "待收货",
    delivered: "待评价",
    completed: "已完成",
    cancelled: "已取消",
    refunded: "已退款",
  };

  return statusMap[status] || status;
};

// 格式化支付方式
const formatPaymentMethod = (method) => {
  const methodMap = {
    wechat: "微信支付",
    alipay: "支付宝",
    balance: "余额支付",
    cod: "货到付款",
  };

  return methodMap[method] || method;
};

// 格式化商品规格
const formatProductSpec = (specs) => {
  if (!specs || specs.length === 0) return "";

  return specs.map((spec) => `${spec.name}:${spec.value}`).join(" ");
};

// 格式化评分
const formatRating = (rating) => {
  if (rating === null || rating === undefined) {
    return "0.0";
  }

  const num = parseFloat(rating);
  if (isNaN(num)) {
    return "0.0";
  }

  return num.toFixed(1);
};

module.exports = {
  formatPrice,
  formatQuantity,
  formatDate,
  formatRelativeTime,
  formatPhone,
  hidePhoneMiddle,
  formatOrderNo,
  formatFileSize,
  formatNumber,
  formatPercent,
  truncateText,
  formatAddress,
  formatOrderStatus,
  formatPaymentMethod,
  formatProductSpec,
  formatRating,
};
