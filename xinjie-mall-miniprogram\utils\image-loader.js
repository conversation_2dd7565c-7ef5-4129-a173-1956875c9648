/**
 * 图片预加载工具
 * 用于解决微信小程序加载HTTPS图片的问题
 */

class ImageLoader {
  /**
   * 预加载图片
   * @param {string} imageUrl - 图片URL
   * @returns {Promise} 返回图片信息
   */
  static preloadImage(imageUrl) {
    return new Promise((resolve, reject) => {
      if (!imageUrl) {
        reject(new Error('图片URL不能为空'));
        return;
      }

      // 确保URL是HTTPS
      let secureUrl = imageUrl;
      if (secureUrl.startsWith('http://')) {
        secureUrl = secureUrl.replace('http://', 'https://');
      }

      wx.getImageInfo({
        src: secureUrl,
        success: (res) => {
          console.log('✅ 图片预加载成功:', secureUrl, res);
          resolve({
            ...res,
            url: secureUrl
          });
        },
        fail: (err) => {
          console.error('❌ 图片预加载失败:', secureUrl, err);
          reject(err);
        }
      });
    });
  }

  /**
   * 批量预加载图片
   * @param {Array} imageUrls - 图片URL数组
   * @returns {Promise} 返回所有图片信息
   */
  static preloadImages(imageUrls) {
    if (!Array.isArray(imageUrls)) {
      return Promise.reject(new Error('图片URL必须是数组'));
    }

    const promises = imageUrls.map(url => this.preloadImage(url));
    return Promise.allSettled(promises);
  }

  /**
   * 检查图片是否可访问
   * @param {string} imageUrl - 图片URL
   * @returns {Promise<boolean>} 是否可访问
   */
  static checkImageAccess(imageUrl) {
    return this.preloadImage(imageUrl)
      .then(() => true)
      .catch(() => false);
  }

  /**
   * 获取图片的本地临时路径（用于显示）
   * @param {string} imageUrl - 图片URL
   * @returns {Promise<string>} 本地临时路径
   */
  static getLocalImagePath(imageUrl) {
    return this.preloadImage(imageUrl)
      .then(res => res.path);
  }
}

module.exports = ImageLoader; 