const { BASE_URL } = require('../config/api');
const { getCurrentEnv } = require('../config/env');

// 获取基础URL
const getBaseUrl = () => {
  // 开发环境强制使用HTTP协议
  const currentEnv = getCurrentEnv();
  console.log('🔍 getBaseUrl - 当前环境:', currentEnv);

  // 开发环境使用HTTP图片地址
  if (currentEnv && currentEnv.imageUrl) {
    console.log('✅ 使用环境配置的imageUrl:', currentEnv.imageUrl);
    return currentEnv.imageUrl;
  }

  // 开发环境默认使用HTTP
  const isDevelop = wx.getAccountInfoSync().miniProgram.envVersion === 'develop';
  if (isDevelop) {
    console.log('🔧 开发环境使用HTTP:', 'http://localhost:4000');
    return 'http://localhost:4000';
  }

  console.log('⚠️ 使用备选BASE_URL:', BASE_URL);
  return BASE_URL;
};

// 检查是否为开发环境
const isDevelopment = () => {
  // 微信小程序开发环境判断
  return typeof wx !== 'undefined' && wx.getSystemInfoSync;
};

// 获取完整的图片URL
const getImageUrl = (imagePath) => {
  if (!imagePath) {
    return '/images/common/default-product.png';
  }
  // 如果已经是完整的HTTP/HTTPS URL，保持原样（开发环境）
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    // 开发环境保持HTTP协议
    const accountInfo = wx.getAccountInfoSync();
    const isDevelop = accountInfo.miniProgram.envVersion === 'develop';

    if (isDevelop && imagePath.includes('localhost:4443')) {
      // 开发环境将HTTPS转回HTTP
      return imagePath.replace('https://localhost:4443', 'http://localhost:4000')
                     .replace('http://localhost:4443', 'http://localhost:4000');
    }
    return imagePath;
  }
  if (imagePath.startsWith('/images/')) {
    return imagePath;
  }
  // 如果已经包含uploads路径，直接拼接baseUrl
  if (imagePath.startsWith('/uploads/')) {
    return `${getBaseUrl()}${imagePath}`;
  }
  return `${getBaseUrl()}/uploads/${imagePath}`;
};

const getCategoryImageUrl = (imagePath) => {
  if (!imagePath) {
    return '/images/common/default-category.png';
  }
  // 如果已经是完整的HTTP/HTTPS URL，保持原样（开发环境）
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    // 开发环境保持HTTP协议
    const accountInfo = wx.getAccountInfoSync();
    const isDevelop = accountInfo.miniProgram.envVersion === 'develop';

    if (isDevelop && imagePath.includes('localhost:4443')) {
      // 开发环境将HTTPS转回HTTP
      return imagePath.replace('https://localhost:4443', 'http://localhost:4000')
                     .replace('http://localhost:4443', 'http://localhost:4000');
    }
    return imagePath;
  }
  if (imagePath.startsWith('/images/')) {
    return imagePath;
  }
  // 如果已经包含uploads/categories路径，直接拼接baseUrl
  if (imagePath.startsWith('/uploads/categories/')) {
    return `${getBaseUrl()}${imagePath}`;
  }
  // 如果已经包含uploads路径，直接拼接baseUrl
  if (imagePath.startsWith('/uploads/')) {
    return `${getBaseUrl()}${imagePath}`;
  }
  return `${getBaseUrl()}/uploads/categories/${imagePath}`;
};

const getProductImageUrl = (imagePath) => {
  if (!imagePath) {
    return '/images/common/default-product.png';
  }
  // 如果已经是完整的HTTP/HTTPS URL，保持原样（开发环境）
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    // 开发环境保持HTTP协议
    const accountInfo = wx.getAccountInfoSync();
    const isDevelop = accountInfo.miniProgram.envVersion === 'develop';

    if (isDevelop && imagePath.includes('localhost:4443')) {
      // 开发环境将HTTPS转回HTTP
      return imagePath.replace('https://localhost:4443', 'http://localhost:4000')
                     .replace('http://localhost:4443', 'http://localhost:4000');
    }
    return imagePath;
  }
  if (imagePath.startsWith('/images/')) {
    return imagePath;
  }
  // 如果已经包含uploads/products路径，直接拼接baseUrl
  if (imagePath.startsWith('/uploads/products/')) {
    return `${getBaseUrl()}${imagePath}`;
  }
  // 如果已经包含uploads路径，直接拼接baseUrl
  if (imagePath.startsWith('/uploads/')) {
    return `${getBaseUrl()}${imagePath}`;
  }
  return `${getBaseUrl()}/uploads/products/${imagePath}`;
};

const getBannerImageUrl = (imagePath) => {
  console.log('🔍 getBannerImageUrl 被调用，输入:', imagePath);

  if (!imagePath) {
    console.log('⚠️ imagePath 为空，返回默认图片');
    return '/images/common/default-banner.png';
  }

  // 如果是本地图片路径，直接返回
  if (imagePath.startsWith('/images/')) {
    console.log('✅ 本地图片路径:', imagePath);
    return imagePath;
  }

  // 如果已经是完整的HTTP/HTTPS URL，直接返回（后端已经处理好了）
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    console.log('✅ 完整URL，直接返回:', imagePath);
    return imagePath;
  }

  // 如果是相对路径，拼接baseUrl
  let finalUrl;
  if (imagePath.startsWith('/uploads/banners/')) {
    finalUrl = `${getBaseUrl()}${imagePath}`;
  } else if (imagePath.startsWith('/uploads/')) {
    finalUrl = `${getBaseUrl()}${imagePath}`;
  } else {
    finalUrl = `${getBaseUrl()}/uploads/banners/${imagePath}`;
  }

  console.log('✅ 最终URL:', finalUrl);
  return finalUrl;
};

const handleImageError = (e, defaultImage = '/images/common/default-product.png') => {
  const { currentTarget } = e;
  if (currentTarget) {
    currentTarget.src = defaultImage;
  }
};

const processImageUrls = (data, imageFields = ['image', 'main_image', 'icon', 'avatar']) => {
  if (!data) return data;
  if (Array.isArray(data)) {
    return data.map(item => processImageUrls(item, imageFields));
  }
  if (typeof data === 'object') {
    const processed = { ...data };
    imageFields.forEach(field => {
      if (processed[field]) {
        if (field === 'icon') {
          processed[field] = getCategoryImageUrl(processed[field]);
        } else if (field === 'main_image' || (field === 'image' && processed.category_id)) {
          processed[field] = getProductImageUrl(processed[field]);
        } else if (field === 'image' && processed.banner_type) {
          processed[field] = getBannerImageUrl(processed[field]);
        } else {
          processed[field] = getImageUrl(processed[field]);
        }
      }
    });
    return processed;
  }
  return data;
};

module.exports = {
  getImageUrl,
  getCategoryImageUrl,
  getProductImageUrl,
  getBannerImageUrl,
  handleImageError,
  processImageUrls
}; 