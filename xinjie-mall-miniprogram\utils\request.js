// 网络请求工具
const { getCurrentEnv } = require("../config/env");

// 请求拦截器
const requestInterceptor = (options) => {
  const app = getApp();

  // 使用环境配置中的 apiUrl 作为基础地址
  const env = getCurrentEnv();

  // 设置基础URL
  if (!options.url.startsWith('http')) {
    options.url = env.apiUrl + options.url;
  }

  // 设置请求头
  options.header = {
    "Content-Type": "application/json",
    ...options.header,
  };

  // 添加token
  const token = wx.getStorageSync('token');
  if (token) {
    options.header.Authorization = `Bearer ${token}`;
  }

  // 设置超时时间
  options.timeout = options.timeout || 10000;

  return options;
};

// 响应拦截器
const responseInterceptor = (response) => {
  const { statusCode, data } = response;
  
  console.log('响应拦截器收到数据:', { statusCode, data });

  // HTTP状态码检查
  if (statusCode >= 200 && statusCode < 300) {
    // 兼容后端code:200和code:0两种成功结构
    if (data.code === 200 || data.code === 0 || data.success) {
      console.log('API响应成功:', data);
      return data;
    } else {
      // 业务错误处理
      console.error('API业务错误:', data);
      wx.showToast({
        title: data.message || "请求失败",
        icon: "none",
      });
      return Promise.reject(data);
    }
  } else {
    // HTTP错误处理
    let errorMessage = "网络请求失败";

    switch (statusCode) {
      case 401:
        errorMessage = "登录已过期，请重新登录";
        // 清除登录信息
        wx.removeStorageSync("token");
        wx.removeStorageSync("openid");
        wx.removeStorageSync("userInfo");
        wx.removeStorageSync("loginTime");
        wx.removeStorageSync("isNewUser");
        getApp().globalData.userInfo = null;
        getApp().globalData.isLoggedIn = false;
        getApp().globalData.isNewUser = false;
        // 跳转到登录页
        wx.redirectTo({
          url: "/pages/login/login",
        });
        break;
      case 403:
        errorMessage = "没有权限访问";
        break;
      case 404:
        errorMessage = "请求的资源不存在";
        break;
      case 500:
        errorMessage = "服务器错误";
        break;
      default:
        errorMessage = `请求失败(${statusCode})`;
    }

    wx.showToast({
      title: errorMessage,
      icon: "none",
    });

    return Promise.reject({
      statusCode,
      message: errorMessage,
    });
  }
};

// 基础请求方法
const request = (options) => {
  return new Promise((resolve, reject) => {
    // 请求拦截
    const interceptedOptions = requestInterceptor(options);

    // 发送请求
    wx.request({
      ...interceptedOptions,
      success: (response) => {
        try {
          const result = responseInterceptor(response);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      },
      fail: (error) => {
        console.error("请求失败:", error);
        wx.showToast({
          title: "网络连接失败",
          icon: "none",
        });
        reject(error);
      },
    });
  });
};

// GET请求
const get = (url, params = {}) => {
  return request({
    url,
    method: "GET",
    data: params,
  });
};

// POST请求
const post = (url, data = {}) => {
  return request({
    url,
    method: "POST",
    data,
  });
};

// PUT请求
const put = (url, data = {}) => {
  return request({
    url,
    method: "PUT",
    data,
  });
};

// DELETE请求
const del = (url, params = {}) => {
  return request({
    url,
    method: "DELETE",
    data: params,
  });
};

// 上传文件
const uploadFile = (filePath, url, formData = {}) => {
  return new Promise((resolve, reject) => {
    const app = getApp();
    const env = getCurrentEnv();

    wx.uploadFile({
      url: env.apiUrl + url,
      filePath,
      name: "file",
      formData,
      header: {
        Authorization: wx.getStorageSync('token')
          ? `Bearer ${wx.getStorageSync('token')}`
          : "",
      },
      success: (response) => {
        try {
          const data = JSON.parse(response.data);
          if (data.code === 0 || data.success) {
            resolve(data);
          } else {
            wx.showToast({
              title: data.message || "上传失败",
              icon: "none",
            });
            reject(data);
          }
        } catch (error) {
          reject(error);
        }
      },
      fail: (error) => {
        console.error("上传失败:", error);
        wx.showToast({
          title: "上传失败",
          icon: "none",
        });
        reject(error);
      },
    });
  });
};

module.exports = {
  request,
  get,
  post,
  put,
  del,
  uploadFile,
};
