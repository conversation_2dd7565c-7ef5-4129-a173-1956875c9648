// 本地存储工具函数

// 设置存储
const setStorage = (key, value) => {
  return new Promise((resolve, reject) => {
    try {
      wx.setStorage({
        key,
        data: value,
        success: resolve,
        fail: reject,
      });
    } catch (error) {
      reject(error);
    }
  });
};

// 获取存储
const getStorage = (key) => {
  return new Promise((resolve, reject) => {
    try {
      wx.getStorage({
        key,
        success: (res) => resolve(res.data),
        fail: reject,
      });
    } catch (error) {
      reject(error);
    }
  });
};

// 删除存储
const removeStorage = (key) => {
  return new Promise((resolve, reject) => {
    try {
      wx.removeStorage({
        key,
        success: resolve,
        fail: reject,
      });
    } catch (error) {
      reject(error);
    }
  });
};

// 清空存储
const clearStorage = () => {
  return new Promise((resolve, reject) => {
    try {
      wx.clearStorage({
        success: resolve,
        fail: reject,
      });
    } catch (error) {
      reject(error);
    }
  });
};

// 同步设置存储
const setStorageSync = (key, value) => {
  try {
    wx.setStorageSync(key, value);
    return true;
  } catch (error) {
    console.error("设置存储失败:", error);
    return false;
  }
};

// 同步获取存储
const getStorageSync = (key, defaultValue = null) => {
  try {
    const value = wx.getStorageSync(key);
    return value !== "" ? value : defaultValue;
  } catch (error) {
    console.error("获取存储失败:", error);
    return defaultValue;
  }
};

// 同步删除存储
const removeStorageSync = (key) => {
  try {
    wx.removeStorageSync(key);
    return true;
  } catch (error) {
    console.error("删除存储失败:", error);
    return false;
  }
};

// 获取存储信息
const getStorageInfo = () => {
  return new Promise((resolve, reject) => {
    try {
      wx.getStorageInfo({
        success: resolve,
        fail: reject,
      });
    } catch (error) {
      reject(error);
    }
  });
};

// 同步获取存储信息
const getStorageInfoSync = () => {
  try {
    return wx.getStorageInfoSync();
  } catch (error) {
    console.error("获取存储信息失败:", error);
    return null;
  }
};

// 缓存管理
class CacheManager {
  constructor(prefix = "cache_") {
    this.prefix = prefix;
    this.defaultExpire = 30 * 60 * 1000; // 30分钟
  }

  // 设置缓存
  set(key, value, expire = this.defaultExpire) {
    const cacheKey = this.prefix + key;
    const cacheData = {
      value,
      expire: Date.now() + expire,
      timestamp: Date.now(),
    };

    return setStorageSync(cacheKey, cacheData);
  }

  // 获取缓存
  get(key, defaultValue = null) {
    const cacheKey = this.prefix + key;
    const cacheData = getStorageSync(cacheKey);

    if (!cacheData) {
      return defaultValue;
    }

    // 检查是否过期
    if (Date.now() > cacheData.expire) {
      this.remove(key);
      return defaultValue;
    }

    return cacheData.value;
  }

  // 删除缓存
  remove(key) {
    const cacheKey = this.prefix + key;
    return removeStorageSync(cacheKey);
  }

  // 清空所有缓存
  clear() {
    try {
      const info = getStorageInfoSync();
      if (info && info.keys) {
        info.keys.forEach((key) => {
          if (key.startsWith(this.prefix)) {
            removeStorageSync(key);
          }
        });
      }
      return true;
    } catch (error) {
      console.error("清空缓存失败:", error);
      return false;
    }
  }

  // 检查缓存是否存在且未过期
  has(key) {
    const cacheKey = this.prefix + key;
    const cacheData = getStorageSync(cacheKey);

    if (!cacheData) {
      return false;
    }

    return Date.now() <= cacheData.expire;
  }
}

// 创建默认缓存实例
const cache = new CacheManager();

module.exports = {
  setStorage,
  getStorage,
  removeStorage,
  clearStorage,
  setStorageSync,
  getStorageSync,
  removeStorageSync,
  getStorageInfo,
  getStorageInfoSync,
  CacheManager,
  cache,
};
