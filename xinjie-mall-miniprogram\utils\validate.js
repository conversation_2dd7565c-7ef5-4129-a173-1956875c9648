// 数据验证工具函数

// 验证手机号
const validatePhone = (phone) => {
  const phoneReg = /^1[3-9]\d{9}$/;
  return phoneReg.test(phone);
};

// 验证邮箱
const validateEmail = (email) => {
  const emailReg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailReg.test(email);
};

// 验证密码（6-20位，包含字母和数字）
const validatePassword = (password) => {
  const passwordReg = /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]{6,20}$/;
  return passwordReg.test(password);
};

// 验证用户名（2-20位，中英文、数字、下划线）
const validateUsername = (username) => {
  const usernameReg = /^[\u4e00-\u9fa5a-zA-Z0-9_]{2,20}$/;
  return usernameReg.test(username);
};

// 验证身份证号
const validateIdCard = (idCard) => {
  const idCardReg =
    /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
  return idCardReg.test(idCard);
};

// 验证中文姓名
const validateChineseName = (name) => {
  const nameReg = /^[\u4e00-\u9fa5]{2,4}$/;
  return nameReg.test(name);
};

// 验证价格
const validatePrice = (price) => {
  const priceReg = /^\d+(\.\d{1,2})?$/;
  return priceReg.test(price) && parseFloat(price) > 0;
};

// 验证整数
const validateInteger = (value) => {
  const intReg = /^[1-9]\d*$/;
  return intReg.test(value);
};

// 验证正整数（包含0）
const validatePositiveInteger = (value) => {
  const intReg = /^\d+$/;
  return intReg.test(value);
};

// 验证必填项
const validateRequired = (value) => {
  return value !== null && value !== undefined && value !== "";
};

// 验证长度
const validateLength = (value, min, max) => {
  if (!value) return false;
  const length = value.length;
  return length >= min && length <= max;
};

// 验证最小长度
const validateMinLength = (value, min) => {
  if (!value) return false;
  return value.length >= min;
};

// 验证最大长度
const validateMaxLength = (value, max) => {
  if (!value) return true;
  return value.length <= max;
};

// 验证数值范围
const validateRange = (value, min, max) => {
  const num = parseFloat(value);
  if (isNaN(num)) return false;
  return num >= min && num <= max;
};

// 验证URL
const validateUrl = (url) => {
  const urlReg = /^https?:\/\/[^\s/$.?#].[^\s]*$/;
  return urlReg.test(url);
};

// 验证IP地址
const validateIp = (ip) => {
  const ipReg = /^(\d{1,3}\.){3}\d{1,3}$/;
  if (!ipReg.test(ip)) return false;

  const parts = ip.split(".");
  return parts.every((part) => parseInt(part) >= 0 && parseInt(part) <= 255);
};

// 验证日期格式
const validateDate = (date) => {
  const dateReg = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateReg.test(date)) return false;

  const dateObj = new Date(date);
  return !isNaN(dateObj.getTime());
};

// 验证时间格式
const validateTime = (time) => {
  const timeReg = /^([01]\d|2[0-3]):([0-5]\d)$/;
  return timeReg.test(time);
};

// 验证银行卡号
const validateBankCard = (cardNo) => {
  const cardReg = /^\d{16,19}$/;
  return cardReg.test(cardNo);
};

// 验证优惠券代码
const validateCouponCode = (code) => {
  const codeReg = /^[A-Z0-9]{6,12}$/;
  return codeReg.test(code);
};

// 验证商品规格
const validateProductSpec = (specs) => {
  if (!Array.isArray(specs)) return false;

  return specs.every((spec) => {
    return (
      spec.name &&
      spec.value &&
      typeof spec.name === "string" &&
      typeof spec.value === "string"
    );
  });
};

// 验证地址信息
const validateAddress = (address) => {
  const required = [
    "province",
    "city",
    "district",
    "detail",
    "consignee",
    "phone",
  ];

  return (
    required.every((field) => {
      return address[field] && validateRequired(address[field]);
    }) && validatePhone(address.phone)
  );
};

// 验证订单商品
const validateOrderItem = (item) => {
  const required = ["productId", "quantity", "price"];

  return (
    required.every((field) => {
      return item[field] !== null && item[field] !== undefined;
    }) &&
    validatePositiveInteger(item.quantity) &&
    validatePrice(item.price)
  );
};

// 组合验证器
const createValidator = (rules) => {
  return (data) => {
    const errors = {};

    Object.keys(rules).forEach((field) => {
      const rule = rules[field];
      const value = data[field];

      // 检查必填项
      if (rule.required && !validateRequired(value)) {
        errors[field] = rule.message || `${field}不能为空`;
        return;
      }

      // 如果不是必填项且值为空，跳过其他验证
      if (!rule.required && !validateRequired(value)) {
        return;
      }

      // 检查长度
      if (rule.minLength && !validateMinLength(value, rule.minLength)) {
        errors[field] =
          rule.message || `${field}长度不能少于${rule.minLength}位`;
        return;
      }

      if (rule.maxLength && !validateMaxLength(value, rule.maxLength)) {
        errors[field] =
          rule.message || `${field}长度不能超过${rule.maxLength}位`;
        return;
      }

      // 检查格式
      if (rule.pattern && !rule.pattern.test(value)) {
        errors[field] = rule.message || `${field}格式不正确`;
        return;
      }

      // 自定义验证函数
      if (rule.validator && !rule.validator(value)) {
        errors[field] = rule.message || `${field}验证失败`;
      }
    });

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    };
  };
};

module.exports = {
  validatePhone,
  validateEmail,
  validatePassword,
  validateUsername,
  validateIdCard,
  validateChineseName,
  validatePrice,
  validateInteger,
  validatePositiveInteger,
  validateRequired,
  validateLength,
  validateMinLength,
  validateMaxLength,
  validateRange,
  validateUrl,
  validateIp,
  validateDate,
  validateTime,
  validateBankCard,
  validateCouponCode,
  validateProductSpec,
  validateAddress,
  validateOrderItem,
  createValidator,
};
