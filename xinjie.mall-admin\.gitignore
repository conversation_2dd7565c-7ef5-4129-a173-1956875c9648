# 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 构建输出
dist/
build/

# 日志文件
logs/
*.log

# 上传文件
uploads/
!uploads/.gitkeep

# 临时文件
temp/
tmp/
*.tmp
*.temp

# 数据库
*.sqlite
*.db

# 缓存
.cache/
.parcel-cache/
.webpack/

# 测试覆盖率
coverage/
.nyc_output/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
Thumbs.db

# 会话文件
sessions/

# 备份文件
*.bak
*.backup

# 密钥文件
*.pem
*.key
*.crt

# 压缩文件
*.gz
*.zip
*.tar.gz

# 测试文件
test-results/
playwright-report/ 