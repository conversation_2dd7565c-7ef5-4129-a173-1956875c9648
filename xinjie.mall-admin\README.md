# 心洁茶叶商城后台管理系统

## 项目概述

心洁茶叶商城后台管理系统是一个基于Node.js + Express + MySQL的现代化电商管理平台，提供完整的商品管理、订单管理、用户管理等功能。

## 技术栈

### 后端技术

- **Node.js** - JavaScript运行时环境
- **Express.js** - Web应用框架
- **MySQL** - 关系型数据库
- **JWT** - 身份认证
- **bcrypt** - 密码加密
- **express-fileupload** - 文件上传
- **helmet** - 安全中间件
- **cors** - 跨域处理
- **compression** - 响应压缩
- **morgan** - 日志记录

### 前端技术

- **EJS** - 服务端模板引擎
- **原生JavaScript** - 前端交互
- **CSS3** - 样式设计
- **Webpack** - 资源构建

## 项目架构

```
xinjie.mall-admin/
├── src/                    # 源代码目录
│   ├── config/            # 配置文件
│   │   ├── index.js       # 配置主文件
│   │   └── database.js    # 数据库配置
│   ├── middleware/        # 中间件
│   │   ├── auth.js        # 认证中间件
│   │   └── error.js       # 错误处理中间件
│   ├── services/          # 服务层
│   │   └── bannerService.js # 轮播图服务
│   └── utils/             # 工具函数
│       ├── upload.js      # 文件上传工具
│       └── logger.js      # 日志工具
├── models/                # 数据模型
├── controllers/           # 控制器
├── routes/                # 路由
├── views/                 # 视图模板
├── public/                # 静态资源
└── uploads/               # 上传文件
```

## 功能模块

### 1. 用户管理

- 用户列表查看
- 用户状态管理
- 用户信息编辑

### 2. 商品管理

- 商品列表查看
- 商品添加/编辑/删除
- 商品分类管理
- 商品图片上传

### 3. 订单管理

- 订单列表查看
- 订单状态更新
- 订单详情查看
- 发货管理

### 4. 轮播图管理

- 轮播图列表
- 轮播图添加/编辑/删除
- 轮播图排序
- 图片上传

### 5. 分类管理

- 分类列表
- 分类添加/编辑/删除
- 分类排序

### 6. 系统设置

- 基础设置
- 支付设置
- 物流设置

## 环境要求

- Node.js >= 14.0.0
- MySQL >= 8.0
- npm >= 6.0.0

## 安装部署

### 1. 克隆项目

```bash
git clone <repository-url>
cd xinjie.mall-admin
```

### 2. 安装依赖

```bash
npm install
```

### 3. 环境配置

复制环境配置文件：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置数据库连接信息：

```env
NODE_ENV=development
PORT=3000
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=
DB_NAME=xinjie_mall
JWT_SECRET=your-jwt-secret-key
```

### 4. 数据库初始化

```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE xinjie_mall CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入表结构
mysql -u root -p xinjie_mall < ../数据库建表语句.sql

# 初始化管理员账号
node init-admin.js
```

### 5. 启动服务

```bash
# 开发环境
npm run dev

# 生产环境
npm start
```

## API文档

### 认证相关

- `POST /api/auth/login` - 管理员登录
- `GET /api/auth/info` - 获取用户信息
- `POST /api/auth/logout` - 退出登录

### 轮播图管理

- `GET /api/banners/list` - 获取轮播图列表
- `POST /api/banners` - 创建轮播图
- `PUT /api/banners/:id` - 更新轮播图
- `DELETE /api/banners/:id` - 删除轮播图
- `POST /api/banners/upload` - 上传轮播图图片

### 商品管理

- `GET /api/products/list` - 获取商品列表
- `POST /api/products` - 创建商品
- `PUT /api/products/:id` - 更新商品
- `DELETE /api/products/:id` - 删除商品

### 订单管理

- `GET /api/orders/list` - 获取订单列表
- `GET /api/orders/:id` - 获取订单详情
- `PUT /api/orders/:id/status` - 更新订单状态
- `PUT /api/orders/:id/ship` - 订单发货

### 用户管理

- `GET /api/users/list` - 获取用户列表
- `GET /api/users/:id` - 获取用户详情
- `PUT /api/users/:id/status` - 更新用户状态

### 分类管理

- `GET /api/categories` - 获取分类列表
- `POST /api/categories` - 创建分类
- `PUT /api/categories/:id` - 更新分类
- `DELETE /api/categories/:id` - 删除分类

## 开发规范

### 代码规范

- 使用ESLint进行代码检查
- 遵循Airbnb JavaScript规范
- 使用Prettier进行代码格式化

### 命名规范

- 文件夹：小写字母，用连字符分隔
- 文件：小写字母，用连字符分隔
- 变量：camelCase命名
- 常量：UPPER_SNAKE_CASE命名
- 类：PascalCase命名

### 错误处理

- 统一错误处理中间件
- 详细的错误日志记录
- 友好的错误响应格式

### 安全规范

- 输入参数验证
- SQL注入防护
- XSS攻击防护
- CSRF防护
- 权限控制

## 部署说明

### 开发环境

```bash
NODE_ENV=development npm run dev
```

### 测试环境

```bash
NODE_ENV=staging npm start
```

### 生产环境

```bash
NODE_ENV=production npm start
```

## 监控日志

### 日志文件

- `logs/app-YYYY-MM-DD.log` - 应用日志
- `logs/error-YYYY-MM-DD.log` - 错误日志
- `logs/access-YYYY-MM-DD.log` - 访问日志
- `logs/business-YYYY-MM-DD.log` - 业务日志
- `logs/security-YYYY-MM-DD.log` - 安全日志

### 日志级别

- error: 错误信息
- warn: 警告信息
- info: 一般信息
- debug: 调试信息

## 常见问题

### 1. 数据库连接失败

- 检查MySQL服务是否启动
- 验证数据库连接配置
- 确认数据库用户权限

### 2. 文件上传失败

- 检查uploads目录权限
- 验证文件大小限制
- 确认文件类型支持

### 3. JWT认证失败

- 检查JWT_SECRET配置
- 验证token格式
- 确认token有效期

## 更新日志

### v1.0.0 (2025-07-07)

- 初始版本发布
- 基础功能实现
- 架构设计完善

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 创建 Pull Request

## 许可证

MIT License

## 联系方式

- 项目维护者：[维护者姓名]
- 邮箱：[邮箱地址]
- 项目地址：[项目地址]

## 会话自动总结（2024-07-09）

- **会话主要目的**：排查并解决后台管理系统接口 504 Gateway Timeout 问题，规范中间件引入与路由挂载，提升系统健壮性。
- **完成的主要任务**：
  1. 检查 banner 路由、controller、service、model、数据库链路，确认无阻塞。
  2. 检查数据库连接，确认数据库服务、表结构、数据量均正常。
  3. 检查并修正所有 requireAuth、requirePermission 的引入方式，统一为从 middleware/auth.js 解构。
  4. 检查 app.js 路由挂载，确认所有 API 路由均已正确注册。
  5. 在数据库 query 函数中增加 SQL 执行耗时日志，便于后续慢查询排查。
- **关键决策和解决方案**：
  - 统一中间件引入方式，杜绝路径错误导致服务崩溃。
  - 路由挂载全部采用清晰、规范的 use 语句，确保接口可达。
  - 通过日志追踪 SQL 执行，辅助性能优化。
- **使用的技术栈**：Node.js、Express、MySQL、mysql2/promise、EJS
- **修改的文件**：
  - xinjie.mall-admin/routes/user.js
  - xinjie.mall-admin/src/config/database.js
  - xinjie.mall-admin/app.js（仅检查，无需修改）

## 会话自动总结（2025-07-15）

- **会话主要目的**：解决微信小程序商城后台管理系统的504网关超时错误，修复multer文件上传配置问题，确保前后端服务正常启动和通信。
- **完成的主要任务**：
  1. 诊断并修复multer配置错误：`upload.array is not a function`，创建独立的单文件和多文件上传实例。
  2. 解决端口配置冲突问题，确保后端API服务器在3000端口，前端开发服务器在8081端口。
  3. 修复数据库字段缺失问题：`last_login_time`字段不存在，提供SQL修复脚本。
  4. 优化错误处理机制，确保非关键错误不影响核心登录流程。
  5. 验证前后端服务正常启动，登录功能正常工作。
- **关键决策和解决方案**：
  - 将multer配置拆分为`uploadSingle`和`uploadMultiple`两个独立实例，避免方法冲突。
  - 统一端口配置：后端3000端口，前端8081端口，webpack代理正确转发API请求。
  - 提供数据库字段修复脚本，同时优化错误处理，确保系统稳定性。
  - 使用concurrently同时启动前后端服务，提升开发效率。
- **使用的技术栈**：Node.js、Express、MySQL、multer、webpack、React、Ant Design
- **修改的文件**：
  - xinjie.mall-admin/routes/upload.js（修复multer配置）
  - xinjie.mall-admin/routes/auth.js（优化错误处理）
  - xinjie.mall-admin/fix-last-login-time.sql（新增数据库修复脚本）

## 会话自动总结（2025-07-15 补充）

- **会话主要目的**：修复BannerList.jsx页面轮播图管理功能的400认证错误，确保前端正确使用认证机制。
- **完成的主要任务**：
  1. 诊断BannerList.jsx中POST请求返回400错误的原因：直接使用axios而非配置好的request实例。
  2. 修复前端认证问题：将所有axios调用替换为request实例，确保请求携带认证token。
  3. 调整API路径和响应数据处理，适配request实例的配置。
  4. 验证轮播图管理功能正常工作：创建、查询、编辑、删除功能均正常。
- **关键决策和解决方案**：
  - 统一使用request实例进行API调用，确保认证token自动携带。
  - 调整API路径格式，从`/api/admin/banner`改为`/admin/banner`。
  - 优化响应数据处理逻辑，适配request拦截器的统一处理。
  - 验证前后端认证机制配合正常，用户登录后可正常使用所有功能。
- **使用的技术栈**：React、Axios、JWT认证、Session认证
- **修改的文件**：
  - xinjie.mall-admin/src/pages/BannerList.jsx（修复认证问题）

## 会话自动总结（2025-07-15 最终修复）

- **会话主要目的**：彻底解决轮播图管理功能的认证问题，修复登录页面和认证机制。
- **完成的主要任务**：
  1. 发现登录页面也使用axios而非request实例，导致session认证失败。
  2. 修复Login.jsx页面，统一使用request实例进行登录请求。
  3. 调整登录响应数据处理，适配request拦截器的统一格式。
  4. 验证完整的认证流程：登录→session保存→API调用→认证成功。
- **关键决策和解决方案**：
  - 统一所有API调用使用request实例，确保认证信息正确传递。
  - 修复登录页面的认证流程，确保session cookie正确保存。
  - 验证webpack代理配置正确工作，API请求正确转发到后端。
  - 确保前后端认证机制完全配合，用户登录后可正常使用所有功能。
- **使用的技术栈**：React、Axios、Session认证、JWT认证、webpack代理
- **修改的文件**：
  - xinjie.mall-admin/src/pages/Login.jsx（修复登录认证）
  - xinjie.mall-admin/src/pages/BannerList.jsx（之前已修复）

## 会话自动总结（2025-07-15 图片上传完全修复）

- **会话主要目的**：彻底解决图片上传500错误和图片访问404问题，实现完整的轮播图管理功能。
- **完成的主要任务**：
  1. 诊断并解决"Unexpected end of form"错误：发现express-fileupload与multer冲突。
  2. 移除express-fileupload中间件，统一使用multer处理文件上传。
  3. 修复静态文件服务配置：uploads路径指向正确的public/uploads目录。
  4. 修复webpack代理配置：uploads请求正确代理到3000端口而非4000端口。
  5. 优化multer配置：添加错误处理和可选链操作符。
  6. 验证完整上传流程：文件上传→数据库保存→图片访问→前端显示。
- **关键决策和解决方案**：
  - 移除express-fileupload避免与multer冲突，解决根本的multipart解析问题。
  - 修正静态文件服务路径：从`/uploads`→`uploads`改为`/uploads`→`public/uploads`。
  - 修正webpack代理配置：uploads请求从4000端口改为3000端口。
  - 优化request.js拦截器：FormData请求时不添加X-Requested-With头。
  - 创建简化上传接口用于调试，确保multer配置正确。
- **使用的技术栈**：multer、express静态文件服务、webpack代理、FormData
- **修改的文件**：
  - xinjie.mall-admin/app.js（移除express-fileupload，修复静态文件服务）
  - xinjie.mall-admin/webpack.config.js（修复uploads代理配置）
  - xinjie.mall-admin/routes/upload.js（优化multer配置，添加简化接口）
  - xinjie.mall-admin/src/utils/request.js（优化FormData请求处理）
  - xinjie.mall-admin/src/pages/BannerList.jsx（使用原生axios上传文件）

---

### 【会话总结 - 前端警告修复】
- **会话主要目的**：修复前端控制台中的 antd 组件警告，包括 Modal 组件的 `destroyOnClose` 废弃警告和 Form 组件的 useForm 警告。
- **完成的主要任务**：
  - 将所有 `destroyOnClose` 属性替换为 `destroyOnHidden`
  - 检查并确认所有 Form 组件正确使用了 `form={form}` 属性
- **关键决策和解决方案**：
  - 使用 `destroyOnHidden` 替代已废弃的 `destroyOnClose` 属性
  - 确认所有 useForm 创建的实例都正确绑定到对应的 Form 组件
- **使用的技术栈**：React、Ant Design、JavaScript ES6+。
- **修改了哪些文件**：
  - `src/components/User/UserList.jsx` - 修复 Modal 属性
  - `src/components/Product/ProductList.jsx` - 修复两个 Modal 属性
  - `src/components/Order/OrderList.jsx` - 修复四个 Modal 属性
  - `src/components/Banner/BannerList.jsx` - 修复 Modal 属性
  - `src/components/Category/CategoryList.jsx` - 修复 Modal 属性
  - `src/components/common/ConfirmModal.jsx` - 修复 Modal 属性
  - 本 README.md 文件（追加会话总结）
