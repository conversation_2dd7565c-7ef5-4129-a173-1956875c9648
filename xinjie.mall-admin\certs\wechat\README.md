# 微信支付证书配置说明

## 证书文件说明

在此目录下需要放置以下微信支付证书文件：

### 1. 商户私钥文件
- **文件名**: `apiclient_key.pem`
- **说明**: 商户API私钥，用于生成签名
- **获取方式**: 从微信商户平台下载

### 2. 商户证书文件（可选）
- **文件名**: `apiclient_cert.pem`
- **说明**: 商户API证书，某些API需要
- **获取方式**: 从微信商户平台下载

## 环境变量配置

在 `.env` 文件中配置以下参数：

```env
# 微信支付配置
WECHAT_APP_ID=你的小程序AppID
WECHAT_APP_SECRET=你的小程序AppSecret
WECHAT_MCH_ID=你的商户号
WECHAT_API_KEY=你的API密钥（v2版本，如果需要）
WECHAT_APIV3_KEY=你的APIv3密钥
WECHAT_SERIAL_NO=你的证书序列号
WECHAT_PRIVATE_KEY_PATH=./certs/wechat/apiclient_key.pem
WECHAT_CERT_PATH=./certs/wechat/apiclient_cert.pem
WECHAT_NOTIFY_URL=https://你的域名/api/payment/wechat/notify
```

## 获取证书步骤

### 1. 登录微信商户平台
访问：https://pay.weixin.qq.com/

### 2. 下载API证书
1. 进入 "账户中心" -> "API安全"
2. 下载API证书
3. 解压后将 `apiclient_key.pem` 和 `apiclient_cert.pem` 放到此目录

### 3. 获取证书序列号
1. 在API安全页面查看证书序列号
2. 将序列号配置到环境变量 `WECHAT_SERIAL_NO`

### 4. 设置APIv3密钥
1. 在API安全页面设置APIv3密钥
2. 将密钥配置到环境变量 `WECHAT_APIV3_KEY`

## 安全注意事项

1. **证书文件安全**
   - 证书文件包含敏感信息，不要提交到版本控制系统
   - 确保服务器文件权限设置正确（600或更严格）

2. **环境变量安全**
   - 生产环境不要在代码中硬编码敏感信息
   - 使用环境变量或安全的配置管理系统

3. **网络安全**
   - 确保回调URL使用HTTPS
   - 验证回调请求的签名

## 开发模式

如果证书文件不存在，系统会自动进入开发模式：
- 使用模拟支付数据
- 跳过签名验证
- 返回测试用的支付参数

这样可以在没有真实证书的情况下进行开发和测试。

## 测试支付

系统提供了测试接口：
- `POST /api/payment/mock/success` - 模拟支付成功
- `GET /api/payment/status/:orderNo` - 查询支付状态

## 常见问题

### Q: 证书文件权限问题
A: 确保Node.js进程有读取证书文件的权限

### Q: 签名验证失败
A: 检查证书序列号和APIv3密钥是否正确配置

### Q: 回调接收不到
A: 检查回调URL是否可以从外网访问，是否使用HTTPS

## 联系支持

如有问题，请联系技术支持或查看微信支付官方文档：
https://pay.weixin.qq.com/wiki/doc/apiv3/index.shtml
