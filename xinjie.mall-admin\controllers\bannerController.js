const bannerService = require('../src/services/bannerService');
const { ValidationError, BusinessError } = require('../src/middleware/error');
const path = require('path');
const fs = require('fs');

exports.list = async (req, res) => {
  console.log('进入bannerController.list', req.query);
  try {
    const { page = 1, pageSize = 10, title = '', status } = req.query;
    const result = await bannerService.getBannerList({
      page,
      pageSize,
      title,
      status,
    });
    res.json({
      success: true,
      data: result,
      message: '获取轮播图列表成功',
    });
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(400).json({
        success: false,
        message: error.message,
        errors: error.errors,
      });
    }
    console.error('获取轮播图列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取轮播图列表失败',
    });
  }
};

exports.create = async (req, res) => {
  console.log('进入bannerController.create', req.body, req.files);
  try {
    // 1. 处理 multipart/form-data
    let imageUrl = '';
    let title = req.body.title;
    let link_url = req.body.link_url;
    let sort_order = req.body.sort_order;
    let status = req.body.status;
    if (req.files && req.files.file) {
      // 有图片上传
      const uploadUtil = require('../src/utils/upload');
      const file = req.files.file;
      const result = await uploadUtil.uploadFile(file, 'banner');
      imageUrl = result.url || result.path || result;
    } else if (req.body.image_url) {
      imageUrl = req.body.image_url;
    }
    if (!imageUrl) {
      return res.status(400).json({ success: false, message: '请上传图片' });
    }
    // 组装数据传给 service
    const result = await bannerService.createBanner({
      title,
      image_url: imageUrl,
      link_url,
      sort_order,
      status,
    });
    res.json({
      success: true,
      message: '轮播图创建成功',
      data: result,
    });
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(400).json({
        success: false,
        message: error.message,
        errors: error.errors,
      });
    }
    console.error('创建轮播图失败:', error);
    res.status(500).json({
      success: false,
      message: '创建轮播图失败',
    });
  }
};

exports.update = async (req, res) => {
  console.log('进入bannerController.update', req.params, req.body);
  try {
    const result = await bannerService.updateBanner(req.params.id, req.body);
    res.json({
      success: true,
      message: '轮播图更新成功',
      data: result,
    });
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(400).json({
        success: false,
        message: error.message,
        errors: error.errors,
      });
    }
    console.error('更新轮播图失败:', error);
    res.status(500).json({
      success: false,
      message: '更新轮播图失败',
    });
  }
};

exports.delete = async (req, res) => {
  console.log('进入bannerController.delete', req.params);
  const id = req.params.id;
  const bannerModel = require('../models/bannerModel');
  try {
    // 1. 查找图片路径
    const banners = await bannerModel.findAll({ page: 1, pageSize: 1000 });
    const banner = banners.list.find(item => item.id == id);
    if (banner && banner.image_url) {
      // 修改文件删除路径，指向mall-server的uploads目录
      const filePath = path.join(
        __dirname,
        '../../mall-server/uploads',
        banner.image_url.replace('/uploads/', '')
      );
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath); // 删除物理文件
      }
    }
    // 2. 删除数据库记录
    await bannerService.deleteBanner(id);
    // 3. 返回最新列表
    const result = await bannerService.getBannerList({ page: 1, pageSize: 10 });
    res.json({
      success: true,
      message: '轮播图删除成功',
      data: result,
    });
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(400).json({
        success: false,
        message: error.message,
        errors: error.errors,
      });
    }
    console.error('删除轮播图失败:', error);
    res.status(500).json({
      success: false,
      message: '删除轮播图失败',
    });
  }
};

exports.upload = async (req, res) => {
  console.log('[唯一调试] bannerController.upload 被调用');
  try {
    if (!req.files || !req.files.file) {
      console.log('[唯一调试] 没有文件上传');
      return res.status(400).json({ code: 1, msg: '未上传文件' });
    }
    const file = req.files.file;
    console.log('[唯一调试] 文件信息:', {
      originalname: file.originalname,
      filename: file.filename,
      path: file.path,
      size: file.size
    });
    const path = require('path');
    const fs = require('fs');
    // 直接上传到 mall-server 的实际目录
    const uploadDir = path.join(__dirname, '../../mall-server/uploads/banners');
    console.log('[唯一调试] mall-server banner 目录:', uploadDir);
    if (!fs.existsSync(uploadDir)) {
      console.log('[唯一调试] 目录不存在，创建目录');
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    const filename = Date.now() + '_' + file.name;
    const filepath = path.join(uploadDir, filename);
    console.log('[唯一调试] 目标文件路径:', filepath);
    file.mv(filepath, err => {
      console.log('[唯一调试] file.mv 回调，err:', err);
      if (err) {
        console.error('[唯一调试] 文件保存失败:', err);
        return res.status(500).json({ code: 1, msg: '上传失败' });
      }
      console.log('[唯一调试] 文件已保存到:', filepath);
      // 验证文件是否真的存在
      if (fs.existsSync(filepath)) {
        console.log('[唯一调试] 文件确实存在，大小:', fs.statSync(filepath).size);
      } else {
        console.log('[唯一调试] 文件不存在！');
      }
      const url = '/uploads/banners/' + filename;
      console.log('[唯一调试] 返回URL:', url);
      res.json({ code: 0, msg: '上传成功', data: { url } });
    });
  } catch (error) {
    console.error('[唯一调试] 上传异常:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};
