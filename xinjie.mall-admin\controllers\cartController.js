const cartModel = require('../models/cartModel');
const discountModel = require('../models/discountModel');
const db = require('../src/config/database');

// 获取购物车列表
exports.getCartList = async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未登录'
      });
    }

    console.log('获取购物车列表，用户ID:', userId);

    // 获取购物车商品
    const cartItems = await cartModel.findByUserId(userId);
    console.log('购物车原始数据:', cartItems);

    // 为每个商品计算折扣
    const itemsWithDiscount = await Promise.all(
      cartItems.map(async (item) => {
        try {
          // 获取商品的有效折扣
          const activeDiscounts = await discountModel.getActiveDiscounts();
          let bestDiscount = null;
          let bestDiscountPrice = item.product_price;

          // 查找最优折扣
          for (const discount of activeDiscounts) {
            // 检查折扣适用范围
            let isApplicable = false;
            
            if (discount.applicable_to === 1) {
              // 全部商品
              isApplicable = true;
            } else if (discount.applicable_to === 2) {
              // 指定商品 - 需要检查商品关联表
              const productDiscountSql = `
                SELECT 1 FROM discount_products 
                WHERE discount_id = ? AND product_id = ?
              `;
              const [productResult] = await db.query(productDiscountSql, [discount.id, item.product_id]);
              isApplicable = productResult.length > 0;
            } else if (discount.applicable_to === 3) {
              // 指定分类 - 需要检查分类关联表
              const categoryDiscountSql = `
                SELECT 1 FROM discount_categories 
                WHERE discount_id = ? AND category_id = ?
              `;
              const [categoryResult] = await db.query(categoryDiscountSql, [discount.id, item.category_id]);
              isApplicable = categoryResult.length > 0;
            }

            if (isApplicable) {
              const discountPrice = discountModel.calculateDiscountPrice(
                item.product_price, 
                discount, 
                item.quantity
              );
              
              if (discountPrice < bestDiscountPrice) {
                bestDiscountPrice = discountPrice;
                bestDiscount = discount;
              }
            }
          }

          const originalPrice = parseFloat(item.product_price);
          const discountPrice = bestDiscountPrice;
          const discountAmount = originalPrice - discountPrice;
          const itemTotal = discountPrice * item.quantity;
          const originalTotal = originalPrice * item.quantity;

          return {
            id: item.id,
            product_id: item.product_id,
            product_name: item.product_name,
            product_image: item.product_image,
            quantity: item.quantity,
            specs: item.specs,
            price: originalPrice,
            originalPrice: originalPrice,
            discountPrice: discountPrice,
            discountAmount: discountAmount,
            hasDiscount: discountAmount > 0,
            itemTotal: itemTotal,
            originalTotal: originalTotal,
            stock: item.product_stock,
            category_id: item.category_id,
            category_name: item.category_name,
            discount: bestDiscount ? {
              id: bestDiscount.id,
              name: bestDiscount.name,
              type: bestDiscount.type,
              value: bestDiscount.value
            } : null
          };
        } catch (error) {
          console.error('计算商品折扣失败:', error);
          // 如果折扣计算失败，返回原价
          return {
            id: item.id,
            product_id: item.product_id,
            product_name: item.product_name,
            product_image: item.product_image,
            quantity: item.quantity,
            specs: item.specs,
            price: parseFloat(item.product_price),
            originalPrice: parseFloat(item.product_price),
            discountPrice: parseFloat(item.product_price),
            discountAmount: 0,
            hasDiscount: false,
            itemTotal: parseFloat(item.product_price) * item.quantity,
            originalTotal: parseFloat(item.product_price) * item.quantity,
            stock: item.product_stock,
            category_id: item.category_id,
            category_name: item.category_name,
            discount: null
          };
        }
      })
    );

    // 计算购物车总计
    const originalTotal = itemsWithDiscount.reduce((sum, item) => sum + item.originalTotal, 0);
    const discountTotal = itemsWithDiscount.reduce((sum, item) => sum + item.itemTotal, 0);
    const totalDiscount = originalTotal - discountTotal;

    // 获取购物车统计
    const stats = await cartModel.getCartStats(userId);

    console.log('购物车折扣计算结果:', {
      itemCount: itemsWithDiscount.length,
      originalTotal,
      discountTotal,
      totalDiscount
    });

    res.json({
      success: true,
      data: {
        items: itemsWithDiscount,
        summary: {
          originalTotal: Math.round(originalTotal * 100) / 100,
          discountTotal: Math.round(discountTotal * 100) / 100,
          totalDiscount: Math.round(totalDiscount * 100) / 100,
          hasDiscount: totalDiscount > 0,
          itemCount: stats.total_items,
          totalQuantity: stats.total_quantity
        }
      },
      message: '获取购物车成功'
    });

  } catch (error) {
    console.error('获取购物车失败:', error);
    res.status(500).json({
      success: false,
      message: '获取购物车失败'
    });
  }
};

// 添加商品到购物车
exports.addToCart = async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未登录'
      });
    }

    const { product_id, quantity = 1, specs = null } = req.body;

    if (!product_id) {
      return res.status(400).json({
        success: false,
        message: '商品ID不能为空'
      });
    }

    console.log('添加商品到购物车:', { userId, product_id, quantity, specs });

    const cartItemId = await cartModel.addItem(userId, product_id, quantity, specs);

    res.json({
      success: true,
      data: { id: cartItemId },
      message: '添加到购物车成功'
    });

  } catch (error) {
    console.error('添加到购物车失败:', error);
    res.status(500).json({
      success: false,
      message: '添加到购物车失败'
    });
  }
};

// 更新购物车商品数量
exports.updateCartItem = async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未登录'
      });
    }

    const { id } = req.params;
    const { quantity } = req.body;

    if (!quantity || quantity < 1) {
      return res.status(400).json({
        success: false,
        message: '数量必须大于0'
      });
    }

    console.log('更新购物车商品数量:', { userId, id, quantity });

    await cartModel.updateQuantity(userId, id, quantity);

    res.json({
      success: true,
      message: '更新成功'
    });

  } catch (error) {
    console.error('更新购物车失败:', error);
    res.status(500).json({
      success: false,
      message: '更新购物车失败'
    });
  }
};

// 删除购物车商品
exports.removeCartItem = async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未登录'
      });
    }

    const { id } = req.params;

    console.log('删除购物车商品:', { userId, id });

    await cartModel.removeItem(userId, id);

    res.json({
      success: true,
      message: '删除成功'
    });

  } catch (error) {
    console.error('删除购物车商品失败:', error);
    res.status(500).json({
      success: false,
      message: '删除失败'
    });
  }
};

// 清空购物车
exports.clearCart = async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未登录'
      });
    }

    console.log('清空购物车:', { userId });

    await cartModel.clearCart(userId);

    res.json({
      success: true,
      message: '清空购物车成功'
    });

  } catch (error) {
    console.error('清空购物车失败:', error);
    res.status(500).json({
      success: false,
      message: '清空购物车失败'
    });
  }
};
