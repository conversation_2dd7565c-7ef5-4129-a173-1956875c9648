const categoryModel = require('../models/categoryModel');
const { syncImageToMallServer } = require('../utils/syncImages');
const { clearMallServerCategoryCache } = require('../utils/clearMallServerCache');

// 分类列表（分页/条件）
exports.list = async (req, res) => {
  try {
    const { page = 1, pageSize = 20, name = '', parent_id = '' } = req.query;
    const result = await categoryModel.findAll({
      page,
      pageSize,
      name,
      parent_id,
    });
    res.json({ success: true, data: result, message: '分类列表' });
  } catch (e) {
    res.status(500).json({ success: false, data: null, message: '获取分类失败' });
  }
};

// 所有分类（下拉用）
exports.all = async (req, res) => {
  try {
    const result = await categoryModel.findAll({ all: true });
    res.json({ success: true, data: result, message: '全部分类' });
  } catch (e) {
    res.status(500).json({ success: false, data: null, message: '获取全部分类失败' });
  }
};

// 分类详情
exports.detail = async (req, res) => {
  try {
    const result = await categoryModel.findById(req.params.id);
    res.json({ success: true, data: result, message: '分类详情' });
  } catch (e) {
    res.status(500).json({ success: false, data: null, message: '获取分类详情失败' });
  }
};

// 新增分类
exports.create = async (req, res) => {
  try {
    const id = await categoryModel.create(req.body);
    
    // 如果有图片，同步到mall-server
    if (req.body.image) {
      let imagePath = req.body.image.replace('/uploads/', '');
      // 确保路径使用正确的目录名
      if (imagePath.startsWith('category/')) {
        imagePath = imagePath.replace('category/', 'categories/');
      }
      syncImageToMallServer(imagePath);
      console.log(`[分类创建] 同步图片到mall-server: ${imagePath}`);
    }
    
    // 清理mall-server的分类缓存
    await clearMallServerCategoryCache();
    
    res.json({ success: true, data: { id }, message: '添加成功' });
  } catch (e) {
    res.status(500).json({ success: false, data: null, message: '添加失败' });
  }
};

// 更新分类
exports.update = async (req, res) => {
  try {
    await categoryModel.update(req.params.id, req.body);
    
    // 如果有图片，同步到mall-server
    if (req.body.image) {
      let imagePath = req.body.image.replace('/uploads/', '');
      // 确保路径使用正确的目录名
      if (imagePath.startsWith('category/')) {
        imagePath = imagePath.replace('category/', 'categories/');
      }
      syncImageToMallServer(imagePath);
      console.log(`[分类更新] 同步图片到mall-server: ${imagePath}`);
    }
    
    // 清理mall-server的分类缓存
    await clearMallServerCategoryCache();
    
    res.json({ success: true, data: null, message: '更新成功' });
  } catch (e) {
    res.status(500).json({ success: false, data: null, message: '更新失败' });
  }
};

// 删除分类
exports.delete = async (req, res) => {
  try {
    await categoryModel.delete(req.params.id);
    res.json({ success: true, data: null, message: '删除成功' });
  } catch (e) {
    res.status(500).json({ success: false, data: null, message: '删除失败' });
  }
};

// 排序（如有需要）
exports.sort = async (req, res) => {
  try {
    await categoryModel.sort(req.body.categories);
    res.json({ success: true, data: null, message: '排序成功' });
  } catch (e) {
    res.status(500).json({ success: false, data: null, message: '排序失败' });
  }
};

// 分类图片上传
exports.upload = (req, res) => {
  console.log('[唯一调试] categoryController.upload 被调用');
  try {
    if (!req.files || !req.files.file) {
      console.log('[唯一调试] categoryController 没有文件上传');
      return res.status(400).json({ code: 1, msg: '未上传文件' });
    }
    const file = req.files.file;
    console.log('[唯一调试] categoryController 文件信息:', {
      originalname: file.originalname,
      filename: file.filename,
      path: file.path,
      size: file.size
    });
    const path = require('path');
    const fs = require('fs');
    const uploadDir = path.join(__dirname, '../public/uploads/categories');
    console.log('[唯一调试] categoryController 上传目录:', uploadDir);
    if (!fs.existsSync(uploadDir)) fs.mkdirSync(uploadDir, { recursive: true });
    const filename = Date.now() + '_' + file.name;
    const filepath = path.join(uploadDir, filename);
    console.log('[唯一调试] categoryController 目标文件路径:', filepath);
    file.mv(filepath, err => {
      console.log('[唯一调试] categoryController file.mv 回调，err:', err);
      if (err) {
        console.error('[唯一调试] categoryController 文件保存失败:', err);
        return res.status(500).json({ code: 1, msg: '上传失败' });
      }
      console.log('[唯一调试] categoryController 文件已保存到:', filepath);
      const url = '/uploads/categories/' + filename;
      console.log('[唯一调试] categoryController 返回URL:', url);
      
      // 立即同步图片到mall-server
      const imagePath = `categories/${filename}`;
      const syncResult = syncImageToMallServer(imagePath);
      console.log('[唯一调试] categoryController 图片同步结果:', syncResult);
      
      res.json({ code: 0, msg: '上传成功', data: { url } });
    });
  } catch (error) {
    console.error('[唯一调试] categoryController 上传异常:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};
