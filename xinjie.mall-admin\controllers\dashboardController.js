const db = require('../src/config/database');

// 仪表板控制器
class DashboardController {
  // 获取订单统计
  static async getOrderStatistics(req, res) {
    try {
      // 获取总订单数
      const [totalResult] = await db.query('SELECT COUNT(*) as total FROM orders');
      const total = totalResult[0]?.total || 0;

      // 获取今日订单数
      const [todayResult] = await db.query(`
        SELECT COUNT(*) as today 
        FROM orders 
        WHERE DATE(created_at) = CURDATE()
      `);
      const today = todayResult[0]?.today || 0;

      // 获取昨日订单数计算趋势
      const [yesterdayResult] = await db.query(`
        SELECT COUNT(*) as yesterday 
        FROM orders 
        WHERE DATE(created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
      `);
      const yesterday = yesterdayResult[0]?.yesterday || 0;
      const trend = yesterday > 0 ? ((today - yesterday) / yesterday * 100) : 0;

      res.json({
        success: true,
        data: {
          total,
          today,
          trend: Math.round(trend * 10) / 10
        }
      });
    } catch (error) {
      console.error('获取订单统计失败:', error);
      res.status(500).json({
        success: false,
        message: '获取订单统计失败'
      });
    }
  }

  // 获取用户统计
  static async getUserStatistics(req, res) {
    try {
      const [totalResult] = await db.query('SELECT COUNT(*) as total FROM users');
      const total = totalResult[0]?.total || 0;

      const [todayResult] = await db.query(`
        SELECT COUNT(*) as today 
        FROM users 
        WHERE DATE(created_at) = CURDATE()
      `);
      const today = todayResult[0]?.today || 0;

      const [yesterdayResult] = await db.query(`
        SELECT COUNT(*) as yesterday 
        FROM users 
        WHERE DATE(created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
      `);
      const yesterday = yesterdayResult[0]?.yesterday || 0;
      const trend = yesterday > 0 ? ((today - yesterday) / yesterday * 100) : 0;

      res.json({
        success: true,
        data: {
          total,
          today,
          trend: Math.round(trend * 10) / 10
        }
      });
    } catch (error) {
      console.error('获取用户统计失败:', error);
      res.status(500).json({
        success: false,
        message: '获取用户统计失败'
      });
    }
  }

  // 获取商品统计
  static async getProductStatistics(req, res) {
    try {
      const [totalResult] = await db.query('SELECT COUNT(*) as total FROM products');
      const total = totalResult[0]?.total || 0;

      const [activeResult] = await db.query('SELECT COUNT(*) as active FROM products WHERE status = 1');
      const active = activeResult[0]?.active || 0;

      const [todayResult] = await db.query(`
        SELECT COUNT(*) as today 
        FROM products 
        WHERE DATE(created_at) = CURDATE()
      `);
      const today = todayResult[0]?.today || 0;

      const [yesterdayResult] = await db.query(`
        SELECT COUNT(*) as yesterday 
        FROM products 
        WHERE DATE(created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
      `);
      const yesterday = yesterdayResult[0]?.yesterday || 0;
      const trend = yesterday > 0 ? ((today - yesterday) / yesterday * 100) : 0;

      res.json({
        success: true,
        data: {
          total,
          active,
          today,
          trend: Math.round(trend * 10) / 10
        }
      });
    } catch (error) {
      console.error('获取商品统计失败:', error);
      res.status(500).json({
        success: false,
        message: '获取商品统计失败'
      });
    }
  }

  // 获取销售统计
  static async getSalesStatistics(req, res) {
    try {
      const [totalResult] = await db.query(`
        SELECT COALESCE(SUM(total_amount), 0) as total 
        FROM orders 
        WHERE status IN (2, 3, 4)
      `);
      const total = totalResult[0]?.total || 0;

      const [todayResult] = await db.query(`
        SELECT COALESCE(SUM(total_amount), 0) as today 
        FROM orders 
        WHERE DATE(created_at) = CURDATE() AND status IN (2, 3, 4)
      `);
      const today = todayResult[0]?.today || 0;

      const [yesterdayResult] = await db.query(`
        SELECT COALESCE(SUM(total_amount), 0) as yesterday 
        FROM orders 
        WHERE DATE(created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND status IN (2, 3, 4)
      `);
      const yesterday = yesterdayResult[0]?.yesterday || 0;
      const trend = yesterday > 0 ? ((today - yesterday) / yesterday * 100) : 0;

      res.json({
        success: true,
        data: {
          total: parseFloat(total),
          today: parseFloat(today),
          trend: Math.round(trend * 10) / 10
        }
      });
    } catch (error) {
      console.error('获取销售统计失败:', error);
      res.status(500).json({
        success: false,
        message: '获取销售统计失败'
      });
    }
  }

  // 获取最近订单
  static async getRecentOrders(req, res) {
    try {
      const { limit = 5 } = req.query;
      
      const [orders] = await db.query(`
        SELECT 
          o.id,
          o.order_no,
          o.total_amount,
          o.status,
          o.created_at,
          u.nickname,
          u.phone,
          CASE o.status
            WHEN 1 THEN '待付款'
            WHEN 2 THEN '待发货'
            WHEN 3 THEN '已发货'
            WHEN 4 THEN '已完成'
            WHEN 5 THEN '已取消'
            ELSE '未知'
          END as status_text
        FROM orders o
        LEFT JOIN users u ON o.user_id = u.id
        ORDER BY o.created_at DESC
        LIMIT ?
      `, [parseInt(limit)]);

      const formattedOrders = orders.map(order => ({
        id: order.order_no,
        customer: order.nickname || order.phone || '匿名用户',
        amount: parseFloat(order.total_amount),
        status: order.status_text,
        time: this.formatTime(order.created_at)
      }));

      res.json({
        success: true,
        data: {
          list: formattedOrders
        }
      });
    } catch (error) {
      console.error('获取最近订单失败:', error);
      res.status(500).json({
        success: false,
        message: '获取最近订单失败'
      });
    }
  }

  // 获取热销商品
  static async getHotProducts(req, res) {
    try {
      const { limit = 5 } = req.query;
      
      const [products] = await db.query(`
        SELECT 
          p.id,
          p.name,
          p.price,
          p.image_url,
          COALESCE(SUM(oi.quantity), 0) as sales_count
        FROM products p
        LEFT JOIN order_items oi ON p.id = oi.product_id
        LEFT JOIN orders o ON oi.order_id = o.id AND o.status IN (2, 3, 4)
        WHERE p.status = 1
        GROUP BY p.id, p.name, p.price, p.image_url
        ORDER BY sales_count DESC
        LIMIT ?
      `, [parseInt(limit)]);

      res.json({
        success: true,
        data: {
          list: products.map(product => ({
            ...product,
            price: parseFloat(product.price),
            sales_count: parseInt(product.sales_count)
          }))
        }
      });
    } catch (error) {
      console.error('获取热销商品失败:', error);
      res.status(500).json({
        success: false,
        message: '获取热销商品失败'
      });
    }
  }

  // 获取系统通知
  static async getSystemNotifications(req, res) {
    try {
      const { limit = 5 } = req.query;

      // 检查库存不足的商品
      const [lowStockProducts] = await db.query(`
        SELECT name, stock FROM products WHERE stock < 10 AND status = 1 LIMIT 3
      `);

      // 检查待处理订单
      const [pendingOrders] = await db.query(`
        SELECT COUNT(*) as count FROM orders WHERE status = 1
      `);

      // 检查今日新用户
      const [newUsers] = await db.query(`
        SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE()
      `);

      const notifications = [];

      // 添加库存不足通知
      lowStockProducts.forEach(product => {
        notifications.push({
          type: 'warning',
          title: '库存不足提醒',
          message: `商品"${product.name}"库存仅剩${product.stock}件`,
          time: '刚刚',
          created_at: new Date().toISOString()
        });
      });

      // 添加待处理订单通知
      if (pendingOrders[0]?.count > 0) {
        notifications.push({
          type: 'info',
          title: '待处理订单',
          message: `您有${pendingOrders[0].count}个订单待处理`,
          time: '5分钟前',
          created_at: new Date(Date.now() - 5 * 60 * 1000).toISOString()
        });
      }

      // 添加新用户注册通知
      if (newUsers[0]?.count > 0) {
        notifications.push({
          type: 'success',
          title: '新用户注册',
          message: `今日新增${newUsers[0].count}位用户注册`,
          time: '10分钟前',
          created_at: new Date(Date.now() - 10 * 60 * 1000).toISOString()
        });
      }

      // 添加系统维护通知（示例）
      notifications.push({
        type: 'info',
        title: '系统维护通知',
        message: '系统将于今晚23:00-01:00进行例行维护',
        time: '1小时前',
        created_at: new Date(Date.now() - 60 * 60 * 1000).toISOString()
      });

      res.json({
        success: true,
        data: {
          list: notifications.slice(0, parseInt(limit))
        }
      });
    } catch (error) {
      console.error('获取系统通知失败:', error);
      res.status(500).json({
        success: false,
        message: '获取系统通知失败'
      });
    }
  }

  // 获取系统状态
  static async getSystemStatus(req, res) {
    try {
      // 这里可以集成真实的系统监控数据
      // 目前返回模拟数据
      const status = {
        cpu: Math.floor(Math.random() * 80) + 10,
        memory: Math.floor(Math.random() * 70) + 20,
        disk: Math.floor(Math.random() * 60) + 15,
        uptime: Date.now() - (Math.random() * 86400000 * 7), // 随机7天内
        network: Math.floor(Math.random() * 30) + 70
      };

      res.json({
        success: true,
        data: status
      });
    } catch (error) {
      console.error('获取系统状态失败:', error);
      res.status(500).json({
        success: false,
        message: '获取系统状态失败'
      });
    }
  }

  // 格式化时间
  static formatTime(date) {
    const now = new Date();
    const targetDate = new Date(date);
    const diff = now - targetDate;

    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;

    return targetDate.toLocaleDateString();
  }
}

module.exports = DashboardController;
