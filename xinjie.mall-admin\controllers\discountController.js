const DiscountModel = require('../models/discountModel');
const productModel = require('../models/productModel');
const categoryModel = require('../models/categoryModel');

// 获取折扣列表
exports.getList = async (req, res) => {
  try {
    const {
      page = 1,
      pageSize = 10,
      name = '',
      type = '',
      status = '',
      applicable_to = ''
    } = req.query;

    const result = await DiscountModel.findAll({
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      name,
      type: type ? parseInt(type) : '',
      status: status !== '' ? parseInt(status) : '',
      applicable_to: applicable_to ? parseInt(applicable_to) : ''
    });

    res.json({
      success: true,
      data: result,
      message: '获取折扣列表成功'
    });
  } catch (error) {
    console.error('获取折扣列表失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '获取折扣列表失败'
    });
  }
};

// 获取折扣详情
exports.getDetail = async (req, res) => {
  try {
    const { id } = req.params;
    console.log('获取折扣详情，ID:', id);

    const discount = await DiscountModel.findById(id);

    if (!discount) {
      return res.status(404).json({
        success: false,
        data: null,
        message: '折扣不存在'
      });
    }

    // 获取关联的商品和分类
    let products = [];
    let categories = [];

    if (discount.applicable_to === 2) {
      products = await DiscountModel.getDiscountProducts(id);
    } else if (discount.applicable_to === 3) {
      categories = await DiscountModel.getDiscountCategories(id);
    }

    res.json({
      success: true,
      data: {
        ...discount,
        products,
        categories
      },
      message: '获取折扣详情成功'
    });
  } catch (error) {
    console.error('获取折扣详情失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '获取折扣详情失败'
    });
  }
};

// 创建折扣
exports.create = async (req, res) => {
  try {
    const discountData = req.body;
    const { product_ids = [], category_ids = [] } = discountData;

    // 验证必填字段
    if (!discountData.name || !discountData.type || !discountData.value || 
        !discountData.start_time || !discountData.end_time) {
      return res.status(400).json({
        success: false,
        data: null,
        message: '请填写完整的折扣信息'
      });
    }

    // 验证时间
    if (new Date(discountData.start_time) >= new Date(discountData.end_time)) {
      return res.status(400).json({
        success: false,
        data: null,
        message: '开始时间必须早于结束时间'
      });
    }

    // 验证折扣值
    if (discountData.type == 1 && (discountData.value <= 0 || discountData.value > 100)) {
      return res.status(400).json({
        success: false,
        data: null,
        message: '百分比折扣值必须在0-100之间'
      });
    }

    if (discountData.type == 2 && discountData.value <= 0) {
      return res.status(400).json({
        success: false,
        data: null,
        message: '固定金额折扣值必须大于0'
      });
    }

    console.log('准备创建折扣，数据:', discountData);

    // 创建折扣
    const discountId = await DiscountModel.create({
      ...discountData,
      created_by: req.user?.id || null
    });

    console.log('折扣创建成功，ID:', discountId);

    // 设置关联商品
    if (discountData.applicable_to == 2 && product_ids.length > 0) {
      console.log('设置关联商品:', product_ids);
      await DiscountModel.saveDiscountProducts(discountId, product_ids);
    }

    // 设置关联分类
    if (discountData.applicable_to == 3 && category_ids.length > 0) {
      console.log('设置关联分类:', category_ids);
      await DiscountModel.saveDiscountCategories(discountId, category_ids);
    }

    console.log('折扣创建完成，返回响应');
    res.json({
      success: true,
      data: { id: discountId },
      message: '折扣创建成功'
    });
  } catch (error) {
    console.error('创建折扣失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '创建折扣失败'
    });
  }
};

// 更新折扣
exports.update = async (req, res) => {
  try {
    const { id } = req.params;
    const discountData = req.body;
    const { product_ids = [], category_ids = [] } = discountData;

    // 检查折扣是否存在
    const existingDiscount = await DiscountModel.findById(id);
    if (!existingDiscount) {
      return res.status(404).json({
        success: false,
        data: null,
        message: '折扣不存在'
      });
    }

    // 验证时间
    if (new Date(discountData.start_time) >= new Date(discountData.end_time)) {
      return res.status(400).json({
        success: false,
        data: null,
        message: '开始时间必须早于结束时间'
      });
    }

    console.log('准备更新折扣，ID:', id, '数据:', discountData);

    // 更新折扣
    await DiscountModel.update(id, {
      ...discountData,
      updated_by: req.user?.id || null
    });

    // 更新关联商品
    if (discountData.applicable_to == 2) {
      await DiscountModel.saveDiscountProducts(id, product_ids);
    } else {
      await DiscountModel.saveDiscountProducts(id, []);
    }

    // 更新关联分类
    if (discountData.applicable_to == 3) {
      await DiscountModel.saveDiscountCategories(id, category_ids);
    } else {
      await DiscountModel.saveDiscountCategories(id, []);
    }

    res.json({
      success: true,
      data: null,
      message: '折扣更新成功'
    });
  } catch (error) {
    console.error('更新折扣失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '更新折扣失败'
    });
  }
};

// 删除折扣
exports.delete = async (req, res) => {
  try {
    const { id } = req.params;

    const existingDiscount = await DiscountModel.findById(id);
    if (!existingDiscount) {
      return res.status(404).json({
        success: false,
        data: null,
        message: '折扣不存在'
      });
    }

    await DiscountModel.delete(id);

    res.json({
      success: true,
      data: null,
      message: '折扣删除成功'
    });
  } catch (error) {
    console.error('删除折扣失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '删除折扣失败'
    });
  }
};

// 更新折扣状态
exports.updateStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (status === undefined || status === null) {
      return res.status(400).json({
        success: false,
        data: null,
        message: '请提供状态参数'
      });
    }

    await DiscountModel.updateStatus(id, status);

    res.json({
      success: true,
      data: null,
      message: '状态更新成功'
    });
  } catch (error) {
    console.error('更新折扣状态失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '更新折扣状态失败'
    });
  }
};

// 获取可选商品列表
exports.getAvailableProducts = async (req, res) => {
  try {
    const { pageSize = 100 } = req.query;

    const result = await productModel.findAll({
      page: 1,
      pageSize: parseInt(pageSize),
      status: 1 // 只获取已上架的商品
    });

    res.json({
      success: true,
      data: result,
      message: '获取可选商品列表成功'
    });
  } catch (error) {
    console.error('获取可选商品列表失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '获取可选商品列表失败'
    });
  }
};

// 获取可选分类列表
exports.getAvailableCategories = async (req, res) => {
  try {
    const categories = await categoryModel.findAll({
      all: true // 获取所有分类，不分页
    });

    res.json({
      success: true,
      data: categories,
      message: '获取可选分类列表成功'
    });
  } catch (error) {
    console.error('获取可选分类列表失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '获取可选分类列表失败'
    });
  }
};

// 获取折扣类型选项
exports.getDiscountTypes = async (req, res) => {
  try {
    const types = [
      { value: 1, label: '百分比折扣' },
      { value: 2, label: '固定金额折扣' },
      { value: 3, label: '满减折扣' }
    ];

    res.json({
      success: true,
      data: types,
      message: '获取折扣类型成功'
    });
  } catch (error) {
    console.error('获取折扣类型失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '获取折扣类型失败'
    });
  }
};

// 获取适用范围选项
exports.getApplicableToOptions = async (req, res) => {
  try {
    const options = [
      { value: 1, label: '全部商品' },
      { value: 2, label: '指定商品' },
      { value: 3, label: '指定分类' }
    ];

    res.json({
      success: true,
      data: options,
      message: '获取适用范围选项成功'
    });
  } catch (error) {
    console.error('获取适用范围选项失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '获取适用范围选项失败'
    });
  }
};
