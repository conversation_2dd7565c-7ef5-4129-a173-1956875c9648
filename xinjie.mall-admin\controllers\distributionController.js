/**
 * 分销管理控制器
 * 负责后台分销商管理、佣金管理、数据统计等功能
 */

const distributionService = require('../services/distributionService');
const distributionRiskService = require('../services/distributionRiskService');
const { query } = require('../src/config/database');

const distributionController = {

  /**
   * 获取分销商列表
   */
  getDistributorList: async (req, res) => {
    try {
      const { 
        page = 1, 
        pageSize = 10, 
        keyword = '', 
        status = '', 
        level = '',
        sortBy = 'created_at',
        sortOrder = 'desc'
      } = req.query;

      const offset = (page - 1) * pageSize;
      let whereConditions = ['u.distributor_status > 0'];
      let queryParams = [];

      // 搜索条件
      if (keyword) {
        whereConditions.push('(u.nickname LIKE ? OR u.phone LIKE ? OR u.distributor_code LIKE ?)');
        queryParams.push(`%${keyword}%`, `%${keyword}%`, `%${keyword}%`);
      }

      if (status) {
        whereConditions.push('u.distributor_status = ?');
        queryParams.push(status);
      }

      if (level) {
        whereConditions.push('u.distributor_level = ?');
        queryParams.push(level);
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      // 获取分销商列表
      const distributorsQuery = `
        SELECT 
          u.id,
          u.nickname,
          u.phone,
          u.avatar,
          u.distributor_code,
          u.distributor_status,
          u.distributor_level,
          u.total_commission,
          u.total_customers,
          u.distributor_apply_time,
          u.distributor_approve_time,
          u.user_level,
          u.points,
          u.balance,
          COALESCE(stats.today_commission, 0) as today_commission,
          COALESCE(stats.month_commission, 0) as month_commission,
          COALESCE(stats.total_orders, 0) as total_orders
        FROM users u
        LEFT JOIN (
          SELECT 
            distributor_user_id,
            SUM(CASE WHEN DATE(created_at) = CURDATE() AND status = 1 THEN commission_amount ELSE 0 END) as today_commission,
            SUM(CASE WHEN YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE()) AND status = 1 THEN commission_amount ELSE 0 END) as month_commission,
            COUNT(CASE WHEN status = 1 THEN 1 END) as total_orders
          FROM distributor_orders
          GROUP BY distributor_user_id
        ) stats ON u.id = stats.distributor_user_id
        ${whereClause}
        ORDER BY u.${sortBy} ${sortOrder.toUpperCase()}
        LIMIT ? OFFSET ?
      `;

      queryParams.push(parseInt(pageSize), offset);
      const distributors = await query(distributorsQuery, queryParams);

      // 获取总数
      const countQuery = `
        SELECT COUNT(*) as total
        FROM users u
        ${whereClause}
      `;
      const countParams = queryParams.slice(0, -2); // 移除 limit 和 offset 参数
      const [countResult] = await query(countQuery, countParams);

      res.json({
        success: true,
        data: {
          list: distributors,
          pagination: {
            current: parseInt(page),
            pageSize: parseInt(pageSize),
            total: countResult.total
          }
        },
        message: '获取分销商列表成功'
      });

    } catch (error) {
      console.error('获取分销商列表失败:', {
        error: error.message,
        stack: error.stack,
        params: { page, pageSize, keyword, status, level }
      });
      res.status(500).json({
        success: false,
        message: '获取分销商列表失败',
        ...(process.env.NODE_ENV === 'development' && { error: error.message })
      });
    }
  },

  /**
   * 获取分销商详情
   */
  getDistributorDetail: async (req, res) => {
    try {
      const { id } = req.params;

      // 获取分销商基本信息
      const [distributorInfo] = await query(`
        SELECT 
          u.*,
          ml.level_name,
          ml.discount_rate
        FROM users u
        LEFT JOIN member_levels ml ON u.user_level = ml.level_code
        WHERE u.id = ? AND u.distributor_status > 0
      `, [id]);

      if (!distributorInfo.length) {
        return res.status(404).json({
          success: false,
          message: '分销商不存在'
        });
      }

      const distributor = distributorInfo[0];

      // 获取分销关系
      const [relations] = await query(`
        SELECT 
          dr.level,
          dr.created_at as bind_time,
          u.id as user_id,
          u.nickname,
          u.phone,
          u.avatar
        FROM distributor_relations dr
        JOIN users u ON dr.child_user_id = u.id
        WHERE dr.parent_user_id = ? AND dr.status = 1
        ORDER BY dr.created_at DESC
        LIMIT 20
      `, [id]);

      // 获取佣金统计
      const [commissionStats] = await query(`
        SELECT 
          COUNT(*) as total_orders,
          SUM(commission_amount) as total_commission,
          SUM(CASE WHEN status = 1 THEN commission_amount ELSE 0 END) as settled_commission,
          SUM(CASE WHEN status = 0 THEN commission_amount ELSE 0 END) as pending_commission,
          SUM(CASE WHEN DATE(created_at) = CURDATE() THEN commission_amount ELSE 0 END) as today_commission,
          SUM(CASE WHEN YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE()) THEN commission_amount ELSE 0 END) as month_commission
        FROM distributor_orders
        WHERE distributor_user_id = ?
      `, [id]);

      // 获取最近分享记录
      const [shareRecords] = await query(`
        SELECT 
          share_type,
          share_title,
          click_count,
          register_count,
          order_count,
          total_commission,
          created_at
        FROM share_records
        WHERE sharer_user_id = ?
        ORDER BY created_at DESC
        LIMIT 10
      `, [id]);

      res.json({
        success: true,
        data: {
          distributor,
          relations,
          commissionStats: commissionStats[0] || {},
          shareRecords
        },
        message: '获取分销商详情成功'
      });

    } catch (error) {
      console.error('获取分销商详情失败:', error);
      res.status(500).json({
        success: false,
        message: '获取分销商详情失败'
      });
    }
  },

  /**
   * 更新分销商状态
   */
  updateDistributorStatus: async (req, res) => {
    try {
      const { id } = req.params;
      const { status, reason = '' } = req.body;

      if (![0, 1, 2, 3].includes(status)) {
        return res.status(400).json({
          success: false,
          message: '无效的状态值'
        });
      }

      // 检查分销商是否存在
      const [distributorInfo] = await query(
        'SELECT id, nickname, distributor_status FROM users WHERE id = ? AND distributor_status > 0',
        [id]
      );

      if (!distributorInfo.length) {
        return res.status(404).json({
          success: false,
          message: '分销商不存在'
        });
      }

      // 更新状态
      await query(
        'UPDATE users SET distributor_status = ?, updated_at = NOW() WHERE id = ?',
        [status, id]
      );

      // 记录操作日志
      const statusText = {
        0: '普通用户',
        1: '正常',
        2: '暂停',
        3: '禁用'
      };

      console.log(`管理员更新分销商状态: 用户ID=${id}, 状态=${statusText[status]}, 原因=${reason}`);

      res.json({
        success: true,
        message: `分销商状态已更新为${statusText[status]}`
      });

    } catch (error) {
      console.error('更新分销商状态失败:', error);
      res.status(500).json({
        success: false,
        message: '更新分销商状态失败'
      });
    }
  },

  /**
   * 获取佣金记录列表
   */
  getCommissionList: async (req, res) => {
    try {
      const { 
        page = 1, 
        pageSize = 10, 
        distributorId = '', 
        status = '',
        startDate = '',
        endDate = '',
        sortBy = 'created_at',
        sortOrder = 'desc'
      } = req.query;

      const offset = (page - 1) * pageSize;
      let whereConditions = [];
      let queryParams = [];

      // 搜索条件
      if (distributorId) {
        whereConditions.push('cr.user_id = ?');
        queryParams.push(distributorId);
      }

      if (status !== '') {
        whereConditions.push('cr.status = ?');
        queryParams.push(status);
      }

      if (startDate) {
        whereConditions.push('DATE(cr.created_at) >= ?');
        queryParams.push(startDate);
      }

      if (endDate) {
        whereConditions.push('DATE(cr.created_at) <= ?');
        queryParams.push(endDate);
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      // 获取佣金记录列表
      const commissionsQuery = `
        SELECT 
          cr.*,
          u.nickname,
          u.phone,
          u.distributor_code,
          o.order_no,
          o.total_amount as order_amount
        FROM commission_records cr
        JOIN users u ON cr.user_id = u.id
        JOIN orders o ON cr.order_id = o.id
        ${whereClause}
        ORDER BY cr.${sortBy} ${sortOrder.toUpperCase()}
        LIMIT ? OFFSET ?
      `;

      queryParams.push(parseInt(pageSize), offset);
      const commissions = await query(commissionsQuery, queryParams);

      // 获取总数
      const countQuery = `
        SELECT COUNT(*) as total
        FROM commission_records cr
        JOIN users u ON cr.user_id = u.id
        JOIN orders o ON cr.order_id = o.id
        ${whereClause}
      `;
      const countParams = queryParams.slice(0, -2);
      const [countResult] = await query(countQuery, countParams);

      // 获取统计数据
      const [statsResult] = await query(`
        SELECT 
          COUNT(*) as total_records,
          SUM(amount) as total_amount,
          SUM(CASE WHEN status = 1 THEN amount ELSE 0 END) as settled_amount,
          SUM(CASE WHEN status = 0 THEN amount ELSE 0 END) as pending_amount
        FROM commission_records cr
        JOIN users u ON cr.user_id = u.id
        JOIN orders o ON cr.order_id = o.id
        ${whereClause}
      `, countParams);

      res.json({
        success: true,
        data: {
          list: commissions,
          pagination: {
            current: parseInt(page),
            pageSize: parseInt(pageSize),
            total: countResult.total
          },
          statistics: statsResult[0] || {}
        },
        message: '获取佣金记录成功'
      });

    } catch (error) {
      console.error('获取佣金记录失败:', error);
      res.status(500).json({
        success: false,
        message: '获取佣金记录失败'
      });
    }
  },

  /**
   * 手动结算佣金
   */
  settleCommission: async (req, res) => {
    try {
      const { commissionIds } = req.body;

      if (!Array.isArray(commissionIds) || commissionIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: '请选择要结算的佣金记录'
        });
      }

      const db = require('../src/config/database');
      const connection = await db.getConnection();

      try {
        await connection.beginTransaction();

        let totalSettled = 0;
        let settledCount = 0;

        for (const commissionId of commissionIds) {
          // 获取佣金记录
          const [commissionInfo] = await connection.query(
            'SELECT * FROM commission_records WHERE id = ? AND status = 0',
            [commissionId]
          );

          if (commissionInfo.length === 0) continue;

          const commission = commissionInfo[0];

          // 更新佣金记录状态
          await connection.query(
            'UPDATE commission_records SET status = 1, settle_time = NOW() WHERE id = ?',
            [commissionId]
          );

          // 更新分销订单状态
          await connection.query(
            'UPDATE distributor_orders SET status = 1, settle_time = NOW() WHERE order_id = ? AND distributor_user_id = ?',
            [commission.order_id, commission.user_id]
          );

          // 增加用户余额
          const balanceService = require('../services/balanceService');
          await balanceService.updateUserBalanceWithTransaction(
            connection,
            commission.user_id,
            commission.amount,
            1, // 增加
            5, // 分销佣金来源
            commission.id,
            `管理员手动结算佣金：${commission.amount}元`
          );

          // 增加积分
          if (commission.points > 0) {
            const pointsService = require('../services/pointsService');
            await pointsService.addPointsForDistribution(connection, commission.user_id, commission.points);
          }

          // 更新用户累计佣金
          await connection.query(
            'UPDATE users SET total_commission = total_commission + ? WHERE id = ?',
            [commission.amount, commission.user_id]
          );

          totalSettled += parseFloat(commission.amount);
          settledCount++;
        }

        await connection.commit();

        res.json({
          success: true,
          data: {
            settledCount,
            totalSettled: totalSettled.toFixed(2)
          },
          message: `成功结算${settledCount}笔佣金，总金额${totalSettled.toFixed(2)}元`
        });

      } catch (error) {
        await connection.rollback();
        throw error;
      } finally {
        connection.release();
      }

    } catch (error) {
      console.error('手动结算佣金失败:', error);
      res.status(500).json({
        success: false,
        message: '手动结算佣金失败'
      });
    }
  },

  /**
   * 获取分销数据统计
   */
  getDistributionStats: async (req, res) => {
    try {
      const { period = '7' } = req.query; // 7天、30天、90天

      // 基础统计数据
      const [basicStats] = await query(`
        SELECT
          COUNT(CASE WHEN distributor_status = 1 THEN 1 END) as active_distributors,
          COUNT(CASE WHEN distributor_status > 0 THEN 1 END) as total_distributors,
          COUNT(CASE WHEN distributor_status = 1 AND DATE(distributor_approve_time) = CURDATE() THEN 1 END) as today_new_distributors
        FROM users
        WHERE distributor_status > 0
      `);

      // 佣金统计
      const [commissionStats] = await query(`
        SELECT
          COUNT(*) as total_orders,
          SUM(commission_amount) as total_commission,
          SUM(CASE WHEN status = 1 THEN commission_amount ELSE 0 END) as settled_commission,
          SUM(CASE WHEN status = 0 THEN commission_amount ELSE 0 END) as pending_commission,
          SUM(CASE WHEN DATE(created_at) = CURDATE() THEN commission_amount ELSE 0 END) as today_commission
        FROM distributor_orders
      `);

      // 分享统计
      const [shareStats] = await query(`
        SELECT
          COUNT(*) as total_shares,
          SUM(click_count) as total_clicks,
          SUM(register_count) as total_registers,
          SUM(order_count) as total_share_orders,
          COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_shares
        FROM share_records
      `);

      // 趋势数据
      const trendQuery = `
        SELECT
          DATE(created_at) as date,
          COUNT(*) as orders,
          SUM(commission_amount) as commission
        FROM distributor_orders
        WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
        GROUP BY DATE(created_at)
        ORDER BY date ASC
      `;
      const trendData = await query(trendQuery, [parseInt(period)]);

      // 分销商等级分布
      const [levelDistribution] = await query(`
        SELECT
          distributor_level,
          COUNT(*) as count
        FROM users
        WHERE distributor_status = 1
        GROUP BY distributor_level
        ORDER BY distributor_level
      `);

      // 热门分享商品
      const [hotProducts] = await query(`
        SELECT
          sr.share_target_id as product_id,
          p.name as product_name,
          COUNT(*) as share_count,
          SUM(sr.click_count) as total_clicks,
          SUM(sr.order_count) as total_orders
        FROM share_records sr
        JOIN products p ON sr.share_target_id = p.id
        WHERE sr.share_type = 1 AND sr.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY sr.share_target_id, p.name
        ORDER BY share_count DESC
        LIMIT 10
      `, [parseInt(period)]);

      res.json({
        success: true,
        data: {
          basicStats: basicStats[0] || {},
          commissionStats: commissionStats[0] || {},
          shareStats: shareStats[0] || {},
          trendData,
          levelDistribution,
          hotProducts
        },
        message: '获取分销统计数据成功'
      });

    } catch (error) {
      console.error('获取分销统计数据失败:', error);
      res.status(500).json({
        success: false,
        message: '获取分销统计数据失败'
      });
    }
  },

  /**
   * 获取分销配置
   */
  getDistributionConfig: async (req, res) => {
    try {
      const configs = await query(
        'SELECT config_key, config_value, config_desc, config_type FROM distribution_config WHERE status = 1 ORDER BY config_type, id'
      );

      // 按类型分组
      const configGroups = {
        system: [],
        business: [],
        risk: []
      };

      const typeMap = { 1: 'system', 2: 'business', 3: 'risk' };

      configs.forEach(config => {
        const groupKey = typeMap[config.config_type] || 'system';
        configGroups[groupKey].push({
          key: config.config_key,
          value: config.config_value,
          desc: config.config_desc
        });
      });

      res.json({
        success: true,
        data: configGroups,
        message: '获取分销配置成功'
      });

    } catch (error) {
      console.error('获取分销配置失败:', error);
      res.status(500).json({
        success: false,
        message: '获取分销配置失败'
      });
    }
  },

  /**
   * 更新分销配置
   */
  updateDistributionConfig: async (req, res) => {
    try {
      const { configs } = req.body;

      if (!Array.isArray(configs) || configs.length === 0) {
        return res.status(400).json({
          success: false,
          message: '配置数据格式错误'
        });
      }

      const db = require('../src/config/database');
      const connection = await db.getConnection();

      try {
        await connection.beginTransaction();

        for (const config of configs) {
          const { key, value } = config;

          if (!key || value === undefined) continue;

          await connection.query(
            'UPDATE distribution_config SET config_value = ?, updated_at = NOW() WHERE config_key = ?',
            [value.toString(), key]
          );
        }

        await connection.commit();

        // 清除分销服务的配置缓存
        distributionService.lastCacheUpdate = 0;

        res.json({
          success: true,
          message: '分销配置更新成功'
        });

      } catch (error) {
        await connection.rollback();
        throw error;
      } finally {
        connection.release();
      }

    } catch (error) {
      console.error('更新分销配置失败:', error);
      res.status(500).json({
        success: false,
        message: '更新分销配置失败'
      });
    }
  },

  /**
   * 获取分销关系树
   */
  getDistributionTree: async (req, res) => {
    try {
      const { userId } = req.params;

      // 获取用户信息
      const [userInfo] = await query(
        'SELECT id, nickname, phone, distributor_code, distributor_status FROM users WHERE id = ?',
        [userId]
      );

      if (!userInfo.length) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      const user = userInfo[0];

      // 获取一级下级
      const [level1Users] = await query(`
        SELECT
          u.id, u.nickname, u.phone, u.avatar, u.distributor_status,
          dr.created_at as bind_time,
          COUNT(sub_dr.id) as sub_count
        FROM distributor_relations dr
        JOIN users u ON dr.child_user_id = u.id
        LEFT JOIN distributor_relations sub_dr ON u.id = sub_dr.parent_user_id AND sub_dr.status = 1
        WHERE dr.parent_user_id = ? AND dr.level = 1 AND dr.status = 1
        GROUP BY u.id, u.nickname, u.phone, u.avatar, u.distributor_status, dr.created_at
        ORDER BY dr.created_at DESC
      `, [userId]);

      // 为每个一级用户获取二级下级
      for (const level1User of level1Users) {
        const [level2Users] = await query(`
          SELECT
            u.id, u.nickname, u.phone, u.avatar, u.distributor_status,
            dr.created_at as bind_time
          FROM distributor_relations dr
          JOIN users u ON dr.child_user_id = u.id
          WHERE dr.parent_user_id = ? AND dr.level = 1 AND dr.status = 1
          ORDER BY dr.created_at DESC
          LIMIT 10
        `, [level1User.id]);

        level1User.children = level2Users;
      }

      res.json({
        success: true,
        data: {
          user,
          level1Users
        },
        message: '获取分销关系树成功'
      });

    } catch (error) {
      console.error('获取分销关系树失败:', error);
      res.status(500).json({
        success: false,
        message: '获取分销关系树失败'
      });
    }
  }
};

module.exports = distributionController;
