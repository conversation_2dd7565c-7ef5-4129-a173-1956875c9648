const balanceService = require('../services/balanceService');
const orderPaymentService = require('../services/orderPaymentService');
const ResponseHelper = require('../utils/responseHelper');

// 获取用户余额信息
exports.getBalanceInfo = ResponseHelper.asyncHandler(async (req, res) => {
  const userId = req.user?.id;

  if (!userId) {
    return ResponseHelper.unauthorized(res);
  }

  const balanceInfo = await balanceService.getUserBalanceInfo(userId);
  return ResponseHelper.success(res, balanceInfo, '获取余额信息成功');
});

// 模拟用户充值（方案B）
exports.simulateRecharge = ResponseHelper.asyncHandler(async (req, res) => {
  const userId = req.user?.id;
  const { amount, paymentMethod = 'wechat', remark } = req.body;

  if (!userId) {
    return ResponseHelper.unauthorized(res);
  }

  // 参数验证
  ResponseHelper.validateRequired({ amount }, ['amount']);
  const validAmount = ResponseHelper.validateNumber(amount, '充值金额', { min: 0.01, max: 10000 });

  const result = await balanceService.simulateUserRecharge(
    userId,
    validAmount,
    paymentMethod,
    remark
  );

  return ResponseHelper.success(res, result, '充值成功');
});

// 获取充值记录
exports.getRechargeHistory = async (req, res) => {
  try {
    const userId = req.user?.id;
    const { page = 1, pageSize = 10 } = req.query;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未登录'
      });
    }

    const result = await balanceService.getUserRechargeHistory(
      userId,
      parseInt(page),
      parseInt(pageSize)
    );

    res.json({
      success: true,
      data: result,
      message: '获取充值记录成功'
    });

  } catch (error) {
    console.error('获取充值记录失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '获取充值记录失败'
    });
  }
};

// 获取余额变动记录
exports.getBalanceHistory = async (req, res) => {
  try {
    const userId = req.user?.id;
    const { page = 1, pageSize = 10 } = req.query;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未登录'
      });
    }

    const result = await balanceService.getUserBalanceHistory(
      userId,
      parseInt(page),
      parseInt(pageSize)
    );

    res.json({
      success: true,
      data: result,
      message: '获取余额记录成功'
    });

  } catch (error) {
    console.error('获取余额记录失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '获取余额记录失败'
    });
  }
};

// 获取订单支付选项
exports.getPaymentOptions = async (req, res) => {
  try {
    const userId = req.user?.id;
    const { orderId } = req.params;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未登录'
      });
    }

    if (!orderId || isNaN(orderId)) {
      return res.status(400).json({
        success: false,
        message: '订单ID无效'
      });
    }

    const result = await orderPaymentService.getPaymentOptions(
      parseInt(orderId),
      userId
    );

    res.json(result);

  } catch (error) {
    console.error('获取支付选项失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '获取支付选项失败'
    });
  }
};

// 创建支付订单
exports.createPayment = async (req, res) => {
  try {
    const userId = req.user?.id;
    const paymentData = {
      ...req.body,
      userId
    };

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未登录'
      });
    }

    // 参数验证
    const { orderId, paymentMethod } = paymentData;

    if (!orderId || isNaN(orderId)) {
      return res.status(400).json({
        success: false,
        message: '订单ID无效'
      });
    }

    if (!paymentMethod) {
      return res.status(400).json({
        success: false,
        message: '请选择支付方式'
      });
    }

    const result = await orderPaymentService.createPayment(paymentData);

    res.json(result);

  } catch (error) {
    console.error('创建支付失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '创建支付失败'
    });
  }
};

// 查询支付状态
exports.getPaymentStatus = async (req, res) => {
  try {
    const userId = req.user?.id;
    const { orderId } = req.params;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未登录'
      });
    }

    if (!orderId || isNaN(orderId)) {
      return res.status(400).json({
        success: false,
        message: '订单ID无效'
      });
    }

    const result = await orderPaymentService.getPaymentStatus(
      parseInt(orderId),
      userId
    );

    res.json(result);

  } catch (error) {
    console.error('查询支付状态失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '查询支付状态失败'
    });
  }
};

// 支付回调处理（为真实支付预留）
exports.handlePaymentCallback = async (req, res) => {
  try {
    const callbackData = req.body;

    const result = await orderPaymentService.handlePaymentCallback(callbackData);

    if (result.success) {
      res.json({
        success: true,
        message: '回调处理成功'
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.message || '回调处理失败'
      });
    }

  } catch (error) {
    console.error('支付回调处理失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '回调处理失败'
    });
  }
};
