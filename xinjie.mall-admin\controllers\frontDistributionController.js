/**
 * 前端分销控制器
 * 负责小程序端分销功能的API接口
 */

const distributionService = require('../services/distributionService');
const distributionRiskService = require('../services/distributionRiskService');
const { query } = require('../src/config/database');

const frontDistributionController = {

  /**
   * 申请成为分销商
   */
  applyDistributor: async (req, res) => {
    try {
      const userId = req.user?.id;
      const { reason = '' } = req.body;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '请先登录'
        });
      }

      const result = await distributionService.applyDistributor(userId, { reason });

      res.json(result);

    } catch (error) {
      console.error('申请分销商失败:', error);
      res.status(500).json({
        success: false,
        message: error.message || '申请分销商失败'
      });
    }
  },

  /**
   * 获取分销中心信息
   */
  getDistributionCenter: async (req, res) => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '请先登录'
        });
      }

      // 获取用户分销商信息
      const [distributorInfo] = await query(`
        SELECT 
          id, nickname, phone, avatar, distributor_status, distributor_code,
          distributor_level, total_commission, total_customers,
          distributor_apply_time, distributor_approve_time
        FROM users 
        WHERE id = ?
      `, [userId]);

      if (!distributorInfo.length) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      const distributor = distributorInfo[0];

      // 如果不是分销商，返回基础信息
      if (distributor.distributor_status === 0) {
        return res.json({
          success: true,
          data: {
            isDistributor: false,
            userInfo: distributor,
            canApply: true
          },
          message: '获取分销中心信息成功'
        });
      }

      // 获取今日数据
      const [todayData] = await query(`
        SELECT 
          COUNT(CASE WHEN sr.created_at >= CURDATE() THEN 1 END) as today_shares,
          SUM(CASE WHEN sr.created_at >= CURDATE() THEN sr.click_count ELSE 0 END) as today_clicks,
          COUNT(CASE WHEN do.created_at >= CURDATE() THEN 1 END) as today_orders,
          SUM(CASE WHEN do.created_at >= CURDATE() THEN do.commission_amount ELSE 0 END) as today_commission
        FROM users u
        LEFT JOIN share_records sr ON u.id = sr.sharer_user_id
        LEFT JOIN distributor_orders do ON u.id = do.distributor_user_id
        WHERE u.id = ?
      `, [userId]);

      // 获取本月数据
      const [monthData] = await query(`
        SELECT 
          COUNT(CASE WHEN YEAR(sr.created_at) = YEAR(CURDATE()) AND MONTH(sr.created_at) = MONTH(CURDATE()) THEN 1 END) as month_shares,
          SUM(CASE WHEN YEAR(sr.created_at) = YEAR(CURDATE()) AND MONTH(sr.created_at) = MONTH(CURDATE()) THEN sr.click_count ELSE 0 END) as month_clicks,
          COUNT(CASE WHEN YEAR(do.created_at) = YEAR(CURDATE()) AND MONTH(do.created_at) = MONTH(CURDATE()) THEN 1 END) as month_orders,
          SUM(CASE WHEN YEAR(do.created_at) = YEAR(CURDATE()) AND MONTH(do.created_at) = MONTH(CURDATE()) THEN do.commission_amount ELSE 0 END) as month_commission
        FROM users u
        LEFT JOIN share_records sr ON u.id = sr.sharer_user_id
        LEFT JOIN distributor_orders do ON u.id = do.distributor_user_id
        WHERE u.id = ?
      `, [userId]);

      // 获取最近订单
      const [recentOrders] = await query(`
        SELECT 
          do.order_id,
          do.commission_amount,
          do.level,
          do.status,
          do.created_at,
          o.order_no,
          u.nickname as buyer_nickname
        FROM distributor_orders do
        JOIN orders o ON do.order_id = o.id
        JOIN users u ON do.buyer_user_id = u.id
        WHERE do.distributor_user_id = ?
        ORDER BY do.created_at DESC
        LIMIT 10
      `, [userId]);

      // 获取我的客户
      const [myCustomers] = await query(`
        SELECT 
          u.id, u.nickname, u.avatar, u.phone,
          dr.level, dr.created_at as bind_time,
          COUNT(o.id) as order_count,
          SUM(CASE WHEN o.order_status >= 2 THEN o.total_amount ELSE 0 END) as total_spent
        FROM distributor_relations dr
        JOIN users u ON dr.child_user_id = u.id
        LEFT JOIN orders o ON u.id = o.user_id
        WHERE dr.parent_user_id = ? AND dr.status = 1
        GROUP BY u.id, u.nickname, u.avatar, u.phone, dr.level, dr.created_at
        ORDER BY dr.created_at DESC
        LIMIT 20
      `, [userId]);

      res.json({
        success: true,
        data: {
          isDistributor: true,
          distributorInfo: distributor,
          todayData: todayData[0] || {},
          monthData: monthData[0] || {},
          recentOrders,
          myCustomers
        },
        message: '获取分销中心信息成功'
      });

    } catch (error) {
      console.error('获取分销中心信息失败:', error);
      res.status(500).json({
        success: false,
        message: '获取分销中心信息失败'
      });
    }
  },

  /**
   * 生成分享链接
   */
  generateShareLink: async (req, res) => {
    try {
      const userId = req.user?.id;
      const { shareType, targetId } = req.body;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '请先登录'
        });
      }

      // 输入验证
      if (!shareType || ![1, 2, 3].includes(Number(shareType))) {
        return res.status(400).json({
          success: false,
          message: '分享类型无效'
        });
      }

      // 如果是商品分享，必须提供商品ID
      if (shareType === 1 && (!targetId || !Number.isInteger(Number(targetId)))) {
        return res.status(400).json({
          success: false,
          message: '商品ID无效'
        });
      }

      // 风控检查
      const riskCheck = await distributionRiskService.checkShareRisk(userId, Number(shareType), targetId);
      if (!riskCheck.passed) {
        return res.status(400).json({
          success: false,
          message: riskCheck.reason
        });
      }

      const result = await distributionService.generateShareLink(userId, Number(shareType), targetId);

      res.json(result);

    } catch (error) {
      console.error('生成分享链接失败:', {
        error: error.message,
        userId: req.user?.id,
        shareType: req.body?.shareType,
        targetId: req.body?.targetId
      });
      res.status(500).json({
        success: false,
        message: error.message || '生成分享链接失败'
      });
    }
  },

  /**
   * 处理分享链接点击
   */
  handleShareClick: async (req, res) => {
    try {
      const { shareCode } = req.body;
      const visitorInfo = {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date()
      };

      const result = await distributionService.handleShareClick(shareCode, visitorInfo);

      res.json(result);

    } catch (error) {
      console.error('处理分享点击失败:', error);
      res.status(500).json({
        success: false,
        message: '处理分享点击失败'
      });
    }
  },

  /**
   * 获取佣金明细
   */
  getCommissionDetail: async (req, res) => {
    try {
      const userId = req.user?.id;
      const { page = 1, pageSize = 10, status = '' } = req.query;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '请先登录'
        });
      }

      const offset = (page - 1) * pageSize;
      let whereConditions = ['cr.user_id = ?'];
      let queryParams = [userId];

      if (status !== '') {
        whereConditions.push('cr.status = ?');
        queryParams.push(status);
      }

      const whereClause = `WHERE ${whereConditions.join(' AND ')}`;

      // 获取佣金记录
      const commissionsQuery = `
        SELECT 
          cr.*,
          o.order_no,
          o.total_amount as order_amount,
          u.nickname as buyer_nickname
        FROM commission_records cr
        JOIN orders o ON cr.order_id = o.id
        JOIN users u ON o.user_id = u.id
        ${whereClause}
        ORDER BY cr.created_at DESC
        LIMIT ? OFFSET ?
      `;

      queryParams.push(parseInt(pageSize), offset);
      const commissions = await query(commissionsQuery, queryParams);

      // 获取总数
      const countQuery = `
        SELECT COUNT(*) as total
        FROM commission_records cr
        ${whereClause}
      `;
      const countParams = queryParams.slice(0, -2);
      const [countResult] = await query(countQuery, countParams);

      // 获取统计数据
      const [statsResult] = await query(`
        SELECT 
          SUM(amount) as total_commission,
          SUM(CASE WHEN status = 1 THEN amount ELSE 0 END) as settled_commission,
          SUM(CASE WHEN status = 0 THEN amount ELSE 0 END) as pending_commission,
          SUM(points) as total_points
        FROM commission_records
        WHERE user_id = ?
      `, [userId]);

      res.json({
        success: true,
        data: {
          list: commissions,
          pagination: {
            current: parseInt(page),
            pageSize: parseInt(pageSize),
            total: countResult.total
          },
          statistics: statsResult[0] || {}
        },
        message: '获取佣金明细成功'
      });

    } catch (error) {
      console.error('获取佣金明细失败:', error);
      res.status(500).json({
        success: false,
        message: '获取佣金明细失败'
      });
    }
  },

  /**
   * 获取分享记录
   */
  getShareRecords: async (req, res) => {
    try {
      const userId = req.user?.id;
      const { page = 1, pageSize = 10 } = req.query;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '请先登录'
        });
      }

      const offset = (page - 1) * pageSize;

      // 获取分享记录
      const shareRecords = await query(`
        SELECT 
          id, share_type, share_target_id, share_title, share_desc,
          click_count, register_count, order_count, total_commission,
          created_at
        FROM share_records
        WHERE sharer_user_id = ?
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
      `, [userId, parseInt(pageSize), offset]);

      // 获取总数
      const [countResult] = await query(
        'SELECT COUNT(*) as total FROM share_records WHERE sharer_user_id = ?',
        [userId]
      );

      res.json({
        success: true,
        data: {
          list: shareRecords,
          pagination: {
            current: parseInt(page),
            pageSize: parseInt(pageSize),
            total: countResult.total
          }
        },
        message: '获取分享记录成功'
      });

    } catch (error) {
      console.error('获取分享记录失败:', error);
      res.status(500).json({
        success: false,
        message: '获取分享记录失败'
      });
    }
  },

  /**
   * 获取我的客户
   */
  getMyCustomers: async (req, res) => {
    try {
      const userId = req.user?.id;
      const { page = 1, pageSize = 10, level = '' } = req.query;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '请先登录'
        });
      }

      const offset = (page - 1) * pageSize;
      let whereConditions = ['dr.parent_user_id = ?', 'dr.status = 1'];
      let queryParams = [userId];

      if (level) {
        whereConditions.push('dr.level = ?');
        queryParams.push(level);
      }

      const whereClause = `WHERE ${whereConditions.join(' AND ')}`;

      // 获取客户列表
      const customersQuery = `
        SELECT 
          u.id, u.nickname, u.avatar, u.phone,
          dr.level, dr.created_at as bind_time,
          COUNT(o.id) as order_count,
          SUM(CASE WHEN o.order_status >= 2 THEN o.total_amount ELSE 0 END) as total_spent,
          SUM(CASE WHEN do.status = 1 THEN do.commission_amount ELSE 0 END) as total_commission
        FROM distributor_relations dr
        JOIN users u ON dr.child_user_id = u.id
        LEFT JOIN orders o ON u.id = o.user_id
        LEFT JOIN distributor_orders do ON o.id = do.order_id AND do.distributor_user_id = ?
        ${whereClause}
        GROUP BY u.id, u.nickname, u.avatar, u.phone, dr.level, dr.created_at
        ORDER BY dr.created_at DESC
        LIMIT ? OFFSET ?
      `;

      queryParams.push(userId); // 用于 LEFT JOIN distributor_orders
      queryParams.push(parseInt(pageSize), offset);
      const customers = await query(customersQuery, queryParams);

      // 获取总数
      const countQuery = `
        SELECT COUNT(*) as total
        FROM distributor_relations dr
        ${whereClause}
      `;
      const countParams = [userId];
      if (level) countParams.push(level);
      const [countResult] = await query(countQuery, countParams);

      res.json({
        success: true,
        data: {
          list: customers,
          pagination: {
            current: parseInt(page),
            pageSize: parseInt(pageSize),
            total: countResult.total
          }
        },
        message: '获取我的客户成功'
      });

    } catch (error) {
      console.error('获取我的客户失败:', error);
      res.status(500).json({
        success: false,
        message: '获取我的客户失败'
      });
    }
  }
};

module.exports = frontDistributionController;
