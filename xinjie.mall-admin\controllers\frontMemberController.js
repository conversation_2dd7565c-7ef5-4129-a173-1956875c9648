const memberService = require('../services/memberService');
const ResponseHelper = require('../utils/responseHelper');

// 获取用户会员信息
exports.getMemberInfo = ResponseHelper.asyncHandler(async (req, res) => {
  const userId = req.user?.id;

  if (!userId) {
    return ResponseHelper.unauthorized(res);
  }

  const memberInfo = await memberService.getUserMemberInfo(userId);
  return ResponseHelper.success(res, memberInfo, '获取会员信息成功');
});

// 获取会员等级列表
exports.getMemberLevels = async (req, res) => {
  try {
    const levels = await memberService.getAllMemberLevels();

    res.json({
      success: true,
      data: levels,
      message: '获取会员等级成功'
    });

  } catch (error) {
    console.error('获取会员等级失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '获取会员等级失败'
    });
  }
};

// 获取会员权益
exports.getMemberBenefits = async (req, res) => {
  try {
    const { levelId } = req.params;

    if (!levelId || isNaN(levelId)) {
      return res.status(400).json({
        success: false,
        message: '会员等级ID无效'
      });
    }

    const benefits = await memberService.getMemberBenefits(parseInt(levelId));

    res.json({
      success: true,
      data: benefits,
      message: '获取会员权益成功'
    });

  } catch (error) {
    console.error('获取会员权益失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '获取会员权益失败'
    });
  }
};

// 获取积分记录
exports.getPointsHistory = async (req, res) => {
  try {
    const userId = req.user?.id;
    const { page = 1, pageSize = 10 } = req.query;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未登录'
      });
    }

    const result = await memberService.getUserPointsHistory(
      userId,
      parseInt(page),
      parseInt(pageSize)
    );

    res.json({
      success: true,
      data: result,
      message: '获取积分记录成功'
    });

  } catch (error) {
    console.error('获取积分记录失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '获取积分记录失败'
    });
  }
};
