const orderModel = require('../models/orderModel');
const cartModel = require('../models/cartModel');
const productModel = require('../models/productModel');

// 创建订单
exports.createOrder = async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未登录'
      });
    }

    const {
      items, // 订单商品列表（包含折扣信息）
      receiver_name,
      receiver_phone,
      receiver_address,
      remark = '',
      cart_item_ids = [] // 需要从购物车删除的商品ID
    } = req.body;

    console.log('创建订单请求:', {
      userId,
      itemsCount: items?.length,
      receiver_name,
      cart_item_ids
    });

    // 验证必填字段
    if (!items || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: '订单商品不能为空'
      });
    }

    if (!receiver_name || !receiver_phone || !receiver_address) {
      return res.status(400).json({
        success: false,
        message: '收货信息不完整'
      });
    }

    // 验证商品库存
    for (const item of items) {
      const product = await productModel.findById(item.product_id);
      if (!product) {
        return res.status(400).json({
          success: false,
          message: `商品 ${item.product_name} 不存在`
        });
      }

      if (product.stock < item.quantity) {
        return res.status(400).json({
          success: false,
          message: `商品 ${item.product_name} 库存不足，当前库存：${product.stock}`
        });
      }
    }

    // 计算订单金额
    const shippingFee = 0; // 暂时免运费，可以根据实际需求计算
    const amounts = orderModel.calculateOrderAmount(items, shippingFee);

    console.log('订单金额计算结果:', amounts);

    // 生成订单号
    const orderNo = orderModel.generateOrderNo();

    // 创建订单
    const orderId = await orderModel.createWithDiscount({
      user_id: userId,
      order_no: orderNo,
      total_amount: amounts.totalAmount,
      original_amount: amounts.originalAmount,
      discount_amount: amounts.discountAmount,
      shipping_fee: amounts.shippingFee,
      final_amount: amounts.finalAmount,
      receiver_name,
      receiver_phone,
      receiver_address,
      remark
    });

    console.log('订单创建成功，ID:', orderId);

    // 创建订单商品
    const orderItems = items.map(item => ({
      product_id: item.product_id,
      product_name: item.product_name,
      product_image: item.product_image,
      quantity: item.quantity,
      original_price: item.originalPrice,
      discount_price: item.discountPrice,
      discount_amount: (item.originalPrice - item.discountPrice) * item.quantity,
      total_price: item.discountPrice * item.quantity,
      specs: item.specs
    }));

    await orderModel.createOrderItems(orderId, orderItems);

    console.log('订单商品创建成功');

    // 减少商品库存
    for (const item of items) {
      await productModel.updateStock(item.product_id, item.quantity, 'decrease');
    }

    console.log('库存扣减完成');

    // 从购物车删除已下单的商品
    if (cart_item_ids.length > 0) {
      await cartModel.removeItems(userId, cart_item_ids);
      console.log('购物车商品清理完成');
    }

    res.json({
      success: true,
      data: {
        orderId,
        orderNo,
        totalAmount: amounts.finalAmount
      },
      message: '订单创建成功'
    });

  } catch (error) {
    console.error('创建订单失败:', error);
    res.status(500).json({
      success: false,
      message: '创建订单失败'
    });
  }
};

// 获取订单详情
exports.getOrderDetail = async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未登录'
      });
    }

    const { id } = req.params;

    console.log('获取订单详情:', { userId, orderId: id });

    const order = await orderModel.findById(id);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: '订单不存在'
      });
    }

    // 验证订单所有权
    if (order.user_id !== userId) {
      return res.status(403).json({
        success: false,
        message: '无权访问此订单'
      });
    }

    // 获取订单商品
    const orderItems = await orderModel.getOrderItems(id);

    res.json({
      success: true,
      data: {
        ...order,
        items: orderItems
      },
      message: '获取订单详情成功'
    });

  } catch (error) {
    console.error('获取订单详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取订单详情失败'
    });
  }
};

// 获取用户订单列表
exports.getUserOrders = async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未登录'
      });
    }

    const {
      page = 1,
      pageSize = 10,
      status = ''
    } = req.query;

    console.log('获取用户订单列表:', { userId, page, pageSize, status });

    const result = await orderModel.findByUserId(userId, {
      page,
      pageSize,
      status
    });

    // 为每个订单获取商品信息
    const ordersWithItems = await Promise.all(
      result.list.map(async (order) => {
        const items = await orderModel.getOrderItems(order.id);
        return {
          ...order,
          items
        };
      })
    );

    res.json({
      success: true,
      data: {
        ...result,
        list: ordersWithItems
      },
      message: '获取订单列表成功'
    });

  } catch (error) {
    console.error('获取订单列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取订单列表失败'
    });
  }
};

// 取消订单
exports.cancelOrder = async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未登录'
      });
    }

    const { id } = req.params;

    console.log('取消订单:', { userId, orderId: id });

    const order = await orderModel.findById(id);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: '订单不存在'
      });
    }

    // 验证订单所有权
    if (order.user_id !== userId) {
      return res.status(403).json({
        success: false,
        message: '无权操作此订单'
      });
    }

    // 只有待支付的订单可以取消
    if (order.payment_status !== 0) {
      return res.status(400).json({
        success: false,
        message: '只有待支付的订单可以取消'
      });
    }

    // 更新订单状态
    await orderModel.updateStatus(id, 'cancelled');

    // 恢复商品库存
    const orderItems = await orderModel.getOrderItems(id);
    for (const item of orderItems) {
      await productModel.updateStock(item.product_id, item.quantity, 'increase');
    }

    console.log('订单取消成功，库存已恢复');

    res.json({
      success: true,
      message: '订单取消成功'
    });

  } catch (error) {
    console.error('取消订单失败:', error);
    res.status(500).json({
      success: false,
      message: '取消订单失败'
    });
  }
};
