const balanceModel = require('../models/balanceModel');
const orderModel = require('../models/orderModel');
const paymentService = require('../services/payment');

const frontOrderPaymentController = {
  // 创建订单支付
  createOrderPayment: async (req, res) => {
    try {
      const { orderId, paymentMethod, useBalance = false, balanceAmount = 0 } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '用户未登录'
        });
      }

      if (!orderId) {
        return res.status(400).json({
          success: false,
          message: '订单ID不能为空'
        });
      }

      // 获取订单信息
      const order = await orderModel.findById(orderId);
      if (!order) {
        return res.status(404).json({
          success: false,
          message: '订单不存在'
        });
      }

      if (order.user_id !== userId) {
        return res.status(403).json({
          success: false,
          message: '无权限访问此订单'
        });
      }

      if (order.payment_status === 1) {
        return res.status(400).json({
          success: false,
          message: '订单已支付'
        });
      }

      const orderAmount = parseFloat(order.final_amount || order.total_amount);
      let remainingAmount = orderAmount;
      let paymentResult = {};

      // 处理余额支付
      if (useBalance && balanceAmount > 0) {
        const actualBalanceAmount = Math.min(balanceAmount, orderAmount);
        
        try {
          const balancePayResult = await balanceModel.balancePayment(
            userId,
            orderId,
            actualBalanceAmount,
            `订单${order.order_no}余额支付`
          );
          
          remainingAmount -= actualBalanceAmount;
          paymentResult.balancePayment = {
            amount: actualBalanceAmount,
            newBalance: balancePayResult.newBalance
          };
          
          // 记录订单余额支付
          await orderModel.recordBalancePayment(orderId, actualBalanceAmount);
          
        } catch (error) {
          return res.status(400).json({
            success: false,
            message: error.message || '余额支付失败'
          });
        }
      }

      // 如果还有剩余金额，需要第三方支付
      if (remainingAmount > 0.01) {
        if (!paymentMethod || (paymentMethod !== 'wechat' && paymentMethod !== 'alipay')) {
          return res.status(400).json({
            success: false,
            message: '请选择有效的支付方式'
          });
        }

        // 创建第三方支付订单
        let thirdPartyPayment;
        if (paymentMethod === 'wechat') {
          thirdPartyPayment = await paymentService.createWechatOrderPayment(orderId, remainingAmount, userId);
        } else if (paymentMethod === 'alipay') {
          thirdPartyPayment = await paymentService.createAlipayOrderPayment(orderId, remainingAmount, userId);
        }

        paymentResult.thirdPartyPayment = {
          amount: remainingAmount,
          paymentData: thirdPartyPayment
        };
      } else {
        // 纯余额支付，直接完成订单
        await orderModel.completePayment(orderId, 'balance', null);
        paymentResult.orderCompleted = true;
      }

      res.json({
        success: true,
        data: {
          orderId,
          orderAmount,
          balanceUsed: paymentResult.balancePayment?.amount || 0,
          thirdPartyAmount: remainingAmount > 0.01 ? remainingAmount : 0,
          paymentCompleted: paymentResult.orderCompleted || false,
          paymentData: paymentResult.thirdPartyPayment?.paymentData || null,
          newBalance: paymentResult.balancePayment?.newBalance || null
        },
        message: paymentResult.orderCompleted ? '支付成功' : '支付订单创建成功'
      });

    } catch (error) {
      console.error('创建订单支付失败:', error);
      res.status(500).json({
        success: false,
        message: error.message || '支付失败'
      });
    }
  },

  // 第三方支付回调
  orderPaymentCallback: async (req, res) => {
    try {
      const { order_no, payment_status, transaction_id, payment_method } = req.body;

      if (payment_status === 'success') {
        // 完成订单支付
        const result = await orderModel.completeThirdPartyPayment(
          order_no, 
          payment_method, 
          transaction_id
        );
        
        console.log('订单支付成功:', result);
        
        res.json({
          success: true,
          message: '支付成功'
        });
      } else {
        res.json({
          success: false,
          message: '支付失败'
        });
      }

    } catch (error) {
      console.error('订单支付回调处理失败:', error);
      res.status(500).json({
        success: false,
        message: '回调处理失败'
      });
    }
  },

  // 查询支付状态
  getPaymentStatus: async (req, res) => {
    try {
      const { orderId } = req.params;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '用户未登录'
        });
      }

      const order = await orderModel.findById(orderId);
      if (!order) {
        return res.status(404).json({
          success: false,
          message: '订单不存在'
        });
      }

      if (order.user_id !== userId) {
        return res.status(403).json({
          success: false,
          message: '无权限访问此订单'
        });
      }

      res.json({
        success: true,
        data: {
          orderId: order.id,
          orderNo: order.order_no,
          paymentStatus: order.payment_status,
          orderStatus: order.order_status,
          totalAmount: order.total_amount,
          finalAmount: order.final_amount,
          balanceUsed: order.balance_used || 0,
          paymentTime: order.payment_time,
          isPaid: order.payment_status === 1
        }
      });

    } catch (error) {
      console.error('查询支付状态失败:', error);
      res.status(500).json({
        success: false,
        message: '查询支付状态失败'
      });
    }
  },

  // 获取支付选项（用户余额等信息）
  getPaymentOptions: async (req, res) => {
    try {
      const { orderId } = req.params;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '用户未登录'
        });
      }

      // 获取用户余额
      const userBalance = await balanceModel.getUserBalance(userId);
      
      // 获取订单信息
      const order = await orderModel.findById(orderId);
      if (!order) {
        return res.status(404).json({
          success: false,
          message: '订单不存在'
        });
      }

      if (order.user_id !== userId) {
        return res.status(403).json({
          success: false,
          message: '无权限访问此订单'
        });
      }

      const orderAmount = parseFloat(order.final_amount || order.total_amount);
      const availableBalance = parseFloat(userBalance);
      const maxBalanceUse = Math.min(availableBalance, orderAmount);

      res.json({
        success: true,
        data: {
          orderId: order.id,
          orderAmount,
          userBalance: availableBalance,
          maxBalanceUse,
          canFullBalancePay: availableBalance >= orderAmount,
          paymentMethods: [
            { value: 'wechat', label: '微信支付', enabled: true },
            { value: 'alipay', label: '支付宝', enabled: true },
            { value: 'balance', label: '余额支付', enabled: availableBalance > 0 }
          ]
        }
      });

    } catch (error) {
      console.error('获取支付选项失败:', error);
      res.status(500).json({
        success: false,
        message: '获取支付选项失败'
      });
    }
  }
};

module.exports = frontOrderPaymentController;
