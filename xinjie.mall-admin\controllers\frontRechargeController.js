const balanceModel = require('../models/balanceModel');
const paymentService = require('../services/payment');

const frontRechargeController = {
  // 用户发起充值
  userRecharge: async (req, res) => {
    try {
      const { amount, payment_method = 'wechat' } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '用户未登录'
        });
      }

      if (!amount || amount <= 0) {
        return res.status(400).json({
          success: false,
          message: '充值金额必须大于0'
        });
      }

      // 创建充值订单
      const rechargeOrder = await balanceModel.createRechargeOrder(
        userId,
        parseFloat(amount),
        payment_method
      );

      // 根据支付方式创建支付订单
      let paymentData;
      if (payment_method === 'wechat') {
        paymentData = await paymentService.createWechatRechargePayment(rechargeOrder);
      } else if (payment_method === 'alipay') {
        paymentData = await paymentService.createAlipayRechargePayment(rechargeOrder);
      } else {
        throw new Error('不支持的支付方式');
      }

      res.json({
        success: true,
        data: {
          rechargeId: rechargeOrder.id,
          orderNo: rechargeOrder.order_no,
          amount: rechargeOrder.amount,
          paymentData
        },
        message: '充值订单创建成功'
      });

    } catch (error) {
      console.error('用户充值失败:', error);
      res.status(500).json({
        success: false,
        message: error.message || '充值失败'
      });
    }
  },

  // 充值支付回调
  rechargeCallback: async (req, res) => {
    try {
      const { order_no, payment_status, transaction_id } = req.body;

      if (payment_status === 'success') {
        // 处理充值成功
        const result = await balanceModel.completeRecharge(order_no, transaction_id);
        
        console.log('充值成功:', result);
        
        res.json({
          success: true,
          message: '充值成功'
        });
      } else {
        res.json({
          success: false,
          message: '充值失败'
        });
      }

    } catch (error) {
      console.error('充值回调处理失败:', error);
      res.status(500).json({
        success: false,
        message: '回调处理失败'
      });
    }
  },

  // 获取用户余额
  getUserBalance: async (req, res) => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '用户未登录'
        });
      }

      const balance = await balanceModel.getUserBalance(userId);

      res.json({
        success: true,
        data: {
          balance: parseFloat(balance).toFixed(2)
        }
      });

    } catch (error) {
      console.error('获取用户余额失败:', error);
      res.status(500).json({
        success: false,
        message: '获取余额失败'
      });
    }
  },

  // 获取充值记录
  getRechargeHistory: async (req, res) => {
    try {
      const userId = req.user?.id;
      const { page = 1, pageSize = 10 } = req.query;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '用户未登录'
        });
      }

      const result = await balanceModel.getUserRechargeHistory(
        userId,
        parseInt(page),
        parseInt(pageSize)
      );

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      console.error('获取充值记录失败:', error);
      res.status(500).json({
        success: false,
        message: '获取充值记录失败'
      });
    }
  },

  // 获取余额变动记录
  getBalanceHistory: async (req, res) => {
    try {
      const userId = req.user?.id;
      const { page = 1, pageSize = 10 } = req.query;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '用户未登录'
        });
      }

      const result = await balanceModel.getUserBalanceHistory(
        userId,
        parseInt(page),
        parseInt(pageSize)
      );

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      console.error('获取余额记录失败:', error);
      res.status(500).json({
        success: false,
        message: '获取余额记录失败'
      });
    }
  }
};

module.exports = frontRechargeController;
