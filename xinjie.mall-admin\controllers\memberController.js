const memberModel = require('../models/memberModel');

// 获取所有会员等级（用于下拉选择）
exports.getAllLevels = async (req, res) => {
  try {
    const levels = await memberModel.getAllLevels();

    res.json({
      success: true,
      data: levels,
      message: '获取会员等级列表成功'
    });
  } catch (error) {
    console.error('获取会员等级列表失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '获取会员等级列表失败'
    });
  }
};

// 获取会员等级列表（分页）
exports.levelList = async (req, res) => {
  try {
    const {
      page = 1,
      pageSize = 10,
      status = '',
      levelName = ''
    } = req.query;

    const result = await memberModel.getLevelList({
      page,
      pageSize,
      status,
      levelName
    });

    res.json({
      success: true,
      data: result,
      message: '获取会员等级列表成功'
    });
  } catch (error) {
    console.error('获取会员等级列表失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '获取会员等级列表失败'
    });
  }
};

// 获取会员等级详情
exports.levelDetail = async (req, res) => {
  try {
    const { id } = req.params;
    const level = await memberModel.getLevelById(id);

    if (!level) {
      return res.status(404).json({
        success: false,
        data: null,
        message: '会员等级不存在'
      });
    }

    // 获取该等级的权益列表
    const benefits = await memberModel.getBenefitsByLevelId(id);

    res.json({
      success: true,
      data: {
        ...level,
        benefits
      },
      message: '获取会员等级详情成功'
    });
  } catch (error) {
    console.error('获取会员等级详情失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '获取会员等级详情失败'
    });
  }
};

// 创建会员等级
exports.createLevel = async (req, res) => {
  try {
    const levelData = req.body;

    // 验证必填字段
    if (!levelData.level_code || !levelData.level_name) {
      return res.status(400).json({
        success: false,
        data: null,
        message: '等级代码和等级名称不能为空'
      });
    }

    // 验证等级代码是否已存在
    const existingLevel = await memberModel.getLevelByCode(levelData.level_code);
    if (existingLevel) {
      return res.status(400).json({
        success: false,
        data: null,
        message: '等级代码已存在'
      });
    }

    console.log('准备创建会员等级，数据:', levelData);

    const levelId = await memberModel.createLevel(levelData);

    console.log('会员等级创建成功，ID:', levelId);

    res.json({
      success: true,
      data: { id: levelId },
      message: '会员等级创建成功'
    });
  } catch (error) {
    console.error('创建会员等级失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '创建会员等级失败'
    });
  }
};

// 更新会员等级
exports.updateLevel = async (req, res) => {
  try {
    const { id } = req.params;
    const levelData = req.body;

    // 检查等级是否存在
    const level = await memberModel.getLevelById(id);
    if (!level) {
      return res.status(404).json({
        success: false,
        data: null,
        message: '会员等级不存在'
      });
    }

    console.log('准备更新会员等级，数据:', levelData);

    await memberModel.updateLevel(id, levelData);

    console.log('会员等级更新成功');

    res.json({
      success: true,
      data: { id },
      message: '会员等级更新成功'
    });
  } catch (error) {
    console.error('更新会员等级失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '更新会员等级失败'
    });
  }
};

// 删除会员等级
exports.deleteLevel = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查等级是否存在
    const level = await memberModel.getLevelById(id);
    if (!level) {
      return res.status(404).json({
        success: false,
        data: null,
        message: '会员等级不存在'
      });
    }

    console.log('准备删除会员等级，ID:', id);

    await memberModel.deleteLevel(id);

    console.log('会员等级删除成功');

    res.json({
      success: true,
      data: null,
      message: '会员等级删除成功'
    });
  } catch (error) {
    console.error('删除会员等级失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: error.message || '删除会员等级失败'
    });
  }
};

// 更新会员等级状态
exports.updateLevelStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    // 检查等级是否存在
    const level = await memberModel.getLevelById(id);
    if (!level) {
      return res.status(404).json({
        success: false,
        data: null,
        message: '会员等级不存在'
      });
    }

    console.log('准备更新会员等级状态，数据:', { id, status });

    await memberModel.updateLevelStatus(id, status);

    console.log('会员等级状态更新成功');

    res.json({
      success: true,
      data: { id },
      message: '会员等级状态更新成功'
    });
  } catch (error) {
    console.error('更新会员等级状态失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '更新会员等级状态失败'
    });
  }
};

// 获取会员权益列表（分页）
exports.benefitList = async (req, res) => {
  try {
    const {
      page = 1,
      pageSize = 10,
      levelId = '',
      benefitType = '',
      status = ''
    } = req.query;

    const result = await memberModel.getBenefitList({
      page,
      pageSize,
      levelId,
      benefitType,
      status
    });

    res.json({
      success: true,
      data: result,
      message: '获取会员权益列表成功'
    });
  } catch (error) {
    console.error('获取会员权益列表失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '获取会员权益列表失败'
    });
  }
};

// 创建会员权益
exports.createBenefit = async (req, res) => {
  try {
    const benefitData = req.body;

    // 验证必填字段
    if (!benefitData.level_id || !benefitData.benefit_type || !benefitData.benefit_name) {
      return res.status(400).json({
        success: false,
        data: null,
        message: '等级ID、权益类型和权益名称不能为空'
      });
    }

    // 验证等级是否存在
    const level = await memberModel.getLevelById(benefitData.level_id);
    if (!level) {
      return res.status(404).json({
        success: false,
        data: null,
        message: '会员等级不存在'
      });
    }

    console.log('准备创建会员权益，数据:', benefitData);

    const benefitId = await memberModel.createBenefit(benefitData);

    console.log('会员权益创建成功，ID:', benefitId);

    res.json({
      success: true,
      data: { id: benefitId },
      message: '会员权益创建成功'
    });
  } catch (error) {
    console.error('创建会员权益失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '创建会员权益失败'
    });
  }
};

// 更新会员权益
exports.updateBenefit = async (req, res) => {
  try {
    const { id } = req.params;
    const benefitData = req.body;

    console.log('准备更新会员权益，数据:', benefitData);

    await memberModel.updateBenefit(id, benefitData);

    console.log('会员权益更新成功');

    res.json({
      success: true,
      data: { id },
      message: '会员权益更新成功'
    });
  } catch (error) {
    console.error('更新会员权益失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '更新会员权益失败'
    });
  }
};

// 删除会员权益
exports.deleteBenefit = async (req, res) => {
  try {
    const { id } = req.params;

    console.log('准备删除会员权益，ID:', id);

    await memberModel.deleteBenefit(id);

    console.log('会员权益删除成功');

    res.json({
      success: true,
      data: null,
      message: '会员权益删除成功'
    });
  } catch (error) {
    console.error('删除会员权益失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '删除会员权益失败'
    });
  }
};
