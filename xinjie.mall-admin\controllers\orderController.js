const orderModel = require('../models/orderModel');
const xlsx = require('xlsx');
const path = require('path');

exports.list = async (req, res) => {
  try {
    const {
      page = 1,
      pageSize = 10,
      order_no = '',
      receiver_name = '',
      order_status,
      start_date = '',
      end_date = '',
    } = req.query;
    const result = await orderModel.findAll({
      page,
      pageSize,
      order_no,
      receiver_name,
      order_status,
      start_date,
      end_date,
    });
    res.json({ success: true, data: result, message: '订单列表' });
  } catch (e) {
    res.status(500).json({ success: false, data: null, message: '获取订单列表失败' });
  }
};

exports.detail = async (req, res) => {
  try {
    const data = await orderModel.findById(req.params.id);
    data.logs = [
      { time: data.created_at, action: '下单', user: data.receiver_name },
      data.delivery_time
        ? { time: data.delivery_time, action: '发货', user: '系统' }
        : null,
    ].filter(<PERSON><PERSON>an);
    res.json({ success: true, data, message: '订单详情' });
  } catch (e) {
    res.status(500).json({ success: false, data: null, message: '获取订单详情失败' });
  }
};

exports.create = async (req, res) => {
  try {
    const orderData = req.body;
    const orderNo = 'ORD' + Date.now() + Math.floor(Math.random() * 1000);
    orderData.order_no = orderNo;
    orderData.pay_status = 0;
    orderData.order_status = 0;
    orderData.freight_amount = orderData.freight_amount || 0;
    orderData.discount_amount = orderData.discount_amount || 0;
    const result = await orderModel.create(orderData);
    res.json({ success: true, data: result, message: '订单创建成功' });
  } catch (e) {
    res.status(500).json({ success: false, data: null, message: '创建订单失败' });
  }
};

exports.ship = async (req, res) => {
  try {
    await orderModel.ship(req.params.id, req.body);
    res.json({ success: true, data: null, message: '发货成功' });
  } catch (e) {
    res.status(500).json({ success: false, data: null, message: '发货失败' });
  }
};

exports.updateStatus = async (req, res) => {
  try {
    await orderModel.updateStatus(req.params.id, req.body.order_status);
    res.json({ success: true, data: null, message: '状态更新成功' });
  } catch (e) {
    res.status(500).json({ success: false, data: null, message: '状态更新失败' });
  }
};

exports.exportExcel = async (req, res) => {
  try {
    const {
      page = 1,
      pageSize = 1000,
      order_no = '',
      receiver_name = '',
      order_status = '',
      start_date = '',
      end_date = '',
    } = req.query;
    const result = await orderModel.findAll({
      page,
      pageSize,
      order_no,
      receiver_name,
      order_status,
      start_date,
      end_date,
    });
    const list = result.list || [];
    const statusMap = {
      0: '待付款',
      1: '待发货',
      2: '待收货',
      3: '已完成',
      4: '已取消',
    };
    const data = list.map(item => ({
      订单号: item.order_no,
      收货人: item.receiver_name,
      手机号: item.receiver_phone,
      金额: item.pay_amount,
      状态: statusMap[item.order_status] || item.order_status,
      下单时间: item.created_at,
    }));
    const ws = xlsx.utils.json_to_sheet(data);
    const wb = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(wb, ws, '订单列表');
    const buffer = xlsx.write(wb, { type: 'buffer', bookType: 'xlsx' });
    let filename = 'order_list';
    if (start_date && end_date) {
      filename = `order_list_${start_date}_${end_date}`;
    } else if (start_date) {
      filename = `order_list_from_${start_date}`;
    } else if (end_date) {
      filename = `order_list_to_${end_date}`;
    }
    res.setHeader(
      'Content-Disposition',
      `attachment; filename=${filename}.xlsx`
    );
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    );
    res.send(buffer);
  } catch (e) {
    res.status(500).json({ success: false, data: null, message: '导出失败' });
  }
};

exports.batchShip = async (req, res) => {
  try {
    const { ids, delivery_company, delivery_no } = req.body;
    if (!Array.isArray(ids) || !ids.length) {
      return res.status(400).json({ success: false, data: null, message: '请选择要发货的订单' });
    }
    for (const id of ids) {
      await orderModel.ship(id, { delivery_company, delivery_no });
    }
    res.json({ success: true, data: null, message: '批量发货成功' });
  } catch (e) {
    res.status(500).json({ success: false, data: null, message: '批量发货失败' });
  }
};

exports.batchStatus = async (req, res) => {
  try {
    const { ids, order_status } = req.body;
    if (!Array.isArray(ids) || !ids.length) {
      return res.status(400).json({ success: false, data: null, message: '请选择要操作的订单' });
    }
    for (const id of ids) {
      await orderModel.updateStatus(id, order_status);
    }
    res.json({ success: true, data: null, message: '批量操作成功' });
  } catch (e) {
    res.status(500).json({ success: false, data: null, message: '批量操作失败' });
  }
};

exports.stats = async (req, res) => {
  try {
    const statusRows = await orderModel.statusStats();
    const amountRows = await orderModel.amountStats();
    const trendRows = await orderModel.trendStats();
    res.json({ success: true, data: { status: statusRows, amount: amountRows, trend: trendRows }, message: '订单统计' });
  } catch (e) {
    res.status(500).json({ success: false, data: null, message: '获取订单统计失败' });
  }
};
