/**
 * 支付回调处理控制器
 * 处理微信支付、支付宝等第三方支付的回调通知
 */

const WechatPayServiceV2 = require('../services/wechatPayServiceV2');
const balanceService = require('../services/balanceService');
const orderModel = require('../models/orderModel');

class PaymentCallbackController {
  constructor() {
    this.wechatPayService = new WechatPayServiceV2();
  }

  /**
   * 微信支付回调处理
   */
  async handleWechatCallback(req, res) {
    try {
      console.log('收到微信支付回调请求');
      console.log('Headers:', req.headers);
      console.log('Body:', req.body);

      // 处理微信支付回调
      const result = await this.wechatPayService.handlePaymentCallback(req.body);
      
      if (result.success) {
        console.log('微信支付回调处理成功:', result);
        
        // 返回成功响应给微信
        res.status(200).json({
          code: 'SUCCESS',
          message: '成功'
        });
      } else {
        console.log('微信支付回调处理失败:', result);
        
        // 返回失败响应给微信
        res.status(400).json({
          code: 'FAIL',
          message: result.message || '处理失败'
        });
      }
      
    } catch (error) {
      console.error('微信支付回调处理异常:', error);
      
      // 返回失败响应给微信
      res.status(500).json({
        code: 'FAIL',
        message: '服务器内部错误'
      });
    }
  }

  /**
   * 支付宝回调处理
   */
  async handleAlipayCallback(req, res) {
    try {
      console.log('收到支付宝回调请求');
      console.log('Body:', req.body);

      const { out_trade_no, trade_status, transaction_id, total_amount } = req.body;
      
      if (trade_status === 'TRADE_SUCCESS') {
        // 更新订单支付状态
        const result = await this.updateOrderPaymentStatus(
          out_trade_no, 
          transaction_id, 
          'alipay',
          parseFloat(total_amount)
        );
        
        console.log('支付宝支付成功，订单更新结果:', result);
        
        // 返回成功响应给支付宝
        res.send('success');
      } else {
        console.log('支付宝支付未成功，状态:', trade_status);
        res.send('fail');
      }
      
    } catch (error) {
      console.error('支付宝回调处理异常:', error);
      res.send('fail');
    }
  }

  /**
   * 通用订单支付状态更新
   */
  async updateOrderPaymentStatus(orderNo, transactionId, paymentMethod, amount) {
    try {
      const db = require('../src/config/database');
      
      // 开始事务
      const connection = await db.getConnection();
      await connection.beginTransaction();
      
      try {
        // 查找订单
        const [orders] = await connection.query(
          'SELECT id, user_id, payment_status, total_amount FROM orders WHERE order_no = ?',
          [orderNo]
        );
        
        if (!orders.length) {
          throw new Error('订单不存在');
        }
        
        const order = orders[0];
        
        if (order.payment_status === 1) {
          console.log('订单已支付，跳过更新');
          await connection.rollback();
          return { orderId: order.id, alreadyPaid: true };
        }
        
        // 更新订单支付状态
        await connection.query(`
          UPDATE orders 
          SET payment_status = 1, 
              payment_method = ?, 
              transaction_id = ?, 
              payment_time = NOW(), 
              updated_at = NOW()
          WHERE order_no = ?
        `, [paymentMethod, transactionId, orderNo]);
        
        // 如果是充值订单，需要增加用户余额
        if (orderNo.startsWith('RC')) {
          await this.handleRechargeSuccess(connection, order.user_id, amount, orderNo, transactionId);
        }
        
        await connection.commit();
        
        console.log('订单支付状态更新成功:', { orderNo, transactionId, paymentMethod });
        
        return { 
          orderId: order.id, 
          orderNo, 
          transactionId,
          paymentMethod,
          updated: true 
        };
        
      } catch (error) {
        await connection.rollback();
        throw error;
      } finally {
        connection.release();
      }
      
    } catch (error) {
      console.error('更新订单支付状态失败:', error);
      throw error;
    }
  }

  /**
   * 处理充值成功
   */
  async handleRechargeSuccess(connection, userId, amount, orderNo, transactionId) {
    try {
      // 查找充值记录
      const [rechargeRecords] = await connection.query(
        'SELECT id FROM recharge_records WHERE order_no = ?',
        [orderNo]
      );
      
      if (rechargeRecords.length) {
        const rechargeId = rechargeRecords[0].id;
        
        // 更新充值记录状态
        await connection.query(`
          UPDATE recharge_records 
          SET payment_status = 1, 
              transaction_id = ?, 
              paid_at = NOW(), 
              updated_at = NOW()
          WHERE id = ?
        `, [transactionId, rechargeId]);
        
        // 增加用户余额
        await balanceService.addBalance(
          userId,
          amount,
          1, // 充值来源
          rechargeId,
          `充值成功：${amount}元，订单号：${orderNo}`
        );
        
        console.log('充值处理成功:', { userId, amount, orderNo });
      }
      
    } catch (error) {
      console.error('处理充值成功失败:', error);
      throw error;
    }
  }

  /**
   * 查询支付状态
   */
  async queryPaymentStatus(req, res) {
    try {
      const { orderNo } = req.params;
      
      if (!orderNo) {
        return res.status(400).json({
          success: false,
          message: '订单号不能为空'
        });
      }
      
      const db = require('../src/config/database');
      
      // 查询订单支付状态
      const [orders] = await db.query(`
        SELECT id, order_no, payment_status, payment_method, 
               transaction_id, payment_time, total_amount
        FROM orders 
        WHERE order_no = ?
      `, [orderNo]);
      
      if (!orders.length) {
        return res.status(404).json({
          success: false,
          message: '订单不存在'
        });
      }
      
      const order = orders[0];
      
      res.json({
        success: true,
        data: {
          orderNo: order.order_no,
          paymentStatus: order.payment_status,
          paymentMethod: order.payment_method,
          transactionId: order.transaction_id,
          paymentTime: order.payment_time,
          totalAmount: order.total_amount,
          isPaid: order.payment_status === 1
        },
        message: '查询成功'
      });
      
    } catch (error) {
      console.error('查询支付状态失败:', error);
      res.status(500).json({
        success: false,
        message: '查询失败'
      });
    }
  }

  /**
   * 手动触发支付成功（开发测试用）
   */
  async mockPaymentSuccess(req, res) {
    try {
      const { orderNo, paymentMethod = 'wechat' } = req.body;
      
      if (!orderNo) {
        return res.status(400).json({
          success: false,
          message: '订单号不能为空'
        });
      }
      
      // 生成模拟交易号
      const transactionId = `mock_${paymentMethod}_${Date.now()}`;
      
      // 更新订单支付状态
      const result = await this.updateOrderPaymentStatus(
        orderNo, 
        transactionId, 
        paymentMethod,
        100 // 模拟金额
      );
      
      res.json({
        success: true,
        data: result,
        message: '模拟支付成功'
      });
      
    } catch (error) {
      console.error('模拟支付失败:', error);
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  /**
   * 支付失败处理
   */
  async handlePaymentFailure(req, res) {
    try {
      const { orderNo, reason } = req.body;
      
      const db = require('../src/config/database');
      
      // 更新订单状态为支付失败
      await db.query(`
        UPDATE orders 
        SET payment_status = 2, 
            payment_failure_reason = ?,
            updated_at = NOW()
        WHERE order_no = ?
      `, [reason || '支付失败', orderNo]);
      
      res.json({
        success: true,
        message: '支付失败处理完成'
      });
      
    } catch (error) {
      console.error('处理支付失败异常:', error);
      res.status(500).json({
        success: false,
        message: '处理失败'
      });
    }
  }
}

module.exports = new PaymentCallbackController();
