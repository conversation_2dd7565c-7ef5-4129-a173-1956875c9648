const permissionModel = require('../models/permissionModel');

exports.list = async (req, res) => {
  try {
    const data = await permissionModel.findAll();
    res.json({ code: 0, data });
  } catch (e) {
    res
      .status(500)
      .json({ code: 1, msg: '获取权限列表失败', error: e.message });
  }
};

exports.detail = async (req, res) => {
  try {
    const data = await permissionModel.findById(req.params.id);
    res.json({ code: 0, data });
  } catch (e) {
    res
      .status(500)
      .json({ code: 1, msg: '获取权限详情失败', error: e.message });
  }
};

exports.create = async (req, res) => {
  try {
    const id = await permissionModel.create(req.body);
    res.json({ code: 0, msg: '添加成功', id });
  } catch (e) {
    res.status(500).json({ code: 1, msg: '添加失败', error: e.message });
  }
};

exports.update = async (req, res) => {
  try {
    await permissionModel.update(req.params.id, req.body);
    res.json({ code: 0, msg: '更新成功' });
  } catch (e) {
    res.status(500).json({ code: 1, msg: '更新失败', error: e.message });
  }
};

exports.delete = async (req, res) => {
  try {
    await permissionModel.delete(req.params.id);
    res.json({ code: 0, msg: '删除成功' });
  } catch (e) {
    res.status(500).json({ code: 1, msg: '删除失败', error: e.message });
  }
};
