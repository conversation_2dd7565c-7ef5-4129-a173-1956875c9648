const productModel = require('../models/productModel');

const xlsx = require('xlsx');
const path = require('path');
const fs = require('fs');

exports.list = async (req, res) => {
  try {
    const {
      page = 1,
      pageSize = 10,
      name = '',
      category_id,
      status,
    } = req.query;
    const result = await productModel.findAll({
      page,
      pageSize,
      name,
      category_id,
      status,
    });
    res.json({ success: true, data: result, message: '商品列表' });
  } catch (e) {
    res.status(500).json({ success: false, data: null, message: '获取商品列表失败' });
  }
};

exports.detail = async (req, res) => {
  try {
    const data = await productModel.findById(req.params.id);
    res.json({ success: true, data, message: '商品详情' });
  } catch (e) {
    res.status(500).json({ success: false, data: null, message: '获取商品详情失败' });
  }
};

exports.create = async (req, res) => {
  try {
    const id = await productModel.create(req.body);
    res.json({ success: true, data: { id }, message: '添加成功' });
  } catch (e) {
    res.status(500).json({ success: false, data: null, message: '添加失败' });
  }
};

exports.update = async (req, res) => {
  try {
    await productModel.update(req.params.id, req.body);
    res.json({ success: true, data: null, message: '更新成功' });
  } catch (e) {
    res.status(500).json({ success: false, data: null, message: '更新失败' });
  }
};

exports.delete = async (req, res) => {
  try {
    await productModel.delete(req.params.id);
    res.json({ success: true, data: null, message: '删除成功' });
  } catch (e) {
    res.status(500).json({ success: false, data: null, message: '删除失败' });
  }
};

exports.upload = (req, res) => {
  console.log('[唯一调试] productController.upload 被调用');
  try {
    if (!req.files || !req.files.file) {
      console.log('[唯一调试] productController 没有文件上传');
      return res.status(400).json({ code: 1, msg: '未上传文件' });
    }
    const file = req.files.file;
    console.log('[唯一调试] productController 文件信息:', {
      originalname: file.originalname,
      filename: file.filename,
      path: file.path,
      size: file.size
    });
    const path = require('path');
    const fs = require('fs');
    const uploadDir = path.join(__dirname, '../public/uploads/products');
    console.log('[唯一调试] productController 上传目录:', uploadDir);
    if (!fs.existsSync(uploadDir)) fs.mkdirSync(uploadDir, { recursive: true });
    const filename = Date.now() + '_' + file.name;
    const filepath = path.join(uploadDir, filename);
    console.log('[唯一调试] productController 目标文件路径:', filepath);
    file.mv(filepath, err => {
      console.log('[唯一调试] productController file.mv 回调，err:', err);
      if (err) {
        console.error('[唯一调试] productController 文件保存失败:', err);
        return res.status(500).json({ code: 1, msg: '上传失败' });
      }
      console.log('[唯一调试] productController 文件已保存到:', filepath);
      const url = '/uploads/products/' + filename;
      console.log('[唯一调试] productController 返回URL:', url);
      res.json({ code: 0, msg: '上传成功', data: { url } });
    });
  } catch (error) {
    console.error('[唯一调试] productController 上传异常:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

exports.import = async (req, res) => {
  try {
    if (!req.files || !req.files.file) {
      return res.status(400).json({ success: false, data: null, message: '未上传文件' });
    }
    const file = req.files.file;
    const uploadDir = path.join(__dirname, '../public/uploads/import');
    if (!fs.existsSync(uploadDir)) fs.mkdirSync(uploadDir, { recursive: true });
    const filename = Date.now() + '_' + file.name;
    const filepath = path.join(uploadDir, filename);
    await file.mv(filepath);

    const workbook = xlsx.readFile(filepath);
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const rows = xlsx.utils.sheet_to_json(sheet);
    if (!rows.length) {
      return res.status(400).json({ success: false, data: null, message: 'Excel内容为空' });
    }

    // 校验与导入
    const results = [];
    let successCount = 0;
    for (const [i, row] of rows.entries()) {
      // 字段校验
      if (!row.name || !row.category_id || !row.price || !row.stock) {
        results.push({ row: i + 2, success: false, reason: '必填字段缺失' });
        continue;
      }
      
      // 验证图片路径安全性
      let safeImagePath = '';
      if (row.main_image) {
        // 如果是完整URL，转换为相对路径
        if (row.main_image.startsWith('http://') || row.main_image.startsWith('https://')) {
          // 提取相对路径部分
          const urlParts = row.main_image.split('/uploads/');
          if (urlParts.length > 1) {
            safeImagePath = '/uploads/' + urlParts[1];
          }
        } else if (row.main_image.startsWith('/uploads/')) {
          // 已经是相对路径，直接使用
          safeImagePath = row.main_image;
        } else {
          // 其他情况，忽略该图片路径
          console.warn(`商品 ${row.name} 的图片路径格式不正确: ${row.main_image}`);
        }
      }
      
      try {
        await productModel.create({
          name: row.name,
          category_id: row.category_id,
          price: row.price,
          stock: row.stock,
          main_image: safeImagePath,
          description: row.description || '',
          status: row.status !== undefined ? row.status : 1,
        });
        results.push({ row: i + 2, success: true });
        successCount++;
      } catch (e) {
        results.push({ row: i + 2, success: false, reason: e.message });
      }
    }
    res.json({ success: true, data: { results, successCount }, message: `批量导入完成，成功${successCount}条` });
  } catch (e) {
    res.status(500).json({ success: false, data: null, message: '批量导入失败' });
  }
};

exports.downloadTemplate = (req, res) => {
  const xlsx = require('xlsx');
  const templateData = [
    {
      name: '示例商品',
      category_id: 1,
      price: 99.99,
      stock: 100,
      main_image: 'http://example.com/image.jpg',
      description: '示例描述',
      status: 1,
    },
  ];
  const ws = xlsx.utils.json_to_sheet(templateData);
  const wb = xlsx.utils.book_new();
  xlsx.utils.book_append_sheet(wb, ws, '商品模板');
  const buffer = xlsx.write(wb, { type: 'buffer', bookType: 'xlsx' });
  res.setHeader(
    'Content-Disposition',
    'attachment; filename=product_import_template.xlsx'
  );
  res.setHeader(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  );
  res.send(buffer);
};
