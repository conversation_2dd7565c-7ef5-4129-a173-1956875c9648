const roleModel = require('../models/roleModel');

exports.list = async (req, res) => {
  try {
    const data = await roleModel.findAll();
    res.json({ code: 0, data });
  } catch (e) {
    res
      .status(500)
      .json({ code: 1, msg: '获取角色列表失败', error: e.message });
  }
};

exports.detail = async (req, res) => {
  try {
    const data = await roleModel.findById(req.params.id);
    res.json({ code: 0, data });
  } catch (e) {
    res
      .status(500)
      .json({ code: 1, msg: '获取角色详情失败', error: e.message });
  }
};

exports.create = async (req, res) => {
  try {
    const id = await roleModel.create(req.body);
    res.json({ code: 0, msg: '添加成功', id });
  } catch (e) {
    res.status(500).json({ code: 1, msg: '添加失败', error: e.message });
  }
};

exports.update = async (req, res) => {
  try {
    await roleModel.update(req.params.id, req.body);
    res.json({ code: 0, msg: '更新成功' });
  } catch (e) {
    res.status(500).json({ code: 1, msg: '更新失败', error: e.message });
  }
};

exports.delete = async (req, res) => {
  try {
    await roleModel.delete(req.params.id);
    res.json({ code: 0, msg: '删除成功' });
  } catch (e) {
    res.status(500).json({ code: 1, msg: '删除失败', error: e.message });
  }
};

exports.getPermissions = async (req, res) => {
  try {
    const data = await roleModel.getPermissions(req.params.id);
    res.json({ code: 0, data });
  } catch (e) {
    res
      .status(500)
      .json({ code: 1, msg: '获取角色权限失败', error: e.message });
  }
};

exports.setPermissions = async (req, res) => {
  try {
    await roleModel.setPermissions(req.params.id, req.body.permissionIds);
    res.json({ code: 0, msg: '分配权限成功' });
  } catch (e) {
    res.status(500).json({ code: 1, msg: '分配权限失败', error: e.message });
  }
};
