const settingsModel = require('../models/settingsModel');

exports.getBasic = async (req, res) => {
  try {
    const data = await settingsModel.getBasic();
    res.json({ code: 0, data, msg: '获取基本设置' });
  } catch (e) {
    res.status(500).json({ code: 1, msg: '获取失败', error: e.message });
  }
};
exports.updateBasic = async (req, res) => {
  try {
    await settingsModel.updateBasic(req.body);
    res.json({ code: 0, msg: '更新基本设置成功' });
  } catch (e) {
    res.status(500).json({ code: 1, msg: '更新失败', error: e.message });
  }
};
exports.getPayment = async (req, res) => {
  try {
    const data = await settingsModel.getPayment();
    res.json({ code: 0, data, msg: '获取支付设置' });
  } catch (e) {
    res.status(500).json({ code: 1, msg: '获取失败', error: e.message });
  }
};
exports.updatePayment = async (req, res) => {
  try {
    await settingsModel.updatePayment(req.body);
    res.json({ code: 0, msg: '更新支付设置成功' });
  } catch (e) {
    res.status(500).json({ code: 1, msg: '更新失败', error: e.message });
  }
};
exports.getShipping = async (req, res) => {
  try {
    const data = await settingsModel.getShipping();
    res.json({ code: 0, data, msg: '获取物流设置' });
  } catch (e) {
    res.status(500).json({ code: 1, msg: '获取失败', error: e.message });
  }
};
exports.updateShipping = async (req, res) => {
  try {
    await settingsModel.updateShipping(req.body);
    res.json({ code: 0, msg: '更新物流设置成功' });
  } catch (e) {
    res.status(500).json({ code: 1, msg: '更新失败', error: e.message });
  }
};
exports.getSms = async (req, res) => {
  try {
    const data = await settingsModel.getSms();
    res.json({ code: 0, data, msg: '获取短信设置' });
  } catch (e) {
    res.status(500).json({ code: 1, msg: '获取失败', error: e.message });
  }
};
exports.updateSms = async (req, res) => {
  try {
    await settingsModel.updateSms(req.body);
    res.json({ code: 0, msg: '更新短信设置成功' });
  } catch (e) {
    res.status(500).json({ code: 1, msg: '更新失败', error: e.message });
  }
};
exports.getEmail = async (req, res) => {
  try {
    const data = await settingsModel.getEmail();
    res.json({ code: 0, data, msg: '获取邮件设置' });
  } catch (e) {
    res.status(500).json({ code: 1, msg: '获取失败', error: e.message });
  }
};
exports.updateEmail = async (req, res) => {
  try {
    await settingsModel.updateEmail(req.body);
    res.json({ code: 0, msg: '更新邮件设置成功' });
  } catch (e) {
    res.status(500).json({ code: 1, msg: '更新失败', error: e.message });
  }
};
