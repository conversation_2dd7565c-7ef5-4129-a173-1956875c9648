const statsModel = require('../models/statsModel');

exports.sales = async (req, res) => {
  try {
    const data = await statsModel.getSalesStats();
    res.json({ code: 0, data, msg: '销售统计' });
  } catch (e) {
    res
      .status(500)
      .json({ code: 1, msg: '获取销售统计失败', error: e.message });
  }
};

exports.products = async (req, res) => {
  try {
    const data = await statsModel.getProductStats();
    res.json({ code: 0, data, msg: '商品统计' });
  } catch (e) {
    res
      .status(500)
      .json({ code: 1, msg: '获取商品统计失败', error: e.message });
  }
};

exports.orders = async (req, res) => {
  try {
    const data = await statsModel.getOrderStats();
    res.json({ code: 0, data, msg: '订单统计' });
  } catch (e) {
    res
      .status(500)
      .json({ code: 1, msg: '获取订单统计失败', error: e.message });
  }
};
