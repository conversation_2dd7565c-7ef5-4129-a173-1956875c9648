const userModel = require('../models/userModel');

exports.list = async (req, res) => {
  try {
    const {
      page = 1,
      pageSize = 10,
      nickname = '',
      phone = '',
      status,
    } = req.query;
    const result = await userModel.findAll({
      page,
      pageSize,
      nickname,
      phone,
      status,
    });
    // 统一返回结构
    res.json({
      success: true,
      data: {
        list: result.list,
        pagination: {
          current: parseInt(page),
          pageSize: parseInt(pageSize),
          total: result.total
        }
      },
      message: '用户列表'
    });
  } catch (e) {
    res
      .status(500)
      .json({ success: false, message: '获取用户列表失败', error: e.message });
  }
};

exports.detail = async (req, res) => {
  try {
    const data = await userModel.findById(req.params.id);
    res.json({ success: true, data, message: '用户详情' });
  } catch (e) {
    res
      .status(500)
      .json({ success: false, message: '获取用户详情失败', error: e.message });
  }
};

exports.create = async (req, res) => {
  try {
    const id = await userModel.create(req.body);
    res.json({ success: true, data: { id }, message: '创建用户成功' });
  } catch (e) {
    res.status(500).json({ success: false, message: '创建用户失败', error: e.message });
  }
};

exports.updateStatus = async (req, res) => {
  try {
    await userModel.updateStatus(req.params.id, req.body.status);
    res.json({ success: true, message: '状态更新成功' });
  } catch (e) {
    res.status(500).json({ success: false, message: '状态更新失败', error: e.message });
  }
};

exports.setRole = async (req, res) => {
  try {
    await userModel.setRole(req.params.id, req.body.roleId);
    res.json({ success: true, message: '分配角色成功' });
  } catch (e) {
    res.status(500).json({ success: false, message: '分配角色失败', error: e.message });
  }
};

exports.update = async (req, res) => {
  try {
    await userModel.update(req.params.id, req.body);
    res.json({ success: true, message: '用户信息更新成功' });
  } catch (e) {
    res.status(500).json({ success: false, message: '用户信息更新失败', error: e.message });
  }
};

// 新增删除用户功能
exports.delete = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查用户是否存在
    const user = await userModel.findById(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 执行删除
    await userModel.delete(id);

    res.json({
      success: true,
      message: '用户删除成功'
    });

  } catch (error) {
    console.error('删除用户失败:', error);
    res.status(500).json({
      success: false,
      message: '删除用户失败',
      error: error.message
    });
  }
};

// 批量删除用户功能
exports.batchDelete = async (req, res) => {
  try {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要删除的用户ID列表'
      });
    }

    // 执行批量删除
    const result = await userModel.batchDelete(ids);

    res.json({
      success: true,
      message: `批量删除完成，删除了 ${result.affectedRows} 个用户`,
      data: { affectedRows: result.affectedRows }
    });

  } catch (error) {
    console.error('批量删除用户失败:', error);
    res.status(500).json({
      success: false,
      message: '批量删除用户失败',
      error: error.message
    });
  }
};

// 获取用户统计信息
exports.getStatistics = async (req, res) => {
  try {
    const sql = `
      SELECT
        COUNT(*) as total,
        SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active,
        SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as disabled,
        SUM(CASE WHEN gender = 1 THEN 1 ELSE 0 END) as male,
        SUM(CASE WHEN gender = 0 THEN 1 ELSE 0 END) as female
      FROM users
    `;
    const db = require('../src/config/database');
    const result = await db.query(sql);

    res.json({
      success: true,
      data: result[0],
      message: '获取用户统计成功'
    });
  } catch (error) {
    console.error('获取用户统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户统计失败',
      error: error.message
    });
  }
};
