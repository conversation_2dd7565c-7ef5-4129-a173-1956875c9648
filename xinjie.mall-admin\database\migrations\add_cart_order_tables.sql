-- 购物车和订单相关表的数据库迁移脚本
-- 执行时间：2025-07-26

-- 1. 创建购物车表
CREATE TABLE IF NOT EXISTS cart_items (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '购物车项ID',
  user_id INT NOT NULL COMMENT '用户ID',
  product_id INT NOT NULL COMMENT '商品ID',
  quantity INT NOT NULL DEFAULT 1 COMMENT '数量',
  specs JSON NULL COMMENT '商品规格（JSON格式）',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_user_id (user_id),
  INDEX idx_product_id (product_id),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
) COMMENT '购物车表';

-- 2. 为现有订单表添加折扣相关字段
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS original_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '原始金额' AFTER total_amount,
ADD COLUMN IF NOT EXISTS discount_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '折扣金额' AFTER original_amount,
ADD COLUMN IF NOT EXISTS shipping_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '运费' AFTER discount_amount,
ADD COLUMN IF NOT EXISTS final_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '最终支付金额' AFTER shipping_fee,
ADD COLUMN IF NOT EXISTS payment_method VARCHAR(50) NULL COMMENT '支付方式' AFTER payment_status,
ADD COLUMN IF NOT EXISTS transaction_id VARCHAR(100) NULL COMMENT '交易号' AFTER payment_method,
ADD COLUMN IF NOT EXISTS paid_at DATETIME NULL COMMENT '支付时间' AFTER transaction_id;

-- 3. 创建订单商品表（支持折扣）
CREATE TABLE IF NOT EXISTS order_items (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '订单商品ID',
  order_id INT NOT NULL COMMENT '订单ID',
  product_id INT NOT NULL COMMENT '商品ID',
  product_name VARCHAR(255) NOT NULL COMMENT '商品名称',
  product_image VARCHAR(500) NULL COMMENT '商品图片',
  quantity INT NOT NULL COMMENT '数量',
  original_price DECIMAL(10,2) NOT NULL COMMENT '原价',
  discount_price DECIMAL(10,2) NOT NULL COMMENT '折扣价',
  discount_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '单品折扣金额',
  total_price DECIMAL(10,2) NOT NULL COMMENT '小计（折扣后）',
  specs JSON NULL COMMENT '商品规格（JSON格式）',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  INDEX idx_order_id (order_id),
  INDEX idx_product_id (product_id),
  FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT
) COMMENT '订单商品表';

-- 4. 创建折扣商品关联表
CREATE TABLE IF NOT EXISTS discount_products (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
  discount_id INT NOT NULL COMMENT '折扣ID',
  product_id INT NOT NULL COMMENT '商品ID',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  UNIQUE KEY uk_discount_product (discount_id, product_id),
  INDEX idx_discount_id (discount_id),
  INDEX idx_product_id (product_id),
  FOREIGN KEY (discount_id) REFERENCES discounts(id) ON DELETE CASCADE,
  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
) COMMENT '折扣商品关联表';

-- 5. 创建折扣分类关联表
CREATE TABLE IF NOT EXISTS discount_categories (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
  discount_id INT NOT NULL COMMENT '折扣ID',
  category_id INT NOT NULL COMMENT '分类ID',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  UNIQUE KEY uk_discount_category (discount_id, category_id),
  INDEX idx_discount_id (discount_id),
  INDEX idx_category_id (category_id),
  FOREIGN KEY (discount_id) REFERENCES discounts(id) ON DELETE CASCADE,
  FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
) COMMENT '折扣分类关联表';

-- 6. 创建折扣使用记录表
CREATE TABLE IF NOT EXISTS discount_usage_records (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
  discount_id INT NOT NULL COMMENT '折扣ID',
  user_id INT NOT NULL COMMENT '用户ID',
  order_id INT NOT NULL COMMENT '订单ID',
  discount_amount DECIMAL(10,2) NOT NULL COMMENT '折扣金额',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '使用时间',
  
  INDEX idx_discount_id (discount_id),
  INDEX idx_user_id (user_id),
  INDEX idx_order_id (order_id),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (discount_id) REFERENCES discounts(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
) COMMENT '折扣使用记录表';

-- 7. 为商品表添加库存字段（如果不存在）
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS stock INT DEFAULT 0 COMMENT '库存数量' AFTER price;

-- 8. 创建库存变动记录表
CREATE TABLE IF NOT EXISTS stock_records (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
  product_id INT NOT NULL COMMENT '商品ID',
  type TINYINT NOT NULL COMMENT '变动类型(1:入库 2:出库)',
  quantity INT NOT NULL COMMENT '变动数量',
  stock_before INT NOT NULL COMMENT '变动前库存',
  stock_after INT NOT NULL COMMENT '变动后库存',
  source TINYINT NOT NULL COMMENT '变动来源(1:采购入库 2:订单出库 3:退货入库 4:后台调整)',
  source_id INT NULL COMMENT '来源ID(订单ID等)',
  remark VARCHAR(255) NULL COMMENT '备注',
  operator_id INT NULL COMMENT '操作员ID',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  INDEX idx_product_id (product_id),
  INDEX idx_type (type),
  INDEX idx_source (source),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
) COMMENT '库存变动记录表';
