-- 充值和会员功能数据库迁移脚本
-- 执行时间：2025-07-24

-- 1. 首先为现有用户表添加缺失的字段
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '账户余额' AFTER phone,
ADD COLUMN IF NOT EXISTS points INT DEFAULT 0 COMMENT '积分' AFTER balance,
ADD COLUMN IF NOT EXISTS user_level TINYINT DEFAULT 1 COMMENT '用户等级(1:普通用户 2:VIP 3:钻石VIP)' AFTER points,
ADD COLUMN IF NOT EXISTS member_expire_time DATETIME NULL COMMENT '会员到期时间' AFTER user_level;

-- 2. 创建充值记录表
CREATE TABLE IF NOT EXISTS recharge_records (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '充值记录ID',
  user_id INT NOT NULL COMMENT '用户ID',
  order_no VARCHAR(32) NOT NULL UNIQUE COMMENT '充值订单号',
  amount DECIMAL(10,2) NOT NULL COMMENT '充值金额',
  bonus_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '赠送金额',
  total_amount DECIMAL(10,2) NOT NULL COMMENT '实际到账金额',
  payment_method TINYINT DEFAULT 1 COMMENT '支付方式(1:微信支付 2:支付宝 3:银行卡 4:后台充值)',
  payment_status TINYINT DEFAULT 0 COMMENT '支付状态(0:待支付 1:已支付 2:支付失败 3:已退款)',
  transaction_id VARCHAR(64) NULL COMMENT '第三方交易号',
  remark TEXT NULL COMMENT '备注信息',
  operator_id INT NULL COMMENT '操作员ID(后台充值时使用)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  paid_at DATETIME NULL COMMENT '支付完成时间',
  
  INDEX idx_user_id (user_id),
  INDEX idx_order_no (order_no),
  INDEX idx_payment_status (payment_status),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT '充值记录表';

-- 3. 创建会员等级配置表
CREATE TABLE IF NOT EXISTS member_levels (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '等级ID',
  level_code TINYINT NOT NULL UNIQUE COMMENT '等级代码(1:普通用户 2:VIP 3:钻石VIP)',
  level_name VARCHAR(50) NOT NULL COMMENT '等级名称',
  level_icon VARCHAR(255) NULL COMMENT '等级图标',
  min_points INT DEFAULT 0 COMMENT '升级所需最低积分',
  min_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '升级所需最低消费金额',
  discount_rate DECIMAL(3,2) DEFAULT 1.00 COMMENT '折扣率(0.95表示95折)',
  points_ratio DECIMAL(3,2) DEFAULT 1.00 COMMENT '积分倍率(1.5表示1.5倍积分)',
  free_shipping_threshold DECIMAL(10,2) DEFAULT 0.00 COMMENT '免邮门槛(0表示无免邮)',
  birthday_discount DECIMAL(3,2) DEFAULT 0.00 COMMENT '生日折扣(0.1表示额外9折)',
  status TINYINT DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  sort_order INT DEFAULT 0 COMMENT '排序',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_level_code (level_code),
  INDEX idx_status (status),
  INDEX idx_sort_order (sort_order)
) COMMENT '会员等级配置表';

-- 4. 创建会员权益表
CREATE TABLE IF NOT EXISTS member_benefits (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '权益ID',
  level_id INT NOT NULL COMMENT '等级ID',
  benefit_type TINYINT NOT NULL COMMENT '权益类型(1:折扣 2:积分倍率 3:免邮 4:专属客服 5:生日特权 6:优先发货)',
  benefit_name VARCHAR(100) NOT NULL COMMENT '权益名称',
  benefit_value VARCHAR(100) NULL COMMENT '权益值',
  benefit_desc TEXT NULL COMMENT '权益描述',
  status TINYINT DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  sort_order INT DEFAULT 0 COMMENT '排序',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_level_id (level_id),
  INDEX idx_benefit_type (benefit_type),
  INDEX idx_status (status),
  FOREIGN KEY (level_id) REFERENCES member_levels(id) ON DELETE CASCADE
) COMMENT '会员权益表';

-- 5. 创建积分记录表
CREATE TABLE IF NOT EXISTS points_records (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '积分记录ID',
  user_id INT NOT NULL COMMENT '用户ID',
  type TINYINT NOT NULL COMMENT '积分类型(1:获得 2:消费)',
  points INT NOT NULL COMMENT '积分数量',
  source TINYINT NOT NULL COMMENT '积分来源(1:注册 2:购物 3:签到 4:评价 5:分享 6:充值 7:后台调整 8:积分兑换)',
  source_id INT NULL COMMENT '来源ID(订单ID、商品ID等)',
  remark VARCHAR(255) NULL COMMENT '备注',
  operator_id INT NULL COMMENT '操作员ID(后台操作时使用)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  INDEX idx_user_id (user_id),
  INDEX idx_type (type),
  INDEX idx_source (source),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT '积分记录表';

-- 6. 创建余额变动记录表
CREATE TABLE IF NOT EXISTS balance_records (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '余额记录ID',
  user_id INT NOT NULL COMMENT '用户ID',
  type TINYINT NOT NULL COMMENT '变动类型(1:增加 2:减少)',
  amount DECIMAL(10,2) NOT NULL COMMENT '变动金额',
  balance_before DECIMAL(10,2) NOT NULL COMMENT '变动前余额',
  balance_after DECIMAL(10,2) NOT NULL COMMENT '变动后余额',
  source TINYINT NOT NULL COMMENT '变动来源(1:充值 2:消费 3:退款 4:后台调整)',
  source_id INT NULL COMMENT '来源ID(充值记录ID、订单ID等)',
  remark VARCHAR(255) NULL COMMENT '备注',
  operator_id INT NULL COMMENT '操作员ID(后台操作时使用)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  INDEX idx_user_id (user_id),
  INDEX idx_type (type),
  INDEX idx_source (source),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT '余额变动记录表';

-- 7. 插入默认会员等级数据
INSERT INTO member_levels (level_code, level_name, level_icon, min_points, min_amount, discount_rate, points_ratio, free_shipping_threshold, birthday_discount, sort_order) VALUES
(1, '普通用户', '/images/level/normal.png', 0, 0.00, 1.00, 1.00, 99.00, 0.00, 1),
(2, 'VIP会员', '/images/level/vip.png', 1000, 500.00, 0.95, 1.20, 0.00, 0.10, 2),
(3, '钻石VIP', '/images/level/diamond.png', 5000, 2000.00, 0.90, 1.50, 0.00, 0.15, 3)
ON DUPLICATE KEY UPDATE 
level_name = VALUES(level_name),
level_icon = VALUES(level_icon),
min_points = VALUES(min_points),
min_amount = VALUES(min_amount),
discount_rate = VALUES(discount_rate),
points_ratio = VALUES(points_ratio),
free_shipping_threshold = VALUES(free_shipping_threshold),
birthday_discount = VALUES(birthday_discount),
sort_order = VALUES(sort_order);

-- 8. 插入默认会员权益数据
INSERT INTO member_benefits (level_id, benefit_type, benefit_name, benefit_value, benefit_desc, sort_order) VALUES
-- 普通用户权益
(1, 2, '基础积分', '1倍', '购物获得1倍积分', 1),
(1, 3, '满99免邮', '99', '单笔订单满99元免运费', 2),

-- VIP会员权益  
(2, 1, '会员折扣', '95折', '全场商品享受95折优惠', 1),
(2, 2, '积分倍率', '1.2倍', '购物获得1.2倍积分', 2),
(2, 3, '全场免邮', '0', '全场商品免运费', 3),
(2, 5, '生日特权', '9折', '生日当月享受额外9折优惠', 4),
(2, 4, '专属客服', '是', '享受专属客服服务', 5),

-- 钻石VIP权益
(3, 1, '钻石折扣', '90折', '全场商品享受90折优惠', 1),
(3, 2, '积分倍率', '1.5倍', '购物获得1.5倍积分', 2),
(3, 3, '全场免邮', '0', '全场商品免运费', 3),
(3, 5, '生日特权', '85折', '生日当月享受额外85折优惠', 4),
(3, 4, '专属客服', '是', '享受专属客服服务', 5),
(3, 6, '优先发货', '是', '订单享受优先发货服务', 6)
ON DUPLICATE KEY UPDATE 
benefit_name = VALUES(benefit_name),
benefit_value = VALUES(benefit_value),
benefit_desc = VALUES(benefit_desc),
sort_order = VALUES(sort_order);
