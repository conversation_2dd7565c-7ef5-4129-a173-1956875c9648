-- 管理后台折扣功能相关数据库表创建脚本
-- 创建时间: 2025-07-24

-- 1. 折扣规则表
CREATE TABLE IF NOT EXISTS discounts (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '折扣ID',
    name VARCHAR(100) NOT NULL COMMENT '折扣名称',
    description TEXT COMMENT '折扣描述',
    type TINYINT NOT NULL DEFAULT 1 COMMENT '折扣类型(1:百分比折扣 2:固定金额折扣 3:满减折扣)',
    value DECIMAL(10,2) NOT NULL COMMENT '折扣值(百分比折扣为0-100，固定金额为具体金额)',
    min_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '最低消费金额(满减折扣使用)',
    max_discount DECIMAL(10,2) DEFAULT NULL COMMENT '最大折扣金额(百分比折扣时限制最大优惠)',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',
    usage_limit INT DEFAULT NULL COMMENT '使用次数限制(NULL为无限制)',
    used_count INT DEFAULT 0 COMMENT '已使用次数',
    user_limit INT DEFAULT NULL COMMENT '单用户使用次数限制(NULL为无限制)',
    status TINYINT DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    priority INT DEFAULT 0 COMMENT '优先级(数字越大优先级越高)',
    applicable_to TINYINT DEFAULT 1 COMMENT '适用范围(1:全部商品 2:指定商品 3:指定分类)',
    created_by INT COMMENT '创建人ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 2. 商品折扣关联表
CREATE TABLE IF NOT EXISTS product_discounts (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    discount_id INT NOT NULL COMMENT '折扣ID',
    product_id INT NOT NULL COMMENT '商品ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_discount_product (discount_id, product_id),
    FOREIGN KEY (discount_id) REFERENCES discounts(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- 3. 分类折扣关联表
CREATE TABLE IF NOT EXISTS category_discounts (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    discount_id INT NOT NULL COMMENT '折扣ID',
    category_id INT NOT NULL COMMENT '分类ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_discount_category (discount_id, category_id),
    FOREIGN KEY (discount_id) REFERENCES discounts(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
);

-- 4. 用户折扣使用记录表
CREATE TABLE IF NOT EXISTS user_discount_usage (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '使用记录ID',
    user_id INT NOT NULL COMMENT '用户ID',
    discount_id INT NOT NULL COMMENT '折扣ID',
    order_id INT COMMENT '订单ID',
    discount_amount DECIMAL(10,2) NOT NULL COMMENT '折扣金额',
    used_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '使用时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (discount_id) REFERENCES discounts(id) ON DELETE CASCADE
);

-- 5. 为现有表添加折扣相关字段

-- 为商品表添加折扣相关字段
ALTER TABLE products 
ADD COLUMN discount_price DECIMAL(10,2) DEFAULT NULL COMMENT '折扣价格' AFTER price,
ADD COLUMN has_discount TINYINT DEFAULT 0 COMMENT '是否有折扣(0:无 1:有)' AFTER discount_price,
ADD COLUMN discount_start_time DATETIME DEFAULT NULL COMMENT '折扣开始时间' AFTER has_discount,
ADD COLUMN discount_end_time DATETIME DEFAULT NULL COMMENT '折扣结束时间' AFTER discount_start_time;

-- 为订单表添加折扣相关字段
ALTER TABLE orders 
ADD COLUMN discount_ids TEXT COMMENT '使用的折扣ID列表(JSON格式)' AFTER total_amount,
ADD COLUMN discount_details TEXT COMMENT '折扣详情(JSON格式)' AFTER discount_ids;

-- 为订单商品表添加折扣相关字段
ALTER TABLE order_items 
ADD COLUMN original_price DECIMAL(10,2) COMMENT '原价' AFTER price,
ADD COLUMN discount_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '折扣金额' AFTER original_price,
ADD COLUMN discount_id INT COMMENT '使用的折扣ID' AFTER discount_amount;

-- 6. 创建索引
CREATE INDEX idx_discounts_type ON discounts(type);
CREATE INDEX idx_discounts_status ON discounts(status);
CREATE INDEX idx_discounts_start_time ON discounts(start_time);
CREATE INDEX idx_discounts_end_time ON discounts(end_time);
CREATE INDEX idx_discounts_priority ON discounts(priority);
CREATE INDEX idx_product_discounts_discount ON product_discounts(discount_id);
CREATE INDEX idx_product_discounts_product ON product_discounts(product_id);
CREATE INDEX idx_category_discounts_discount ON category_discounts(discount_id);
CREATE INDEX idx_category_discounts_category ON category_discounts(category_id);
CREATE INDEX idx_user_discount_usage_user ON user_discount_usage(user_id);
CREATE INDEX idx_user_discount_usage_discount ON user_discount_usage(discount_id);
CREATE INDEX idx_products_discount ON products(has_discount, discount_start_time, discount_end_time);

-- 7. 插入示例折扣数据
INSERT INTO discounts (name, description, type, value, start_time, end_time, status, applicable_to, priority) VALUES
('新用户专享9折', '新用户首次购买享受9折优惠', 1, 10.00, '2025-01-01 00:00:00', '2025-12-31 23:59:59', 1, 1, 10),
('满100减20', '单笔订单满100元减20元', 3, 20.00, '2025-01-01 00:00:00', '2025-12-31 23:59:59', 1, 1, 5),
('绿茶专区8折', '绿茶分类商品8折优惠', 1, 20.00, '2025-01-01 00:00:00', '2025-06-30 23:59:59', 1, 3, 8);

-- 设置满减折扣的最低消费金额
UPDATE discounts SET min_amount = 100.00 WHERE name = '满100减20';
