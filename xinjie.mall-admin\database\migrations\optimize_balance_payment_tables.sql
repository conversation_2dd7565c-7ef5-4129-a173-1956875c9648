-- 优化余额和支付功能数据库表结构
-- 执行时间：2025-07-27
-- 说明：优化现有表结构，确保数据完整性和性能

-- 1. 确保用户表有余额字段
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '账户余额' AFTER phone,
ADD COLUMN IF NOT EXISTS points INT DEFAULT 0 COMMENT '积分' AFTER balance,
ADD COLUMN IF NOT EXISTS user_level TINYINT DEFAULT 1 COMMENT '用户等级' AFTER points,
ADD COLUMN IF NOT EXISTS member_expire_time DATETIME NULL COMMENT '会员到期时间' AFTER user_level;

-- 2. 优化充值记录表
CREATE TABLE IF NOT EXISTS recharge_records (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL COMMENT '用户ID',
  order_no VARCHAR(32) NOT NULL UNIQUE COMMENT '充值订单号',
  amount DECIMAL(10,2) NOT NULL COMMENT '充值金额',
  bonus_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '赠送金额',
  total_amount DECIMAL(10,2) NOT NULL COMMENT '总金额',
  payment_method TINYINT NOT NULL DEFAULT 1 COMMENT '支付方式(1:微信 2:支付宝 3:余额 4:后台)',
  payment_status TINYINT NOT NULL DEFAULT 0 COMMENT '支付状态(0:待支付 1:已支付 2:已退款)',
  transaction_id VARCHAR(64) NULL COMMENT '第三方交易号',
  remark VARCHAR(255) NULL COMMENT '备注',
  operator_id INT NULL COMMENT '操作员ID(后台充值时)',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  paid_at TIMESTAMP NULL COMMENT '支付时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_user_id (user_id),
  INDEX idx_order_no (order_no),
  INDEX idx_payment_status (payment_status),
  INDEX idx_created_at (created_at),
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值记录表';

-- 3. 优化余额变动记录表
CREATE TABLE IF NOT EXISTS balance_records (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL COMMENT '用户ID',
  type TINYINT NOT NULL COMMENT '变动类型(1:增加 2:减少)',
  amount DECIMAL(10,2) NOT NULL COMMENT '变动金额',
  balance_before DECIMAL(10,2) NOT NULL COMMENT '变动前余额',
  balance_after DECIMAL(10,2) NOT NULL COMMENT '变动后余额',
  source TINYINT NOT NULL COMMENT '来源(1:充值 2:消费 3:退款 4:调整)',
  source_id INT NULL COMMENT '来源ID(订单ID/充值ID等)',
  remark VARCHAR(255) NULL COMMENT '备注',
  operator_id INT NULL COMMENT '操作员ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  INDEX idx_user_id (user_id),
  INDEX idx_type (type),
  INDEX idx_source (source),
  INDEX idx_created_at (created_at),
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='余额变动记录表';

-- 4. 优化订单表，添加余额支付相关字段
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS balance_used DECIMAL(10,2) DEFAULT 0.00 COMMENT '使用余额金额' AFTER total_amount,
ADD COLUMN IF NOT EXISTS payment_method VARCHAR(20) NULL COMMENT '支付方式' AFTER payment_status,
ADD COLUMN IF NOT EXISTS transaction_id VARCHAR(64) NULL COMMENT '第三方交易号' AFTER payment_method;

-- 5. 创建会员等级表（如果不存在）
CREATE TABLE IF NOT EXISTS member_levels (
  id INT PRIMARY KEY AUTO_INCREMENT,
  level_code VARCHAR(20) NOT NULL UNIQUE COMMENT '等级代码',
  level_name VARCHAR(50) NOT NULL COMMENT '等级名称',
  min_points INT NOT NULL DEFAULT 0 COMMENT '最低积分要求',
  discount_rate DECIMAL(3,2) DEFAULT 1.00 COMMENT '折扣率',
  description TEXT NULL COMMENT '等级描述',
  benefits TEXT NULL COMMENT '会员权益(JSON格式)',
  sort_order INT DEFAULT 0 COMMENT '排序',
  status TINYINT DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_level_code (level_code),
  INDEX idx_status (status),
  INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员等级表';

-- 6. 插入默认会员等级数据
INSERT IGNORE INTO member_levels (level_code, level_name, min_points, discount_rate, description, sort_order) VALUES
('bronze', '青铜会员', 0, 1.00, '新用户默认等级', 1),
('silver', '白银会员', 1000, 0.95, '消费满1000积分可升级', 2),
('gold', '黄金会员', 5000, 0.90, '消费满5000积分可升级', 3),
('platinum', '铂金会员', 10000, 0.85, '消费满10000积分可升级', 4),
('diamond', '钻石会员', 50000, 0.80, '消费满50000积分可升级', 5);

-- 7. 创建会员权益表（如果不存在）
CREATE TABLE IF NOT EXISTS member_benefits (
  id INT PRIMARY KEY AUTO_INCREMENT,
  level_id INT NOT NULL COMMENT '会员等级ID',
  benefit_type VARCHAR(20) NOT NULL COMMENT '权益类型',
  benefit_name VARCHAR(100) NOT NULL COMMENT '权益名称',
  benefit_value VARCHAR(100) NULL COMMENT '权益值',
  description TEXT NULL COMMENT '权益描述',
  status TINYINT DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  sort_order INT DEFAULT 0 COMMENT '排序',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_level_id (level_id),
  INDEX idx_benefit_type (benefit_type),
  INDEX idx_status (status),
  
  FOREIGN KEY (level_id) REFERENCES member_levels(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员权益表';

-- 8. 添加数据库索引优化
-- 用户表索引
ALTER TABLE users ADD INDEX IF NOT EXISTS idx_balance (balance);
ALTER TABLE users ADD INDEX IF NOT EXISTS idx_user_level (user_level);

-- 订单表索引
ALTER TABLE orders ADD INDEX IF NOT EXISTS idx_payment_status (payment_status);
ALTER TABLE orders ADD INDEX IF NOT EXISTS idx_payment_method (payment_method);
ALTER TABLE orders ADD INDEX IF NOT EXISTS idx_user_payment (user_id, payment_status);

-- 9. 创建视图：用户余额统计
CREATE OR REPLACE VIEW user_balance_summary AS
SELECT 
  u.id as user_id,
  u.nickname,
  u.phone,
  u.balance,
  u.points,
  u.user_level,
  ml.level_name,
  ml.discount_rate,
  (SELECT COUNT(*) FROM recharge_records WHERE user_id = u.id AND payment_status = 1) as recharge_count,
  (SELECT COALESCE(SUM(total_amount), 0) FROM recharge_records WHERE user_id = u.id AND payment_status = 1) as total_recharged,
  (SELECT COUNT(*) FROM orders WHERE user_id = u.id AND payment_method = 'balance') as balance_order_count,
  (SELECT COALESCE(SUM(balance_used), 0) FROM orders WHERE user_id = u.id) as total_balance_used
FROM users u
LEFT JOIN member_levels ml ON u.user_level = ml.id
WHERE u.status = 1;

-- 10. 创建存储过程：安全的余额更新
DELIMITER //
CREATE OR REPLACE PROCEDURE UpdateUserBalance(
  IN p_user_id INT,
  IN p_amount DECIMAL(10,2),
  IN p_type TINYINT,
  IN p_source TINYINT,
  IN p_source_id INT,
  IN p_remark VARCHAR(255),
  IN p_operator_id INT
)
BEGIN
  DECLARE v_current_balance DECIMAL(10,2);
  DECLARE v_new_balance DECIMAL(10,2);
  DECLARE EXIT HANDLER FOR SQLEXCEPTION
  BEGIN
    ROLLBACK;
    RESIGNAL;
  END;
  
  START TRANSACTION;
  
  -- 锁定用户记录并获取当前余额
  SELECT balance INTO v_current_balance 
  FROM users 
  WHERE id = p_user_id 
  FOR UPDATE;
  
  -- 计算新余额
  IF p_type = 1 THEN
    SET v_new_balance = v_current_balance + p_amount;
  ELSE
    SET v_new_balance = v_current_balance - p_amount;
    IF v_new_balance < 0 THEN
      SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '余额不足';
    END IF;
  END IF;
  
  -- 更新用户余额
  UPDATE users 
  SET balance = v_new_balance, updated_at = NOW() 
  WHERE id = p_user_id;
  
  -- 插入余额变动记录
  INSERT INTO balance_records (
    user_id, type, amount, balance_before, balance_after,
    source, source_id, remark, operator_id, created_at
  ) VALUES (
    p_user_id, p_type, p_amount, v_current_balance, v_new_balance,
    p_source, p_source_id, p_remark, p_operator_id, NOW()
  );
  
  COMMIT;
  
  SELECT v_new_balance as new_balance;
END //
DELIMITER ;

-- 11. 创建积分记录表
CREATE TABLE IF NOT EXISTS points_records (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL COMMENT '用户ID',
  type TINYINT NOT NULL COMMENT '变动类型(1:增加 2:减少)',
  points INT NOT NULL COMMENT '变动积分',
  points_before INT NOT NULL COMMENT '变动前积分',
  points_after INT NOT NULL COMMENT '变动后积分',
  source TINYINT NOT NULL COMMENT '来源(1:购物获得 2:签到获得 3:活动获得 4:消费抵扣 5:系统调整)',
  source_id INT NULL COMMENT '来源ID(订单ID等)',
  remark VARCHAR(255) NULL COMMENT '备注',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

  INDEX idx_user_id (user_id),
  INDEX idx_type (type),
  INDEX idx_source (source),
  INDEX idx_created_at (created_at),

  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分记录表';

-- 12. 创建会员折扣使用记录表
CREATE TABLE IF NOT EXISTS member_discount_records (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL COMMENT '用户ID',
  order_id INT NOT NULL COMMENT '订单ID',
  discount_amount DECIMAL(10,2) NOT NULL COMMENT '折扣金额',
  level_name VARCHAR(50) NOT NULL COMMENT '会员等级名称',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

  INDEX idx_user_id (user_id),
  INDEX idx_order_id (order_id),
  INDEX idx_created_at (created_at),

  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员折扣使用记录表';

-- 执行完成提示
SELECT '余额、支付、会员、积分功能数据库优化完成！' as message;
