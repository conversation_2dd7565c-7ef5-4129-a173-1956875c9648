# 支付功能升级指南

## 概述

本文档说明如何将当前的模拟支付系统（方案B）升级为真实的微信支付/支付宝系统。

## 当前架构（方案B - 模拟支付）

### 功能特点
- ✅ 完整的余额支付功能
- ✅ 模拟的第三方支付
- ✅ 混合支付支持
- ✅ 完整的支付记录和状态管理
- ✅ 用户友好的前端界面

### 技术实现
```javascript
// 当前模拟支付实现
async processThirdPartyPayment(userId, orderId, amount, method) {
  // 模拟支付成功
  await this.completeThirdPartyPayment(orderId, method, `mock_${Date.now()}`);
  return { success: true, mockPayment: true };
}
```

## 升级到真实支付（方案A）

### 1. 前置条件

#### 微信支付
- [ ] 微信小程序已认证
- [ ] 申请微信商户号
- [ ] 获取以下配置信息：
  - `appId`: 小程序AppID
  - `mchId`: 商户号
  - `apiKey`: API密钥
  - `certPath`: 证书路径

#### 支付宝
- [ ] 支付宝开放平台账号
- [ ] 创建小程序应用
- [ ] 获取以下配置信息：
  - `appId`: 应用ID
  - `privateKey`: 应用私钥
  - `alipayPublicKey`: 支付宝公钥

### 2. 配置文件修改

#### 创建支付配置文件
```javascript
// config/payment.js
module.exports = {
  // 微信支付配置
  wechat: {
    appId: process.env.WECHAT_APP_ID,
    mchId: process.env.WECHAT_MCH_ID,
    apiKey: process.env.WECHAT_API_KEY,
    certPath: process.env.WECHAT_CERT_PATH,
    notifyUrl: process.env.WECHAT_NOTIFY_URL,
    apiUrl: 'https://api.mch.weixin.qq.com'
  },
  
  // 支付宝配置
  alipay: {
    appId: process.env.ALIPAY_APP_ID,
    privateKey: process.env.ALIPAY_PRIVATE_KEY,
    alipayPublicKey: process.env.ALIPAY_PUBLIC_KEY,
    notifyUrl: process.env.ALIPAY_NOTIFY_URL,
    gatewayUrl: 'https://openapi.alipay.com/gateway.do'
  }
};
```

### 3. 核心代码修改

#### A. 微信支付实现
```javascript
// services/wechatPaymentService.js
const axios = require('axios');
const crypto = require('crypto');
const paymentConfig = require('../config/payment');

class WechatPaymentService {
  async createPayment(orderId, amount, userId) {
    // 获取用户openid
    const user = await this.getUserInfo(userId);
    
    // 构建支付参数
    const paymentData = {
      appid: paymentConfig.wechat.appId,
      mchid: paymentConfig.wechat.mchId,
      description: '商品购买',
      out_trade_no: `ORDER_${orderId}_${Date.now()}`,
      amount: {
        total: Math.round(amount * 100) // 转换为分
      },
      payer: {
        openid: user.openid
      },
      notify_url: paymentConfig.wechat.notifyUrl
    };
    
    // 调用微信支付API
    const response = await this.callWechatAPI('/v3/pay/transactions/jsapi', paymentData);
    
    return {
      prepayId: response.prepay_id,
      paymentParams: this.generatePaymentParams(response.prepay_id)
    };
  }
  
  // 生成前端支付参数
  generatePaymentParams(prepayId) {
    const timeStamp = Math.floor(Date.now() / 1000).toString();
    const nonceStr = this.generateNonceStr();
    const packageStr = `prepay_id=${prepayId}`;
    
    const signData = [
      paymentConfig.wechat.appId,
      timeStamp,
      nonceStr,
      packageStr
    ].join('\n') + '\n';
    
    const paySign = this.generateSignature(signData);
    
    return {
      appId: paymentConfig.wechat.appId,
      timeStamp,
      nonceStr,
      package: packageStr,
      signType: 'RSA',
      paySign
    };
  }
}
```

#### B. 支付回调处理
```javascript
// 真实支付回调处理
async handleWechatCallback(callbackData) {
  // 1. 验证签名
  if (!this.verifyWechatSignature(callbackData)) {
    throw new Error('签名验证失败');
  }
  
  // 2. 解析回调数据
  const { out_trade_no, trade_state, transaction_id } = callbackData;
  
  // 3. 处理支付结果
  if (trade_state === 'SUCCESS') {
    await this.completePayment(out_trade_no, 'wechat', transaction_id);
    return { code: 'SUCCESS', message: '处理成功' };
  }
  
  return { code: 'FAIL', message: '支付失败' };
}
```

### 4. 需要修改的文件

#### 修改量：小
- `config/payment.js` - 新增配置文件
- `.env` - 添加支付配置环境变量

#### 修改量：中等
- `services/orderPaymentService.js` - 替换模拟支付为真实API调用
- `controllers/frontBalanceController.js` - 更新回调处理逻辑

#### 修改量：无
- 数据库表结构 - 完全兼容
- 前端界面 - 无需修改
- 余额支付逻辑 - 保持不变
- 业务流程 - 保持不变

### 5. 升级步骤

#### 第一步：准备工作
1. 申请微信商户号和支付宝商户号
2. 配置支付回调域名（必须是HTTPS）
3. 准备测试环境

#### 第二步：代码修改
1. 创建真实支付服务类
2. 替换模拟支付调用
3. 实现签名验证逻辑
4. 配置支付参数

#### 第三步：测试验证
1. 沙箱环境测试
2. 小额真实支付测试
3. 各种异常情况测试
4. 回调处理测试

#### 第四步：上线部署
1. 配置生产环境参数
2. 部署到HTTPS域名
3. 监控支付成功率
4. 处理异常情况

### 6. 风险控制

#### 技术风险
- 网络异常导致支付状态不一致
- 回调重复处理
- 签名验证失败

#### 解决方案
- 实现支付状态查询接口
- 添加幂等性控制
- 完善错误处理和重试机制

### 7. 监控和维护

#### 关键指标
- 支付成功率
- 回调处理成功率
- 支付异常率
- 用户投诉率

#### 日志记录
```javascript
// 支付日志记录
const paymentLogger = {
  logPaymentRequest: (orderId, amount, method) => {
    console.log(`[PAYMENT] 发起支付 - 订单:${orderId}, 金额:${amount}, 方式:${method}`);
  },
  
  logPaymentSuccess: (orderId, transactionId) => {
    console.log(`[PAYMENT] 支付成功 - 订单:${orderId}, 交易号:${transactionId}`);
  },
  
  logPaymentError: (orderId, error) => {
    console.error(`[PAYMENT] 支付失败 - 订单:${orderId}, 错误:${error.message}`);
  }
};
```

## 总结

### 升级优势
- 🟢 **代码改动量小**：80%代码无需修改
- 🟢 **业务逻辑不变**：用户体验保持一致
- 🟢 **渐进式升级**：可以分步骤实施
- 🟢 **风险可控**：充分的测试和回滚机制

### 预估工作量
- **配置准备**：1-2天（申请商户号等）
- **代码开发**：2-3天
- **测试验证**：2-3天
- **上线部署**：1天
- **总计**：6-9天

### 建议
1. 先完善方案B的功能和界面
2. 准备好商户资质后再升级
3. 在测试环境充分验证后再上线
4. 保留模拟支付作为开发环境选项
