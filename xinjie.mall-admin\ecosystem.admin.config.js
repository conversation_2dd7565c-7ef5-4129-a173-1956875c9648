module.exports = {
  apps: [
    {
      name: 'xinjie-mall-admin',
      script: 'app.js',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        PORT: 8081
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 8081,
        API_URL: 'https://api.xinjie-tea.com',
        DB_HOST: 'localhost',
        DB_PORT: 3306,
        DB_NAME: 'xinjie_mall_',
        DB_USER: 'root',
        DB_PASSWORD: ''
      },
      error_file: './logs/admin-error.log',
      out_file: './logs/admin-out.log',
      log_file: './logs/admin-combined.log',
      time: true,
      max_memory_restart: '512M',
      watch: false,
      ignore_watch: [
        'node_modules',
        'logs',
        'public/uploads',
        'dist',
        '.git'
      ],
      max_restarts: 10,
      min_uptime: '10s',
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 10000,
      
      // 自动重启配置
      autorestart: true,
      
      // 日志配置
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // 环境变量
      env_file: '.env.production'
    }
  ],

  // 部署配置
  deploy: {
    production: {
      user: 'root',
      host: 'your-server-ip',
      ref: 'origin/main',
      repo: 'https://github.com/your-username/xinjie-tea.git',
      path: '/var/www/xinjie-tea/xinjie.mall-admin',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.admin.config.js --env production',
      'pre-setup': ''
    }
  }
};
