const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '', // MySQL重新初始化后，root用户没有密码
  database: 'xinjie_mall',
  charset: 'utf8mb4'
};

async function executeMigration() {
  let connection;
  
  try {
    console.log('开始执行购物车和订单功能数据库迁移...');
    
    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功');
    
    // 读取迁移SQL文件
    const sqlFile = path.join(__dirname, 'database/migrations/add_cart_order_tables.sql');
    const sqlContent = fs.readFileSync(sqlFile, 'utf8');
    
    // 分割SQL语句
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt && !stmt.startsWith('--') && stmt.length > 10);
    
    console.log(`准备执行 ${statements.length} 条SQL语句`);
    
    // 逐条执行SQL语句
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement) {
        try {
          console.log(`执行第 ${i + 1} 条语句...`);
          await connection.execute(statement);
          console.log(`✓ 第 ${i + 1} 条语句执行成功`);
        } catch (error) {
          if (error.code === 'ER_DUP_FIELDNAME' || error.code === 'ER_TABLE_EXISTS_ERROR') {
            console.log(`✓ 第 ${i + 1} 条语句跳过（已存在）`);
          } else {
            console.error(`✗ 第 ${i + 1} 条语句执行失败:`, error.message);
            console.log('失败的语句:', statement.substring(0, 100) + '...');
          }
        }
      }
    }
    
    console.log('数据库迁移完成！');
    
    // 验证表是否创建成功
    console.log('\n验证表结构...');
    const tables = [
      'cart_items',
      'order_items', 
      'discount_products',
      'discount_categories',
      'discount_usage_records',
      'stock_records'
    ];
    
    for (const table of tables) {
      try {
        const [rows] = await connection.execute(`SHOW TABLES LIKE '${table}'`);
        if (rows.length > 0) {
          console.log(`✓ 表 ${table} 创建成功`);
        } else {
          console.log(`✗ 表 ${table} 创建失败`);
        }
      } catch (error) {
        console.error(`检查表 ${table} 时出错:`, error.message);
      }
    }
    
    // 检查订单表字段是否添加成功
    console.log('\n检查订单表字段...');
    try {
      const [columns] = await connection.execute(`SHOW COLUMNS FROM orders`);
      const columnNames = columns.map(col => col.Field);
      
      const requiredFields = ['original_amount', 'discount_amount', 'shipping_fee', 'final_amount', 'payment_method', 'transaction_id', 'paid_at'];
      for (const field of requiredFields) {
        if (columnNames.includes(field)) {
          console.log(`✓ 订单表字段 ${field} 添加成功`);
        } else {
          console.log(`✗ 订单表字段 ${field} 添加失败`);
        }
      }
    } catch (error) {
      console.error('检查订单表字段时出错:', error.message);
    }
    
    // 检查商品表库存字段
    console.log('\n检查商品表字段...');
    try {
      const [columns] = await connection.execute(`SHOW COLUMNS FROM products`);
      const columnNames = columns.map(col => col.Field);
      
      if (columnNames.includes('stock')) {
        console.log(`✓ 商品表字段 stock 添加成功`);
      } else {
        console.log(`✗ 商品表字段 stock 添加失败`);
      }
    } catch (error) {
      console.error('检查商品表字段时出错:', error.message);
    }
    
    console.log('\n迁移完成！购物车和订单功能已准备就绪。');
    
  } catch (error) {
    console.error('数据库迁移失败:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 执行迁移
if (require.main === module) {
  executeMigration().catch(console.error);
}

module.exports = { executeMigration };
