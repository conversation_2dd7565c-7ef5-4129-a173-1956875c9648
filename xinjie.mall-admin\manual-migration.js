const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'xinjie_mall',
  charset: 'utf8mb4'
};

async function manualMigration() {
  let connection;
  
  try {
    console.log('开始手动执行数据库迁移...');
    
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功');
    
    // 1. 为订单表添加字段
    console.log('1. 为订单表添加字段...');
    
    const orderFields = [
      'ADD COLUMN original_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT \'原始金额\' AFTER total_amount',
      'ADD COLUMN shipping_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT \'运费\' AFTER discount_amount',
      'ADD COLUMN final_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT \'最终支付金额\' AFTER shipping_fee',
      'ADD COLUMN transaction_id VARCHAR(100) NULL COMMENT \'交易号\' AFTER payment_method',
      'ADD COLUMN paid_at DATETIME NULL COMMENT \'支付时间\' AFTER transaction_id'
    ];
    
    for (const field of orderFields) {
      try {
        await connection.execute(`ALTER TABLE orders ${field}`);
        console.log(`✓ 添加字段成功: ${field.split(' ')[2]}`);
      } catch (error) {
        if (error.code === 'ER_DUP_FIELDNAME') {
          console.log(`✓ 字段已存在: ${field.split(' ')[2]}`);
        } else {
          console.error(`✗ 添加字段失败: ${error.message}`);
        }
      }
    }
    
    // 2. 创建折扣商品关联表
    console.log('2. 创建折扣商品关联表...');
    try {
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS discount_products (
          id INT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
          discount_id INT NOT NULL COMMENT '折扣ID',
          product_id INT NOT NULL COMMENT '商品ID',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          
          UNIQUE KEY uk_discount_product (discount_id, product_id),
          INDEX idx_discount_id (discount_id),
          INDEX idx_product_id (product_id),
          FOREIGN KEY (discount_id) REFERENCES discounts(id) ON DELETE CASCADE,
          FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
        ) COMMENT '折扣商品关联表'
      `);
      console.log('✓ 创建折扣商品关联表成功');
    } catch (error) {
      console.error('✗ 创建折扣商品关联表失败:', error.message);
    }
    
    // 3. 创建折扣分类关联表
    console.log('3. 创建折扣分类关联表...');
    try {
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS discount_categories (
          id INT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
          discount_id INT NOT NULL COMMENT '折扣ID',
          category_id INT NOT NULL COMMENT '分类ID',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          
          UNIQUE KEY uk_discount_category (discount_id, category_id),
          INDEX idx_discount_id (discount_id),
          INDEX idx_category_id (category_id),
          FOREIGN KEY (discount_id) REFERENCES discounts(id) ON DELETE CASCADE,
          FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
        ) COMMENT '折扣分类关联表'
      `);
      console.log('✓ 创建折扣分类关联表成功');
    } catch (error) {
      console.error('✗ 创建折扣分类关联表失败:', error.message);
    }
    
    // 4. 创建折扣使用记录表
    console.log('4. 创建折扣使用记录表...');
    try {
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS discount_usage_records (
          id INT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
          discount_id INT NOT NULL COMMENT '折扣ID',
          user_id INT NOT NULL COMMENT '用户ID',
          order_id INT NOT NULL COMMENT '订单ID',
          discount_amount DECIMAL(10,2) NOT NULL COMMENT '折扣金额',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '使用时间',
          
          INDEX idx_discount_id (discount_id),
          INDEX idx_user_id (user_id),
          INDEX idx_order_id (order_id),
          INDEX idx_created_at (created_at),
          FOREIGN KEY (discount_id) REFERENCES discounts(id) ON DELETE CASCADE,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
        ) COMMENT '折扣使用记录表'
      `);
      console.log('✓ 创建折扣使用记录表成功');
    } catch (error) {
      console.error('✗ 创建折扣使用记录表失败:', error.message);
    }
    
    // 5. 创建库存变动记录表
    console.log('5. 创建库存变动记录表...');
    try {
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS stock_records (
          id INT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
          product_id INT NOT NULL COMMENT '商品ID',
          type TINYINT NOT NULL COMMENT '变动类型(1:入库 2:出库)',
          quantity INT NOT NULL COMMENT '变动数量',
          stock_before INT NOT NULL COMMENT '变动前库存',
          stock_after INT NOT NULL COMMENT '变动后库存',
          source TINYINT NOT NULL COMMENT '变动来源(1:采购入库 2:订单出库 3:退货入库 4:后台调整)',
          source_id INT NULL COMMENT '来源ID(订单ID等)',
          remark VARCHAR(255) NULL COMMENT '备注',
          operator_id INT NULL COMMENT '操作员ID',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          
          INDEX idx_product_id (product_id),
          INDEX idx_type (type),
          INDEX idx_source (source),
          INDEX idx_created_at (created_at),
          FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
        ) COMMENT '库存变动记录表'
      `);
      console.log('✓ 创建库存变动记录表成功');
    } catch (error) {
      console.error('✗ 创建库存变动记录表失败:', error.message);
    }
    
    console.log('手动迁移完成！');
    
  } catch (error) {
    console.error('手动迁移失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

manualMigration().catch(console.error);
