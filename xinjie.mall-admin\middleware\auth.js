const jwt = require('jsonwebtoken');
const { query } = require('../src/config/database');
const roleModel = require('../models/roleModel');

// 验证管理员登录状态
const requireAuth = async (req, res, next) => {
  try {
    console.log('认证检查:', {
      sessionID: req.sessionID,
      hasSessionToken: !!req.session.token,
      hasSessionAdminId: !!req.session.adminId,
      hasAuthHeader: !!req.headers.authorization,
      userAgent: req.headers['user-agent'],
    });

    let token = req.session.token;

    // 如果session中没有token，尝试从Authorization头获取
    if (!token && req.headers.authorization) {
      token = req.headers.authorization.replace('Bearer ', '');
      console.log('从Authorization头获取token');
    }

    if (!token) {
      console.log('未找到有效token');
      return res.status(401).json({
        success: false,
        message: '请先登录',
      });
    }

    // 验证JWT token
    const decoded = jwt.verify(
      token,
      process.env.JWT_SECRET || 'xinjie-mall-jwt-secret'
    );
    console.log('Token验证成功:', {
      userId: decoded.id,
      username: decoded.username,
    });

    // 查询管理员信息
    const admin = await query(
      'SELECT id, username, email, role_id as role, status FROM admin_users WHERE id = ? AND status = 1',
      [decoded.id]
    );

    if (!admin || admin.length === 0) {
      console.log('管理员不存在或已被禁用:', decoded.id);
      return res.status(401).json({
        success: false,
        message: '管理员不存在或已被禁用',
      });
    }

    req.admin = admin[0];
    console.log('认证成功:', {
      adminId: req.admin.id,
      username: req.admin.username,
    });

    // 加载角色权限
    if (req.admin.role) {
      const rolePermissions = await roleModel.getPermissions(req.admin.role);
      req.admin.permissions = rolePermissions.map(p => p.code);
    } else {
      req.admin.permissions = [];
    }

    next();
  } catch (error) {
    console.error('认证错误:', error);
    return res.status(401).json({
      success: false,
      message: '认证失败，请重新登录',
    });
  }
};

// 验证权限
const requirePermission = permission => {
  return (req, res, next) => {
    if (!req.admin) {
      return res.status(401).json({
        success: false,
        message: '请先登录',
      });
    }

    // 超级管理员拥有所有权限
    if (req.admin.role === 'super_admin') {
      return next();
    }

    // 检查具体权限
    if (req.admin.permissions && req.admin.permissions.includes(permission)) {
      return next();
    }

    return res.status(403).json({
      success: false,
      message: '权限不足',
    });
  };
};

// 可选认证（不强制要求登录）
const optionalAuth = async (req, res, next) => {
  try {
    const token =
      req.session.token || req.headers.authorization?.replace('Bearer ', '');

    if (token) {
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'xinjie-mall-jwt-secret'
      );
      const admin = await query(
        'SELECT id, username, email, role_id as role, status FROM admin_users WHERE id = ? AND status = 1',
        [decoded.id]
      );

      if (admin && admin.length > 0) {
        req.admin = admin[0];
      }
    }

    next();
  } catch (error) {
    // 可选认证失败不影响后续处理
    next();
  }
};

module.exports = {
  requireAuth,
  requirePermission,
  optionalAuth,
};
