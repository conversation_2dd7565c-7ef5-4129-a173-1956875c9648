const jwt = require('jsonwebtoken');
const { query } = require('../src/config/database');

/**
 * 前端用户认证中间件
 * 用于验证小程序用户的登录状态
 */

// 验证前端用户登录状态
const requireFrontAuth = async (req, res, next) => {
  try {
    console.log('前端用户认证检查:', {
      hasAuthHeader: !!req.headers.authorization,
      userAgent: req.headers['user-agent'],
      path: req.path
    });

    let token = null;

    // 从Authorization头获取token
    if (req.headers.authorization) {
      token = req.headers.authorization.replace('Bearer ', '');
      console.log('从Authorization头获取token');
    }

    if (!token) {
      console.log('未找到有效token');
      return res.status(401).json({
        success: false,
        message: '请先登录',
        code: 401
      });
    }

    // 验证JWT token
    const decoded = jwt.verify(
      token,
      process.env.JWT_SECRET || 'xinjie-mall-jwt-secret'
    );
    
    console.log('Token验证成功:', {
      userId: decoded.userId || decoded.id,
      openid: decoded.openid
    });

    // 查询用户信息
    const users = await query(
      'SELECT id, openid, nickname, phone, avatar_url, status, created_at FROM users WHERE id = ? AND status = 1',
      [decoded.userId || decoded.id]
    );

    if (!users || users.length === 0) {
      console.log('用户不存在或已被禁用:', decoded.userId || decoded.id);
      return res.status(401).json({
        success: false,
        message: '用户不存在或已被禁用',
        code: 401
      });
    }

    // 将用户信息添加到请求对象
    req.user = {
      id: users[0].id,
      userId: users[0].id,
      openid: users[0].openid,
      nickname: users[0].nickname,
      phone: users[0].phone,
      avatarUrl: users[0].avatar_url,
      status: users[0].status,
      createdAt: users[0].created_at
    };

    console.log('前端用户认证成功:', {
      userId: req.user.id,
      nickname: req.user.nickname
    });

    // 检查是否需要处理分享码绑定（仅对新用户）
    const shareCode = req.query.share || req.body.share;
    if (shareCode && users[0].created_at) {
      const userCreatedTime = new Date(users[0].created_at);
      const now = new Date();
      const timeDiff = now - userCreatedTime;

      // 如果用户是5分钟内注册的新用户，尝试绑定分销关系
      if (timeDiff < 5 * 60 * 1000) {
        try {
          const distributionService = require('../services/distributionService');

          // 处理分享点击
          const shareResult = await distributionService.handleShareClick(shareCode);

          if (shareResult.success && shareResult.data.shareInfo) {
            const sharerId = shareResult.data.shareInfo.sharerId;

            // 绑定分销关系
            await distributionService.bindDistributorRelation(sharerId, users[0].id, shareCode);
            console.log('新用户分销关系绑定成功:', { sharerId, userId: users[0].id, shareCode });
          }
        } catch (bindError) {
          console.error('分销关系绑定失败:', bindError);
          // 绑定失败不影响正常登录流程
        }
      }
    }

    next();
  } catch (error) {
    console.error('前端用户认证错误:', error);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Token格式错误',
        code: 401
      });
    } else if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token已过期，请重新登录',
        code: 401
      });
    } else {
      return res.status(401).json({
        success: false,
        message: '认证失败，请重新登录',
        code: 401
      });
    }
  }
};

// 可选认证（不强制要求登录）
const optionalFrontAuth = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (token) {
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'xinjie-mall-jwt-secret'
      );
      
      const users = await query(
        'SELECT id, openid, nickname, phone, avatar_url, status, created_at FROM users WHERE id = ? AND status = 1',
        [decoded.userId || decoded.id]
      );

      if (users && users.length > 0) {
        req.user = {
          id: users[0].id,
          userId: users[0].id,
          openid: users[0].openid,
          nickname: users[0].nickname,
          phone: users[0].phone,
          avatarUrl: users[0].avatar_url,
          status: users[0].status,
          createdAt: users[0].created_at
        };
      }
    }

    next();
  } catch (error) {
    // 可选认证失败不影响后续处理
    console.log('可选认证失败，继续处理:', error.message);
    next();
  }
};

// 创建JWT Token
const createToken = (user) => {
  return jwt.sign(
    {
      userId: user.id,
      id: user.id,
      openid: user.openid,
      nickname: user.nickname
    },
    process.env.JWT_SECRET || 'xinjie-mall-jwt-secret',
    { expiresIn: '7d' }
  );
};

// 验证Token（不查询数据库）
const verifyToken = (token) => {
  try {
    return jwt.verify(token, process.env.JWT_SECRET || 'xinjie-mall-jwt-secret');
  } catch (error) {
    throw error;
  }
};

module.exports = {
  requireFrontAuth,
  optionalFrontAuth,
  createToken,
  verifyToken
};
