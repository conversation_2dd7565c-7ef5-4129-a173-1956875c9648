const db = require('../src/config/database');

const balanceModel = {
  // 获取用户余额
  getUserBalance: async (userId) => {
    const sql = 'SELECT balance FROM users WHERE id = ?';
    const [result] = await db.query(sql, [userId]);
    return result[0]?.balance || 0;
  },
  
  // 更新用户余额（带事务）
  updateUserBalance: async (userId, amount, type, source, sourceId = null, remark = '', operatorId = null) => {
    const connection = await db.getConnection();
    
    try {
      await connection.beginTransaction();
      
      // 获取当前余额
      const [userResult] = await connection.query('SELECT balance FROM users WHERE id = ?', [userId]);
      if (!userResult.length) {
        throw new Error('用户不存在');
      }
      
      const balanceBefore = parseFloat(userResult[0].balance);
      let balanceAfter;
      
      if (type === 1) { // 增加
        balanceAfter = balanceBefore + parseFloat(amount);
      } else { // 减少
        balanceAfter = balanceBefore - parseFloat(amount);
        if (balanceAfter < 0) {
          throw new Error('余额不足');
        }
      }
      
      // 更新用户余额
      await connection.query(
        'UPDATE users SET balance = ?, updated_at = NOW() WHERE id = ?',
        [balanceAfter, userId]
      );
      
      // 记录余额变动
      await connection.query(`
        INSERT INTO balance_records (
          user_id, type, amount, balance_before, balance_after, 
          source, source_id, remark, operator_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        userId, type, amount, balanceBefore, balanceAfter,
        source, sourceId, remark, operatorId
      ]);
      
      await connection.commit();
      return balanceAfter;
      
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  },
  
  // 获取余额变动记录
  getBalanceRecords: async ({
    page = 1,
    pageSize = 10,
    userId = '',
    type = '',
    source = '',
    startDate = '',
    endDate = ''
  }) => {
    try {
      const offset = (page - 1) * pageSize;
      let where = 'WHERE 1=1';
      let params = [];

      if (userId) {
        where += ' AND br.user_id = ?';
        params.push(userId);
      }

      if (type !== '') {
        where += ' AND br.type = ?';
        params.push(type);
      }

      if (source !== '') {
        where += ' AND br.source = ?';
        params.push(source);
      }

      if (startDate) {
        where += ' AND DATE(br.created_at) >= ?';
        params.push(startDate);
      }

      if (endDate) {
        where += ' AND DATE(br.created_at) <= ?';
        params.push(endDate);
      }

      // 先检查表是否存在
      try {
        await db.query("SHOW TABLES LIKE 'balance_records'");
      } catch (tableError) {
        console.error('balance_records表不存在:', tableError);
        // 返回空数据
        return {
          list: [],
          total: 0,
          page: parseInt(page),
          pageSize: parseInt(pageSize)
        };
      }

      // 查询总数
      const countSql = `
        SELECT COUNT(*) as total
        FROM balance_records br
        LEFT JOIN users u ON br.user_id = u.id
        ${where}
      `;

      let total = 0;
      try {
        const countResult = await db.query(countSql, params);
        total = countResult[0]?.total || 0;
      } catch (countError) {
        console.error('查询余额记录总数失败:', countError);
        total = 0;
      }

      // 查询列表数据
      const listSql = `
        SELECT
          br.*,
          u.nickname,
          u.phone,
          CASE br.type
            WHEN 1 THEN '增加'
            WHEN 2 THEN '减少'
            ELSE '未知'
          END as type_text,
          CASE br.source
            WHEN 1 THEN '充值'
            WHEN 2 THEN '消费'
            WHEN 3 THEN '退款'
            WHEN 4 THEN '后台调整'
            ELSE '未知'
          END as source_text
        FROM balance_records br
        LEFT JOIN users u ON br.user_id = u.id
        ${where}
        ORDER BY br.created_at DESC
        LIMIT ? OFFSET ?
      `;

      let list = [];
      try {
        const listResult = await db.query(listSql, [...params, parseInt(pageSize), offset]);
        list = listResult || [];
      } catch (listError) {
        console.error('查询余额记录列表失败:', listError);
        list = [];
      }

      return {
        list,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      };

    } catch (error) {
      console.error('getBalanceRecords方法执行失败:', error);
      // 返回空数据而不是抛出错误
      return {
        list: [],
        total: 0,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      };
    }
  },
  
  // 后台充值（增加余额）
  adminRecharge: async (userId, amount, bonusAmount = 0, remark = '', operatorId = null) => {
    const connection = await db.getConnection();
    
    try {
      await connection.beginTransaction();
      
      const totalAmount = parseFloat(amount) + parseFloat(bonusAmount);
      const orderNo = `RC${Date.now()}${Math.floor(Math.random() * 1000)}`;
      
      // 创建充值记录
      const [rechargeResult] = await connection.query(`
        INSERT INTO recharge_records (
          user_id, order_no, amount, bonus_amount, total_amount,
          payment_method, payment_status, remark, operator_id, paid_at
        ) VALUES (?, ?, ?, ?, ?, 4, 1, ?, ?, NOW())
      `, [userId, orderNo, amount, bonusAmount, totalAmount, remark, operatorId]);
      
      const rechargeId = rechargeResult.insertId;
      
      // 更新用户余额
      const newBalance = await balanceModel.updateUserBalance(
        userId, totalAmount, 1, 1, rechargeId, 
        `后台充值：${amount}元${bonusAmount > 0 ? ` + 赠送${bonusAmount}元` : ''}`, 
        operatorId
      );
      
      await connection.commit();
      
      return {
        rechargeId,
        newBalance,
        totalAmount
      };
      
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  },
  
  // 后台调整余额
  adminAdjustBalance: async (userId, amount, type, remark = '', operatorId = null) => {
    return await balanceModel.updateUserBalance(
      userId, Math.abs(amount), type, 4, null, remark, operatorId
    );
  },

  // 创建用户充值订单
  createRechargeOrder: async (userId, amount, paymentMethod) => {
    const connection = await db.getConnection();

    try {
      await connection.beginTransaction();

      const orderNo = `RC${Date.now()}${Math.floor(Math.random() * 1000)}`;

      // 创建充值记录（待支付状态）
      const [rechargeResult] = await connection.query(`
        INSERT INTO recharge_records (
          user_id, order_no, amount, bonus_amount, total_amount,
          payment_method, payment_status, created_at
        ) VALUES (?, ?, ?, 0, ?, ?, 0, NOW())
      `, [userId, orderNo, amount, amount, paymentMethod === 'wechat' ? 1 : 2]);

      await connection.commit();

      return {
        id: rechargeResult.insertId,
        order_no: orderNo,
        amount: amount,
        user_id: userId
      };

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  },

  // 完成充值（支付成功后调用）
  completeRecharge: async (orderNo, transactionId) => {
    const connection = await db.getConnection();

    try {
      await connection.beginTransaction();

      // 查找充值记录
      const [rechargeRecords] = await connection.query(
        'SELECT * FROM recharge_records WHERE order_no = ? AND payment_status = 0',
        [orderNo]
      );

      if (!rechargeRecords.length) {
        throw new Error('充值记录不存在或已处理');
      }

      const recharge = rechargeRecords[0];

      // 更新充值记录状态
      await connection.query(`
        UPDATE recharge_records
        SET payment_status = 1, transaction_id = ?, paid_at = NOW(), updated_at = NOW()
        WHERE id = ?
      `, [transactionId, recharge.id]);

      // 增加用户余额
      const newBalance = await balanceModel.updateUserBalance(
        recharge.user_id,
        recharge.total_amount,
        1, // 增加
        1, // 充值来源
        recharge.id,
        `用户充值：${recharge.amount}元`,
        null
      );

      await connection.commit();

      return {
        rechargeId: recharge.id,
        newBalance,
        amount: recharge.total_amount
      };

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  },

  // 获取用户充值记录
  getUserRechargeHistory: async (userId, page = 1, pageSize = 10) => {
    const offset = (page - 1) * pageSize;

    // 查询总数
    const [countResult] = await db.query(
      'SELECT COUNT(*) as total FROM recharge_records WHERE user_id = ?',
      [userId]
    );
    const total = countResult[0]?.total || 0;

    // 查询列表
    const [list] = await db.query(`
      SELECT
        id, order_no, amount, bonus_amount, total_amount,
        payment_method, payment_status, transaction_id,
        created_at, paid_at, remark
      FROM recharge_records
      WHERE user_id = ?
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `, [userId, pageSize, offset]);

    return {
      list: list || [],
      total,
      page,
      pageSize
    };
  },

  // 获取用户余额变动记录
  getUserBalanceHistory: async (userId, page = 1, pageSize = 10) => {
    const offset = (page - 1) * pageSize;

    // 查询总数
    const [countResult] = await db.query(
      'SELECT COUNT(*) as total FROM balance_records WHERE user_id = ?',
      [userId]
    );
    const total = countResult[0]?.total || 0;

    // 查询列表
    const [list] = await db.query(`
      SELECT
        id, type, amount, balance_before, balance_after,
        source, remark, created_at
      FROM balance_records
      WHERE user_id = ?
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `, [userId, pageSize, offset]);

    return {
      list: list || [],
      total,
      page,
      pageSize
    };
  },

  // 余额支付（订单支付时使用）
  balancePayment: async (userId, orderId, amount, remark = '') => {
    const connection = await db.getConnection();

    try {
      await connection.beginTransaction();

      // 检查余额是否足够
      const [userResult] = await connection.query('SELECT balance FROM users WHERE id = ?', [userId]);
      if (!userResult.length) {
        throw new Error('用户不存在');
      }

      const currentBalance = parseFloat(userResult[0].balance);
      if (currentBalance < amount) {
        throw new Error('余额不足');
      }

      // 扣除余额
      const newBalance = await balanceModel.updateUserBalance(
        userId,
        amount,
        2, // 减少
        2, // 订单支付来源
        orderId,
        remark || `订单支付：${amount}元`,
        null
      );

      await connection.commit();

      return {
        success: true,
        newBalance,
        paidAmount: amount
      };

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }
};

module.exports = balanceModel;
