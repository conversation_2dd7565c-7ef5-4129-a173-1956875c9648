// 这里可以使用sequelize、knex或原生mysql
// 示例骨架
const db = require('../src/config/database');

const bannerModel = {
  findAll: async ({ page = 1, pageSize = 10, title = '', status }) => {
    // 强制类型转换，确保为数字
    const pageNum = Number(page);
    const pageSizeNum = Number(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    let where = 'WHERE 1=1';
    let params = [];
    if (title) {
      where += ' AND title LIKE ?';
      params.push(`%${title}%`);
    }
    if (status !== undefined && status !== '') {
      where += ' AND status = ?';
      params.push(status);
    }
    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM banners ${where}`;
    const countRows = await db.query(countSql, params);
    const total = countRows[0].total;
    // 获取分页数据（直接拼接 offset 和 pageSize）
    const baseSql = `SELECT * FROM banners ${where} ORDER BY sort_order ASC, created_at ASC, id ASC LIMIT ${offset}, ${pageSizeNum}`;
    const rows = await db.query(baseSql, params);
    return { list: rows, total };
  },
  create: async data => {
    const {
      title,
      image_url,
      link_url,
      sort_order = 0,
      status = 1,
      start_time,
      end_time,
    } = data;
    const sql = `INSERT INTO banners (title, image_url, link_url, sort_order, status, start_time, end_time, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`;
    // 将 undefined 转为 null
    const safeStartTime = start_time === undefined ? null : start_time;
    const safeEndTime = end_time === undefined ? null : end_time;
    const result = await db.query(sql, [
      title,
      image_url,
      link_url,
      sort_order,
      status,
      safeStartTime,
      safeEndTime,
    ]);
    return result.insertId;
  },
  update: async (id, data) => {
    const {
      title,
      image_url,
      link_url,
      sort_order,
      status,
      start_time,
      end_time,
    } = data;
    const sql = `UPDATE banners SET title=?, image_url=?, link_url=?, sort_order=?, status=?, start_time=?, end_time=?, updated_at=NOW() WHERE id=?`;
    await db.query(sql, [
      title,
      image_url,
      link_url,
      sort_order,
      status,
      start_time,
      end_time,
      id,
    ]);
    return true;
  },
  delete: async id => {
    const sql = `DELETE FROM banners WHERE id=?`;
    await db.query(sql, [id]);
    return true;
  },
};

module.exports = bannerModel;
