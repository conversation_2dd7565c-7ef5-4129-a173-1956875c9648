const db = require('../src/config/database');

const cartModel = {
  // 获取用户购物车列表
  findByUserId: async (userId) => {
    const sql = `
      SELECT 
        c.*,
        p.name as product_name,
        p.image as product_image,
        p.price as product_price,
        p.stock as product_stock,
        p.status as product_status,
        p.category_id,
        cat.name as category_name
      FROM cart_items c
      LEFT JOIN products p ON c.product_id = p.id
      LEFT JOIN categories cat ON p.category_id = cat.id
      WHERE c.user_id = ? AND p.status = 1
      ORDER BY c.created_at DESC
    `;
    const [result] = await db.query(sql, [userId]);
    return result;
  },

  // 添加商品到购物车
  addItem: async (userId, productId, quantity = 1, specs = null) => {
    // 先检查是否已存在相同商品
    const existingSql = `
      SELECT * FROM cart_items 
      WHERE user_id = ? AND product_id = ? AND specs = ?
    `;
    const [existing] = await db.query(existingSql, [userId, productId, specs]);

    if (existing.length > 0) {
      // 更新数量
      const updateSql = `
        UPDATE cart_items 
        SET quantity = quantity + ?, updated_at = NOW()
        WHERE id = ?
      `;
      await db.query(updateSql, [quantity, existing[0].id]);
      return existing[0].id;
    } else {
      // 新增商品
      const insertSql = `
        INSERT INTO cart_items (user_id, product_id, quantity, specs, created_at, updated_at)
        VALUES (?, ?, ?, ?, NOW(), NOW())
      `;
      const [result] = await db.query(insertSql, [userId, productId, quantity, specs]);
      return result.insertId;
    }
  },

  // 更新购物车商品数量
  updateQuantity: async (userId, cartItemId, quantity) => {
    const sql = `
      UPDATE cart_items 
      SET quantity = ?, updated_at = NOW()
      WHERE id = ? AND user_id = ?
    `;
    await db.query(sql, [quantity, cartItemId, userId]);
    return true;
  },

  // 删除购物车商品
  removeItem: async (userId, cartItemId) => {
    const sql = `
      DELETE FROM cart_items 
      WHERE id = ? AND user_id = ?
    `;
    await db.query(sql, [cartItemId, userId]);
    return true;
  },

  // 清空购物车
  clearCart: async (userId) => {
    const sql = `
      DELETE FROM cart_items 
      WHERE user_id = ?
    `;
    await db.query(sql, [userId]);
    return true;
  },

  // 获取购物车统计信息
  getCartStats: async (userId) => {
    const sql = `
      SELECT 
        COUNT(*) as total_items,
        SUM(quantity) as total_quantity
      FROM cart_items c
      LEFT JOIN products p ON c.product_id = p.id
      WHERE c.user_id = ? AND p.status = 1
    `;
    const [result] = await db.query(sql, [userId]);
    return result[0] || { total_items: 0, total_quantity: 0 };
  },

  // 批量删除购物车商品（结算后）
  removeItems: async (userId, cartItemIds) => {
    if (!cartItemIds || cartItemIds.length === 0) return true;
    
    const placeholders = cartItemIds.map(() => '?').join(',');
    const sql = `
      DELETE FROM cart_items 
      WHERE user_id = ? AND id IN (${placeholders})
    `;
    await db.query(sql, [userId, ...cartItemIds]);
    return true;
  }
};

module.exports = cartModel;
