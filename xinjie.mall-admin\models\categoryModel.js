const db = require('../src/config/database');

const categoryModel = {
  findAll: async ({
    page = 1,
    pageSize = 20,
    name = '',
    parent_id = '',
    all = false,
  } = {}) => {
    page = parseInt(page) || 1;
    pageSize = parseInt(pageSize) || 20;
    let where = 'WHERE 1=1';
    let params = [];
    if (name) {
      where += ' AND name LIKE ?';
      params.push(`%${name}%`);
    }
    if (parent_id !== '' && parent_id !== undefined) {
      where += ' AND parent_id = ?';
      params.push(parent_id);
    }
    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM categories ${where}`;
    const countRows = await db.query(countSql, params);
    const total = countRows[0].total;
    // 获取数据
    let sql = `SELECT * FROM categories ${where} ORDER BY sort_order ASC, id DESC`;
    let rows;
    if (all) {
      rows = await db.query(sql, params);
      return rows;
    } else {
      sql += ` LIMIT ${pageSize} OFFSET ${(page - 1) * pageSize}`;
      rows = await db.query(sql, params);
      return { list: rows, pagination: { current: page, pageSize, total } };
    }
  },
  findAllTree: async () => {
    // 获取所有分类，组装树结构
    const rows = await db.query(
      'SELECT * FROM categories ORDER BY sort_order DESC, id DESC',
      []
    );
    const map = {};
    rows.forEach(row => {
      map[row.id] = { ...row, children: [] };
    });
    const tree = [];
    rows.forEach(row => {
      if (row.parent_id && row.parent_id !== 0 && map[row.parent_id]) {
        map[row.parent_id].children.push(map[row.id]);
      } else {
        tree.push(map[row.id]);
      }
    });
    return tree;
  },
  findById: async id => {
    const rows = await db.query('SELECT * FROM categories WHERE id=?', [id]);
    return rows[0] || null;
  },
  create: async data => {
    const { name, parent_id = 0, status = 1, image } = data;

    // 获取当前最大的sort_order，新增时自动+1
    const maxSortRows = await db.query(
      'SELECT COALESCE(MAX(sort_order), 0) as max_sort FROM categories'
    );
    const nextSortOrder = maxSortRows[0].max_sort + 1;

    const sql = `INSERT INTO categories (name, parent_id, sort_order, status, image, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())`;
    const result = await db.query(sql, [
      name,
      parent_id,
      nextSortOrder,
      status,
      image,
    ]);
    return result.insertId;
  },
  update: async (id, data) => {
    const { name, parent_id = 0, sort_order = 0, status = 1, image } = data;
    const sql = `UPDATE categories SET name=?, parent_id=?, sort_order=?, status=?, image=?, updated_at=NOW() WHERE id=?`;
    await db.query(sql, [name, parent_id, sort_order, status, image, id]);
    return true;
  },
  delete: async id => {
    const sql = `DELETE FROM categories WHERE id=?`;
    await db.query(sql, [id]);

    // 删除后重新排序
    await categoryModel.reorderSortOrder();

    return true;
  },

  // 重新排序方法
  reorderSortOrder: async () => {
    // 获取所有分类按创建时间排序
    const categories = await db.query(
      'SELECT id FROM categories ORDER BY created_at ASC'
    );

    // 重新分配sort_order
    for (let i = 0; i < categories.length; i++) {
      const newSortOrder = i + 1;
      await db.query('UPDATE categories SET sort_order = ? WHERE id = ?', [
        newSortOrder,
        categories[i].id,
      ]);
    }

    return true;
  },
  sort: async categories => {
    if (!Array.isArray(categories)) return false;
    for (const item of categories) {
      await db.query('UPDATE categories SET sort_order=? WHERE id=?', [
        item.sort_order,
        item.id,
      ]);
    }
    return true;
  },
};

module.exports = categoryModel;
