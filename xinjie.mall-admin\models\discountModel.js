const db = require('../src/config/database');

class DiscountModel {
  // 获取折扣列表
  static async findAll(options = {}) {
    const {
      page = 1,
      pageSize = 10,
      name = '',
      type = '',
      status = '',
      applicable_to = ''
    } = options;

    const offset = (page - 1) * pageSize;
    let whereClause = 'WHERE 1=1';
    const params = [];

    if (name) {
      whereClause += ' AND name LIKE ?';
      params.push(`%${name}%`);
    }

    if (type) {
      whereClause += ' AND type = ?';
      params.push(type);
    }

    if (status !== '') {
      whereClause += ' AND status = ?';
      params.push(status);
    }

    if (applicable_to) {
      whereClause += ' AND applicable_to = ?';
      params.push(applicable_to);
    }

    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM discounts ${whereClause}`;
    const countResult = await db.query(countSql, params);
    const total = countResult[0].total;

    // 获取列表数据 - 使用字符串拼接避免LIMIT参数化问题
    const listSql = `
      SELECT
        d.*,
        CASE
          WHEN d.end_time < NOW() THEN 'expired'
          WHEN d.start_time > NOW() THEN 'pending'
          WHEN d.status = 0 THEN 'disabled'
          WHEN d.usage_limit IS NOT NULL AND d.used_count >= d.usage_limit THEN 'exhausted'
          ELSE 'active'
        END as discount_status,
        CASE d.type
          WHEN 1 THEN '百分比折扣'
          WHEN 2 THEN '固定金额折扣'
          WHEN 3 THEN '满减折扣'
          ELSE '未知类型'
        END as type_name,
        CASE d.applicable_to
          WHEN 1 THEN '全部商品'
          WHEN 2 THEN '指定商品'
          WHEN 3 THEN '指定分类'
          ELSE '未知范围'
        END as applicable_to_name
      FROM discounts d
      ${whereClause}
      ORDER BY d.priority DESC, d.created_at DESC
      LIMIT ${parseInt(pageSize)} OFFSET ${offset}
    `;

    const rows = await db.query(listSql, params);

    return {
      list: rows,
      total,
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      totalPages: Math.ceil(total / pageSize)
    };
  }

  // 根据ID获取折扣详情
  static async findById(id) {
    const sql = `
      SELECT 
        d.*,
        CASE 
          WHEN d.end_time < NOW() THEN 'expired'
          WHEN d.start_time > NOW() THEN 'pending'
          WHEN d.status = 0 THEN 'disabled'
          WHEN d.usage_limit IS NOT NULL AND d.used_count >= d.usage_limit THEN 'exhausted'
          ELSE 'active'
        END as discount_status
      FROM discounts d 
      WHERE d.id = ?
    `;
    const rows = await db.query(sql, [id]);
    return rows[0] || null;
  }

  // 创建折扣
  static async create(discountData) {
    const {
      name,
      description,
      type,
      value,
      min_amount = 0,
      max_discount = null,
      start_time,
      end_time,
      usage_limit = null,
      user_limit = null,
      status = 1,
      priority = 0,
      applicable_to = 1,
      created_by = null
    } = discountData;

    const sql = `
      INSERT INTO discounts (
        name, description, type, value, min_amount, max_discount,
        start_time, end_time, usage_limit, user_limit, status,
        priority, applicable_to, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    // 确保所有参数都不是undefined，将undefined转换为null
    const params = [
      name || null,
      description || null,
      type,
      value,
      min_amount,
      max_discount,
      start_time,
      end_time,
      usage_limit,
      user_limit,
      status,
      priority,
      applicable_to,
      created_by
    ];

    console.log('执行折扣创建SQL:', sql);
    console.log('SQL参数:', params);

    const result = await db.query(sql, params);
    console.log('折扣创建结果:', result);

    if (!result.insertId) {
      throw new Error('折扣创建失败，未获取到插入ID');
    }

    return result.insertId;
  }

  // 更新折扣
  static async update(id, discountData) {
    const {
      name,
      description,
      type,
      value,
      min_amount,
      max_discount,
      start_time,
      end_time,
      usage_limit,
      user_limit,
      status,
      priority,
      applicable_to
    } = discountData;

    const sql = `
      UPDATE discounts SET 
        name = ?, description = ?, type = ?, value = ?, min_amount = ?,
        max_discount = ?, start_time = ?, end_time = ?, usage_limit = ?,
        user_limit = ?, status = ?, priority = ?, applicable_to = ?,
        updated_at = NOW()
      WHERE id = ?
    `;

    // 确保所有参数都不是undefined，将undefined转换为null
    const params = [
      name || null,
      description || null,
      type,
      value,
      min_amount !== undefined ? min_amount : null,
      max_discount !== undefined ? max_discount : null,
      start_time,
      end_time,
      usage_limit !== undefined ? usage_limit : null,
      user_limit !== undefined ? user_limit : null,
      status,
      priority,
      applicable_to,
      id
    ];

    const result = await db.query(sql, params);
    return result.affectedRows > 0;
  }

  // 删除折扣
  static async delete(id) {
    const sql = 'DELETE FROM discounts WHERE id = ?';
    const result = await db.query(sql, [id]);
    return result.affectedRows > 0;
  }

  // 更新折扣状态
  static async updateStatus(id, status) {
    const sql = 'UPDATE discounts SET status = ?, updated_at = NOW() WHERE id = ?';
    const result = await db.query(sql, [status, id]);
    return result.affectedRows > 0;
  }

  // 获取折扣关联的商品
  static async getDiscountProducts(discountId) {
    const sql = `
      SELECT p.*, pd.created_at as linked_at
      FROM products p
      INNER JOIN product_discounts pd ON p.id = pd.product_id
      WHERE pd.discount_id = ?
      ORDER BY pd.created_at DESC
    `;
    const rows = await db.query(sql, [discountId]);
    return rows;
  }

  // 获取折扣关联的分类
  static async getDiscountCategories(discountId) {
    const sql = `
      SELECT c.*, cd.created_at as linked_at
      FROM categories c
      INNER JOIN category_discounts cd ON c.id = cd.category_id
      WHERE cd.discount_id = ?
      ORDER BY cd.created_at DESC
    `;
    const rows = await db.query(sql, [discountId]);
    return rows;
  }

  // 设置折扣关联商品
  static async setDiscountProducts(discountId, productIds) {
    try {
      // 删除现有关联
      await db.query('DELETE FROM product_discounts WHERE discount_id = ?', [discountId]);

      // 添加新关联
      if (productIds && productIds.length > 0) {
        const values = productIds.map(productId => [discountId, productId]);
        const placeholders = values.map(() => '(?, ?)').join(', ');
        const sql = `INSERT INTO product_discounts (discount_id, product_id) VALUES ${placeholders}`;
        const params = values.flat();
        await db.query(sql, params);
      }

      return true;
    } catch (error) {
      throw error;
    }
  }

  // 设置折扣关联分类
  static async setDiscountCategories(discountId, categoryIds) {
    try {
      // 删除现有关联
      await db.query('DELETE FROM category_discounts WHERE discount_id = ?', [discountId]);

      // 添加新关联
      if (categoryIds && categoryIds.length > 0) {
        const values = categoryIds.map(categoryId => [discountId, categoryId]);
        const placeholders = values.map(() => '(?, ?)').join(', ');
        const sql = `INSERT INTO category_discounts (discount_id, category_id) VALUES ${placeholders}`;
        const params = values.flat();
        await db.query(sql, params);
      }

      return true;
    } catch (error) {
      throw error;
    }
  }

  // 获取有效的折扣列表
  static async getActiveDiscounts() {
    const sql = `
      SELECT * FROM discounts 
      WHERE status = 1 
        AND start_time <= NOW() 
        AND end_time >= NOW()
        AND (usage_limit IS NULL OR used_count < usage_limit)
      ORDER BY priority DESC, created_at ASC
    `;
    const rows = await db.query(sql);
    return rows;
  }

  // 计算商品折扣价格
  static calculateDiscountPrice(originalPrice, discount, quantity = 1) {
    if (!discount || discount.discount_status !== 'active') {
      return originalPrice;
    }

    let discountPrice = originalPrice;

    switch (discount.type) {
      case 1: // 百分比折扣
        discountPrice = originalPrice * (100 - discount.value) / 100;
        if (discount.max_discount && (originalPrice - discountPrice) > discount.max_discount) {
          discountPrice = originalPrice - discount.max_discount;
        }
        break;

      case 2: // 固定金额折扣
        discountPrice = Math.max(originalPrice - discount.value, 0.01);
        break;

      case 3: // 满减折扣
        const totalAmount = originalPrice * quantity;
        if (totalAmount >= discount.min_amount) {
          const discountAmount = discount.value;
          discountPrice = Math.max((totalAmount - discountAmount) / quantity, 0.01);
        }
        break;
    }

    return Math.round(discountPrice * 100) / 100; // 保留两位小数
  }

  // 保存折扣商品关联
  static async saveDiscountProducts(discountId, productIds) {
    if (!productIds || productIds.length === 0) return;

    // 先删除现有关联
    await db.query('DELETE FROM discount_products WHERE discount_id = ?', [discountId]);

    // 插入新关联
    const values = productIds.map(productId => [discountId, productId]);
    await db.query(
      'INSERT INTO discount_products (discount_id, product_id) VALUES ?',
      [values]
    );
  }

  // 保存折扣分类关联
  static async saveDiscountCategories(discountId, categoryIds) {
    if (!categoryIds || categoryIds.length === 0) return;

    // 先删除现有关联
    await db.query('DELETE FROM discount_categories WHERE discount_id = ?', [discountId]);

    // 插入新关联
    const values = categoryIds.map(categoryId => [discountId, categoryId]);
    await db.query(
      'INSERT INTO discount_categories (discount_id, category_id) VALUES ?',
      [values]
    );
  }

  // 获取折扣关联的商品
  static async getDiscountProducts(discountId) {
    const sql = `
      SELECT p.* FROM products p
      INNER JOIN discount_products dp ON p.id = dp.product_id
      WHERE dp.discount_id = ?
    `;
    const [result] = await db.query(sql, [discountId]);
    return result;
  }

  // 获取折扣关联的分类
  static async getDiscountCategories(discountId) {
    const sql = `
      SELECT c.* FROM categories c
      INNER JOIN discount_categories dc ON c.id = dc.category_id
      WHERE dc.discount_id = ?
    `;
    const [result] = await db.query(sql, [discountId]);
    return result;
  }
}

module.exports = DiscountModel;
