const db = require('../src/config/database');

const memberModel = {
  // 获取所有会员等级
  getAllLevels: async () => {
    const sql = `
      SELECT * FROM member_levels 
      WHERE status = 1 
      ORDER BY sort_order ASC, level_code ASC
    `;
    const [result] = await db.query(sql);
    return result;
  },
  
  // 获取会员等级列表（分页）
  getLevelList: async ({
    page = 1,
    pageSize = 10,
    status = '',
    levelName = ''
  }) => {
    try {
      const offset = (page - 1) * pageSize;
      let where = 'WHERE 1=1';
      let params = [];

      if (status !== '') {
        where += ' AND status = ?';
        params.push(status);
      }

      if (levelName) {
        where += ' AND level_name LIKE ?';
        params.push(`%${levelName}%`);
      }

      // 查询总数
      const countSql = `SELECT COUNT(*) as total FROM member_levels ${where}`;
      const [countResult] = await db.query(countSql, params);
      const total = countResult && countResult[0] ? countResult[0].total : 0;
    
    // 查询列表数据
    const listSql = `
      SELECT 
        ml.*,
        CASE ml.status
          WHEN 1 THEN '启用'
          WHEN 0 THEN '禁用'
          ELSE '未知'
        END as status_text
      FROM member_levels ml 
      ${where}
      ORDER BY ml.sort_order ASC, ml.level_code ASC
      LIMIT ? OFFSET ?
    `;
    
    const [list] = await db.query(listSql, [...params, parseInt(pageSize), offset]);

      return {
        list: list || [],
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      };
    } catch (error) {
      console.error('获取会员等级列表失败:', error);
      // 如果表不存在或其他错误，返回空数据
      return {
        list: [],
        total: 0,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      };
    }
  },
  
  // 根据ID获取会员等级详情
  getLevelById: async (id) => {
    const sql = 'SELECT * FROM member_levels WHERE id = ?';
    const [result] = await db.query(sql, [id]);
    return result[0] || null;
  },
  
  // 根据等级代码获取会员等级
  getLevelByCode: async (levelCode) => {
    const sql = 'SELECT * FROM member_levels WHERE level_code = ? AND status = 1';
    const [result] = await db.query(sql, [levelCode]);
    return result[0] || null;
  },
  
  // 创建会员等级
  createLevel: async (data) => {
    const {
      level_code,
      level_name,
      level_icon = '',
      min_points = 0,
      min_amount = 0,
      discount_rate = 1.00,
      points_ratio = 1.00,
      free_shipping_threshold = 0,
      birthday_discount = 0,
      sort_order = 0
    } = data;
    
    const sql = `
      INSERT INTO member_levels (
        level_code, level_name, level_icon, min_points, min_amount,
        discount_rate, points_ratio, free_shipping_threshold, 
        birthday_discount, sort_order
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const [result] = await db.query(sql, [
      level_code, level_name, level_icon, min_points, min_amount,
      discount_rate, points_ratio, free_shipping_threshold,
      birthday_discount, sort_order
    ]);
    
    return result.insertId;
  },
  
  // 更新会员等级
  updateLevel: async (id, data) => {
    const {
      level_name,
      level_icon,
      min_points,
      min_amount,
      discount_rate,
      points_ratio,
      free_shipping_threshold,
      birthday_discount,
      sort_order,
      status
    } = data;
    
    const sql = `
      UPDATE member_levels 
      SET level_name = ?, level_icon = ?, min_points = ?, min_amount = ?,
          discount_rate = ?, points_ratio = ?, free_shipping_threshold = ?,
          birthday_discount = ?, sort_order = ?, status = ?, updated_at = NOW()
      WHERE id = ?
    `;
    
    await db.query(sql, [
      level_name, level_icon, min_points, min_amount,
      discount_rate, points_ratio, free_shipping_threshold,
      birthday_discount, sort_order, status, id
    ]);
    
    return true;
  },
  
  // 删除会员等级
  deleteLevel: async (id) => {
    // 检查是否有用户使用该等级
    const [userCount] = await db.query('SELECT COUNT(*) as count FROM users WHERE user_level = ?', [id]);
    if (userCount[0].count > 0) {
      throw new Error('该等级下还有用户，无法删除');
    }
    
    const sql = 'DELETE FROM member_levels WHERE id = ?';
    await db.query(sql, [id]);
    return true;
  },
  
  // 更新等级状态
  updateLevelStatus: async (id, status) => {
    const sql = 'UPDATE member_levels SET status = ?, updated_at = NOW() WHERE id = ?';
    await db.query(sql, [status, id]);
    return true;
  },
  
  // 获取会员权益列表
  getBenefitsByLevelId: async (levelId) => {
    const sql = `
      SELECT * FROM member_benefits 
      WHERE level_id = ? AND status = 1 
      ORDER BY sort_order ASC, id ASC
    `;
    const [result] = await db.query(sql, [levelId]);
    return result;
  },
  
  // 获取所有会员权益（分页）
  getBenefitList: async ({
    page = 1,
    pageSize = 10,
    levelId = '',
    benefitType = '',
    status = ''
  }) => {
    const offset = (page - 1) * pageSize;
    let where = 'WHERE 1=1';
    let params = [];
    
    if (levelId) {
      where += ' AND mb.level_id = ?';
      params.push(levelId);
    }
    
    if (benefitType !== '') {
      where += ' AND mb.benefit_type = ?';
      params.push(benefitType);
    }
    
    if (status !== '') {
      where += ' AND mb.status = ?';
      params.push(status);
    }
    
    // 查询总数
    const countSql = `
      SELECT COUNT(*) as total 
      FROM member_benefits mb 
      LEFT JOIN member_levels ml ON mb.level_id = ml.id 
      ${where}
    `;
    const [countResult] = await db.query(countSql, params);
    const total = countResult[0].total;
    
    // 查询列表数据
    const listSql = `
      SELECT 
        mb.*,
        ml.level_name,
        CASE mb.benefit_type
          WHEN 1 THEN '折扣'
          WHEN 2 THEN '积分倍率'
          WHEN 3 THEN '免邮'
          WHEN 4 THEN '专属客服'
          WHEN 5 THEN '生日特权'
          WHEN 6 THEN '优先发货'
          ELSE '未知'
        END as benefit_type_text,
        CASE mb.status
          WHEN 1 THEN '启用'
          WHEN 0 THEN '禁用'
          ELSE '未知'
        END as status_text
      FROM member_benefits mb 
      LEFT JOIN member_levels ml ON mb.level_id = ml.id 
      ${where}
      ORDER BY ml.sort_order ASC, mb.sort_order ASC
      LIMIT ? OFFSET ?
    `;
    
    const [list] = await db.query(listSql, [...params, parseInt(pageSize), offset]);
    
    return {
      list,
      total,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    };
  },
  
  // 创建会员权益
  createBenefit: async (data) => {
    const {
      level_id,
      benefit_type,
      benefit_name,
      benefit_value = '',
      benefit_desc = '',
      sort_order = 0
    } = data;
    
    const sql = `
      INSERT INTO member_benefits (
        level_id, benefit_type, benefit_name, benefit_value, 
        benefit_desc, sort_order
      ) VALUES (?, ?, ?, ?, ?, ?)
    `;
    
    const [result] = await db.query(sql, [
      level_id, benefit_type, benefit_name, benefit_value,
      benefit_desc, sort_order
    ]);
    
    return result.insertId;
  },
  
  // 更新会员权益
  updateBenefit: async (id, data) => {
    const {
      benefit_name,
      benefit_value,
      benefit_desc,
      sort_order,
      status
    } = data;
    
    const sql = `
      UPDATE member_benefits 
      SET benefit_name = ?, benefit_value = ?, benefit_desc = ?,
          sort_order = ?, status = ?, updated_at = NOW()
      WHERE id = ?
    `;
    
    await db.query(sql, [
      benefit_name, benefit_value, benefit_desc,
      sort_order, status, id
    ]);
    
    return true;
  },
  
  // 删除会员权益
  deleteBenefit: async (id) => {
    const sql = 'DELETE FROM member_benefits WHERE id = ?';
    await db.query(sql, [id]);
    return true;
  },
  
  // 根据用户消费金额和积分自动升级会员等级
  autoUpgradeUserLevel: async (userId) => {
    // 获取用户当前信息
    const [userResult] = await db.query(`
      SELECT user_level, points, 
             (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE user_id = ? AND status = 'completed') as total_spent
      FROM users WHERE id = ?
    `, [userId, userId]);
    
    if (!userResult.length) return false;
    
    const user = userResult[0];
    
    // 获取所有等级，按升级条件降序排列
    const [levels] = await db.query(`
      SELECT * FROM member_levels 
      WHERE status = 1 
      ORDER BY min_points DESC, min_amount DESC
    `);
    
    // 找到符合条件的最高等级
    let targetLevel = null;
    for (const level of levels) {
      if (user.points >= level.min_points && user.total_spent >= level.min_amount) {
        targetLevel = level;
        break;
      }
    }
    
    // 如果找到更高等级且与当前等级不同，则升级
    if (targetLevel && targetLevel.level_code > user.user_level) {
      await db.query('UPDATE users SET user_level = ?, updated_at = NOW() WHERE id = ?', [
        targetLevel.level_code, userId
      ]);
      return targetLevel;
    }
    
    return false;
  }
};

module.exports = memberModel;
