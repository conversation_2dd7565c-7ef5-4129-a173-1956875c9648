const db = require('../src/config/database');

const orderModel = {
  findAll: async ({
    page = 1,
    pageSize = 10,
    order_no = '',
    receiver_name = '',
    order_status,
    start_date = '',
    end_date = '',
  }) => {
    const offset = (page - 1) * pageSize;
    let where = 'WHERE 1=1';
    let params = [];
    if (order_no) {
      where += ' AND o.order_no LIKE ?';
      params.push(`%${order_no}%`);
    }
    if (receiver_name) {
      where += ' AND o.receiver_name LIKE ?';
      params.push(`%${receiver_name}%`);
    }
    if (order_status !== undefined && order_status !== '') {
      where += ' AND o.order_status = ?';
      params.push(order_status);
    }
    if (start_date) {
      where += ' AND DATE(o.created_at) >= ?';
      params.push(start_date);
    }
    if (end_date) {
      where += ' AND DATE(o.created_at) <= ?';
      params.push(end_date);
    }
    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM orders o ${where}`;
    const countRows = await db.query(countSql, params);
    const total = countRows[0].total;
    // 获取分页数据
    const baseSql = `SELECT o.* FROM orders o ${where} ORDER BY o.id DESC`;
    const { sql, params: paginationParams } = db.buildPaginationQuery(
      baseSql,
      params,
      page,
      pageSize
    );
    const rows = await db.query(sql, paginationParams);
    return { list: rows, total };
  },
  findById: async id => {
    // 查询订单基本信息
    const sql = `SELECT * FROM orders WHERE id = ?`;
    const rows = await db.query(sql, [id]);
    if (!rows[0]) return null;
    // 查询订单商品
    const items = await db.query(
      'SELECT * FROM order_items WHERE order_id = ?',
      [id]
    );
    rows[0].items = items;
    return rows[0];
  },
  create: async orderData => {
    const {
      order_no,
      user_id = 0,
      total_amount,
      pay_amount,
      freight_amount = 0,
      discount_amount = 0,
      receiver_name,
      receiver_phone,
      receiver_address,
      pay_type = 1,
      pay_status = 0,
      order_status = 0,
      remark = '',
    } = orderData;

    const sql = `INSERT INTO orders (
      order_no, user_id, total_amount, pay_amount, freight_amount, discount_amount,
      receiver_name, receiver_phone, receiver_address, pay_type, pay_status, order_status, remark
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

    const params = [
      order_no,
      user_id,
      total_amount,
      pay_amount,
      freight_amount,
      discount_amount,
      receiver_name,
      receiver_phone,
      receiver_address,
      pay_type,
      pay_status,
      order_status,
      remark,
    ];

    const result = await db.query(sql, params);
    return { id: result.insertId, order_no };
  },
  ship: async (id, { delivery_company, delivery_no }) => {
    const sql = `UPDATE orders SET order_status=2, delivery_company=?, delivery_no=?, delivery_time=NOW(), updated_at=NOW() WHERE id=?`;
    await db.query(sql, [delivery_company, delivery_no, id]);
    return true;
  },
  updateStatus: async (id, order_status) => {
    const sql = `UPDATE orders SET order_status=?, updated_at=NOW() WHERE id=?`;
    await db.query(sql, [order_status, id]);
    return true;
  },
  statusStats: async () => {
    const sql =
      'SELECT order_status, COUNT(*) as count FROM orders GROUP BY order_status';
    return await db.query(sql);
  },
  amountStats: async () => {
    const sql =
      'SELECT order_status, SUM(pay_amount) as total_amount FROM orders GROUP BY order_status';
    return await db.query(sql);
  },
  trendStats: async () => {
    const sql = `SELECT DATE(created_at) as date, COUNT(*) as count, SUM(pay_amount) as total_amount FROM orders WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 6 DAY) GROUP BY DATE(created_at) ORDER BY date`;
    return await db.query(sql);
  },

  // 创建订单（支持折扣）
  createWithDiscount: async (orderData) => {
    const {
      user_id,
      order_no,
      total_amount,
      original_amount,
      discount_amount,
      shipping_fee,
      final_amount,
      receiver_name,
      receiver_phone,
      receiver_address,
      remark = ''
    } = orderData;

    const sql = `
      INSERT INTO orders (
        user_id, order_no, total_amount, original_amount, discount_amount,
        shipping_fee, final_amount, receiver_name, receiver_phone,
        receiver_address, remark, order_status, payment_status, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0, 0, NOW(), NOW())
    `;

    const [result] = await db.query(sql, [
      user_id, order_no, total_amount, original_amount, discount_amount,
      shipping_fee, final_amount, receiver_name, receiver_phone,
      receiver_address, remark
    ]);

    return result.insertId;
  },

  // 创建订单商品（支持折扣）
  createOrderItems: async (orderId, items) => {
    const sql = `
      INSERT INTO order_items (
        order_id, product_id, product_name, product_image, quantity,
        original_price, discount_price, discount_amount, total_price,
        specs, created_at
      ) VALUES ?
    `;

    const values = items.map(item => [
      orderId,
      item.product_id,
      item.product_name,
      item.product_image,
      item.quantity,
      item.original_price,
      item.discount_price,
      item.discount_amount,
      item.total_price,
      item.specs || null,
      new Date()
    ]);

    await db.query(sql, [values]);
    return true;
  },

  // 计算订单金额（支持折扣）
  calculateOrderAmount: (items, shippingFee = 0) => {
    let originalAmount = 0;
    let discountAmount = 0;
    let totalAmount = 0;

    items.forEach(item => {
      const itemOriginal = item.originalPrice * item.quantity;
      const itemDiscount = (item.originalPrice - item.discountPrice) * item.quantity;
      const itemTotal = item.discountPrice * item.quantity;

      originalAmount += itemOriginal;
      discountAmount += itemDiscount;
      totalAmount += itemTotal;
    });

    const finalAmount = totalAmount + shippingFee;

    return {
      originalAmount: Math.round(originalAmount * 100) / 100,
      discountAmount: Math.round(discountAmount * 100) / 100,
      totalAmount: Math.round(totalAmount * 100) / 100,
      shippingFee: Math.round(shippingFee * 100) / 100,
      finalAmount: Math.round(finalAmount * 100) / 100
    };
  },

  // 生成订单号
  generateOrderNo: () => {
    const now = new Date();
    const timestamp = now.getTime();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `ORD${timestamp}${random}`;
  },

  // 获取订单商品列表
  getOrderItems: async (orderId) => {
    const sql = `
      SELECT * FROM order_items
      WHERE order_id = ?
      ORDER BY id ASC
    `;
    const [result] = await db.query(sql, [orderId]);
    return result;
  },

  // 更新支付状态
  updatePaymentStatus: async (id, paymentStatus, paymentMethod = null, transactionId = null) => {
    const sql = `
      UPDATE orders
      SET payment_status = ?, payment_method = ?, transaction_id = ?,
          paid_at = ${paymentStatus === 1 ? 'NOW()' : 'NULL'},
          updated_at = NOW()
      WHERE id = ?
    `;
    await db.query(sql, [paymentStatus, paymentMethod, transactionId, id]);
    return true;
  }
};

module.exports = orderModel;
