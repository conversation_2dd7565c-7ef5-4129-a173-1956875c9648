const db = require('../src/config/database');

const permissionModel = {
  findAll: async () => {
    return await db.query('SELECT * FROM permissions ORDER BY id DESC');
  },
  findById: async id => {
    const rows = await db.query('SELECT * FROM permissions WHERE id = ?', [id]);
    return rows[0] || null;
  },
  create: async data => {
    const { name, code, description } = data;
    const sql =
      'INSERT INTO permissions (name, code, description) VALUES (?, ?, ?)';
    const result = await db.query(sql, [name, code, description]);
    return result.insertId;
  },
  update: async (id, data) => {
    const { name, code, description } = data;
    const sql =
      'UPDATE permissions SET name=?, code=?, description=?, updated_at=NOW() WHERE id=?';
    await db.query(sql, [name, code, description, id]);
    return true;
  },
  delete: async id => {
    await db.query('DELETE FROM permissions WHERE id=?', [id]);
    return true;
  },
};

module.exports = permissionModel;
