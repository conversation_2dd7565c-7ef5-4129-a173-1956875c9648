const db = require('../src/config/database');

const productModel = {
  findAll: async ({
    page = 1,
    pageSize = 10,
    name = '',
    category_id,
    status,
  }) => {
    page = Number(page) || 1;
    pageSize = Number(pageSize) || 10;
    let where = 'WHERE 1=1';
    let params = [];
    if (name) {
      where += ' AND p.name LIKE ?';
      params.push(`%${name}%`);
    }
    if (
      category_id !== undefined &&
      category_id !== null &&
      category_id !== ''
    ) {
      where += ' AND p.category_id = ?';
      params.push(parseInt(category_id) || 0);
    }
    if (status !== undefined && status !== '') {
      where += ' AND p.status = ?';
      params.push(status);
    }
    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM products p ${where}`;
    const countRows = await db.query(countSql, params);
    const total = countRows[0].total;
    // 获取分页数据
    const offset = (page - 1) * pageSize;
    const sql = `SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id ${where} ORDER BY p.id DESC LIMIT ${offset}, ${pageSize}`;
    const rows = await db.query(sql, params);
    return { list: rows, total };
  },
  findById: async id => {
    const sql = `SELECT * FROM products WHERE id = ?`;
    const rows = await db.query(sql, [id]);
    return rows[0] || null;
  },
  create: async data => {
    const {
      name,
      category_id,
      price,
      stock,
      main_image,
      description = '',
      status = 1,
    } = data;

    // 确保必填字段不为空
    if (!name || !category_id || !price) {
      throw new Error('商品名称、分类和价格为必填字段');
    }

    // 确保数据类型正确
    const safeData = {
      name: String(name),
      category_id: parseInt(category_id) || 0,
      price: parseFloat(price) || 0,
      stock: parseInt(stock) || 0,
      main_image: main_image || '',
      description: description || '',
      status: parseInt(status) || 1,
    };

    const sql = `INSERT INTO products (name, category_id, price, stock, main_image, description, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`;
    const result = await db.query(sql, [
      safeData.name,
      safeData.category_id,
      safeData.price,
      safeData.stock,
      safeData.main_image,
      safeData.description,
      safeData.status,
    ]);
    return result.insertId;
  },
  update: async (id, data) => {
    const {
      name,
      category_id,
      price,
      stock,
      main_image,
      description = '',
      status = 1,
    } = data;

    // 确保数据类型正确
    const safeData = {
      name: String(name),
      category_id: parseInt(category_id) || 0,
      price: parseFloat(price) || 0,
      stock: parseInt(stock) || 0,
      main_image: main_image || '',
      description: description || '',
      status: parseInt(status) || 1,
    };

    const sql = `UPDATE products SET name=?, category_id=?, price=?, stock=?, main_image=?, description=?, status=?, updated_at=NOW() WHERE id=?`;
    await db.query(sql, [
      safeData.name,
      safeData.category_id,
      safeData.price,
      safeData.stock,
      safeData.main_image,
      safeData.description,
      safeData.status,
      id,
    ]);
    return true;
  },
  delete: async id => {
    const sql = `DELETE FROM products WHERE id=?`;
    await db.query(sql, [id]);
    return true;
  },
  bulkCreate: async rows => {
    if (!Array.isArray(rows) || !rows.length) return 0;
    const values = [];
    const fields = [
      'name',
      'category_id',
      'price',
      'stock',
      'main_image',
      'description',
      'status',
    ];
    for (const row of rows) {
      values.push([
        row.name,
        row.category_id,
        row.price,
        row.stock,
        row.main_image,
        row.description || '',
        row.status !== undefined ? row.status : 1,
      ]);
    }
    const sql = `INSERT INTO products (name, category_id, price, stock, main_image, description, status, created_at, updated_at) VALUES ?`;
    const flatValues = values.map(v => [...v, new Date(), new Date()]);
    await db.query(sql, [flatValues]);
    return values.length;
  },

  // 更新商品库存
  updateStock: async (productId, quantity, operation = 'decrease') => {
    // 获取当前库存
    const product = await productModel.findById(productId);
    if (!product) {
      throw new Error('商品不存在');
    }

    const currentStock = product.stock || 0;
    let newStock;

    if (operation === 'decrease') {
      newStock = currentStock - quantity;
      if (newStock < 0) {
        throw new Error('库存不足');
      }
    } else if (operation === 'increase') {
      newStock = currentStock + quantity;
    } else {
      throw new Error('无效的库存操作类型');
    }

    // 更新库存
    const sql = `
      UPDATE products
      SET stock = ?, updated_at = NOW()
      WHERE id = ?
    `;
    await db.query(sql, [newStock, productId]);

    // 记录库存变动
    await productModel.recordStockChange(
      productId,
      operation === 'decrease' ? 2 : 1, // 1:入库 2:出库
      quantity,
      currentStock,
      newStock,
      operation === 'decrease' ? 2 : 1, // 1:采购入库 2:订单出库
      null, // source_id
      `${operation === 'decrease' ? '订单出库' : '库存增加'}: ${quantity}`,
      null // operator_id
    );

    return newStock;
  },

  // 记录库存变动
  recordStockChange: async (productId, type, quantity, stockBefore, stockAfter, source, sourceId, remark, operatorId) => {
    const sql = `
      INSERT INTO stock_records (
        product_id, type, quantity, stock_before, stock_after,
        source, source_id, remark, operator_id, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `;
    await db.query(sql, [
      productId, type, quantity, stockBefore, stockAfter,
      source, sourceId, remark, operatorId
    ]);
  },

  // 批量更新库存
  batchUpdateStock: async (updates) => {
    for (const update of updates) {
      await productModel.updateStock(update.productId, update.quantity, update.operation);
    }
  }
};

module.exports = productModel;
