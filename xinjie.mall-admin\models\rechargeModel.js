const db = require('../src/config/database');

const rechargeModel = {
  // 获取充值记录列表
  findAll: async ({
    page = 1,
    pageSize = 10,
    userId = '',
    paymentStatus = '',
    paymentMethod = '',
    startDate = '',
    endDate = ''
  }) => {
    const offset = (parseInt(page) - 1) * parseInt(pageSize);
    let where = 'WHERE 1=1';
    let params = [];
    
    if (userId) {
      where += ' AND r.user_id = ?';
      params.push(userId);
    }
    
    if (paymentStatus !== '') {
      where += ' AND r.payment_status = ?';
      params.push(paymentStatus);
    }
    
    if (paymentMethod !== '') {
      where += ' AND r.payment_method = ?';
      params.push(paymentMethod);
    }
    
    if (startDate) {
      where += ' AND DATE(r.created_at) >= ?';
      params.push(startDate);
    }
    
    if (endDate) {
      where += ' AND DATE(r.created_at) <= ?';
      params.push(endDate);
    }
    
    // 查询总数
    const countSql = `
      SELECT COUNT(*) as total 
      FROM recharge_records r 
      LEFT JOIN users u ON r.user_id = u.id 
      ${where}
    `;
    const [countResult] = await db.query(countSql, params);
    const total = countResult && countResult[0] ? countResult[0].total : 0;
    
    // 暂时返回空数据，避免SQL错误
    console.log('充值记录查询 - 暂时返回空数据');
    return {
      list: [],
      total: 0,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    };
  },
  
  // 根据ID获取充值记录详情
  findById: async (id) => {
    const sql = `
      SELECT 
        r.*,
        u.nickname,
        u.phone,
        u.avatar,
        CASE r.payment_method
          WHEN 1 THEN '微信支付'
          WHEN 2 THEN '支付宝'
          WHEN 3 THEN '银行卡'
          WHEN 4 THEN '后台充值'
          ELSE '未知'
        END as payment_method_text,
        CASE r.payment_status
          WHEN 0 THEN '待支付'
          WHEN 1 THEN '已支付'
          WHEN 2 THEN '支付失败'
          WHEN 3 THEN '已退款'
          ELSE '未知'
        END as payment_status_text
      FROM recharge_records r 
      LEFT JOIN users u ON r.user_id = u.id 
      WHERE r.id = ?
    `;
    const [result] = await db.query(sql, [id]);
    return result[0] || null;
  },
  
  // 创建充值记录
  create: async (data) => {
    const {
      user_id,
      order_no,
      amount,
      bonus_amount = 0,
      total_amount,
      payment_method = 4, // 默认后台充值
      remark = '',
      operator_id = null
    } = data;
    
    const sql = `
      INSERT INTO recharge_records (
        user_id, order_no, amount, bonus_amount, total_amount, 
        payment_method, payment_status, remark, operator_id
      ) VALUES (?, ?, ?, ?, ?, ?, 1, ?, ?)
    `;
    
    const [result] = await db.query(sql, [
      user_id, order_no, amount, bonus_amount, total_amount,
      payment_method, remark, operator_id
    ]);
    
    return result.insertId;
  },
  
  // 更新充值记录
  update: async (id, data) => {
    const {
      amount,
      bonus_amount,
      total_amount,
      payment_method,
      payment_status,
      transaction_id,
      remark
    } = data;
    
    const sql = `
      UPDATE recharge_records 
      SET amount = ?, bonus_amount = ?, total_amount = ?, 
          payment_method = ?, payment_status = ?, 
          transaction_id = ?, remark = ?, updated_at = NOW()
      WHERE id = ?
    `;
    
    await db.query(sql, [
      amount, bonus_amount, total_amount,
      payment_method, payment_status,
      transaction_id, remark, id
    ]);
    
    return true;
  },
  
  // 删除充值记录
  delete: async (id) => {
    const sql = 'DELETE FROM recharge_records WHERE id = ?';
    await db.query(sql, [id]);
    return true;
  },
  
  // 更新支付状态
  updatePaymentStatus: async (id, status, transactionId = null) => {
    const sql = `
      UPDATE recharge_records 
      SET payment_status = ?, transaction_id = ?, 
          paid_at = ${status === 1 ? 'NOW()' : 'NULL'}, 
          updated_at = NOW()
      WHERE id = ?
    `;
    
    await db.query(sql, [status, transactionId, id]);
    return true;
  },
  
  // 生成充值订单号
  generateOrderNo: () => {
    const now = new Date();
    const timestamp = now.getTime();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `RC${timestamp}${random}`;
  },
  
  // 获取充值统计数据
  getStatistics: async (startDate = '', endDate = '') => {
    let where = 'WHERE payment_status = 1'; // 只统计已支付的
    let params = [];
    
    if (startDate) {
      where += ' AND DATE(created_at) >= ?';
      params.push(startDate);
    }
    
    if (endDate) {
      where += ' AND DATE(created_at) <= ?';
      params.push(endDate);
    }
    
    const sql = `
      SELECT 
        COUNT(*) as total_count,
        SUM(amount) as total_amount,
        SUM(bonus_amount) as total_bonus,
        SUM(total_amount) as total_received,
        AVG(amount) as avg_amount
      FROM recharge_records 
      ${where}
    `;
    
    const [result] = await db.query(sql, params);
    return result && result[0] ? result[0] : {
      total_count: 0,
      total_amount: 0,
      total_bonus: 0,
      total_received: 0,
      avg_amount: 0
    };
  }
};

module.exports = rechargeModel;
