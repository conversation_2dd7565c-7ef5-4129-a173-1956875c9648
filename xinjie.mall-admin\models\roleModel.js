const db = require('../src/config/database');

const roleModel = {
  findAll: async () => {
    return await db.query('SELECT * FROM roles ORDER BY id DESC');
  },
  findById: async id => {
    const rows = await db.query('SELECT * FROM roles WHERE id = ?', [id]);
    return rows[0] || null;
  },
  create: async data => {
    const { name, description } = data;
    const sql = 'INSERT INTO roles (name, description) VALUES (?, ?)';
    const result = await db.query(sql, [name, description]);
    return result.insertId;
  },
  update: async (id, data) => {
    const { name, description } = data;
    const sql =
      'UPDATE roles SET name=?, description=?, updated_at=NOW() WHERE id=?';
    await db.query(sql, [name, description, id]);
    return true;
  },
  delete: async id => {
    await db.query('DELETE FROM roles WHERE id=?', [id]);
    return true;
  },
  getPermissions: async roleId => {
    const sql = `SELECT p.* FROM permissions p INNER JOIN role_permissions rp ON p.id = rp.permission_id WHERE rp.role_id = ?`;
    return await db.query(sql, [roleId]);
  },
  setPermissions: async (roleId, permissionIds) => {
    // 先删除原有
    await db.query('DELETE FROM role_permissions WHERE role_id=?', [roleId]);
    if (Array.isArray(permissionIds) && permissionIds.length > 0) {
      const values = permissionIds.map(pid => [roleId, pid]);
      await db.query(
        'INSERT INTO role_permissions (role_id, permission_id) VALUES ?',
        [values]
      );
    }
    return true;
  },
};

module.exports = roleModel;
