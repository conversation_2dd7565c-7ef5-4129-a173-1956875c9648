const db = require('../src/config/database');

const settingsModel = {
  getBasic: async () => {
    const rows = await db.query(
      "SELECT `key`, `value` FROM settings WHERE `key` LIKE 'basic_%'"
    );
    const result = {};
    rows.forEach(r => {
      result[r.key.replace('basic_', '')] = r.value;
    });
    return result;
  },
  updateBasic: async data => {
    for (const k in data) {
      await db.query(
        'INSERT INTO settings (`key`, `value`) VALUES (?, ?) ON DUPLICATE KEY UPDATE `value`=?',
        ['basic_' + k, data[k], data[k]]
      );
    }
    return true;
  },
  getPayment: async () => {
    const rows = await db.query(
      "SELECT `key`, `value` FROM settings WHERE `key` LIKE 'payment_%'"
    );
    const result = {};
    rows.forEach(r => {
      result[r.key.replace('payment_', '')] = r.value;
    });
    return result;
  },
  updatePayment: async data => {
    for (const k in data) {
      await db.query(
        'INSERT INTO settings (`key`, `value`) VALUES (?, ?) ON DUPLICATE KEY UPDATE `value`=?',
        ['payment_' + k, data[k], data[k]]
      );
    }
    return true;
  },
  getShipping: async () => {
    const rows = await db.query(
      "SELECT `key`, `value` FROM settings WHERE `key` LIKE 'shipping_%'"
    );
    const result = {};
    rows.forEach(r => {
      result[r.key.replace('shipping_', '')] = r.value;
    });
    return result;
  },
  updateShipping: async data => {
    for (const k in data) {
      await db.query(
        'INSERT INTO settings (`key`, `value`) VALUES (?, ?) ON DUPLICATE KEY UPDATE `value`=?',
        ['shipping_' + k, data[k], data[k]]
      );
    }
    return true;
  },
  getSms: async () => {
    const rows = await db.query(
      "SELECT `key`, `value` FROM settings WHERE `key` LIKE 'sms_%'"
    );
    const result = {};
    rows.forEach(r => {
      result[r.key.replace('sms_', '')] = r.value;
    });
    return result;
  },
  updateSms: async data => {
    for (const k in data) {
      await db.query(
        'INSERT INTO settings (`key`, `value`) VALUES (?, ?) ON DUPLICATE KEY UPDATE `value`=?',
        ['sms_' + k, data[k], data[k]]
      );
    }
    return true;
  },
  getEmail: async () => {
    const rows = await db.query(
      "SELECT `key`, `value` FROM settings WHERE `key` LIKE 'email_%'"
    );
    const result = {};
    rows.forEach(r => {
      result[r.key.replace('email_', '')] = r.value;
    });
    return result;
  },
  updateEmail: async data => {
    for (const k in data) {
      await db.query(
        'INSERT INTO settings (`key`, `value`) VALUES (?, ?) ON DUPLICATE KEY UPDATE `value`=?',
        ['email_' + k, data[k], data[k]]
      );
    }
    return true;
  },
};

module.exports = settingsModel;
