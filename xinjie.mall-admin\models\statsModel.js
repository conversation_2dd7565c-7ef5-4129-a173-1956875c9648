const db = require('../src/config/database');

const statsModel = {
  getSalesStats: async () => {
    // 日销售额（近7天）
    const daily = await db.query(
      `SELECT DATE(created_at) as date, SUM(pay_amount) as total
       FROM orders WHERE order_status IN (2,3) AND created_at >= DATE_SUB(CURDATE(), INTERVAL 6 DAY)
       GROUP BY DATE(created_at) ORDER BY date ASC`
    );
    // 月销售额（近12个月）
    const monthly = await db.query(
      `SELECT DATE_FORMAT(created_at, '%Y-%m') as month, SUM(pay_amount) as total
       FROM orders WHERE order_status IN (2,3) AND created_at >= DATE_SUB(CURDATE(), INTERVAL 11 MONTH)
       GROUP BY month ORDER BY month ASC`
    );
    return { daily, monthly };
  },
  getProductStats: async () => {
    // 热销商品排行（销量前10）
    const hot = await db.query(
      `SELECT p.id, p.name, SUM(oi.quantity) as sales
       FROM order_items oi
       JOIN products p ON oi.product_id = p.id
       GROUP BY oi.product_id
       ORDER BY sales DESC
       LIMIT 10`
    );
    return { hot };
  },
  getOrderStats: async () => {
    // 订单总量、各状态订单数量
    const total = await db.query('SELECT COUNT(*) as total FROM orders');
    const status = await db.query(
      'SELECT order_status, COUNT(*) as count FROM orders GROUP BY order_status'
    );
    // 转化率（已完成/总）
    const finished = await db.query(
      'SELECT COUNT(*) as finished FROM orders WHERE order_status=3'
    );
    const conversion = total[0].total
      ? finished[0].finished / total[0].total
      : 0;
    return { total: total[0].total, status, conversion };
  },
};

module.exports = statsModel;
