const db = require('../src/config/database');

// 内置日期处理工具
const formatDateForMySQL = (dateInput) => {
  if (!dateInput) return null;

  try {
    let date;
    if (typeof dateInput === 'string') {
      if (dateInput.includes('T') || dateInput.includes('Z')) {
        date = new Date(dateInput);
      } else {
        date = new Date(dateInput + 'T00:00:00.000Z');
      }
    } else if (dateInput instanceof Date) {
      date = dateInput;
    } else {
      return null;
    }

    if (isNaN(date.getTime())) {
      console.warn('无效的日期格式:', dateInput);
      return null;
    }

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error('日期格式化错误:', error, '输入:', dateInput);
    return null;
  }
};

const userModel = {
  findAll: async ({
    page = 1,
    pageSize = 10,
    nickname = '',
    phone = '',
    status,
  }) => {
    const offset = (page - 1) * pageSize;
    let where = 'WHERE 1=1';
    let params = [];
    if (nickname) {
      where += ' AND nickname LIKE ?';
      params.push(`%${nickname}%`);
    }
    if (phone) {
      where += ' AND phone LIKE ?';
      params.push(`%${phone}%`);
    }
    if (status !== undefined && status !== '') {
      where += ' AND status = ?';
      params.push(status);
    }
    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM users ${where}`;
    const countRows = await db.query(countSql, params);
    const total = countRows[0].total;
    // 获取分页数据
    const baseSql = `SELECT * FROM users ${where} ORDER BY id DESC`;
    const { sql, params: paginationParams } = db.buildPaginationQuery(
      baseSql,
      params,
      page,
      pageSize
    );
    const rows = await db.query(sql, paginationParams);
    return { list: rows, total };
  },
  findById: async id => {
    const sql = `SELECT * FROM users WHERE id = ?`;
    const rows = await db.query(sql, [id]);
    return rows[0] || null;
  },
  create: async data => {
    // users表只有这些字段：openid, unionid, nickname, avatar, phone, gender, birthday, status
    const { openid, unionid, nickname, avatar, phone, gender = 0, birthday, status = 1 } = data;

    // 将布尔值转换为数字
    const statusValue = typeof status === 'boolean' ? (status ? 1 : 0) : status;
    const genderValue = typeof gender === 'boolean' ? (gender ? 1 : 0) : (gender || 0);

    // 处理日期格式 - 使用内置工具
    const formattedBirthday = formatDateForMySQL(birthday);

    // 处理undefined值，转换为null
    const cleanData = {
      openid: openid || null,
      unionid: unionid || null,
      nickname: nickname || null,
      avatar: avatar || null,
      phone: phone || null,
      gender: genderValue,
      birthday: formattedBirthday,
      status: statusValue
    };

    console.log('创建用户数据:', cleanData);

    const sql = `INSERT INTO users (openid, unionid, nickname, avatar, phone, gender, birthday, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`;
    const result = await db.query(sql, [
      cleanData.openid,
      cleanData.unionid,
      cleanData.nickname,
      cleanData.avatar,
      cleanData.phone,
      cleanData.gender,
      cleanData.birthday,
      cleanData.status,
    ]);
    return result.insertId;
  },
  updateStatus: async (id, status) => {
    // 将布尔值转换为数字
    const statusValue = typeof status === 'boolean' ? (status ? 1 : 0) : status;
    const sql = `UPDATE users SET status=?, updated_at=NOW() WHERE id=?`;
    await db.query(sql, [statusValue, id]);
    return true;
  },
  setRole: async (adminId, roleId) => {
    const sql = 'UPDATE admin_users SET role_id=? WHERE id=?';
    await db.query(sql, [roleId, adminId]);
    return true;
  },
  update: async (id, data) => {
    // users表字段：openid, unionid, nickname, avatar, phone, gender, birthday, status
    const { openid, unionid, nickname, avatar, phone, gender, birthday, status } = data;

    // 将布尔值转换为数字
    const statusValue = typeof status === 'boolean' ? (status ? 1 : 0) : status;
    const genderValue = typeof gender === 'boolean' ? (gender ? 1 : 0) : (gender || 0);

    // 处理日期格式 - 使用内置工具
    const formattedBirthday = formatDateForMySQL(birthday);

    // 处理undefined值，转换为null
    const cleanData = {
      openid: openid || null,
      unionid: unionid || null,
      nickname: nickname || null,
      avatar: avatar || null,
      phone: phone || null,
      gender: genderValue,
      birthday: formattedBirthday,
      status: statusValue
    };

    console.log('更新用户数据:', cleanData);

    const sql = `UPDATE users SET openid=?, unionid=?, nickname=?, avatar=?, phone=?, gender=?, birthday=?, status=?, updated_at=NOW() WHERE id=?`;
    await db.query(sql, [
      cleanData.openid,
      cleanData.unionid,
      cleanData.nickname,
      cleanData.avatar,
      cleanData.phone,
      cleanData.gender,
      cleanData.birthday,
      cleanData.status,
      id
    ]);
    return true;
  },

  // 新增删除功能
  delete: async (id) => {
    const sql = `DELETE FROM users WHERE id = ?`;
    const result = await db.query(sql, [id]);
    return result.affectedRows > 0;
  },

  // 批量删除功能
  batchDelete: async (ids) => {
    if (!ids || ids.length === 0) {
      return { affectedRows: 0 };
    }

    const placeholders = ids.map(() => '?').join(',');
    const sql = `DELETE FROM users WHERE id IN (${placeholders})`;
    const result = await db.query(sql, ids);
    return result;
  },
};

module.exports = userModel;
