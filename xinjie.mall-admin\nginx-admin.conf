# 心洁茶叶商城后台管理系统 Nginx配置

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name admin.xinjie-tea.com;
    return 301 https://$server_name$request_uri;
}

# HTTPS主服务
server {
    listen 443 ssl http2;
    server_name admin.xinjie-tea.com;

    # SSL证书配置
    ssl_certificate /etc/ssl/xinjie-tea/admin-cert.pem;
    ssl_certificate_key /etc/ssl/xinjie-tea/admin-key.pem;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options SAMEORIGIN always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # 日志配置
    access_log /var/log/nginx/admin-xinjie-tea-access.log;
    error_log /var/log/nginx/admin-xinjie-tea-error.log;

    # 根目录配置
    root /var/www/xinjie-tea/xinjie.mall-admin/dist;
    index index.html;

    # 文件上传大小限制
    client_max_body_size 10M;

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }

    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:8081;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # 上传文件服务
    location /uploads/ {
        alias /var/www/xinjie-tea/xinjie.mall-admin/public/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }

    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:8081/health;
        access_log off;
    }

    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 安全配置 - 隐藏敏感文件
    location ~ /\. {
        deny all;
    }

    location ~ \.(env|log|sql|md)$ {
        deny all;
    }

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
}
