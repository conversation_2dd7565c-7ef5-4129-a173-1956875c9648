{"name": "xinjie-mall-admin", "version": "1.0.0", "description": "心洁茗茶商城后台管理系统", "main": "app.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "nodemon app.js", "client": "webpack serve --mode development --open", "build": "webpack --mode production", "build:dev": "webpack --mode development", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/", "format:check": "prettier --check src/", "test": "jest", "test:watch": "jest --watch", "security:audit": "npm audit", "security:fix": "npm audit fix", "clean": "rm -rf dist/* node_modules/.cache"}, "keywords": ["mall", "admin", "express", "react", "antd"], "author": "心洁茶叶", "license": "MIT", "dependencies": {"@reduxjs/toolkit": "^1.9.5", "antd": "^5.26.4", "axios": "^1.6.7", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "connect-mongo": "^5.0.0", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.3.1", "ejs": "^3.1.9", "express": "^4.18.2", "express-fileupload": "^1.5.2", "express-session": "^1.18.1", "fs-extra": "^11.3.0", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "mongoose": "^7.5.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.1", "nodemailer": "^6.9.4", "pinyin": "^4.0.0", "qiniu": "^7.8.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.1.2", "react-router-dom": "^6.22.3", "recharts": "^2.5.0", "twilio": "^4.10.0", "xlsx": "^0.18.5", "zustand": "^4.4.1"}, "devDependencies": {"@babel/core": "^7.22.10", "@babel/preset-env": "^7.22.10", "@babel/preset-react": "^7.22.5", "babel-loader": "^9.1.3", "clean-webpack-plugin": "^4.0.0", "concurrently": "^9.2.0", "css-loader": "^6.8.1", "eslint": "^8.48.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.3", "jest": "^29.6.4", "nodemon": "^3.0.1", "sass": "^1.66.1", "sass-loader": "^13.3.2", "style-loader": "^3.3.3", "supertest": "^6.3.3", "url-loader": "^4.1.1", "webpack": "^5.88.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}}