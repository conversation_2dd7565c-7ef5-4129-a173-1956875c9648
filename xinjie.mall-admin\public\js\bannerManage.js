function loadBannerList(page = 1) {
  fetch('/api/banners/list?page=' + page, { credentials: 'same-origin' })
    .then(res => res.json())
    .then(data => {
      if (data.code === 0 && data.data && Array.isArray(data.data.list)) {
        const list = data.data.list;
        const tbody = document.getElementById('bannerTableBody');
        tbody.innerHTML = list
          .map(
            b =>
              `<tr><td>${b.id}</td><td><img src="${b.image_url}" style="height:40px;max-width:80px;"></td><td>${b.title || ''}</td><td>${b.status == 1 ? '启用' : '禁用'}</td><td><button>编辑</button> <button>删除</button></td></tr>`
          )
          .join('');
        // 分页
        const total = data.data.total || 0;
        document.getElementById('bannerPagination').innerHTML = total
          ? `共${total}条`
          : '';
      }
    });
}
loadBannerList();
