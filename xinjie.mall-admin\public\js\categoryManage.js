function loadCategoryList(page = 1) {
  fetch('/api/categories?page=' + page, { credentials: 'same-origin' })
    .then(res => res.json())
    .then(data => {
      if (data.code === 0 && data.data && Array.isArray(data.data.list)) {
        const list = data.data.list;
        const tbody = document.getElementById('categoryTableBody');
        tbody.innerHTML = list
          .map(
            c =>
              `<tr><td>${c.id}</td><td>${c.name || ''}</td><td>${(c.image || c.image_url) ? `<img src='${c.image || c.image_url}' style='height:32px;'>` : ''}</td><td>${c.sort_order || ''}</td><td>${c.status == 1 ? '启用' : '禁用'}</td><td><button>编辑</button> <button>删除</button></td></tr>`
          )
          .join('');
        const total = data.data.total || 0;
        document.getElementById('categoryPagination').innerHTML = total
          ? `共${total}条`
          : '';
      }
    });
}
loadCategoryList();
