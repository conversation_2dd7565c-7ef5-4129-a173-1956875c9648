function logout() {
  fetch('/api/auth/logout', {
    method: 'POST',
    credentials: 'same-origin',
  }).then(() => (window.location.href = '/login'));
}

// 切换子菜单展开/收起
function toggleMenuGroup(element) {
  const menuGroup = element.parentElement;
  menuGroup.classList.toggle('expanded');
}

// 动态菜单高亮与内容区切换
const menuLinks = document.querySelectorAll('.menu a[data-section]');
const contentArea = document.getElementById('contentArea');
const sectionNames = {
  home: '后台首页',
  banners: '轮播图管理',
  products: '商品管理',
  discounts: '折扣管理',
  orders: '订单管理',
  users: '用户管理',
  categories: '分类管理',
  settings: '系统设置',
};
const sectionRoutes = {
  home: null,
  banners: '/manage/banners',
  products: '/manage/products',
  discounts: '/manage/discounts',
  orders: '/manage/orders',
  users: '/manage/users',
  categories: '/manage/categories',
  settings: '/manage/settings',
};
menuLinks.forEach(link => {
  link.onclick = function (e) {
    e.preventDefault();
    menuLinks.forEach(l => l.classList.remove('active'));
    this.classList.add('active');
    const section = this.getAttribute('data-section');
    renderSection(section);
  };
});
function getAuthHeaders() {
  const token = localStorage.getItem('token');
  return token ? { Authorization: 'Bearer ' + token } : {};
}
function renderSection(section) {
  document.getElementById('breadcrumbText').innerText =
    sectionNames[section] || '后台首页';
  if (section === 'home') {
    contentArea.innerHTML =
      '<div class="welcome">欢迎进入心洁茶叶后台管理系统</div><div class="desc">请通过左侧菜单选择管理功能</div>';
  } else if (sectionRoutes[section]) {
    fetch(sectionRoutes[section], {
      credentials: 'same-origin',
      headers: getAuthHeaders(),
    })
      .then(res => res.text())
      .then(html => {
        if (html.includes('<form') && html.includes('loginForm')) {
          window.location.href = '/login';
        } else {
          contentArea.innerHTML = html;
          // 自动执行片段内的<script>
          const scripts = contentArea.querySelectorAll('script');
          scripts.forEach(oldScript => {
            const newScript = document.createElement('script');
            if (oldScript.src) {
              newScript.src = oldScript.src;
            } else {
              newScript.textContent = oldScript.textContent;
            }
            document.body.appendChild(newScript);
            document.body.removeChild(newScript);
          });
        }
      })
      .catch(() => {
        contentArea.innerHTML =
          '<div style="color:#d32f2f;">加载失败，请重试或检查登录状态。</div>';
      });
  }
}
// 菜单收缩功能
document.getElementById('collapseBtn').onclick = function () {
  document.getElementById('sidebar').classList.toggle('collapsed');
};
// 首页按钮点击时校验是否已登录，未登录跳转到登录页
document.getElementById('homeLink').onclick = function (e) {
  e.preventDefault();
  fetch('/api/auth/info', { credentials: 'same-origin' })
    .then(res => res.json())
    .then(data => {
      if (data.success) {
        window.location.href = '/';
      } else {
        window.location.href = '/login';
      }
    })
    .catch(() => (window.location.href = '/login'));
};
// 动态显示用户昵称（如有API可用）
fetch('/api/auth/info', { credentials: 'same-origin' })
  .then(res => res.json())
  .then(data => {
    if (data.success && data.data && data.data.username) {
      document.getElementById('topbarUser').innerText =
        '欢迎您，' + data.data.username;
      document.getElementById('avatar').innerText =
        data.data.username[0] || '管';
    }
  });
