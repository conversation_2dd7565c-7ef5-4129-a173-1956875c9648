let currentPage = 1;
const pageSize = 10;
let currentEditingId = null;

// Initialize after page load
document.addEventListener('DOMContentLoaded', function() {
  console.log('页面加载完成，开始初始化折扣管理...');
  loadDiscountList();
});

// Load discount list
function loadDiscountList(page = 1) {
  currentPage = page;
  
  const name = document.getElementById('searchName').value;
  const type = document.getElementById('searchType').value;
  const status = document.getElementById('searchStatus').value;
  
  const params = new URLSearchParams({
    page: page,
    pageSize: pageSize
  });
  
  if (name) params.append('name', name);
  if (type) params.append('type', type);
  if (status !== '') params.append('status', status);

  console.log(`Loading discount list page ${page}...`);

  fetch(`/api/admin/discount/list?${params.toString()}`, {
    credentials: 'same-origin',
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    }
  })
  .then(res => {
    console.log('API response status:', res.status);
    if (!res.ok) {
      throw new Error(`HTTP error: ${res.status} ${res.statusText}`);
    }
    return res.json();
  })
  .then(data => {
    console.log('Discount list data:', data);
    if (data.success) {
      console.log('折扣列表数据:', data.data);
      if (data.data && data.data.list) {
        renderDiscountTable(data.data.list);
        renderPagination(data.data);
      } else {
        console.warn('折扣列表数据格式异常:', data.data);
        renderDiscountTable([]);
        renderPagination({ total: 0, page: 1, pageSize: 10, totalPages: 0 });
      }
    } else {
      console.error('Failed to get discount list:', data.message);
      showMessage('获取折扣列表失败: ' + (data.message || '未知错误'), 'error');
      renderDiscountTable([]);
    }
  })
  .catch(error => {
    console.error('Request failed:', error);
    showMessage('Network request failed, please check network connection', 'error');
  });
}

// Render discount table
function renderDiscountTable(discounts) {
  const tbody = document.getElementById('discountTableBody');

  console.log('渲染折扣表格，数据:', discounts);

  if (!discounts || discounts.length === 0) {
    tbody.innerHTML = '<tr><td colspan="9" style="text-align: center; padding: 40px; color: #999;">暂无折扣数据</td></tr>';
    return;
  }

  tbody.innerHTML = discounts.map(discount => {
    console.log('渲染折扣项:', discount);

    const typeNames = {
      1: '百分比折扣',
      2: '固定金额折扣',
      3: '满减折扣'
    };

    const applicableToNames = {
      1: '全部商品',
      2: '指定商品',
      3: '指定分类'
    };

    const statusInfo = getDiscountStatusInfo(discount);
    const usageInfo = getUsageInfo(discount);
    
    return `
      <tr style="border-bottom: 1px solid #f0f0f0;">
        <td style="padding: 12px 8px;">${discount.id}</td>
        <td style="padding: 12px 8px; font-weight: 500;">${discount.name}</td>
        <td style="padding: 12px 8px;">${typeNames[discount.type] || 'Unknown'}</td>
        <td style="padding: 12px 8px;">${formatDiscountValue(discount)}</td>
        <td style="padding: 12px 8px;">${applicableToNames[discount.applicable_to] || 'Unknown'}</td>
        <td style="padding: 12px 8px; font-size: 12px;">
          ${formatDateTime(discount.start_time)}<br>
          to<br>
          ${formatDateTime(discount.end_time)}
        </td>
        <td style="padding: 12px 8px;">
          <span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>
        </td>
        <td style="padding: 12px 8px; font-size: 12px;">${usageInfo}</td>
        <td style="padding: 12px 8px;">
          <button onclick="editDiscount(${discount.id})"
                  style="margin-right: 8px; padding: 4px 8px; background: #2d8cf0; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
            编辑
          </button>
          <button onclick="toggleDiscountStatus(${discount.id}, ${discount.status})"
                  style="margin-right: 8px; padding: 4px 8px; background: ${discount.status ? '#ff4d4f' : '#52c41a'}; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
            ${discount.status ? '禁用' : '启用'}
          </button>
          <button onclick="deleteDiscount(${discount.id})"
                  style="padding: 4px 8px; background: #ff4d4f; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
            删除
          </button>
        </td>
      </tr>
    `;
  }).join('');
}

// Get discount status information
function getDiscountStatusInfo(discount) {
  const now = new Date();
  const startTime = new Date(discount.start_time);
  const endTime = new Date(discount.end_time);

  if (endTime < now) {
    return { class: 'status-expired', text: '已过期' };
  } else if (startTime > now) {
    return { class: 'status-pending', text: '未开始' };
  } else if (discount.status === 0) {
    return { class: 'status-disabled', text: '已禁用' };
  } else if (discount.usage_limit && discount.used_count >= discount.usage_limit) {
    return { class: 'status-expired', text: '已用完' };
  } else {
    return { class: 'status-active', text: '进行中' };
  }
}

// Get usage information
function getUsageInfo(discount) {
  if (discount.usage_limit) {
    return `${discount.used_count || 0}/${discount.usage_limit}`;
  } else {
    return `${discount.used_count || 0}/无限制`;
  }
}

// Format discount value display
function formatDiscountValue(discount) {
  switch (discount.type) {
    case 1:
      return `${discount.value}%`;
    case 2:
      return `¥${discount.value}`;
    case 3:
      return `满¥${discount.min_amount || 0}减¥${discount.value}`;
    default:
      return discount.value;
  }
}

// Format date time
function formatDateTime(dateTimeStr) {
  if (!dateTimeStr) return '';
  const date = new Date(dateTimeStr);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// Render pagination
function renderPagination(data) {
  const paginationEl = document.getElementById('discountPagination');
  const { page, totalPages, total } = data;

  if (totalPages <= 1) {
    paginationEl.innerHTML = `<div style="color: #666; font-size: 14px;">Total ${total} records</div>`;
    return;
  }

  let paginationHTML = `<div style="display: flex; align-items: center; gap: 8px; justify-content: flex-end;">`;
  paginationHTML += `<span style="color: #666; font-size: 14px; margin-right: 16px;">Total ${total} records, Page ${page}/${totalPages}</span>`;

  // Previous page button
  if (page > 1) {
    paginationHTML += `<button onclick="loadDiscountList(${page - 1})" style="padding: 4px 8px; background: #2d8cf0; color: white; border: none; border-radius: 4px; cursor: pointer;">Previous</button>`;
  }

  // Page number buttons
  const startPage = Math.max(1, page - 2);
  const endPage = Math.min(totalPages, page + 2);

  for (let i = startPage; i <= endPage; i++) {
    const isActive = i === page;
    paginationHTML += `<button onclick="loadDiscountList(${i})" style="padding: 4px 8px; background: ${isActive ? '#2d8cf0' : '#f7f7f7'}; color: ${isActive ? 'white' : '#666'}; border: 1px solid ${isActive ? '#2d8cf0' : '#ddd'}; border-radius: 4px; cursor: pointer;">${i}</button>`;
  }

  // Next page button
  if (page < totalPages) {
    paginationHTML += `<button onclick="loadDiscountList(${page + 1})" style="padding: 4px 8px; background: #2d8cf0; color: white; border: none; border-radius: 4px; cursor: pointer;">Next</button>`;
  }

  paginationHTML += `</div>`;
  paginationEl.innerHTML = paginationHTML;
}

// Search discounts
function searchDiscounts() {
  loadDiscountList(1);
}

// 重置搜索
function resetSearch() {
  document.getElementById('searchName').value = '';
  document.getElementById('searchType').value = '';
  document.getElementById('searchStatus').value = '';
  loadDiscountList(1);
}

// 显示添加折扣模态框
function showAddDiscountModal() {
  currentEditingId = null;
  document.getElementById('discountModalTitle').textContent = '添加折扣';
  document.getElementById('discountForm').reset();
  document.getElementById('discountId').value = '';
  
  // 设置默认值
  document.getElementById('discountStatus').checked = true;
  document.getElementById('priority').value = '0';
  
  // 重置显示状态
  onDiscountTypeChange();
  onApplicableToChange();
  
  document.getElementById('discountModal').style.display = 'flex';
}

// 编辑折扣
function editDiscount(id) {
  currentEditingId = id;
  document.getElementById('discountModalTitle').textContent = '编辑折扣';
  
  // 获取折扣详情
  fetch(`/api/admin/discount/detail/${id}`, {
    credentials: 'same-origin',
    headers: {
      'Accept': 'application/json'
    }
  })
  .then(res => res.json())
  .then(data => {
    if (data.success) {
      fillDiscountForm(data.data);
      document.getElementById('discountModal').style.display = 'flex';
    } else {
      showMessage('获取折扣详情失败: ' + data.message, 'error');
    }
  })
  .catch(error => {
    console.error('获取折扣详情失败:', error);
    showMessage('获取折扣详情失败', 'error');
  });
}

// 填充折扣表单
function fillDiscountForm(discount) {
  document.getElementById('discountId').value = discount.id;
  document.getElementById('discountName').value = discount.name;
  document.getElementById('description').value = discount.description || '';
  document.getElementById('discountType').value = discount.type;
  document.getElementById('discountValue').value = discount.value;
  document.getElementById('minAmount').value = discount.min_amount || '';
  document.getElementById('maxDiscount').value = discount.max_discount || '';
  document.getElementById('priority').value = discount.priority || 0;
  document.getElementById('startTime').value = formatDateTimeForInput(discount.start_time);
  document.getElementById('endTime').value = formatDateTimeForInput(discount.end_time);
  document.getElementById('usageLimit').value = discount.usage_limit || '';
  document.getElementById('userLimit').value = discount.user_limit || '';
  document.getElementById('applicableTo').value = discount.applicable_to;
  document.getElementById('discountStatus').checked = discount.status === 1;
  
  // 触发类型和适用范围变化事件
  onDiscountTypeChange();
  onApplicableToChange();
}

// 格式化日期时间为输入框格式
function formatDateTimeForInput(dateTimeStr) {
  if (!dateTimeStr) return '';
  const date = new Date(dateTimeStr);
  return date.toISOString().slice(0, 16);
}

// 折扣类型变化处理
function onDiscountTypeChange() {
  console.log('onDiscountTypeChange function called');

  const type = document.getElementById('discountType').value;
  console.log('Selected discount type:', type);

  const valueInput = document.getElementById('discountValue');
  const valueHint = document.getElementById('discountValueHint');
  const minAmountGroup = document.getElementById('minAmountGroup');
  const maxDiscountGroup = document.getElementById('maxDiscountGroup');

  // 获取标签元素以便动态修改
  const valueLabel = document.querySelector('label[for="discountValue"]');
  const minAmountLabel = document.querySelector('label[for="minAmount"]');

  console.log('Found elements:', {
    valueInput: !!valueInput,
    valueHint: !!valueHint,
    minAmountGroup: !!minAmountGroup,
    maxDiscountGroup: !!maxDiscountGroup,
    valueLabel: !!valueLabel,
    minAmountLabel: !!minAmountLabel
  });

  switch (type) {
    case '1': // 百分比折扣
      console.log('Processing percentage discount');
      if (valueLabel) valueLabel.innerHTML = '折扣百分比 <span class="required">*</span>';
      if (valueInput) {
        valueInput.placeholder = '请输入折扣百分比（如：10表示9折）';
        valueInput.max = '99.99';
        valueInput.min = '0.01';
        valueInput.step = '0.01';
      }
      if (valueHint) valueHint.textContent = '输入0.01-99.99之间的数值，如输入10表示9折（原价的90%）';
      if (minAmountGroup) minAmountGroup.style.display = 'none';
      if (maxDiscountGroup) maxDiscountGroup.style.display = 'block'; // 百分比折扣可以设置最大折扣金额
      console.log('Percentage discount setup completed');
      break;

    case '2': // 固定金额折扣
      console.log('处理固定金额折扣');
      if (valueLabel) valueLabel.innerHTML = '折扣金额 <span class="required">*</span>';
      if (valueInput) {
        valueInput.placeholder = '请输入折扣金额';
        valueInput.removeAttribute('max');
        valueInput.min = '0.01';
        valueInput.step = '0.01';
      }
      if (valueHint) valueHint.textContent = '输入固定的折扣金额，如输入20表示减20元';
      if (minAmountGroup) minAmountGroup.style.display = 'block'; // 显示最低消费金额设置
      if (minAmountLabel) minAmountLabel.innerHTML = '最低消费金额 <span class="required">*</span>';
      const minAmountInput = document.getElementById('minAmount');
      if (minAmountInput) minAmountInput.placeholder = '设置使用此折扣的最低消费金额';
      if (maxDiscountGroup) maxDiscountGroup.style.display = 'none';
      console.log('固定金额折扣设置完成');
      break;

    case '3': // 满减折扣
      console.log('处理满减折扣');
      if (valueLabel) valueLabel.innerHTML = '减免金额 <span class="required">*</span>';
      if (valueInput) {
        valueInput.placeholder = '请输入减免金额';
        valueInput.removeAttribute('max');
        valueInput.min = '0.01';
        valueInput.step = '0.01';
      }
      if (valueHint) valueHint.textContent = '输入满足条件后的减免金额';
      if (minAmountGroup) minAmountGroup.style.display = 'block';
      if (minAmountLabel) minAmountLabel.innerHTML = '最低消费金额 <span class="required">*</span>';
      const minAmountInput3 = document.getElementById('minAmount');
      if (minAmountInput3) minAmountInput3.placeholder = '满减折扣的最低消费金额';
      if (maxDiscountGroup) maxDiscountGroup.style.display = 'none';
      console.log('满减折扣设置完成');
      break;

    default:
      console.log('处理默认情况');
      if (valueLabel) valueLabel.innerHTML = '折扣值 <span class="required">*</span>';
      if (valueInput) {
        valueInput.placeholder = '请输入折扣值';
        valueInput.removeAttribute('max');
        valueInput.removeAttribute('min');
        valueInput.step = '0.01';
      }
      if (valueHint) valueHint.textContent = '';
      if (minAmountGroup) minAmountGroup.style.display = 'none';
      if (maxDiscountGroup) maxDiscountGroup.style.display = 'none';
      console.log('默认情况设置完成');
  }

  console.log('onDiscountTypeChange 函数执行完成');
}

// 适用范围变化处理
function onApplicableToChange() {
  const applicableTo = document.getElementById('applicableTo').value;
  const productSelection = document.getElementById('productSelection');
  const categorySelection = document.getElementById('categorySelection');
  
  // 隐藏所有选择区域
  productSelection.style.display = 'none';
  categorySelection.style.display = 'none';
  
  if (applicableTo === '2') {
    // 指定商品
    productSelection.style.display = 'block';
    loadAvailableProducts();
  } else if (applicableTo === '3') {
    // 指定分类
    categorySelection.style.display = 'block';
    loadAvailableCategories();
  }
}

// 加载可选商品
function loadAvailableProducts() {
  fetch('/api/admin/discount/available-products', {
    credentials: 'same-origin',
    headers: {
      'Accept': 'application/json'
    }
  })
  .then(res => res.json())
  .then(data => {
    if (data.success) {
      renderProductList(data.data.list || []);
    } else {
      console.error('获取可选商品失败:', data.message);
    }
  })
  .catch(error => {
    console.error('获取可选商品失败:', error);
  });
}

// 渲染商品列表
function renderProductList(products) {
  const productList = document.getElementById('productList');
  
  if (!products || products.length === 0) {
    productList.innerHTML = '<div style="text-align: center; color: #999; padding: 20px;">暂无可选商品</div>';
    return;
  }
  
  productList.innerHTML = products.map(product => `
    <label style="display: block; padding: 8px 0; border-bottom: 1px solid #f0f0f0; cursor: pointer;">
      <input type="checkbox" name="product_ids" value="${product.id}" style="margin-right: 8px;">
      <img src="${product.image_url || '/images/placeholder.png'}" 
           style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px; margin-right: 8px; vertical-align: middle;"
           onerror="this.src='/images/placeholder.png'">
      <span style="vertical-align: middle;">${product.name} - ¥${product.price}</span>
    </label>
  `).join('');
}

// 加载可选分类
function loadAvailableCategories() {
  fetch('/api/admin/discount/available-categories', {
    credentials: 'same-origin',
    headers: {
      'Accept': 'application/json'
    }
  })
  .then(res => res.json())
  .then(data => {
    if (data.success) {
      renderCategoryList(data.data || []);
    } else {
      console.error('获取可选分类失败:', data.message);
    }
  })
  .catch(error => {
    console.error('获取可选分类失败:', error);
  });
}

// 渲染分类列表
function renderCategoryList(categories) {
  const categoryList = document.getElementById('categoryList');
  
  if (!categories || categories.length === 0) {
    categoryList.innerHTML = '<div style="text-align: center; color: #999; padding: 20px;">暂无可选分类</div>';
    return;
  }
  
  categoryList.innerHTML = categories.map(category => `
    <label style="display: block; padding: 8px 0; border-bottom: 1px solid #f0f0f0; cursor: pointer;">
      <input type="checkbox" name="category_ids" value="${category.id}" style="margin-right: 8px;">
      <span>${category.name}</span>
    </label>
  `).join('');
}

// 保存折扣
function saveDiscount() {
  const form = document.getElementById('discountForm');
  const formData = new FormData(form);
  
  // 验证必填字段
  if (!formData.get('name') || !formData.get('type') || !formData.get('value') || 
      !formData.get('start_time') || !formData.get('end_time')) {
    showMessage('请填写完整的折扣信息', 'error');
    return;
  }
  
  // 验证时间
  const startTime = new Date(formData.get('start_time'));
  const endTime = new Date(formData.get('end_time'));
  if (startTime >= endTime) {
    showMessage('开始时间必须早于结束时间', 'error');
    return;
  }
  
  // 构建请求数据
  const requestData = {
    name: formData.get('name'),
    description: formData.get('description'),
    type: parseInt(formData.get('type')),
    value: parseFloat(formData.get('value')),
    min_amount: formData.get('min_amount') ? parseFloat(formData.get('min_amount')) : null,
    max_discount: formData.get('max_discount') ? parseFloat(formData.get('max_discount')) : null,
    start_time: formData.get('start_time'),
    end_time: formData.get('end_time'),
    usage_limit: formData.get('usage_limit') ? parseInt(formData.get('usage_limit')) : null,
    user_limit: formData.get('user_limit') ? parseInt(formData.get('user_limit')) : null,
    status: formData.get('status') ? 1 : 0,
    priority: parseInt(formData.get('priority')) || 0,
    applicable_to: parseInt(formData.get('applicable_to'))
  };
  
  // 获取选中的商品或分类
  if (requestData.applicable_to === 2) {
    const selectedProducts = Array.from(document.querySelectorAll('input[name="product_ids"]:checked'))
                                  .map(input => parseInt(input.value));
    requestData.product_ids = selectedProducts;
  } else if (requestData.applicable_to === 3) {
    const selectedCategories = Array.from(document.querySelectorAll('input[name="category_ids"]:checked'))
                                    .map(input => parseInt(input.value));
    requestData.category_ids = selectedCategories;
  }
  
  const isEdit = currentEditingId !== null;
  const url = isEdit ? `/api/admin/discount/update/${currentEditingId}` : '/api/admin/discount/create';
  const method = isEdit ? 'PUT' : 'POST';
  
  fetch(url, {
    method: method,
    credentials: 'same-origin',
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(requestData)
  })
  .then(res => {
    console.log('保存折扣响应状态:', res.status);
    if (!res.ok) {
      throw new Error(`HTTP error: ${res.status} ${res.statusText}`);
    }
    return res.json();
  })
  .then(data => {
    console.log('保存折扣响应数据:', data);
    if (data.success) {
      showMessage(isEdit ? '折扣更新成功' : '折扣创建成功', 'success');
      closeDiscountModal();
      // 延迟一点再加载列表，确保数据已保存
      setTimeout(() => {
        loadDiscountList(currentPage);
      }, 500);
    } else {
      showMessage(data.message || '操作失败', 'error');
    }
  })
  .catch(error => {
    console.error('保存折扣失败:', error);
    showMessage('保存失败，请重试', 'error');
  });
}

// 关闭折扣模态框
function closeDiscountModal() {
  document.getElementById('discountModal').style.display = 'none';
  currentEditingId = null;
}

// 切换折扣状态
function toggleDiscountStatus(id, currentStatus) {
  const newStatus = currentStatus ? 0 : 1;
  const action = newStatus ? '启用' : '禁用';
  
  if (!confirm(`确定要${action}这个折扣吗？`)) {
    return;
  }
  
  fetch(`/api/admin/discount/status/${id}`, {
    method: 'PUT',
    credentials: 'same-origin',
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ status: newStatus })
  })
  .then(res => res.json())
  .then(data => {
    if (data.success) {
      showMessage(`${action}成功`, 'success');
      loadDiscountList(currentPage);
    } else {
      showMessage(data.message || `${action}失败`, 'error');
    }
  })
  .catch(error => {
    console.error(`${action}折扣失败:`, error);
    showMessage(`${action}失败，请重试`, 'error');
  });
}

// 删除折扣
function deleteDiscount(id) {
  if (!confirm('确定要删除这个折扣吗？删除后无法恢复！')) {
    return;
  }
  
  fetch(`/api/admin/discount/delete/${id}`, {
    method: 'DELETE',
    credentials: 'same-origin',
    headers: {
      'Accept': 'application/json'
    }
  })
  .then(res => res.json())
  .then(data => {
    if (data.success) {
      showMessage('删除成功', 'success');
      loadDiscountList(currentPage);
    } else {
      showMessage(data.message || '删除失败', 'error');
    }
  })
  .catch(error => {
    console.error('删除折扣失败:', error);
    showMessage('删除失败，请重试', 'error');
  });
}

// 显示消息提示
function showMessage(message, type = 'info') {
  // 创建消息元素
  const messageEl = document.createElement('div');
  messageEl.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 4px;
    color: white;
    font-size: 14px;
    z-index: 10000;
    max-width: 300px;
    word-wrap: break-word;
  `;
  
  // 根据类型设置样式
  switch (type) {
    case 'success':
      messageEl.style.backgroundColor = '#52c41a';
      break;
    case 'error':
      messageEl.style.backgroundColor = '#ff4d4f';
      break;
    case 'warning':
      messageEl.style.backgroundColor = '#fa8c16';
      break;
    default:
      messageEl.style.backgroundColor = '#2d8cf0';
  }
  
  messageEl.textContent = message;
  document.body.appendChild(messageEl);
  
  // 3秒后自动移除
  setTimeout(() => {
    if (messageEl.parentNode) {
      messageEl.parentNode.removeChild(messageEl);
    }
  }, 3000);
}
