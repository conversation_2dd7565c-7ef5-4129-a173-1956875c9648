document.getElementById('loginForm').onsubmit = async function (e) {
  e.preventDefault();
  const form = e.target;
  const username = form.username.value.trim();
  const password = form.password.value;
  const res = await fetch('/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username, password }),
  });
  const data = await res.json();
  if (data.success) {
    if (data.token) {
      localStorage.setItem('token', data.token);
    }
    window.location.href = '/dashboard';
  } else {
    document.getElementById('errorMsg').innerText = data.message || '登录失败';
    document.getElementById('errorModal').style.display = 'flex';
  }
};

document.getElementById('closeErrorModal').onclick = function () {
  document.getElementById('errorModal').style.display = 'none';
};
