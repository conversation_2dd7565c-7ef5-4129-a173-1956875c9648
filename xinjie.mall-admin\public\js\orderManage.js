function loadOrderList(page = 1) {
  fetch('/api/orders/list?page=' + page, { credentials: 'same-origin' })
    .then(res => res.json())
    .then(data => {
      if (data.code === 0 && data.data && Array.isArray(data.data.list)) {
        const list = data.data.list;
        const tbody = document.getElementById('orderTableBody');
        tbody.innerHTML = list
          .map(
            o =>
              `<tr><td>${o.order_no || o.id}</td><td>${o.user_id || ''}</td><td>${o.total_amount || ''}</td><td>${o.status || ''}</td><td>${o.created_at || ''}</td><td><button>详情</button></td></tr>`
          )
          .join('');
        const total = data.data.total || 0;
        document.getElementById('orderPagination').innerHTML = total
          ? `共${total}条`
          : '';
      }
    });
}
loadOrderList();
