let currentPage = 1;
const pageSize = 10;

function loadProductList(page = 1) {
  currentPage = page;

  console.log(`开始加载第${page}页商品列表...`);

  fetch(`/api/products/list?page=${page}&pageSize=${pageSize}`, {
    credentials: 'same-origin',
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    }
  })
    .then(res => {
      console.log('API响应状态:', res.status);
      if (!res.ok) {
        throw new Error(`HTTP错误: ${res.status} ${res.statusText}`);
      }
      return res.text();
    })
    .then(responseText => {
      console.log('原始响应:', responseText);

      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('JSON解析失败:', parseError);
        console.error('响应内容:', responseText);
        throw new Error(`无法解析JSON响应: ${parseError.message}`);
      }

      console.log('解析后的数据:', data);

      // 兼容不同的响应格式
      let list = [];
      let total = 0;

      if (data.success && data.data) {
        list = data.data.list || [];
        total = data.data.pagination ? data.data.pagination.total : (data.data.total || 0);
      } else if (data.code === 0 && data.data) {
        list = data.data.list || [];
        total = data.data.total || 0;
      } else {
        console.error('API返回错误:', data);
        throw new Error(data.message || '获取商品列表失败');
      }

      if (Array.isArray(list)) {
        const tbody = document.getElementById('productTableBody');
        tbody.innerHTML = list
          .map(p => {
            const imageUrl = p.image_url || p.image || '';
            const statusText = p.status == 1 ? '上架' : '下架';
            // 计算折扣价格显示
            const originalPrice = p.price || 0;
            const discountPrice = p.discount_price || null;
            const hasDiscount = p.has_discount && discountPrice && discountPrice < originalPrice;

            const priceDisplay = hasDiscount
              ? `<span style="text-decoration: line-through; color: #999;">¥${originalPrice}</span>`
              : `¥${originalPrice}`;

            const discountPriceDisplay = hasDiscount
              ? `<span style="color: #ff4d4f; font-weight: bold;">¥${discountPrice}</span>`
              : '-';

            return `<tr>
              <td>${p.id}</td>
              <td><img src="${imageUrl}" style="height:40px;max-width:80px;" onerror="this.src='/images/placeholder.png'"></td>
              <td>${p.name || ''}</td>
              <td>${priceDisplay}</td>
              <td>${discountPriceDisplay}</td>
              <td>${p.stock || '0'}</td>
              <td>${statusText}</td>
              <td>
                <button onclick="editProduct(${p.id})" style="margin-right:8px;padding:4px 8px;background:#2d8cf0;color:white;border:none;border-radius:4px;cursor:pointer;">编辑</button>
                <button onclick="setProductDiscount(${p.id})" style="margin-right:8px;padding:4px 8px;background:#52c41a;color:white;border:none;border-radius:4px;cursor:pointer;">设置折扣</button>
                <button onclick="deleteProduct(${p.id})" style="padding:4px 8px;background:#ed4014;color:white;border:none;border-radius:4px;cursor:pointer;">删除</button>
              </td>
            </tr>`;
          })
          .join('');

        // 生成分页控件
        renderPagination(total, page, pageSize);
      }
    })
    .catch(error => {
      console.error('加载商品列表失败:', error);
    });
}

function renderPagination(total, currentPage, pageSize) {
  const totalPages = Math.ceil(total / pageSize);
  const paginationEl = document.getElementById('productPagination');

  if (totalPages <= 1) {
    paginationEl.innerHTML = total > 0 ? `共${total}条` : '';
    return;
  }

  let paginationHTML = `<div style="display:flex;align-items:center;justify-content:flex-end;gap:8px;">`;
  paginationHTML += `<span>共${total}条，第${currentPage}/${totalPages}页</span>`;

  // 上一页按钮
  if (currentPage > 1) {
    paginationHTML += `<button onclick="loadProductList(${currentPage - 1})" style="padding:4px 8px;background:#2d8cf0;color:white;border:none;border-radius:4px;cursor:pointer;">上一页</button>`;
  }

  // 页码按钮
  const startPage = Math.max(1, currentPage - 2);
  const endPage = Math.min(totalPages, currentPage + 2);

  if (startPage > 1) {
    paginationHTML += `<button onclick="loadProductList(1)" style="padding:4px 8px;background:#f7f7f7;border:1px solid #ddd;border-radius:4px;cursor:pointer;">1</button>`;
    if (startPage > 2) {
      paginationHTML += `<span>...</span>`;
    }
  }

  for (let i = startPage; i <= endPage; i++) {
    const isActive = i === currentPage;
    const buttonStyle = isActive
      ? "padding:4px 8px;background:#2d8cf0;color:white;border:none;border-radius:4px;cursor:pointer;"
      : "padding:4px 8px;background:#f7f7f7;border:1px solid #ddd;border-radius:4px;cursor:pointer;";
    paginationHTML += `<button onclick="loadProductList(${i})" style="${buttonStyle}">${i}</button>`;
  }

  if (endPage < totalPages) {
    if (endPage < totalPages - 1) {
      paginationHTML += `<span>...</span>`;
    }
    paginationHTML += `<button onclick="loadProductList(${totalPages})" style="padding:4px 8px;background:#f7f7f7;border:1px solid #ddd;border-radius:4px;cursor:pointer;">${totalPages}</button>`;
  }

  // 下一页按钮
  if (currentPage < totalPages) {
    paginationHTML += `<button onclick="loadProductList(${currentPage + 1})" style="padding:4px 8px;background:#2d8cf0;color:white;border:none;border-radius:4px;cursor:pointer;">下一页</button>`;
  }

  paginationHTML += `</div>`;
  paginationEl.innerHTML = paginationHTML;
}

// 编辑商品函数
function editProduct(id) {
  alert('编辑商品功能开发中，商品ID: ' + id);
}

// 删除商品函数
function deleteProduct(id) {
  if (confirm('确定要删除这个商品吗？')) {
    fetch(`/api/products/delete/${id}`, {
      method: 'DELETE',
      credentials: 'same-origin'
    })
    .then(res => res.json())
    .then(data => {
      if (data.success) {
        alert('删除成功');
        loadProductList(currentPage);
      } else {
        alert('删除失败: ' + (data.message || '未知错误'));
      }
    })
    .catch(error => {
      console.error('删除商品失败:', error);
      alert('删除失败');
    });
  }
}

// 设置商品折扣函数
function setProductDiscount(id) {
  // 跳转到折扣管理页面，并传递商品ID参数
  const currentUrl = window.location.href;
  const baseUrl = currentUrl.split('#')[0];

  // 切换到折扣管理页面
  const menuLinks = document.querySelectorAll('.menu a[data-section]');
  menuLinks.forEach(l => l.classList.remove('active'));

  // 激活折扣管理菜单项
  const discountLink = document.querySelector('.menu a[data-section="discounts"]');
  if (discountLink) {
    discountLink.classList.add('active');

    // 展开商品管理子菜单
    const menuGroup = discountLink.closest('.menu-group');
    if (menuGroup) {
      menuGroup.classList.add('expanded');
    }
  }

  // 渲染折扣管理页面
  if (typeof renderSection === 'function') {
    renderSection('discounts');
  } else {
    // 如果renderSection函数不存在，直接跳转
    window.location.hash = '#discounts';
    fetch('/manage/discounts', {
      credentials: 'same-origin'
    })
    .then(response => response.text())
    .then(html => {
      const contentArea = document.getElementById('contentArea');
      if (contentArea) {
        contentArea.innerHTML = html;
      }
    })
    .catch(error => {
      console.error('加载折扣管理页面失败:', error);
    });
  }

  // 存储要设置折扣的商品ID，供折扣管理页面使用
  sessionStorage.setItem('selectedProductId', id);

  // 显示提示信息
  setTimeout(() => {
    alert(`已切换到折扣管理页面，可以为商品ID ${id} 设置折扣`);
  }, 500);
}

// 初始化加载
loadProductList();
