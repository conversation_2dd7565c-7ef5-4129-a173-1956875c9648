function loadUserList(page = 1) {
  fetch('/api/users/list?page=' + page, { credentials: 'same-origin' })
    .then(res => res.json())
    .then(data => {
      if (data.code === 0 && data.data && Array.isArray(data.data.list)) {
        const list = data.data.list;
        const tbody = document.getElementById('userTableBody');
        tbody.innerHTML = list
          .map(
            u =>
              `<tr><td>${u.id}</td><td>${u.username || u.nickname || ''}</td><td>${u.phone || ''}</td><td>${u.status == 1 ? '正常' : '禁用'}</td><td>${u.created_at || ''}</td><td><button>详情</button></td></tr>`
          )
          .join('');
        const total = data.data.total || 0;
        document.getElementById('userPagination').innerHTML = total
          ? `共${total}条`
          : '';
      }
    });
}
loadUserList();
