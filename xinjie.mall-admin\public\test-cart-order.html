<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>购物车和订单功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .section h2 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .cart-item {
            border: 1px solid #eee;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        .price {
            font-weight: bold;
            color: #e74c3c;
        }
        .original-price {
            text-decoration: line-through;
            color: #999;
        }
        .discount {
            color: #27ae60;
        }
    </style>
</head>
<body>
    <h1>购物车和订单功能测试</h1>

    <!-- 添加到购物车 -->
    <div class="section">
        <h2>1. 添加商品到购物车</h2>
        <div class="form-group">
            <label>商品ID:</label>
            <input type="number" id="productId" value="1" placeholder="输入商品ID">
        </div>
        <div class="form-group">
            <label>数量:</label>
            <input type="number" id="quantity" value="1" min="1" placeholder="输入数量">
        </div>
        <button onclick="addToCart()">添加到购物车</button>
        <div id="addResult" class="result"></div>
    </div>

    <!-- 查看购物车 -->
    <div class="section">
        <h2>2. 查看购物车</h2>
        <button onclick="getCartList()">获取购物车列表</button>
        <div id="cartResult" class="result"></div>
    </div>

    <!-- 创建订单 -->
    <div class="section">
        <h2>3. 创建订单</h2>
        <div class="form-group">
            <label>收货人姓名:</label>
            <input type="text" id="receiverName" value="张三" placeholder="输入收货人姓名">
        </div>
        <div class="form-group">
            <label>收货人电话:</label>
            <input type="text" id="receiverPhone" value="13800138000" placeholder="输入收货人电话">
        </div>
        <div class="form-group">
            <label>收货地址:</label>
            <input type="text" id="receiverAddress" value="北京市朝阳区某某街道某某小区" placeholder="输入收货地址">
        </div>
        <button onclick="createOrder()">创建订单</button>
        <div id="orderResult" class="result"></div>
    </div>

    <!-- 查看订单 -->
    <div class="section">
        <h2>4. 查看订单列表</h2>
        <button onclick="getOrderList()">获取订单列表</button>
        <div id="orderListResult" class="result"></div>
    </div>

    <script>
        // 模拟用户token（实际应用中应该从登录获取）
        const userToken = 'test-user-token';

        // 通用请求函数
        async function apiRequest(url, method = 'GET', data = null) {
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${userToken}`
                }
            };

            if (data) {
                options.body = JSON.stringify(data);
            }

            try {
                const response = await fetch(url, options);
                const result = await response.json();
                return result;
            } catch (error) {
                return { success: false, message: error.message };
            }
        }

        // 添加到购物车
        async function addToCart() {
            const productId = document.getElementById('productId').value;
            const quantity = parseInt(document.getElementById('quantity').value);

            const result = await apiRequest('/api/cart/add', 'POST', {
                product_id: parseInt(productId),
                quantity: quantity
            });

            document.getElementById('addResult').textContent = JSON.stringify(result, null, 2);
        }

        // 获取购物车列表
        async function getCartList() {
            const result = await apiRequest('/api/cart/list');
            
            if (result.success && result.data) {
                let html = `<h3>购物车摘要</h3>`;
                html += `<p>商品数量: ${result.data.summary.itemCount}</p>`;
                html += `<p>原价总计: ¥${result.data.summary.originalTotal}</p>`;
                html += `<p>折扣总计: ¥${result.data.summary.discountTotal}</p>`;
                html += `<p>优惠金额: ¥${result.data.summary.totalDiscount}</p>`;
                
                html += `<h3>商品列表</h3>`;
                result.data.items.forEach(item => {
                    html += `<div class="cart-item">`;
                    html += `<h4>${item.product_name}</h4>`;
                    html += `<p>数量: ${item.quantity}</p>`;
                    if (item.hasDiscount) {
                        html += `<p>原价: <span class="original-price">¥${item.originalPrice}</span></p>`;
                        html += `<p>折扣价: <span class="price">¥${item.discountPrice}</span></p>`;
                        html += `<p>优惠: <span class="discount">-¥${item.discountAmount}</span></p>`;
                        if (item.discount) {
                            html += `<p>折扣活动: ${item.discount.name}</p>`;
                        }
                    } else {
                        html += `<p>价格: <span class="price">¥${item.price}</span></p>`;
                    }
                    html += `<p>小计: <span class="price">¥${item.itemTotal}</span></p>`;
                    html += `</div>`;
                });
                
                document.getElementById('cartResult').innerHTML = html;
            } else {
                document.getElementById('cartResult').textContent = JSON.stringify(result, null, 2);
            }
        }

        // 创建订单
        async function createOrder() {
            // 先获取购物车数据
            const cartResult = await apiRequest('/api/cart/list');
            
            if (!cartResult.success || !cartResult.data.items.length) {
                document.getElementById('orderResult').textContent = '购物车为空，请先添加商品';
                return;
            }

            const receiverName = document.getElementById('receiverName').value;
            const receiverPhone = document.getElementById('receiverPhone').value;
            const receiverAddress = document.getElementById('receiverAddress').value;

            if (!receiverName || !receiverPhone || !receiverAddress) {
                document.getElementById('orderResult').textContent = '请填写完整的收货信息';
                return;
            }

            // 准备订单数据
            const orderData = {
                items: cartResult.data.items.map(item => ({
                    product_id: item.product_id,
                    product_name: item.product_name,
                    product_image: item.product_image,
                    quantity: item.quantity,
                    originalPrice: item.originalPrice,
                    discountPrice: item.discountPrice,
                    specs: item.specs
                })),
                receiver_name: receiverName,
                receiver_phone: receiverPhone,
                receiver_address: receiverAddress,
                remark: '测试订单',
                cart_item_ids: cartResult.data.items.map(item => item.id)
            };

            const result = await apiRequest('/api/order/create', 'POST', orderData);
            document.getElementById('orderResult').textContent = JSON.stringify(result, null, 2);
        }

        // 获取订单列表
        async function getOrderList() {
            const result = await apiRequest('/api/order/list');
            document.getElementById('orderListResult').textContent = JSON.stringify(result, null, 2);
        }

        // 页面加载时自动获取购物车
        window.onload = function() {
            getCartList();
        };
    </script>
</body>
</html>
