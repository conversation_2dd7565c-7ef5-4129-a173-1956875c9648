const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { query } = require('../src/config/database');
const { requireAuth } = require('../middleware/auth');

const router = express.Router();

// 管理员登录
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    console.log('登录请求:', {
      username,
      password: password ? '***' : 'empty',
    });

    // 参数验证
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空',
      });
    }

    // 查询管理员
    const admins = await query(
      'SELECT id, username, password, email, role_id as role, status FROM admin_users WHERE username = ?',
      [username]
    );
    console.log(
      '数据库查到的admin:',
      admins.length > 0 ? '找到用户' : '未找到用户'
    );

    if (admins.length === 0) {
      console.log('未查到用户');
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误',
      });
    }

    const admin = admins[0];

    // 检查状态
    if (admin.status !== 1) {
      console.log('账号被禁用:', admin.status);
      return res.status(401).json({
        success: false,
        message: '账号已被禁用',
      });
    }

    // 验证密码（临时支持明文密码）
    console.log('前端发送的密码:', password);
    console.log('数据库中的密码:', admin.password);
    
    let isPasswordValid = false;
    
    // 先尝试明文比较（开发环境）
    isPasswordValid = (password === admin.password);
    console.log('明文比较结果:', isPasswordValid);
    
    // 如果明文比较失败，再尝试bcrypt
    if (!isPasswordValid) {
      try {
        isPasswordValid = await bcrypt.compare(password, admin.password);
        console.log('bcrypt比较结果:', isPasswordValid);
      } catch (error) {
        console.log('bcrypt比较出错:', error.message);
      }
    }
    
    console.log('最终密码校验:', isPasswordValid ? '成功' : '失败');

    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误',
      });
    }

    // 生成JWT token
    const token = jwt.sign(
      { id: admin.id, username: admin.username },
      process.env.JWT_SECRET || 'xinjie-mall-jwt-secret',
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );

    // 保存到session
    req.session.token = token;
    req.session.adminId = admin.id;

    // 强制保存session
    req.session.save(err => {
      if (err) {
        console.error('Session保存失败:', err);
        return res.status(500).json({
          success: false,
          message: '登录失败，请重试',
        });
      }

      console.log('Session保存成功:', {
        sessionID: req.sessionID,
        hasToken: !!req.session.token,
        hasAdminId: !!req.session.adminId,
      });

      // 更新最后登录时间（可选，不影响登录流程）
      query('UPDATE admin_users SET last_login_time = NOW() WHERE id = ?', [
        admin.id,
      ]).catch(err => {
        console.error('更新登录时间失败（非关键错误）:', err.message);
        // 不抛出错误，不影响登录流程
      });

      res.json({
        success: true,
        message: '登录成功',
        data: {
          token,
          admin: {
            id: admin.id,
            username: admin.username,
            email: admin.email,
            role: admin.role,
          },
        },
      });
    });
  } catch (error) {
    console.error('登录错误:', error.stack || error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message || error, // 便于前端调试时可见
    });
  }
});

// 管理员登出
router.post('/logout', requireAuth, (req, res) => {
  try {
    // 清除session
    req.session.destroy();

    res.json({
      success: true,
      message: '登出成功',
    });
  } catch (error) {
    console.error('登出错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

// 获取当前管理员信息
router.get('/info', requireAuth, async (req, res) => {
  try {
    const admin = await query(
      'SELECT id, username, email, role_id as role, status, created_at FROM admin_users WHERE id = ?',
      [req.admin.id]
    );

    if (admin.length === 0) {
      return res.status(404).json({
        success: false,
        message: '管理员不存在',
      });
    }

    res.json({
      success: true,
      data: admin[0],
    });
  } catch (error) {
    console.error('获取管理员信息错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

// 修改密码
router.put('/password', requireAuth, async (req, res) => {
  try {
    const { oldPassword, newPassword } = req.body;

    if (!oldPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        message: '旧密码和新密码不能为空',
      });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({
        success: false,
        message: '新密码长度不能少于6位',
      });
    }

    // 获取当前密码
    const admins = await query(
      'SELECT password FROM admin_users WHERE id = ?',
      [req.admin.id]
    );

    if (admins.length === 0) {
      return res.status(404).json({
        success: false,
        message: '管理员不存在',
      });
    }

    // 验证旧密码
    const isOldPasswordValid = await bcrypt.compare(
      oldPassword,
      admins[0].password
    );
    if (!isOldPasswordValid) {
      return res.status(400).json({
        success: false,
        message: '旧密码错误',
      });
    }

    // 加密新密码
    const hashedNewPassword = await bcrypt.hash(newPassword, 10);

    // 更新密码
    await query('UPDATE admin_users SET password = ? WHERE id = ?', [
      hashedNewPassword,
      req.admin.id,
    ]);

    res.json({
      success: true,
      message: '密码修改成功',
    });
  } catch (error) {
    console.error('修改密码错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

// 登录状态校验接口
router.get('/check', async (req, res) => {
  try {
    let token = req.session.token;

    // 如果session中没有token，尝试从Authorization头获取
    if (!token && req.headers.authorization) {
      token = req.headers.authorization.replace('Bearer ', '');
    }

    if (!token) {
      return res.status(401).json({ success: false, message: '未登录' });
    }

    // 验证JWT token
    const decoded = jwt.verify(
      token,
      process.env.JWT_SECRET || 'xinjie-mall-jwt-secret'
    );

    // 查询管理员信息
    const admin = await query(
      'SELECT id, username, status FROM admin_users WHERE id = ? AND status = 1',
      [decoded.id]
    );

    if (!admin || admin.length === 0) {
      return res
        .status(401)
        .json({ success: false, message: '管理员不存在或已被禁用' });
    }

    res.json({
      success: true,
      data: { id: admin[0].id, username: admin[0].username },
    });
  } catch (error) {
    console.error('登录状态校验错误:', error);
    res.status(401).json({ success: false, message: '认证失败' });
  }
});

// Token刷新接口
router.post('/refresh-token', async (req, res) => {
  try {
    let token = req.session.token;

    // 如果session中没有token，尝试从Authorization头获取
    if (!token && req.headers.authorization) {
      token = req.headers.authorization.replace('Bearer ', '');
    }

    if (!token) {
      return res.status(401).json({ 
        success: false, 
        message: '未提供token' 
      });
    }

    // 验证当前token
    const decoded = jwt.verify(
      token,
      process.env.JWT_SECRET || 'xinjie-mall-jwt-secret'
    );

    // 查询管理员信息
    const admin = await query(
      'SELECT id, username, email, role_id as role, status FROM admin_users WHERE id = ? AND status = 1',
      [decoded.id]
    );

    if (!admin || admin.length === 0) {
      return res.status(401).json({ 
        success: false, 
        message: '管理员不存在或已被禁用' 
      });
    }

    // 生成新的token
    const newToken = jwt.sign(
      { id: admin[0].id, username: admin[0].username },
      process.env.JWT_SECRET || 'xinjie-mall-jwt-secret',
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );

    // 更新session中的token
    req.session.token = newToken;
    req.session.adminId = admin[0].id;

    // 保存session
    req.session.save(err => {
      if (err) {
        console.error('Session保存失败:', err);
        return res.status(500).json({
          success: false,
          message: 'Token刷新失败',
        });
      }

      res.json({
        success: true,
        message: 'Token刷新成功',
        data: {
          token: newToken,
          admin: {
            id: admin[0].id,
            username: admin[0].username,
            email: admin[0].email,
            role: admin[0].role,
          },
        },
      });
    });
  } catch (error) {
    console.error('Token刷新错误:', error);
    res.status(401).json({ 
      success: false, 
      message: 'Token无效或已过期' 
    });
  }
});

// 获取当前用户信息接口
router.get('/profile', requireAuth, async (req, res) => {
  try {
    const admin = await query(
      'SELECT id, username, email, role_id as role, status, created_at, last_login_time as last_login FROM admin_users WHERE id = ?',
      [req.admin.id]
    );

    if (admin.length === 0) {
      return res.status(404).json({
        success: false,
        message: '管理员不存在',
      });
    }

    res.json({
      success: true,
      data: admin[0],
    });
  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

module.exports = router;
