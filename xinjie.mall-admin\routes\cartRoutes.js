const express = require('express');
const router = express.Router();
const cartController = require('../controllers/cartController');

// 获取购物车列表
router.get('/list', cartController.getCartList);

// 添加商品到购物车
router.post('/add', cartController.addToCart);

// 更新购物车商品数量
router.put('/update/:id', cartController.updateCartItem);

// 删除购物车商品
router.delete('/remove/:id', cartController.removeCartItem);

// 清空购物车
router.delete('/clear', cartController.clearCart);

module.exports = router;
