const express = require('express');
const { query } = require('../src/config/database');
const { requireAuth } = require('../middleware/auth');
const fs = require('fs-extra');
const path = require('path');
const { uploadAndSync } = require('../utils/uploadAndSync');
const { deleteAndSync } = require('../utils/deleteAndSync');

const router = express.Router();

// 获取分类列表
router.get('/list', async (req, res) => {
  try {
    const { status } = req.query;
    
    let whereClause = '';
    let params = [];
    
    if (status !== undefined) {
      whereClause = 'WHERE status = ?';
      params.push(status);
    }

    // 查询时 image 字段和 image_url 字段都返回，值一致
    const categories = await query(
      `SELECT c.*, c.image as image_url, (SELECT COUNT(*) FROM products WHERE category_id = c.id AND status = 1) as product_count
       FROM categories c 
       ${whereClause} 
       ORDER BY sort_order ASC, created_at DESC`,
      params
    );

    res.json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('获取分类列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 获取分类列表（兼容旧接口）
router.get('/', async (req, res) => {
  try {
    const { status } = req.query;
    
    let whereClause = '';
    let params = [];
    
    if (status !== undefined) {
      whereClause = 'WHERE status = ?';
      params.push(status);
    }

    const categories = await query(
      `SELECT c.*, 
       (SELECT COUNT(*) FROM products WHERE category_id = c.id AND status = 1) as product_count
       FROM categories c 
       ${whereClause} 
       ORDER BY sort_order ASC, created_at DESC`,
      params
    );

    res.json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('获取分类列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 创建分类（兼容前端API调用）
router.post('/create', requireAuth, async (req, res) => {
  try {
    const { name, description, image, image_url, sort_order, status } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: '分类名称不能为空'
      });
    }

    // 检查分类名称是否已存在
    const existingCategory = await query('SELECT id FROM categories WHERE name = ?', [name]);
    if (existingCategory.length > 0) {
      return res.status(400).json({
        success: false,
        message: '分类名称已存在'
      });
    }

    // 路径优先用 image，没有就用 image_url
    const imgPath = image || image_url || '';
    const result = await query(
      'INSERT INTO categories (name, description, image, image_url, sort_order, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())',
      [name, description || '', imgPath, imgPath, sort_order || 0, status ? 1 : 0]
    );

    // 同步图片和清理缓存
    if (imgPath) {
      const { syncImageToMallServer } = require('../utils/syncImages');
      const imagePath = imgPath.replace('/uploads/', '');
      syncImageToMallServer(imagePath);
    }
    const { clearMallServerCategoryCache } = require('../utils/clearMallServerCache');
    await clearMallServerCategoryCache();

    res.json({
      success: true,
      message: '分类创建成功',
      data: { id: result.insertId }
    });
  } catch (error) {
    console.error('创建分类错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 更新分类（兼容前端API调用）
router.put('/update/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, image, image_url, sort_order, status } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: '分类名称不能为空'
      });
    }

    // 检查分类是否存在
    const existingCategory = await query('SELECT * FROM categories WHERE id = ?', [id]);
    if (existingCategory.length === 0) {
      return res.status(404).json({
        success: false,
        message: '分类不存在'
      });
    }

    // 检查分类名称是否已被其他分类使用
    const nameExists = await query('SELECT id FROM categories WHERE name = ? AND id != ?', [name, id]);
    if (nameExists.length > 0) {
      return res.status(400).json({
        success: false,
        message: '分类名称已存在'
      });
    }

    // 路径优先用 image，没有就用 image_url
    const imgPath = image || image_url || '';
    // 如果更新了图片，删除旧图片
    if (imgPath && imgPath !== existingCategory[0].image && existingCategory[0].image) {
      const deleteResult = await deleteAndSync(existingCategory[0].image, 'categories');
      if (!deleteResult.success) {
        console.warn('删除旧图片失败，但继续更新:', deleteResult.message);
      }
    }

    await query(
      'UPDATE categories SET name = ?, description = ?, image = ?, image_url = ?, sort_order = ?, status = ?, updated_at = NOW() WHERE id = ?',
      [name, description || '', imgPath, imgPath, sort_order || 0, status ? 1 : 0, id]
    );

    // 同步图片和清理缓存
    if (imgPath) {
      const { syncImageToMallServer } = require('../utils/syncImages');
      const imagePath = imgPath.replace('/uploads/', '');
      syncImageToMallServer(imagePath);
    }
    const { clearMallServerCategoryCache } = require('../utils/clearMallServerCache');
    await clearMallServerCategoryCache();

    res.json({
      success: true,
      message: '分类更新成功'
    });
  } catch (error) {
    console.error('更新分类错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 删除分类（兼容前端API调用）
router.delete('/delete/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;

    // 获取分类信息
    const category = await query('SELECT * FROM categories WHERE id = ?', [id]);
    if (category.length === 0) {
      return res.status(404).json({
        success: false,
        message: '分类不存在'
      });
    }

    // 检查分类下是否有商品
    const products = await query('SELECT COUNT(*) as count FROM products WHERE category_id = ?', [id]);
    if (products[0].count > 0) {
      return res.status(400).json({
        success: false,
        message: '该分类下还有商品，无法删除'
      });
    }

    // 使用统一的删除工具删除图片
    if (category[0].image) {
      const deleteResult = await deleteAndSync(category[0].image, 'categories');
      if (!deleteResult.success) {
        console.warn('删除图片失败，但继续删除数据库记录:', deleteResult.message);
      }
    }

    // 删除数据库记录
    await query('DELETE FROM categories WHERE id = ?', [id]);

    res.json({
      success: true,
      message: '分类删除成功'
    });
  } catch (error) {
    console.error('删除分类错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 获取单个分类
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    // 查询时 image 字段和 image_url 字段都返回，值一致
    const categories = await query('SELECT *, image as image_url FROM categories WHERE id = ?', [id]);
    if (categories.length === 0) {
      return res.status(404).json({
        success: false,
        message: '分类不存在'
      });
    }

    res.json({
      success: true,
      data: categories[0]
    });
  } catch (error) {
    console.error('获取分类错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 获取分类下的商品
router.get('/:id/products', async (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    // 检查分类是否存在
    const category = await query('SELECT * FROM categories WHERE id = ?', [id]);
    if (category.length === 0) {
      return res.status(404).json({
        success: false,
        message: '分类不存在'
      });
    }

    const products = await query(
      `SELECT p.*, c.name as category_name 
       FROM products p 
       LEFT JOIN categories c ON p.category_id = c.id 
       WHERE p.category_id = ? AND p.status = 1 
       ORDER BY p.sort_order ASC, p.created_at DESC 
       LIMIT ? OFFSET ?`,
      [id, parseInt(limit), offset]
    );

    // 获取总数
    const countResult = await query(
      'SELECT COUNT(*) as total FROM products WHERE category_id = ? AND status = 1',
      [id]
    );
    const total = countResult[0].total;

    res.json({
      success: true,
      data: {
        category: category[0],
        products,
        pagination: {
          current: parseInt(page),
          pageSize: parseInt(limit),
          total
        }
      }
    });
  } catch (error) {
    console.error('获取分类商品错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 分类图片上传
router.post('/upload', requireAuth, async (req, res) => {
  console.log('[唯一调试] category upload 被调用');
  try {
    if (!req.files || !req.files.file) {
      console.log('[唯一调试] category 没有文件上传');
      return res.status(400).json({ code: 1, msg: '未上传文件' });
    }
    const file = req.files.file;
    console.log('[唯一调试] category 文件信息:', {
      originalname: file.originalname,
      filename: file.filename,
      path: file.path,
      size: file.size
    });
    const path = require('path');
    const fs = require('fs');
    // 上传到后台管理系统的目录，然后同步到mall-server
    const uploadDir = path.join(__dirname, '../public/uploads/categories');
    console.log('[唯一调试] mall-server category 目录:', uploadDir);
    if (!fs.existsSync(uploadDir)) {
      console.log('[唯一调试] 目录不存在，创建目录');
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    const filename = Date.now() + '_' + file.name;
    const filepath = path.join(uploadDir, filename);
    console.log('[唯一调试] 目标文件路径:', filepath);
    file.mv(filepath, err => {
      console.log('[唯一调试] category file.mv 回调，err:', err);
      if (err) {
        console.error('[唯一调试] category 文件保存失败:', err);
        return res.status(500).json({ code: 1, msg: '上传失败' });
      }
      console.log('[唯一调试] category 文件已保存到:', filepath);
      // 验证文件是否真的存在
      if (fs.existsSync(filepath)) {
        console.log('[唯一调试] category 文件确实存在，大小:', fs.statSync(filepath).size);
      } else {
        console.log('[唯一调试] category 文件不存在！');
      }
      const url = '/uploads/categories/' + filename;
      console.log('[唯一调试] category 返回URL:', url);
      
      // 同步图片到mall-server
      const { syncImageToMallServer } = require('../utils/syncImages');
      const imagePath = `categories/${filename}`;
      const syncResult = syncImageToMallServer(imagePath);
      console.log('[唯一调试] category 图片同步结果:', syncResult);
      
      // 返回 image 和 image_url 字段
      res.json({ code: 0, msg: '上传成功', data: { url, image: url, image_url: url } });
    });
  } catch (error) {
    console.error('[唯一调试] category 上传异常:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
});

module.exports = router;
