const express = require('express');
const { query } = require('../src/config/database');
const { requireAuth } = require('../middleware/auth');

const router = express.Router();

// 获取仪表板概览数据
router.get('/overview', requireAuth, async (req, res) => {
  try {
    // 获取今日数据
    const today = new Date().toISOString().split('T')[0];

    // 今日订单数
    const todayOrders = await query(
      'SELECT COUNT(*) as count, SUM(total_amount) as amount FROM orders WHERE DATE(created_at) = ?',
      [today]
    );

    // 今日新增用户
    const todayUsers = await query(
      'SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = ?',
      [today]
    );

    // 商品总数
    const totalProducts = await query(
      'SELECT COUNT(*) as count FROM products WHERE status = 1'
    );

    // 用户总数
    const totalUsers = await query(
      'SELECT COUNT(*) as count FROM users WHERE status = 1'
    );

    // 订单总数
    const totalOrders = await query(
      'SELECT COUNT(*) as count, SUM(total_amount) as amount FROM orders'
    );

    // 待处理订单
    const pendingOrders = await query(
      'SELECT COUNT(*) as count FROM orders WHERE status IN (1, 2)'
    );

    res.json({
      success: true,
      data: {
        today: {
          orders: todayOrders[0].count || 0,
          sales: todayOrders[0].amount || 0,
          users: todayUsers[0].count || 0,
        },
        total: {
          products: totalProducts[0].count || 0,
          users: totalUsers[0].count || 0,
          orders: totalOrders[0].count || 0,
          sales: totalOrders[0].amount || 0,
        },
        pending: {
          orders: pendingOrders[0].count || 0,
        },
      },
    });
  } catch (error) {
    console.error('获取仪表板数据错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

// 获取销售统计数据
router.get('/sales', requireAuth, async (req, res) => {
  try {
    const { period = '7d' } = req.query;

    let dateFilter = '';
    let params = [];

    switch (period) {
      case '7d':
        dateFilter = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
        break;
      case '30d':
        dateFilter = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
        break;
      case '90d':
        dateFilter = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)';
        break;
      default:
        dateFilter = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
    }

    // 按日期分组统计
    const salesData = await query(
      `
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as order_count,
        SUM(total_amount) as sales_amount
      FROM orders 
      ${dateFilter}
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `,
      params
    );

    res.json({
      success: true,
      data: salesData,
    });
  } catch (error) {
    console.error('获取销售统计错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

// 获取热销商品排行
router.get('/hot-products', requireAuth, async (req, res) => {
  try {
    const { limit = 10 } = req.query;

    const hotProducts = await query(
      `
      SELECT 
        p.id,
        p.name,
        p.image,
        p.price,
        COUNT(oi.id) as sales_count,
        SUM(oi.quantity) as total_quantity
      FROM products p
      LEFT JOIN order_items oi ON p.id = oi.product_id
      LEFT JOIN orders o ON oi.order_id = o.id
      WHERE p.status = 1 AND o.status = 4
      GROUP BY p.id
      ORDER BY total_quantity DESC
      LIMIT ?
    `,
      [parseInt(limit)]
    );

    res.json({
      success: true,
      data: hotProducts,
    });
  } catch (error) {
    console.error('获取热销商品错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

// 获取用户增长趋势
router.get('/user-growth', requireAuth, async (req, res) => {
  try {
    const { period = '7d' } = req.query;

    let dateFilter = '';

    switch (period) {
      case '7d':
        dateFilter = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
        break;
      case '30d':
        dateFilter = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
        break;
      case '90d':
        dateFilter = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)';
        break;
      default:
        dateFilter = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
    }

    const userGrowth = await query(`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as new_users
      FROM users 
      ${dateFilter}
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `);

    res.json({
      success: true,
      data: userGrowth,
    });
  } catch (error) {
    console.error('获取用户增长错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

// 获取订单状态分布
router.get('/order-status', requireAuth, async (req, res) => {
  try {
    const orderStatus = await query(`
      SELECT 
        status,
        COUNT(*) as count
      FROM orders 
      GROUP BY status
    `);

    res.json({
      success: true,
      data: orderStatus,
    });
  } catch (error) {
    console.error('获取订单状态分布错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

module.exports = router;
