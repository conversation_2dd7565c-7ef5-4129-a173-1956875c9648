const express = require('express');
const router = express.Router();
const DashboardController = require('../controllers/dashboardController');

// 获取订单统计
router.get('/order/statistics', DashboardController.getOrderStatistics);

// 获取用户统计
router.get('/user/statistics', DashboardController.getUserStatistics);

// 获取商品统计
router.get('/product/statistics', DashboardController.getProductStatistics);

// 获取销售统计
router.get('/order/sales-statistics', DashboardController.getSalesStatistics);

// 获取最近订单
router.get('/order/recent', DashboardController.getRecentOrders);

// 获取热销商品
router.get('/product/hot', DashboardController.getHotProducts);

// 获取系统通知
router.get('/system/notifications', DashboardController.getSystemNotifications);

// 获取系统状态
router.get('/system/status', DashboardController.getSystemStatus);

module.exports = router;
