const express = require('express');
const router = express.Router();
const discountController = require('../controllers/discountController');

// 获取折扣列表
router.get('/list', discountController.getList);

// 获取折扣详情
router.get('/detail/:id', discountController.getDetail);

// 创建折扣
router.post('/create', discountController.create);

// 更新折扣
router.put('/update/:id', discountController.update);

// 删除折扣
router.delete('/delete/:id', discountController.delete);

// 更新折扣状态
router.put('/status/:id', discountController.updateStatus);

// 获取折扣类型选项
router.get('/types', discountController.getDiscountTypes);

// 获取适用范围选项
router.get('/applicable-to-options', discountController.getApplicableToOptions);

// 获取可选商品列表
router.get('/available-products', discountController.getAvailableProducts);

// 获取可选分类列表
router.get('/available-categories', discountController.getAvailableCategories);

module.exports = router;
