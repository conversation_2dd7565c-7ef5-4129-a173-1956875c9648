/**
 * 分销管理路由
 * 后台管理系统的分销相关路由
 */

const express = require('express');
const router = express.Router();
const distributionController = require('../controllers/distributionController');
const { requireAuth } = require('../middleware/auth');

// 应用认证中间件
router.use(requireAuth);

// 分销商管理
router.get('/distributors', distributionController.getDistributorList);
router.get('/distributors/:id', distributionController.getDistributorDetail);
router.put('/distributors/:id/status', distributionController.updateDistributorStatus);
router.get('/distributors/:userId/tree', distributionController.getDistributionTree);

// 佣金管理
router.get('/commissions', distributionController.getCommissionList);
router.post('/commissions/settle', distributionController.settleCommission);

// 数据统计
router.get('/stats', distributionController.getDistributionStats);

// 配置管理
router.get('/config', distributionController.getDistributionConfig);
router.put('/config', distributionController.updateDistributionConfig);

module.exports = router;
