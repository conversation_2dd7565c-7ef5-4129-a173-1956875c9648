const express = require('express');
const router = express.Router();
const frontBalanceController = require('../controllers/frontBalanceController');
const { requireFrontAuth } = require('../middleware/frontAuth');

// 获取用户余额信息
router.get('/info', requireFrontAuth, frontBalanceController.getBalanceInfo);

// 模拟用户充值（方案B - 开发测试用）
router.post('/recharge', requireFrontAuth, frontBalanceController.simulateRecharge);

// 获取充值记录
router.get('/recharge/history', requireFrontAuth, frontBalanceController.getRechargeHistory);

// 获取余额变动记录
router.get('/history', requireFrontAuth, frontBalanceController.getBalanceHistory);

// 获取充值档位推荐
router.get('/recharge/packages', async (req, res) => {
  try {
    const balanceService = require('../services/balanceService');
    const packages = balanceService.getRechargePackages();

    res.json({
      success: true,
      data: packages,
      message: '获取充值档位成功'
    });
  } catch (error) {
    console.error('获取充值档位失败:', error);
    res.status(500).json({
      success: false,
      message: '获取充值档位失败'
    });
  }
});

// 获取充值奖励预览
router.get('/recharge/reward-preview', async (req, res) => {
  try {
    const { amount } = req.query;

    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: '充值金额无效'
      });
    }

    const balanceService = require('../services/balanceService');
    const reward = balanceService.calculateRechargeReward(amount);

    res.json({
      success: true,
      data: {
        amount: parseFloat(amount),
        reward: reward,
        total: parseFloat(amount) + reward.bonusAmount,
        description: `充值${amount}元，获得${reward.points}积分${reward.bonusAmount > 0 ? `+${reward.bonusAmount}元奖励` : ''}`
      },
      message: '获取奖励预览成功'
    });
  } catch (error) {
    console.error('获取充值奖励预览失败:', error);
    res.status(500).json({
      success: false,
      message: '获取充值奖励预览失败'
    });
  }
});

module.exports = router;
