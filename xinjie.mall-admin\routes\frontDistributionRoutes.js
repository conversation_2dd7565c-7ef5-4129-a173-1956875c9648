/**
 * 前端分销路由
 * 小程序端分销功能的API路由
 */

const express = require('express');
const router = express.Router();
const frontDistributionController = require('../controllers/frontDistributionController');
const { requireFrontAuth } = require('../middleware/frontAuth');

// 分享链接点击处理（无需认证）
router.post('/share/click', frontDistributionController.handleShareClick);

// 以下接口需要用户认证
router.use(requireFrontAuth);

// 分销商申请和管理
router.post('/apply', frontDistributionController.applyDistributor);
router.get('/center', frontDistributionController.getDistributionCenter);

// 分享功能
router.post('/share/generate', frontDistributionController.generateShareLink);
router.get('/share/records', frontDistributionController.getShareRecords);

// 佣金管理
router.get('/commission/detail', frontDistributionController.getCommissionDetail);

// 客户管理
router.get('/customers', frontDistributionController.getMyCustomers);

module.exports = router;
