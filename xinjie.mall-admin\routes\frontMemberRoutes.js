const express = require('express');
const router = express.Router();
const frontMemberController = require('../controllers/frontMemberController');
const { requireFrontAuth, optionalFrontAuth } = require('../middleware/frontAuth');

// 获取用户会员信息
router.get('/info', requireFrontAuth, frontMemberController.getMemberInfo);

// 获取会员等级列表（无需登录）
router.get('/levels', optionalFrontAuth, frontMemberController.getMemberLevels);

// 获取会员权益（无需登录）
router.get('/benefits/:levelId', optionalFrontAuth, frontMemberController.getMemberBenefits);

// 获取积分记录
router.get('/points/history', requireFrontAuth, frontMemberController.getPointsHistory);

module.exports = router;
