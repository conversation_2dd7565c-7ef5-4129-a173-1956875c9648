const express = require('express');
const router = express.Router();
const frontBalanceController = require('../controllers/frontBalanceController');

// 获取订单支付选项
router.get('/options/:orderId', frontBalanceController.getPaymentOptions);

// 创建支付订单
router.post('/create', frontBalanceController.createPayment);

// 查询支付状态
router.get('/status/:orderId', frontBalanceController.getPaymentStatus);

// 支付回调处理（为真实支付预留）
router.post('/callback', frontBalanceController.handlePaymentCallback);

module.exports = router;
