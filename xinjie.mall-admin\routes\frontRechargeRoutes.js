const express = require('express');
const router = express.Router();
const frontRechargeController = require('../controllers/frontRechargeController');
const frontOrderPaymentController = require('../controllers/frontOrderPaymentController');
const auth = require('../middleware/auth');

// 用户充值相关路由
router.post('/recharge', auth.requireAuth, frontRechargeController.userRecharge);
router.post('/recharge/callback', frontRechargeController.rechargeCallback);
router.get('/balance', auth.requireAuth, frontRechargeController.getUserBalance);
router.get('/recharge/history', auth.requireAuth, frontRechargeController.getRechargeHistory);
router.get('/balance/history', auth.requireAuth, frontRechargeController.getBalanceHistory);

// 订单支付相关路由
router.post('/order/payment', auth.requireAuth, frontOrderPaymentController.createOrderPayment);
router.post('/order/payment/callback', frontOrderPaymentController.orderPaymentCallback);
router.get('/order/:orderId/payment/status', auth.requireAuth, frontOrderPaymentController.getPaymentStatus);
router.get('/order/:orderId/payment/options', auth.requireAuth, frontOrderPaymentController.getPaymentOptions);

module.exports = router;
