/**
 * 路由统一注册文件
 * 用于无侵入式地注册所有新增路由
 * 在 app.js 中只需要 require('./routes') 即可
 */

const express = require('express');
const router = express.Router();

// 导入所有前端路由
const frontBalanceRoutes = require('./frontBalanceRoutes');
const frontMemberRoutes = require('./frontMemberRoutes');

// 注册前端API路由
router.use('/front/balance', frontBalanceRoutes);
router.use('/front/member', frontMemberRoutes);

module.exports = router;
