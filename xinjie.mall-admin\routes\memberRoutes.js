const express = require('express');
const router = express.Router();
const memberController = require('../controllers/memberController');

// 获取所有会员等级（用于下拉选择）
router.get('/levels/all', memberController.getAllLevels);

// 获取会员等级列表（分页）
router.get('/levels/list', memberController.levelList);

// 获取会员等级详情
router.get('/levels/detail/:id', memberController.levelDetail);

// 创建会员等级
router.post('/levels/create', memberController.createLevel);

// 更新会员等级
router.put('/levels/update/:id', memberController.updateLevel);

// 删除会员等级
router.delete('/levels/delete/:id', memberController.deleteLevel);

// 更新会员等级状态
router.put('/levels/status/:id', memberController.updateLevelStatus);

// 获取会员权益列表（分页）
router.get('/benefits/list', memberController.benefitList);

// 创建会员权益
router.post('/benefits/create', memberController.createBenefit);

// 更新会员权益
router.put('/benefits/update/:id', memberController.updateBenefit);

// 删除会员权益
router.delete('/benefits/delete/:id', memberController.deleteBenefit);

module.exports = router;
