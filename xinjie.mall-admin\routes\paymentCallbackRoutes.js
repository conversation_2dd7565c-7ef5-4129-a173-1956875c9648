/**
 * 支付回调路由
 * 处理各种支付平台的回调通知
 */

const express = require('express');
const router = express.Router();
const paymentCallbackController = require('../controllers/paymentCallbackController');

// 微信支付回调
router.post('/wechat/notify', paymentCallbackController.handleWechatCallback);

// 支付宝回调
router.post('/alipay/notify', paymentCallbackController.handleAlipayCallback);

// 查询支付状态
router.get('/status/:orderNo', paymentCallbackController.queryPaymentStatus);

// 支付失败处理
router.post('/failure', paymentCallbackController.handlePaymentFailure);

// 开发测试：模拟支付成功
router.post('/mock/success', paymentCallbackController.mockPaymentSuccess);

module.exports = router;
