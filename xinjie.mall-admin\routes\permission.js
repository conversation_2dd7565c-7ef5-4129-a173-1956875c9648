const express = require('express');
const { requireAuth, requirePermission } = require('../middleware/auth');
const permissionController = require('../controllers/permissionController');

const router = express.Router();

router.get(
  '/list',
  requireAuth,
  requirePermission('permission:list'),
  permissionController.list
);
router.get(
  '/detail/:id',
  requireAuth,
  requirePermission('permission:detail'),
  permissionController.detail
);
router.post(
  '/create',
  requireAuth,
  requirePermission('permission:create'),
  permissionController.create
);
router.put(
  '/update/:id',
  requireAuth,
  requirePermission('permission:update'),
  permissionController.update
);
router.delete(
  '/delete/:id',
  requireAuth,
  requirePermission('permission:delete'),
  permissionController.delete
);

module.exports = router;
