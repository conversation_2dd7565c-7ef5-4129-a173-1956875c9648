const express = require('express');
const { query } = require('../src/config/database');
const { requireAuth } = require('../middleware/auth');
const fs = require('fs-extra');
const path = require('path');
const { uploadAndSync } = require('../utils/uploadAndSync');
const { deleteAndSync } = require('../utils/deleteAndSync');

const router = express.Router();

// 简单测试API
router.get('/test', (req, res) => {
  console.log('商品测试API被调用');
  res.json({
    success: true,
    message: '商品API路由正常工作',
    timestamp: new Date().toISOString(),
    path: req.path,
    originalUrl: req.originalUrl
  });
});

// 获取商品列表
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, category_id, status, keyword } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE 1=1';
    let params = [];

    if (category_id) {
      whereClause += ' AND p.category_id = ?';
      params.push(category_id);
    }

    if (status !== undefined) {
      whereClause += ' AND p.status = ?';
      params.push(status);
    }

    if (keyword) {
      whereClause += ' AND (p.name LIKE ? OR p.description LIKE ?)';
      params.push(`%${keyword}%`, `%${keyword}%`);
    }

    // 检查是否需要分页（React前端通常获取所有数据进行前端分页）
    const needPagination = req.query.page || req.query.limit;

    let products;
    if (needPagination) {
      // 有分页参数，进行后端分页
      products = await query(
        `SELECT p.*, c.name as category_name
         FROM products p
         LEFT JOIN categories c ON p.category_id = c.id
         ${whereClause}
         ORDER BY p.created_at DESC
         LIMIT ? OFFSET ?`,
        [...params, parseInt(limit), offset]
      );
    } else {
      // 无分页参数，返回所有数据（用于React前端分页）
      products = await query(
        `SELECT p.*, c.name as category_name
         FROM products p
         LEFT JOIN categories c ON p.category_id = c.id
         ${whereClause}
         ORDER BY p.created_at DESC`,
        params
      );
    }

    // 获取总数
    const countResult = await query(
      `SELECT COUNT(*) as total FROM products p ${whereClause}`,
      params
    );
    const total = countResult[0].total;

    res.json({
      success: true,
      data: products,
      pagination: {
        current: parseInt(page),
        pageSize: parseInt(limit),
        total
      }
    });
  } catch (error) {
    console.error('获取商品列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 获取商品列表 - 兼容前端API调用
router.get('/list', async (req, res) => {
  console.log('=== 商品列表API调用开始 ===');
  console.log('请求参数:', req.query);
  console.log('请求路径:', req.path);
  console.log('完整URL:', req.originalUrl);

  try {
    const { page = 1, pageSize = 10, category_id, status, name } = req.query;
    const offset = (page - 1) * pageSize;

    console.log('解析后的参数:', { page, pageSize, offset, category_id, status, name });

    let whereClause = 'WHERE 1=1';
    let params = [];

    if (category_id) {
      whereClause += ' AND p.category_id = ?';
      params.push(category_id);
    }

    if (status !== undefined) {
      whereClause += ' AND p.status = ?';
      params.push(status);
    }

    if (name) {
      whereClause += ' AND (p.name LIKE ? OR p.description LIKE ?)';
      params.push(`%${name}%`, `%${name}%`);
    }

    console.log('SQL WHERE子句:', whereClause);
    console.log('SQL参数:', params);

    // 获取商品列表
    const listSql = `SELECT p.*, c.name as category_name
       FROM products p
       LEFT JOIN categories c ON p.category_id = c.id
       ${whereClause}
       ORDER BY p.created_at DESC
       LIMIT ? OFFSET ?`;

    console.log('执行商品列表查询...');
    const products = await query(listSql, [...params, parseInt(pageSize), offset]);
    console.log('商品列表查询结果:', products.length, '条记录');

    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM products p ${whereClause}`;
    console.log('执行总数查询...');
    const countResult = await query(countSql, params);
    const total = countResult[0].total;
    console.log('总数查询结果:', total);

    const responseData = {
      success: true,
      data: {
        list: products,
        pagination: {
          current: parseInt(page),
          pageSize: parseInt(pageSize),
          total
        }
      }
    };

    console.log('准备返回响应:', {
      success: responseData.success,
      listLength: responseData.data.list.length,
      pagination: responseData.data.pagination
    });

    res.json(responseData);
    console.log('=== 商品列表API调用成功结束 ===');

  } catch (error) {
    console.error('=== 商品列表API调用失败 ===');
    console.error('错误详情:', error);
    console.error('错误堆栈:', error.stack);

    const errorResponse = {
      success: false,
      message: '服务器错误: ' + error.message,
      error: error.toString(),
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    };

    console.log('返回错误响应:', errorResponse);
    res.status(500).json(errorResponse);
  }
});

// 创建商品
router.post('/', requireAuth, async (req, res) => {
  try {
    const {
      name,
      description,
      price,
      original_price,
      stock,
      category_id,
      image_url,
      images,
      status,
      is_hot,
      is_recommend,
      sort_order
    } = req.body;

    console.log('创建商品请求数据:', req.body);
    console.log('验证字段:', { name, price, category_id, image_url });

    if (!name || !price || !category_id || !image_url) {
      console.log('验证失败 - 缺少必填字段:', {
        name: !name ? 'missing' : 'ok',
        price: !price ? 'missing' : 'ok', 
        category_id: !category_id ? 'missing' : 'ok',
        image_url: !image_url ? 'missing' : 'ok'
      });
      return res.status(400).json({
        success: false,
        message: '商品名称、价格、分类和图片不能为空'
      });
    }

    // 验证图片路径安全性
    const ProductImageUtils = require('../utils/productImageUtils');
    const safeImageUrl = ProductImageUtils.fixImagePath(image_url);
    if (!safeImageUrl) {
      return res.status(400).json({
        success: false,
        message: '商品图片路径格式不正确'
      });
    }

    const result = await query(
      `INSERT INTO products (name, description, price, original_price, stock, category_id, 
       image_url, main_image, images, status, is_hot, is_recommend, created_at, updated_at) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [
        name,
        description || '',
        price,
        original_price || price,
        stock || 0,
        category_id,
        safeImageUrl,
        safeImageUrl, // main_image 和 image_url 保持一致
        JSON.stringify(images || []),
        status ? 1 : 0,
        is_hot ? 1 : 0,
        is_recommend ? 1 : 0
      ]
    );

    res.json({
      success: true,
      message: '商品创建成功',
      data: { id: result.insertId }
    });
  } catch (error) {
    console.error('创建商品错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 创建商品 - 兼容前端API调用
router.post('/create', requireAuth, async (req, res) => {
  try {
    const {
      name,
      description,
      price,
      original_price,
      stock,
      category_id,
      main_image,
      image_url,
      images,
      status,
      is_hot,
      is_recommend,
      sort_order
    } = req.body;

    console.log('收到商品创建请求:', req.body);

    // 兼容前端传递的main_image字段
    const finalImageUrl = main_image || image_url;

    if (!name || !price || !category_id || !finalImageUrl) {
      console.log('参数验证失败:', { name, price, category_id, finalImageUrl });
      return res.status(400).json({
        success: false,
        message: '商品名称、价格、分类和图片不能为空'
      });
    }

    const result = await query(
      `INSERT INTO products (name, description, price, original_price, stock, category_id, 
       image_url, images, status, is_hot, is_recommend, created_at, updated_at) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [
        name,
        description || '',
        price,
        original_price || price,
        stock || 0,
        category_id,
        finalImageUrl,
        JSON.stringify(images || []),
        status ? 1 : 0,
        is_hot ? 1 : 0,
        is_recommend ? 1 : 0
      ]
    );

    console.log('商品创建成功:', result.insertId);
    res.json({
      success: true,
      message: '商品创建成功',
      data: { id: result.insertId }
    });
  } catch (error) {
    console.error('创建商品错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误: ' + error.message
    });
  }
});

// 更新商品
router.put('/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      price,
      original_price,
      stock,
      category_id,
      image_url,
      images,
      status,
      is_hot,
      is_recommend,
      sort_order
    } = req.body;

    if (!name || !price || !category_id || !image_url) {
      return res.status(400).json({
        success: false,
        message: '商品名称、价格、分类和图片不能为空'
      });
    }

    // 检查商品是否存在
    const existingProduct = await query('SELECT * FROM products WHERE id = ?', [id]);
    if (existingProduct.length === 0) {
      return res.status(404).json({
        success: false,
        message: '商品不存在'
      });
    }

    // 验证图片路径安全性
    const ProductImageUtils = require('../utils/productImageUtils');
    const safeImageUrl = ProductImageUtils.fixImagePath(image_url);
    if (!safeImageUrl) {
      return res.status(400).json({
        success: false,
        message: '商品图片路径格式不正确'
      });
    }

    // 如果更新了图片，删除旧图片
    if (safeImageUrl !== existingProduct[0].image_url && existingProduct[0].image_url) {
      const deleteResult = await deleteAndSync(existingProduct[0].image_url, 'products');
      if (!deleteResult.success) {
        console.warn('删除旧图片失败，但继续更新:', deleteResult.message);
      }
    }

    await query(
      `UPDATE products SET name = ?, description = ?, price = ?, original_price = ?, 
       stock = ?, category_id = ?, image_url = ?, main_image = ?, images = ?, status = ?, is_hot = ?, 
       is_recommend = ?, updated_at = NOW() WHERE id = ?`,
      [
        name,
        description || '',
        price,
        original_price || price,
        stock || 0,
        category_id,
        safeImageUrl,
        safeImageUrl, // main_image 和 image_url 保持一致
        JSON.stringify(images || []),
        status ? 1 : 0,
        is_hot ? 1 : 0,
        is_recommend ? 1 : 0,
        id
      ]
    );

    res.json({
      success: true,
      message: '商品更新成功'
    });
  } catch (error) {
    console.error('更新商品错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 更新商品 - 兼容前端API调用
router.put('/update/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      price,
      original_price,
      stock,
      category_id,
      main_image,
      image_url,
      images,
      status,
      is_hot,
      is_recommend,
      sort_order
    } = req.body;

    console.log('收到商品更新请求:', { id, body: req.body });

    // 兼容前端传递的main_image字段
    const finalImageUrl = main_image || image_url;

    if (!name || !price || !category_id || !finalImageUrl) {
      console.log('参数验证失败:', { name, price, category_id, finalImageUrl });
      return res.status(400).json({
        success: false,
        message: '商品名称、价格、分类和图片不能为空'
      });
    }

    // 检查商品是否存在
    const existingProduct = await query('SELECT * FROM products WHERE id = ?', [id]);
    if (existingProduct.length === 0) {
      return res.status(404).json({
        success: false,
        message: '商品不存在'
      });
    }

    // 如果更新了图片，删除旧图片
    if (finalImageUrl !== existingProduct[0].image_url && existingProduct[0].image_url) {
      const deleteResult = await deleteAndSync(existingProduct[0].image_url, 'products');
      if (!deleteResult.success) {
        console.warn('删除旧图片失败，但继续更新:', deleteResult.message);
      }
    }

    await query(
      `UPDATE products SET name = ?, description = ?, price = ?, original_price = ?, 
       stock = ?, category_id = ?, image_url = ?, main_image = ?, images = ?, status = ?, is_hot = ?, 
       is_recommend = ?, updated_at = NOW() WHERE id = ?`,
      [
        name,
        description || '',
        price,
        original_price || price,
        stock || 0,
        category_id,
        finalImageUrl,
        finalImageUrl, // main_image 和 image_url 保持一致
        JSON.stringify(images || []),
        status ? 1 : 0,
        is_hot ? 1 : 0,
        is_recommend ? 1 : 0,
        id
      ]
    );

    console.log('商品更新成功:', id);
    res.json({
      success: true,
      message: '商品更新成功'
    });
  } catch (error) {
    console.error('更新商品错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误: ' + error.message
    });
  }
});

// 删除商品
router.delete('/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;

    // 获取商品信息
    const product = await query('SELECT * FROM products WHERE id = ?', [id]);
    if (product.length === 0) {
      return res.status(404).json({
        success: false,
        message: '商品不存在'
      });
    }

    // 使用统一的删除工具删除图片
    if (product[0].image_url) {
      const deleteResult = await deleteAndSync(product[0].image_url, 'products');
      if (!deleteResult.success) {
        console.warn('删除图片失败，但继续删除数据库记录:', deleteResult.message);
      }
    }

    // 删除数据库记录
    await query('DELETE FROM products WHERE id = ?', [id]);

    res.json({
      success: true,
      message: '商品删除成功'
    });
  } catch (error) {
    console.error('删除商品错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 删除商品 - 兼容前端API调用
router.delete('/delete/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;

    console.log('收到商品删除请求:', id);

    // 获取商品信息
    const product = await query('SELECT * FROM products WHERE id = ?', [id]);
    if (product.length === 0) {
      return res.status(404).json({
        success: false,
        message: '商品不存在'
      });
    }

    // 使用统一的删除工具删除图片
    if (product[0].image_url) {
      const deleteResult = await deleteAndSync(product[0].image_url, 'products');
      if (!deleteResult.success) {
        console.warn('删除图片失败，但继续删除数据库记录:', deleteResult.message);
      }
    }

    // 删除数据库记录
    await query('DELETE FROM products WHERE id = ?', [id]);

    console.log('商品删除成功:', id);
    res.json({
      success: true,
      message: '商品删除成功'
    });
  } catch (error) {
    console.error('删除商品错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误: ' + error.message
    });
  }
});

// 获取热销商品 - 基于真实数据 (必须在 /:id 路由之前)
router.get('/hot', async (req, res) => {
  try {
    console.log('🔥 热销商品API被调用，参数:', req.query);
    const { limit = 8 } = req.query;

    console.log('📊 开始查询热销商品，限制数量:', limit);

    // 获取热销商品（按销量排序）
    const hotProducts = await query(`
      SELECT
        p.id,
        p.name,
        p.price,
        p.original_price,
        p.main_image,
        p.image_url,
        p.sales_count,
        p.stock,
        p.rating,
        p.review_count,
        c.name as category_name
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.status = 1
      ORDER BY COALESCE(p.sales_count, 0) DESC, p.id DESC
      LIMIT ${parseInt(limit)}
    `);

    console.log('📊 查询到热销商品数量:', hotProducts.length);

    // 处理图片URL
    const processedProducts = hotProducts.map(product => ({
      ...product,
      image_url: product.main_image || product.image_url || '/images/products/default.jpg',
      sales_count: product.sales_count || 0,
      rating: parseFloat(product.rating || 0),
      review_count: product.review_count || 0,
      price: parseFloat(product.price),
      original_price: product.original_price ? parseFloat(product.original_price) : null
    }));

    console.log('✅ 热销商品处理完成，返回数量:', processedProducts.length);

    res.json({
      success: true,
      data: processedProducts,
      total: processedProducts.length
    });
  } catch (error) {
    console.error('❌ 获取热销商品失败:', error);
    console.error('错误堆栈:', error.stack);
    res.status(500).json({
      success: false,
      message: '获取热销商品失败: ' + error.message
    });
  }
});

// 仪表板需要的产品统计接口 - 必须在 /:id 路由之前
router.get('/statistics', async (req, res) => {
  try {
    // 模拟产品统计数据
    const statistics = {
      totalProducts: 450,
      activeProducts: 420,
      inactiveProducts: 30,
      lowStockProducts: 15,
      outOfStockProducts: 8,
      hotProducts: 25,
      recommendProducts: 18
    };

    res.json({
      success: true,
      data: statistics
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取产品统计失败'
    });
  }
});

// 获取单个商品
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const products = await query(
      `SELECT p.*, c.name as category_name 
       FROM products p 
       LEFT JOIN categories c ON p.category_id = c.id 
       WHERE p.id = ?`,
      [id]
    );

    if (products.length === 0) {
      return res.status(404).json({
        success: false,
        message: '商品不存在'
      });
    }

    // 解析图片数组
    const product = products[0];
    try {
      product.images = JSON.parse(product.images || '[]');
    } catch (error) {
      product.images = [];
    }

    res.json({
      success: true,
      data: product
      });
  } catch (error) {
    console.error('获取商品错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 获取单个商品详情 - 兼容前端API调用
router.get('/detail/:id', async (req, res) => {
  try {
    const { id } = req.params;

    console.log('收到商品详情请求:', id);

    const products = await query(
      `SELECT p.*, c.name as category_name 
       FROM products p 
       LEFT JOIN categories c ON p.category_id = c.id 
       WHERE p.id = ?`,
      [id]
    );

    if (products.length === 0) {
      return res.status(404).json({
        success: false,
        message: '商品不存在'
      });
    }

    // 解析图片数组
    const product = products[0];
    try {
      product.images = JSON.parse(product.images || '[]');
    } catch (error) {
      product.images = [];
    }

    console.log('商品详情查询成功:', product.name);
    res.json({
      success: true,
      data: product
    });
  } catch (error) {
    console.error('获取商品详情错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误: ' + error.message
    });
  }
});

// 获取热门商品
router.get('/hot/list', async (req, res) => {
  try {
    const products = await query(
      `SELECT p.*, c.name as category_name 
       FROM products p 
       LEFT JOIN categories c ON p.category_id = c.id 
       WHERE p.is_hot = 1 AND p.status = 1 
       ORDER BY p.sort_order ASC, p.created_at DESC 
       LIMIT 10`
    );

    res.json({
      success: true,
      data: products
    });
  } catch (error) {
    console.error('获取热门商品错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 获取推荐商品
router.get('/recommend/list', async (req, res) => {
  try {
    const products = await query(
      `SELECT p.*, c.name as category_name 
       FROM products p 
       LEFT JOIN categories c ON p.category_id = c.id 
       WHERE p.is_recommend = 1 AND p.status = 1 
       ORDER BY p.sort_order ASC, p.created_at DESC 
       LIMIT 10`
    );

    res.json({
      success: true,
      data: products
    });
  } catch (error) {
    console.error('获取推荐商品错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 商品图片上传 - 使用express-fileupload
router.post('/upload', requireAuth, async (req, res) => {
  console.log('[唯一调试] product upload 被调用');
  try {
    if (!req.files || !req.files.file) {
      console.log('[唯一调试] product 没有文件上传');
      return res.status(400).json({ code: 1, msg: '未上传文件' });
    }
    
    const file = req.files.file;
    console.log('[唯一调试] product 文件信息:', {
      originalname: file.originalname,
      filename: file.filename,
      path: file.path,
      size: file.size
    });
    
    // 检查文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.mimetype)) {
      return res.status(400).json({ 
        code: 1, 
        msg: '不支持的文件类型，只支持 JPG、PNG、GIF、WebP 格式' 
      });
    }
    
    // 检查文件大小（5MB限制）
    if (file.size > 5 * 1024 * 1024) {
      return res.status(400).json({ 
        code: 1, 
        msg: '文件大小超过限制（最大5MB）' 
      });
    }
    
    // 直接上传到 mall-server 的实际目录
    const uploadDir = path.join(__dirname, '../../mall-server/uploads/products');
    console.log('[唯一调试] mall-server product 目录:', uploadDir);
    if (!fs.existsSync(uploadDir)) {
      console.log('[唯一调试] 目录不存在，创建目录');
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    // 生成安全的文件名：时间戳_随机数_原扩展名
    const ext = path.extname(file.name);
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    const filename = `product_${timestamp}_${random}${ext}`;
    const filepath = path.join(uploadDir, filename);
    console.log('[唯一调试] 目标文件路径:', filepath);
    
    file.mv(filepath, err => {
      console.log('[唯一调试] product file.mv 回调，err:', err);
      if (err) {
        console.error('[唯一调试] product 文件保存失败:', err);
        return res.status(500).json({ code: 1, msg: '上传失败' });
      }
      console.log('[唯一调试] product 文件已保存到:', filepath);
      const url = '/uploads/products/' + filename;
      console.log('[唯一调试] product 返回URL:', url);
      res.json({ code: 0, msg: '上传成功', data: { url } });
    });
  } catch (error) {
    console.error('[唯一调试] product 上传异常:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
});

// 批量删除商品
router.post('/batch-delete', requireAuth, async (req, res) => {
  try {
    const { ids } = req.body;
    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ success: false, message: '未选择要删除的商品' });
    }
    
    // 获取商品信息并删除图片
    const products = await query('SELECT image_url FROM products WHERE id IN (?)', [ids]);
    for (const product of products) {
      if (product.image_url) {
        const deleteResult = await deleteAndSync(product.image_url, 'products');
        if (!deleteResult.success) {
          console.warn('批量删除时删除图片失败，但继续删除数据库记录:', deleteResult.message);
        }
      }
    }
    
    // 删除数据库记录
    await query(`DELETE FROM products WHERE id IN (${ids.map(() => '?').join(',')})`, ids);
    res.json({ success: true, message: `成功删除${ids.length}个商品` });
  } catch (error) {
    console.error('批量删除商品错误:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

// 批量导入商品（CSV）
const csv = require('csv-parser');

router.post('/batch-import', requireAuth, async (req, res) => {
  try {
    if (!req.files || !req.files.file) {
      return res.status(400).json({ success: false, message: '未上传文件' });
    }
    
    const file = req.files.file;
    
    // 检查文件类型
    if (!file.originalname.endsWith('.csv')) {
      return res.status(400).json({ success: false, message: '只支持CSV文件' });
    }
    
    // 创建临时目录
    const tempDir = path.join(__dirname, '../uploads/temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    const tempPath = path.join(tempDir, `batch-import-${Date.now()}.csv`);
    
    // 保存文件到临时目录
    file.mv(tempPath, async (err) => {
      if (err) {
        return res.status(500).json({ success: false, message: '文件保存失败' });
      }
      
      try {
        const results = [];
        const fs = require('fs');
        fs.createReadStream(tempPath)
          .pipe(csv())
          .on('data', (data) => results.push(data))
          .on('end', async () => {
            let successCount = 0;
            for (const row of results) {
              // 字段映射
              const name = row['商品名称'] || row['name'];
              const description = row['商品描述'] || '';
              const price = parseFloat(row['价格'] || row['price'] || 0);
              const original_price = parseFloat(row['原价'] || row['original_price'] || price);
              const stock = parseInt(row['库存'] || row['stock'] || 0);
              const category_id = parseInt(row['分类ID'] || row['category_id'] || 0);
              const status = parseInt(row['状态'] || row['status'] || 1);
              const is_hot = parseInt(row['热门'] || row['is_hot'] || 0);
              const is_recommend = parseInt(row['推荐'] || row['is_recommend'] || 0);
              const sort_order = parseInt(row['排序'] || row['sort_order'] || 0);
              if (!name || !price || !category_id) continue;
              await query(
                `INSERT INTO products (name, description, price, original_price, stock, category_id, image_url, images, status, is_hot, is_recommend, sort_order, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, '', '[]', ?, ?, ?, ?, NOW(), NOW())`,
                [name, description, price, original_price, stock, category_id, status, is_hot, is_recommend, sort_order]
              );
              successCount++;
            }
            // 删除临时文件
            if (fs.existsSync(tempPath)) {
              fs.unlinkSync(tempPath);
            }
            res.json({ success: true, message: `成功导入${successCount}个商品`, data: { count: successCount } });
          });
      } catch (error) {
        // 删除临时文件
        if (fs.existsSync(tempPath)) {
          fs.unlinkSync(tempPath);
        }
        console.error('批量导入商品错误:', error);
        res.status(500).json({ success: false, message: '服务器错误' });
      }
    });
  } catch (error) {
    console.error('批量导入商品错误:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

// 商品图片验证接口
router.post('/validate-images', requireAuth, async (req, res) => {
  try {
    const ProductImageUtils = require('../utils/productImageUtils');
    
    // 获取所有商品
    const products = await query('SELECT id, name, image_url, main_image FROM products');
    
    // 验证图片路径
    const validationResults = await ProductImageUtils.validateProductImages(products);
    
    res.json({
      success: true,
      data: {
        total: products.length,
        valid: validationResults.valid.length,
        invalid: validationResults.invalid.length,
        missing: validationResults.missing.length,
        fixed: validationResults.fixed.length,
        details: validationResults
      },
      message: '商品图片验证完成'
    });
  } catch (error) {
    console.error('商品图片验证错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 商品图片批量修复接口
router.post('/fix-images', requireAuth, async (req, res) => {
  try {
    const ProductImageUtils = require('../utils/productImageUtils');
    
    // 获取所有商品
    const products = await query('SELECT id, name, image_url, main_image FROM products');
    
    // 验证图片路径
    const validationResults = await ProductImageUtils.validateProductImages(products);
    
    let fixedCount = 0;
    let defaultCount = 0;
    
    // 修复可修复的路径
    for (const item of validationResults.fixed) {
      try {
        await query(
          'UPDATE products SET image_url = ?, main_image = ? WHERE id = ?',
          [item.fixed, item.fixed, item.id]
        );
        fixedCount++;
      } catch (error) {
        console.error(`修复商品 "${item.name}" 失败:`, error.message);
      }
    }
    
    // 为缺失和无效的商品设置默认图片
    const itemsToFix = [...validationResults.missing, ...validationResults.invalid];
    for (const item of itemsToFix) {
      try {
        const defaultPath = ProductImageUtils.getDefaultImagePath();
        await query(
          'UPDATE products SET image_url = ?, main_image = ? WHERE id = ?',
          [defaultPath, defaultPath, item.id]
        );
        defaultCount++;
      } catch (error) {
        console.error(`为商品 "${item.name}" 设置默认图片失败:`, error.message);
      }
    }
    
    res.json({
      success: true,
      data: {
        total: products.length,
        fixed: fixedCount,
        defaultSet: defaultCount,
        summary: {
          valid: validationResults.valid.length,
          invalid: validationResults.invalid.length,
          missing: validationResults.missing.length,
          fixed: validationResults.fixed.length
        }
      },
      message: `批量修复完成：修复 ${fixedCount} 个路径，设置 ${defaultCount} 个默认图片`
    });
  } catch (error) {
    console.error('商品图片批量修复错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 测试路由 - 检查数据库连接
router.get('/test-hot', async (req, res) => {
  try {
    console.log('测试热销商品路由被调用');

    // 先检查数据库连接
    const testQuery = await query('SELECT COUNT(*) as count FROM products');
    console.log('商品总数:', testQuery[0].count);

    // 检查活跃商品
    const activeProducts = await query('SELECT COUNT(*) as count FROM products WHERE status = 1');
    console.log('活跃商品数:', activeProducts[0].count);

    // 获取前5个商品
    const products = await query('SELECT id, name, price, sales_count FROM products LIMIT 5');
    console.log('前5个商品:', products);

    res.json({
      success: true,
      data: {
        totalProducts: testQuery[0].count,
        activeProducts: activeProducts[0].count,
        sampleProducts: products
      }
    });
  } catch (error) {
    console.error('测试路由错误:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

module.exports = router;
