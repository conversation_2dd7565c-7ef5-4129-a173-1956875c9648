const express = require('express');
const router = express.Router();
const rechargeController = require('../controllers/rechargeController');

// 获取充值记录列表
router.get('/list', rechargeController.list);

// 获取充值记录详情
router.get('/detail/:id', rechargeController.detail);

// 后台充值
router.post('/admin-recharge', rechargeController.adminRecharge);

// 更新充值记录
router.put('/update/:id', rechargeController.update);

// 删除充值记录
router.delete('/delete/:id', rechargeController.delete);

// 更新支付状态
router.put('/payment-status/:id', rechargeController.updatePaymentStatus);

// 获取充值统计数据
router.get('/statistics', rechargeController.statistics);

// 获取余额变动记录
router.get('/balance-records', rechargeController.balanceRecords);

// 后台调整余额
router.post('/adjust-balance', rechargeController.adjustBalance);

module.exports = router;
