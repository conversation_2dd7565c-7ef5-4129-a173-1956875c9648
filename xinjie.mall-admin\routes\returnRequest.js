const express = require('express');
const router = express.Router();
const { query } = require('../src/config/database');
const { requireAuth } = require('../middleware/auth');

// 退货状态映射
const RETURN_STATUS = {
  PENDING: 0,        // 待审核
  APPROVED: 1,       // 审核通过
  REJECTED: 2,       // 审核拒绝
  WAIT_RETURN: 3,    // 待寄回
  RETURNED: 4,       // 已寄回
  INSPECTING: 5,     // 验收中
  INSPECT_PASS: 6,   // 验收通过
  INSPECT_FAIL: 7,   // 验收不通过
  REFUNDED: 8,       // 退款完成
  CANCELLED: 9       // 已取消
};

const RETURN_STATUS_TEXT = {
  [RETURN_STATUS.PENDING]: '待审核',
  [RETURN_STATUS.APPROVED]: '审核通过',
  [RETURN_STATUS.REJECTED]: '审核拒绝',
  [RETURN_STATUS.WAIT_RETURN]: '待寄回',
  [RETURN_STATUS.RETURNED]: '已寄回',
  [RETURN_STATUS.INSPECTING]: '验收中',
  [RETURN_STATUS.INSPECT_PASS]: '验收通过',
  [RETURN_STATUS.INSPECT_FAIL]: '验收不通过',
  [RETURN_STATUS.REFUNDED]: '退款完成',
  [RETURN_STATUS.CANCELLED]: '已取消'
};

// 生成退货单号
function generateReturnNo() {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `RT${timestamp}${random}`;
}

// 生成退款单号
function generateRefundNo() {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `RF${timestamp}${random}`;
}

// 添加状态日志
async function addStatusLog(returnId, status, options = {}) {
  const {
    operatorType = 3, // 系统
    operatorId = null,
    operatorName = '系统',
    remark = ''
  } = options;

  await query(`
    INSERT INTO return_status_logs 
    (return_id, status, status_text, operator_type, operator_id, operator_name, remark)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `, [returnId, status, RETURN_STATUS_TEXT[status], operatorType, operatorId, operatorName, remark]);
}



// 获取退货申请列表
router.get('/', requireAuth, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      returnNo = '',
      orderNo = '',
      startDate = '',
      endDate = ''
    } = req.query;

    // 确保分页参数是数字类型
    const pageNum = parseInt(page) || 1;
    const limitNum = parseInt(limit) || 10;
    const offset = (pageNum - 1) * limitNum;

    console.log('分页参数调试:', {
      原始page: page, 原始limit: limit,
      pageNum, limitNum, offset,
      类型检查: { pageNum: typeof pageNum, limitNum: typeof limitNum, offset: typeof offset }
    });

    let whereClause = 'WHERE 1=1';
    const queryParams = [];

    if (status !== undefined && status !== '') {
      whereClause += ' AND rr.status = ?';
      queryParams.push(parseInt(status));
    }

    if (returnNo) {
      whereClause += ' AND rr.return_no LIKE ?';
      queryParams.push(`%${returnNo}%`);
    }

    if (orderNo) {
      whereClause += ' AND rr.order_no LIKE ?';
      queryParams.push(`%${orderNo}%`);
    }

    if (startDate) {
      whereClause += ' AND DATE(rr.created_at) >= ?';
      queryParams.push(startDate);
    }

    if (endDate) {
      whereClause += ' AND DATE(rr.created_at) <= ?';
      queryParams.push(endDate);
    }

    // 获取总数
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM return_requests rr 
      LEFT JOIN users u ON rr.user_id = u.id 
      ${whereClause}
    `;
    const countResult = await query(countQuery, queryParams);
    const total = countResult[0].total;

    // 获取分页数据 - 使用字符串拼接避免参数绑定问题
    const dataQuery = `
      SELECT
        rr.*,
        u.nickname, u.phone,
        o.total_amount, o.pay_amount
      FROM return_requests rr
      LEFT JOIN users u ON rr.user_id = u.id
      LEFT JOIN orders o ON rr.order_id = o.id
      ${whereClause}
      ORDER BY rr.created_at DESC
      LIMIT ${limitNum} OFFSET ${offset}
    `;

    console.log('SQL查询调试:', {
      whereClause,
      queryParams,
      limitNum,
      offset,
      finalQuery: dataQuery
    });

    const list = await query(dataQuery, queryParams);

    // 获取状态统计
    const statusStats = await query(`
      SELECT 
        status,
        COUNT(*) as count,
        SUM(return_amount) as total_amount
      FROM return_requests 
      GROUP BY status
    `);

    res.json({
      success: true,
      data: {
        list: list.map(item => ({
          ...item,
          user: {
            nickname: item.nickname,
            phone: item.phone
          }
        })),
        total: total,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(total / limitNum),
        statusStats
      }
    });
  } catch (error) {
    console.error('获取退货申请列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取退货申请列表失败',
      error: error.message
    });
  }
});

// 获取退货申请详情
router.get('/detail/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;

    // 获取退货申请基本信息
    const returnRequests = await query(`
      SELECT
        rr.*,
        u.nickname, u.phone,
        o.total_amount, o.pay_amount, o.delivery_time
      FROM return_requests rr
      LEFT JOIN users u ON rr.user_id = u.id
      LEFT JOIN orders o ON rr.order_id = o.id
      WHERE rr.id = ?
    `, [id]);

    if (returnRequests.length === 0) {
      return res.status(404).json({
        success: false,
        message: '退货申请不存在'
      });
    }

    const returnRequest = returnRequests[0];

    // 获取退货商品明细
    const returnItems = await query(`
      SELECT 
        ri.*,
        p.name as product_name, p.main_image as product_image,
        oi.quantity as order_quantity, oi.price as order_price
      FROM return_items ri
      LEFT JOIN products p ON ri.product_id = p.id
      LEFT JOIN order_items oi ON ri.order_item_id = oi.id
      WHERE ri.return_id = ?
    `, [id]);

    // 获取状态日志
    const statusLogs = await query(`
      SELECT * FROM return_status_logs 
      WHERE return_id = ? 
      ORDER BY created_at ASC
    `, [id]);

    // 获取退款记录
    const refundRecords = await query(`
      SELECT * FROM refund_records 
      WHERE return_id = ?
    `, [id]);

    res.json({
      success: true,
      data: {
        ...returnRequest,
        user: {
          nickname: returnRequest.nickname,
          phone: returnRequest.phone
        },
        order: {
          total_amount: returnRequest.total_amount,
          pay_amount: returnRequest.pay_amount,
          delivery_time: returnRequest.delivery_time
        },
        returnItems,
        statusLogs,
        refundRecords
      }
    });
  } catch (error) {
    console.error('获取退货申请详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取退货申请详情失败',
      error: error.message
    });
  }
});

// 审核退货申请
router.put('/approve/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { approved, refuseReason, adminRemark } = req.body;
    const adminId = req.user?.id || 1;
    const adminName = req.user?.username || '管理员';

    // 检查退货申请是否存在
    const returnRequests = await query('SELECT * FROM return_requests WHERE id = ?', [id]);
    if (returnRequests.length === 0) {
      return res.status(404).json({
        success: false,
        message: '退货申请不存在'
      });
    }

    const returnRequest = returnRequests[0];
    if (returnRequest.status !== RETURN_STATUS.PENDING) {
      return res.status(400).json({
        success: false,
        message: '该申请已处理，无法重复操作'
      });
    }

    const newStatus = approved ? RETURN_STATUS.APPROVED : RETURN_STATUS.REJECTED;
    
    // 更新退货申请状态
    await query(`
      UPDATE return_requests 
      SET status = ?, admin_remark = ?, refuse_reason = ?, updated_at = NOW()
      WHERE id = ?
    `, [newStatus, adminRemark, approved ? null : refuseReason, id]);

    // 记录状态日志
    await addStatusLog(id, newStatus, {
      operatorType: 2, // 管理员
      operatorId: adminId,
      operatorName: adminName,
      remark: approved ? '审核通过' : `审核拒绝：${refuseReason}`
    });

    res.json({
      success: true,
      message: approved ? '审核通过' : '审核拒绝'
    });
  } catch (error) {
    console.error('审核退货申请失败:', error);
    res.status(500).json({
      success: false,
      message: '审核退货申请失败',
      error: error.message
    });
  }
});

// 确认收货
router.put('/receive/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { remark } = req.body;
    const adminId = req.user?.id || 1;
    const adminName = req.user?.username || '管理员';

    // 检查退货申请状态
    const returnRequests = await query('SELECT * FROM return_requests WHERE id = ?', [id]);
    if (returnRequests.length === 0) {
      return res.status(404).json({
        success: false,
        message: '退货申请不存在'
      });
    }

    const returnRequest = returnRequests[0];
    if (returnRequest.status !== RETURN_STATUS.RETURNED) {
      return res.status(400).json({
        success: false,
        message: '当前状态不允许确认收货'
      });
    }

    // 更新状态
    await query(`
      UPDATE return_requests 
      SET status = ?, receive_time = NOW(), admin_remark = ?, updated_at = NOW()
      WHERE id = ?
    `, [RETURN_STATUS.INSPECTING, remark, id]);

    // 记录状态日志
    await addStatusLog(id, RETURN_STATUS.INSPECTING, {
      operatorType: 2,
      operatorId: adminId,
      operatorName: adminName,
      remark: '确认收货，开始验收'
    });

    res.json({
      success: true,
      message: '确认收货成功'
    });
  } catch (error) {
    console.error('确认收货失败:', error);
    res.status(500).json({
      success: false,
      message: '确认收货失败',
      error: error.message
    });
  }
});

// 验收商品
router.put('/inspect/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { inspectResult, inspectRemark } = req.body;
    const adminId = req.user?.id || 1;
    const adminName = req.user?.username || '管理员';

    // 检查退货申请状态
    const returnRequests = await query('SELECT * FROM return_requests WHERE id = ?', [id]);
    if (returnRequests.length === 0) {
      return res.status(404).json({
        success: false,
        message: '退货申请不存在'
      });
    }

    const returnRequest = returnRequests[0];
    if (returnRequest.status !== RETURN_STATUS.INSPECTING) {
      return res.status(400).json({
        success: false,
        message: '当前状态不允许验收'
      });
    }

    const newStatus = inspectResult ? RETURN_STATUS.INSPECT_PASS : RETURN_STATUS.INSPECT_FAIL;

    // 更新状态
    await query(`
      UPDATE return_requests
      SET status = ?, inspect_time = NOW(), admin_remark = ?, updated_at = NOW()
      WHERE id = ?
    `, [newStatus, inspectRemark, id]);

    // 记录状态日志
    await addStatusLog(id, newStatus, {
      operatorType: 2,
      operatorId: adminId,
      operatorName: adminName,
      remark: inspectResult ? '验收通过' : `验收不通过：${inspectRemark}`
    });

    res.json({
      success: true,
      message: inspectResult ? '验收通过' : '验收不通过'
    });
  } catch (error) {
    console.error('验收商品失败:', error);
    res.status(500).json({
      success: false,
      message: '验收商品失败',
      error: error.message
    });
  }
});

// 处理退款
router.put('/refund/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { refundType = 2, refundAmount, remark } = req.body;
    const adminId = req.user?.id || 1;
    const adminName = req.user?.username || '管理员';

    // 检查退货申请状态
    const returnRequests = await query('SELECT * FROM return_requests WHERE id = ?', [id]);
    if (returnRequests.length === 0) {
      return res.status(404).json({
        success: false,
        message: '退货申请不存在'
      });
    }

    const returnRequest = returnRequests[0];
    if (returnRequest.status !== RETURN_STATUS.INSPECT_PASS) {
      return res.status(400).json({
        success: false,
        message: '当前状态不允许退款'
      });
    }

    const finalRefundAmount = refundAmount || returnRequest.return_amount;

    // 创建退款记录
    const refundNo = generateRefundNo();
    await query(`
      INSERT INTO refund_records
      (refund_no, return_id, order_id, user_id, refund_amount, refund_type, refund_status,
       refund_time, success_time, operator_id, operator_name, remark)
      VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), ?, ?, ?)
    `, [refundNo, id, returnRequest.order_id, returnRequest.user_id, finalRefundAmount,
        refundType, 2, adminId, adminName, remark]);

    // 更新退货申请状态
    await query(`
      UPDATE return_requests
      SET status = ?, refund_time = NOW(), admin_remark = ?, updated_at = NOW()
      WHERE id = ?
    `, [RETURN_STATUS.REFUNDED, remark, id]);

    // 记录状态日志
    await addStatusLog(id, RETURN_STATUS.REFUNDED, {
      operatorType: 2,
      operatorId: adminId,
      operatorName: adminName,
      remark: `退款完成，金额：${finalRefundAmount}元`
    });

    res.json({
      success: true,
      message: '退款处理成功'
    });
  } catch (error) {
    console.error('处理退款失败:', error);
    res.status(500).json({
      success: false,
      message: '处理退款失败',
      error: error.message
    });
  }
});

// 获取退货统计数据
router.get('/statistics', requireAuth, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    let whereClause = 'WHERE 1=1';
    const queryParams = [];

    if (startDate) {
      whereClause += ' AND DATE(created_at) >= ?';
      queryParams.push(startDate);
    }

    if (endDate) {
      whereClause += ' AND DATE(created_at) <= ?';
      queryParams.push(endDate);
    }

    // 状态统计
    const statusStats = await query(`
      SELECT
        status,
        COUNT(*) as count,
        SUM(return_amount) as total_amount
      FROM return_requests
      ${whereClause}
      GROUP BY status
    `, queryParams);

    // 总体统计
    const totalStats = await query(`
      SELECT
        COUNT(*) as total_count,
        SUM(return_amount) as total_amount,
        AVG(return_amount) as avg_amount
      FROM return_requests
      ${whereClause}
    `, queryParams);

    res.json({
      success: true,
      data: {
        statusStats,
        totalStats: totalStats[0] || {}
      }
    });
  } catch (error) {
    console.error('获取退货统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取退货统计失败',
      error: error.message
    });
  }
});

// 批量处理退货申请
router.post('/batch', requireAuth, async (req, res) => {
  try {
    const { returnIds, action, data } = req.body;
    const adminId = req.user?.id || 1;
    const adminName = req.user?.username || '管理员';

    if (!returnIds || !Array.isArray(returnIds) || returnIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请选择要处理的退货申请'
      });
    }

    const results = [];

    for (const returnId of returnIds) {
      try {
        if (action === 'approve') {
          await query(`
            UPDATE return_requests
            SET status = ?, admin_remark = ?, updated_at = NOW()
            WHERE id = ? AND status = ?
          `, [RETURN_STATUS.APPROVED, data.remark || '批量审核通过', returnId, RETURN_STATUS.PENDING]);

          await addStatusLog(returnId, RETURN_STATUS.APPROVED, {
            operatorType: 2,
            operatorId: adminId,
            operatorName: adminName,
            remark: '批量审核通过'
          });

          results.push({ returnId, success: true });
        } else if (action === 'reject') {
          await query(`
            UPDATE return_requests
            SET status = ?, refuse_reason = ?, admin_remark = ?, updated_at = NOW()
            WHERE id = ? AND status = ?
          `, [RETURN_STATUS.REJECTED, data.refuseReason || '批量拒绝',
              data.remark || '批量审核拒绝', returnId, RETURN_STATUS.PENDING]);

          await addStatusLog(returnId, RETURN_STATUS.REJECTED, {
            operatorType: 2,
            operatorId: adminId,
            operatorName: adminName,
            remark: `批量审核拒绝：${data.refuseReason || '批量拒绝'}`
          });

          results.push({ returnId, success: true });
        }
      } catch (error) {
        results.push({ returnId, success: false, error: error.message });
      }
    }

    res.json({
      success: true,
      message: '批量处理完成',
      data: results
    });
  } catch (error) {
    console.error('批量处理退货申请失败:', error);
    res.status(500).json({
      success: false,
      message: '批量处理退货申请失败',
      error: error.message
    });
  }
});

module.exports = router;
