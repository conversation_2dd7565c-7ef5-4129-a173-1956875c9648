const express = require('express');
const { requireAuth, requirePermission } = require('../middleware/auth');
const roleController = require('../controllers/roleController');

const router = express.Router();

router.get(
  '/list',
  requireAuth,
  requirePermission('role:list'),
  roleController.list
);
router.get(
  '/detail/:id',
  requireAuth,
  requirePermission('role:detail'),
  roleController.detail
);
router.post(
  '/create',
  requireAuth,
  requirePermission('role:create'),
  roleController.create
);
router.put(
  '/update/:id',
  requireAuth,
  requirePermission('role:update'),
  roleController.update
);
router.delete(
  '/delete/:id',
  requireAuth,
  requirePermission('role:delete'),
  roleController.delete
);
router.get(
  '/:id/permissions',
  requireAuth,
  requirePermission('role:permission:list'),
  roleController.getPermissions
);
router.post(
  '/:id/permissions',
  requireAuth,
  requirePermission('role:permission:set'),
  roleController.setPermissions
);

module.exports = router;
