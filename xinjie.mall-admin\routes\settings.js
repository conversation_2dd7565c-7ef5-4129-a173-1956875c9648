const express = require('express');
const router = express.Router();
const settingsController = require('../controllers/settingsController');

router.get('/basic', settingsController.getBasic);
router.put('/basic', settingsController.updateBasic);
router.get('/payment', settingsController.getPayment);
router.put('/payment', settingsController.updatePayment);
router.get('/shipping', settingsController.getShipping);
router.put('/shipping', settingsController.updateShipping);
router.get('/sms', settingsController.getSms);
router.put('/sms', settingsController.updateSms);
router.get('/email', settingsController.getEmail);
router.put('/email', settingsController.updateEmail);

module.exports = router;
