const express = require('express');
const router = express.Router();
const { requireAuth } = require('../middleware/auth');
const { query } = require('../src/config/database');

// 获取系统通知
router.get('/notifications', requireAuth, async (req, res) => {
  try {
    const { limit = 10 } = req.query;

    // 基于真实数据生成通知
    const notifications = [];

    // 1. 检查待处理订单
    const pendingOrders = await query(
      'SELECT COUNT(*) as count FROM orders WHERE order_status = 1'
    );
    if (pendingOrders[0].count > 0) {
      notifications.push({
        id: 1,
        title: '新订单提醒',
        content: `您有${pendingOrders[0].count}个新订单待处理`,
        type: 'info',
        isRead: false,
        createdAt: new Date(Date.now() - 30 * 60 * 1000)
      });
    }

    // 2. 检查库存不足的商品
    const lowStockProducts = await query(
      'SELECT name, stock FROM products WHERE stock < 10 AND status = 1 LIMIT 3'
    );
    if (lowStockProducts.length > 0) {
      const productNames = lowStockProducts.map(p => p.name).join('、');
      notifications.push({
        id: 2,
        title: '库存预警',
        content: `${productNames} 等商品库存不足，请及时补货`,
        type: 'warning',
        isRead: false,
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
      });
    }

    // 3. 今日销售统计
    const todaySales = await query(
      'SELECT COUNT(*) as orders, COALESCE(SUM(total_amount), 0) as amount FROM orders WHERE DATE(created_at) = CURDATE() AND order_status >= 2'
    );
    if (todaySales[0].orders > 0) {
      notifications.push({
        id: 3,
        title: '今日销售报告',
        content: `今日已完成${todaySales[0].orders}个订单，销售额￥${todaySales[0].amount}`,
        type: 'success',
        isRead: true,
        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000)
      });
    }

    // 4. 系统维护通知（固定）
    notifications.push({
      id: 4,
      title: '系统维护通知',
      content: '系统运行正常，所有功能可正常使用',
      type: 'success',
      isRead: true,
      createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000)
    });

    const limitedNotifications = notifications.slice(0, parseInt(limit));

    res.json({
      success: true,
      data: limitedNotifications,
      total: notifications.length,
      unreadCount: notifications.filter(n => !n.isRead).length
    });
  } catch (error) {
    console.error('获取系统通知失败:', error);
    res.status(500).json({
      success: false,
      message: '获取系统通知失败'
    });
  }
});

// 获取系统状态
router.get('/status', requireAuth, async (req, res) => {
  try {
    // 获取数据库统计信息
    const dbStats = await Promise.all([
      query('SELECT COUNT(*) as count FROM users'),
      query('SELECT COUNT(*) as count FROM orders'),
      query('SELECT COUNT(*) as count FROM products'),
      query('SELECT COUNT(*) as count FROM admin_users'),
      query('SHOW STATUS LIKE "Threads_connected"'),
      query('SHOW VARIABLES LIKE "max_connections"')
    ]);

    const userCount = dbStats[0][0].count;
    const orderCount = dbStats[1][0].count;
    const productCount = dbStats[2][0].count;
    const adminCount = dbStats[3][0].count;
    const currentConnections = parseInt(dbStats[4][0].Value);
    const maxConnections = parseInt(dbStats[5][0].Value);

    // 计算系统运行时间（模拟）
    const startTime = new Date('2024-01-01');
    const now = new Date();
    const uptime = Math.floor((now - startTime) / (1000 * 60 * 60 * 24));

    const systemStatus = {
      server: {
        status: 'healthy',
        uptime: `${uptime}天`,
        cpu: Math.round((Math.random() * 30 + 20) * 100) / 100, // 20-50%
        memory: Math.round((Math.random() * 40 + 40) * 100) / 100, // 40-80%
        disk: Math.round((Math.random() * 20 + 20) * 100) / 100 // 20-40%
      },
      database: {
        status: 'healthy',
        connections: currentConnections,
        maxConnections: maxConnections,
        responseTime: Math.round(Math.random() * 10 + 5), // 5-15ms
        tables: {
          users: userCount,
          orders: orderCount,
          products: productCount,
          admins: adminCount
        }
      },
      cache: {
        status: 'healthy',
        hitRate: Math.round((Math.random() * 10 + 85) * 100) / 100, // 85-95%
        memoryUsage: Math.round(Math.random() * 200 + 100), // 100-300MB
        maxMemory: 512
      },
      services: [
        { name: 'API服务', status: 'running', port: 8081 },
        { name: '前端服务', status: 'running', port: 4000 },
        { name: '数据库', status: 'running', port: 3306 },
        { name: 'Redis缓存', status: 'warning', port: 6379 } // Redis可能未配置
      ],
      lastUpdate: new Date()
    };

    res.json({
      success: true,
      data: systemStatus
    });
  } catch (error) {
    console.error('获取系统状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取系统状态失败'
    });
  }
});

// 标记通知为已读
router.put('/notifications/:id/read', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    
    // 这里应该更新数据库中的通知状态
    // 暂时返回成功响应
    
    res.json({
      success: true,
      message: '通知已标记为已读'
    });
  } catch (error) {
    console.error('标记通知已读失败:', error);
    res.status(500).json({
      success: false,
      message: '标记通知已读失败'
    });
  }
});

// 清空所有通知
router.delete('/notifications', requireAuth, async (req, res) => {
  try {
    // 这里应该清空数据库中的通知
    // 暂时返回成功响应
    
    res.json({
      success: true,
      message: '所有通知已清空'
    });
  } catch (error) {
    console.error('清空通知失败:', error);
    res.status(500).json({
      success: false,
      message: '清空通知失败'
    });
  }
});

module.exports = router;
