const express = require('express');
const path = require('path');
const fs = require('fs-extra');
const { requireAuth } = require('../middleware/auth');
const { syncImageToMallServer } = require('../utils/syncImages');
const { deleteAndSync } = require('../utils/deleteAndSync');

const router = express.Router();

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../public/uploads');
console.log('上传目录:', uploadDir);

try {
  fs.ensureDirSync(uploadDir);
  fs.ensureDirSync(path.join(uploadDir, 'banners'));
  fs.ensureDirSync(path.join(uploadDir, 'products'));
  fs.ensureDirSync(path.join(uploadDir, 'categories'));
  console.log('上传目录创建成功');
} catch (error) {
  console.error('创建上传目录失败:', error);
}



// 上传单张图片 - 使用express-fileupload
router.post('/image', requireAuth, async (req, res) => {
  try {
    if (!req.files || !req.files.image) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的图片'
      });
    }

    const file = req.files.image;
    const type = req.body.type || 'temp';

    // 检查文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.mimetype)) {
      return res.status(400).json({
        success: false,
        message: '不支持的文件类型，只支持 JPG、PNG、GIF、WebP 格式'
      });
    }

    // 检查文件大小
    if (file.size > 5 * 1024 * 1024) {
      return res.status(400).json({
        success: false,
        message: '文件大小超过限制（最大5MB）'
      });
    }

    // 生成文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.name);
    const filename = `${type}-${uniqueSuffix}${ext}`;
    const uploadPath = path.join(uploadDir, type, filename);

    // 确保目录存在
    fs.ensureDirSync(path.dirname(uploadPath));

    // 移动文件
    await file.mv(uploadPath);

    const imageUrl = `/uploads/${type}/${filename}`;

    // 同步图片到mall-server
    const relativePath = `${type}/${filename}`;
    syncImageToMallServer(relativePath);

    res.json({
      success: true,
      message: '图片上传成功',
      data: {
        url: imageUrl,
        filename: filename,
        originalname: file.name,
        size: file.size,
        mimetype: file.mimetype
      }
    });
  } catch (error) {
    console.error('图片上传错误:', error);
    res.status(500).json({
      success: false,
      message: '图片上传失败: ' + error.message
    });
  }
});

// 上传多张图片 - 使用express-fileupload
router.post('/images', requireAuth, async (req, res) => {
  try {
    if (!req.files || Object.keys(req.files).length === 0) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的图片'
      });
    }

    const type = req.body.type || 'temp';
    const uploadedFiles = [];
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

    // 处理多个文件
    const files = Array.isArray(req.files.images) ? req.files.images : [req.files.images];

    for (const file of files) {
      // 检查文件类型
      if (!allowedTypes.includes(file.mimetype)) {
        continue; // 跳过不支持的文件类型
      }

      // 检查文件大小
      if (file.size > 5 * 1024 * 1024) {
        continue; // 跳过过大的文件
      }

      // 生成文件名
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
      const ext = path.extname(file.name);
      const filename = `${type}-${uniqueSuffix}${ext}`;
      const uploadPath = path.join(uploadDir, type, filename);

      // 确保目录存在
      fs.ensureDirSync(path.dirname(uploadPath));

      // 移动文件
      await file.mv(uploadPath);

      const imageUrl = `/uploads/${type}/${filename}`;

      // 同步图片到mall-server
      const relativePath = `${type}/${filename}`;
      syncImageToMallServer(relativePath);

      uploadedFiles.push({
        url: imageUrl,
        filename: filename,
        originalname: file.name,
        size: file.size,
        mimetype: file.mimetype
      });
    }

    if (uploadedFiles.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有有效的图片文件'
      });
    }

    res.json({
      success: true,
      message: '图片上传成功',
      data: uploadedFiles
    });
  } catch (error) {
    console.error('多图片上传错误:', error);
    res.status(500).json({
      success: false,
      message: '图片上传失败: ' + error.message
    });
  }
});

// 删除图片
router.delete('/image', requireAuth, async (req, res) => {
  try {
    const { url } = req.body;

    if (!url) {
      return res.status(400).json({
        success: false,
        message: '请提供要删除的图片URL'
      });
    }

    // 从URL中提取类型，如 /uploads/banners/image.jpg -> banners
    const urlParts = url.split('/');
    const type = urlParts.length >= 3 ? urlParts[2] : 'temp';

    // 使用统一的删除工具删除图片
    const deleteResult = await deleteAndSync(url, type);
    
    if (deleteResult.success) {
      res.json({
        success: true,
        message: '图片删除成功'
      });
    } else {
      res.status(500).json({
        success: false,
        message: '删除图片失败: ' + deleteResult.message
      });
    }
  } catch (error) {
    console.error('删除图片错误:', error);
    res.status(500).json({
      success: false,
      message: '删除图片失败: ' + error.message
    });
  }
});

// 获取上传目录信息
router.get('/info', requireAuth, async (req, res) => {
  try {
    const uploadInfo = {
      banners: {
        path: '/uploads/banners',
        count: await getFileCount(path.join(uploadDir, 'banners'))
      },
      products: {
        path: '/uploads/products',
        count: await getFileCount(path.join(uploadDir, 'products'))
      },
      categories: {
        path: '/uploads/categories',
        count: await getFileCount(path.join(uploadDir, 'categories'))
      }
    };

    res.json({
      success: true,
      data: uploadInfo
    });
  } catch (error) {
    console.error('获取上传信息错误:', error);
    res.status(500).json({
      success: false,
      message: '获取上传信息失败'
    });
  }
});

// 辅助函数：获取目录文件数量
async function getFileCount(dirPath) {
  try {
    if (await fs.pathExists(dirPath)) {
      const files = await fs.readdir(dirPath);
      return files.length;
    }
    return 0;
  } catch (error) {
    return 0;
  }
}



module.exports = router; 