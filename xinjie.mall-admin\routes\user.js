const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const { requireAuth, requirePermission } = require('../middleware/auth');
const { uploadAndSync } = require('../utils/uploadAndSync');

router.get('/list', userController.list);
router.get('/detail/:id', userController.detail);
router.post('/create', userController.create);
router.put('/status/:id', userController.updateStatus);
router.put('/update/:id', userController.update);
router.post(
  '/:id/role',
  requireAuth,
  requirePermission('user:role:set'),
  userController.setRole
);

// 新增删除路由
router.delete('/delete/:id', userController.delete);
router.post('/batch-delete', userController.batchDelete);

// 新增统计路由
router.get('/statistics', userController.getStatistics);

module.exports = router;
