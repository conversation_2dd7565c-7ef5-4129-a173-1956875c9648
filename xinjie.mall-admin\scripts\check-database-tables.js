const { query } = require('../src/config/database');

async function checkDatabaseTables() {
  try {
    console.log('🔍 检查数据库表结构...\n');
    
    // 1. 查看所有表
    const tables = await query('SHOW TABLES');
    console.log('📋 数据库中的表:');
    tables.forEach((table, index) => {
      const tableName = Object.values(table)[0];
      console.log(`${index + 1}. ${tableName}`);
    });
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // 2. 检查关键表的结构
    const keyTables = ['users', 'orders', 'products', 'admin_users'];
    
    for (const tableName of keyTables) {
      try {
        console.log(`📊 表 "${tableName}" 的结构:`);
        const columns = await query(`DESCRIBE ${tableName}`);
        columns.forEach(col => {
          console.log(`  - ${col.Field}: ${col.Type} ${col.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${col.Key ? `(${col.Key})` : ''}`);
        });
        
        // 查看数据量
        const count = await query(`SELECT COUNT(*) as count FROM ${tableName}`);
        console.log(`  📈 数据量: ${count[0].count} 条记录\n`);
        
      } catch (error) {
        console.log(`  ❌ 表 "${tableName}" 不存在或无法访问\n`);
      }
    }
    
    // 3. 检查是否有余额和会员相关字段
    try {
      console.log('🔍 检查用户表的余额和会员字段:');
      const userColumns = await query('DESCRIBE users');
      const hasBalance = userColumns.some(col => col.Field === 'balance');
      const hasUserLevel = userColumns.some(col => col.Field === 'user_level');
      const hasPoints = userColumns.some(col => col.Field === 'points');
      
      console.log(`  - balance字段: ${hasBalance ? '✅ 存在' : '❌ 不存在'}`);
      console.log(`  - user_level字段: ${hasUserLevel ? '✅ 存在' : '❌ 不存在'}`);
      console.log(`  - points字段: ${hasPoints ? '✅ 存在' : '❌ 不存在'}`);
      
      if (!hasBalance || !hasUserLevel || !hasPoints) {
        console.log('\n💡 建议执行数据库迁移脚本添加缺失字段');
      }
    } catch (error) {
      console.log('❌ 无法检查用户表字段');
    }
    
  } catch (error) {
    console.error('❌ 检查数据库失败:', error);
  } finally {
    process.exit(0);
  }
}

checkDatabaseTables();
