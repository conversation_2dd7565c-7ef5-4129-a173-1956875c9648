const fs = require('fs-extra');
const path = require('path');

async function createDefaultImages() {
  try {
    console.log('🖼️ 创建默认图片文件...\n');
    
    // 创建图片目录
    const imageDir = path.join(__dirname, '../images');
    await fs.ensureDir(imageDir);
    
    // 创建茶叶产品的默认图片（使用SVG占位符）
    const teaImages = [
      'tea-1.jpg', 'tea-2.jpg', 'tea-3.jpg', 'tea-4.jpg',
      'tea-5.jpg', 'tea-6.jpg', 'tea-7.jpg', 'tea-8.jpg'
    ];
    
    // SVG占位符内容
    const createSvgPlaceholder = (name, color) => `
<svg width="300" height="300" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="${color}"/>
  <text x="50%" y="45%" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="white">
    ${name}
  </text>
  <text x="50%" y="60%" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
    300x300
  </text>
</svg>`;

    const colors = ['#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336', '#607D8B', '#795548', '#009688'];
    
    for (let i = 0; i < teaImages.length; i++) {
      const imageName = teaImages[i];
      const imagePath = path.join(imageDir, imageName);
      
      // 如果文件不存在，创建SVG占位符
      if (!await fs.pathExists(imagePath)) {
        const svgContent = createSvgPlaceholder(`茶叶 ${i + 1}`, colors[i]);
        await fs.writeFile(imagePath.replace('.jpg', '.svg'), svgContent);
        console.log(`✅ 创建占位符: ${imageName.replace('.jpg', '.svg')}`);
      } else {
        console.log(`⏭️ 文件已存在: ${imageName}`);
      }
    }
    
    // 创建默认产品图片
    const defaultProductSvg = createSvgPlaceholder('默认商品图片', '#666666');
    await fs.writeFile(path.join(imageDir, 'default-product.svg'), defaultProductSvg);
    console.log('✅ 创建默认商品图片占位符');
    
    console.log('\n🎉 默认图片创建完成！');
    console.log('💡 提示：这些是SVG占位符，你可以替换为真实的茶叶图片');
    
  } catch (error) {
    console.error('❌ 创建默认图片失败:', error);
  }
}

createDefaultImages();
