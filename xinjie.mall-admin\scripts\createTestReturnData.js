// 创建退货功能测试数据
const { query } = require('../src/config/database');

async function createTestReturnData() {
  try {
    console.log('开始创建退货功能测试数据...');

    // 生成退货单号
    function generateReturnNo() {
      const timestamp = Date.now();
      const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
      return `RT${timestamp}${random}`;
    }

    // 1. 创建测试退货申请
    const testReturns = [
      {
        return_no: generateReturnNo(),
        order_id: 1,
        order_no: 'ORDER001',
        user_id: 1,
        return_type: 2,
        return_reason: '商品质量问题',
        return_description: '收到的茶叶有异味，包装破损',
        return_amount: 99.00,
        contact_phone: '13800138000',
        return_images: JSON.stringify(['https://example.com/image1.jpg', 'https://example.com/image2.jpg']),
        status: 0
      },
      {
        return_no: generateReturnNo(),
        order_id: 2,
        order_no: 'ORDER002',
        user_id: 2,
        return_type: 1,
        return_reason: '不喜欢/不合适',
        return_description: '口感不符合预期',
        return_amount: 158.00,
        contact_phone: '13900139000',
        return_images: JSON.stringify([]),
        status: 1
      },
      {
        return_no: generateReturnNo(),
        order_id: 3,
        order_no: 'ORDER003',
        user_id: 1,
        return_type: 2,
        return_reason: '收到商品破损',
        return_description: '快递运输过程中包装破损，茶叶散落',
        return_amount: 268.00,
        contact_phone: '13800138000',
        return_images: JSON.stringify(['https://example.com/damage1.jpg']),
        status: 4
      },
      {
        return_no: generateReturnNo(),
        order_id: 4,
        order_no: 'ORDER004',
        user_id: 3,
        return_type: 2,
        return_reason: '商品描述不符',
        return_description: '实际收到的茶叶品质与描述不符',
        return_amount: 188.00,
        contact_phone: '13700137000',
        return_images: JSON.stringify([]),
        status: 6
      },
      {
        return_no: generateReturnNo(),
        order_id: 5,
        order_no: 'ORDER005',
        user_id: 2,
        return_type: 1,
        return_reason: '其他原因',
        return_description: '临时有事，无法使用',
        return_amount: 128.00,
        contact_phone: '13900139000',
        return_images: JSON.stringify([]),
        status: 8
      }
    ];

    // 插入退货申请数据
    for (const returnData of testReturns) {
      const result = await query(`
        INSERT INTO return_requests 
        (return_no, order_id, order_no, user_id, return_type, return_reason, return_description, 
         return_amount, contact_phone, return_images, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        returnData.return_no, returnData.order_id, returnData.order_no, returnData.user_id,
        returnData.return_type, returnData.return_reason, returnData.return_description,
        returnData.return_amount, returnData.contact_phone, returnData.return_images,
        returnData.status
      ]);

      console.log(`✅ 退货申请 ${returnData.return_no} 创建成功，ID: ${result.insertId}`);

      // 2. 为每个退货申请创建商品明细
      await query(`
        INSERT INTO return_items 
        (return_id, order_item_id, product_id, product_name, product_image, product_price, 
         return_quantity, return_amount, reason)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        result.insertId, 1, 1, '福建铁观音特级茶叶', 
        'https://example.com/product1.jpg', returnData.return_amount, 
        1, returnData.return_amount, returnData.return_reason
      ]);

      console.log(`✅ 退货商品明细创建成功，退货ID: ${result.insertId}`);

      // 3. 创建状态日志
      await query(`
        INSERT INTO return_status_logs 
        (return_id, status, status_text, operator_type, operator_name, remark)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [
        result.insertId, returnData.status, 
        returnData.status === 0 ? '待审核' : 
        returnData.status === 1 ? '审核通过' :
        returnData.status === 4 ? '已寄回' :
        returnData.status === 6 ? '验收通过' :
        returnData.status === 8 ? '退款完成' : '未知状态',
        1, '测试用户', '测试数据创建'
      ]);

      console.log(`✅ 状态日志创建成功，退货ID: ${result.insertId}`);

      // 4. 如果是已退款状态，创建退款记录
      if (returnData.status === 8) {
        const refundNo = `RF${Date.now()}${Math.floor(Math.random() * 1000)}`;
        await query(`
          INSERT INTO refund_records 
          (refund_no, return_id, order_id, user_id, refund_amount, refund_type, refund_status,
           refund_time, success_time, operator_name, remark)
          VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), ?, ?)
        `, [
          refundNo, result.insertId, returnData.order_id, returnData.user_id,
          returnData.return_amount, 2, 2, '系统管理员', '测试退款记录'
        ]);

        console.log(`✅ 退款记录创建成功，退款单号: ${refundNo}`);
      }
    }

    // 5. 验证创建结果
    const returnCount = await query('SELECT COUNT(*) as count FROM return_requests');
    const itemCount = await query('SELECT COUNT(*) as count FROM return_items');
    const logCount = await query('SELECT COUNT(*) as count FROM return_status_logs');
    const refundCount = await query('SELECT COUNT(*) as count FROM refund_records');

    console.log('\n📊 测试数据创建统计:');
    console.log(`  - 退货申请: ${returnCount[0].count} 条`);
    console.log(`  - 退货商品: ${itemCount[0].count} 条`);
    console.log(`  - 状态日志: ${logCount[0].count} 条`);
    console.log(`  - 退款记录: ${refundCount[0].count} 条`);

    // 6. 显示各状态的统计
    const statusStats = await query(`
      SELECT status, COUNT(*) as count 
      FROM return_requests 
      GROUP BY status 
      ORDER BY status
    `);

    console.log('\n📈 状态分布统计:');
    const statusMap = {
      0: '待审核', 1: '审核通过', 2: '审核拒绝', 3: '待寄回', 4: '已寄回',
      5: '验收中', 6: '验收通过', 7: '验收不通过', 8: '退款完成', 9: '已取消'
    };
    
    statusStats.forEach(stat => {
      console.log(`  - ${statusMap[stat.status] || '未知状态'}: ${stat.count} 条`);
    });

    console.log('\n🎉 退货功能测试数据创建完成！');
    
  } catch (error) {
    console.error('❌ 创建测试数据失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  createTestReturnData()
    .then(() => {
      console.log('测试数据创建完成，退出程序');
      process.exit(0);
    })
    .catch((error) => {
      console.error('创建测试数据失败:', error);
      process.exit(1);
    });
}

module.exports = { createTestReturnData };
