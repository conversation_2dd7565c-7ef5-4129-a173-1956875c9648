#!/usr/bin/env node

// 初始化管理员用户脚本
const bcrypt = require('bcryptjs');
const mysql = require('mysql2/promise');
require('dotenv').config();

class AdminUserInitializer {
  constructor() {
    this.dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'xinjie_mall'
    };
  }

  async createConnection() {
    try {
      const connection = await mysql.createConnection(this.dbConfig);
      console.log('✅ 数据库连接成功');
      return connection;
    } catch (error) {
      console.error('❌ 数据库连接失败:', error.message);
      throw error;
    }
  }

  async initAdminUser() {
    const connection = await this.createConnection();

    try {
      console.log('🔧 开始初始化管理员用户...');

      // 1. 检查admin_users表是否存在
      await this.createAdminUsersTable(connection);

      // 2. 检查是否已存在管理员
      const [existingAdmins] = await connection.execute(
        'SELECT COUNT(*) as count FROM admin_users WHERE username = "admin"'
      );

      if (existingAdmins[0].count > 0) {
        console.log('⚠️ 管理员用户已存在，跳过初始化');
        return;
      }

      // 3. 创建默认管理员
      const defaultAdmin = {
        username: 'admin',
        password: 'admin123456',
        email: '<EMAIL>',
        realName: '系统管理员',
        role_id: 1,
        status: 1
      };

      await this.createAdminUser(connection, defaultAdmin);

      // 4. 创建默认角色和权限
      await this.initRolesAndPermissions(connection);

      console.log('🎉 管理员用户初始化完成！');
      console.log('📋 默认管理员信息:');
      console.log(`   用户名: ${defaultAdmin.username}`);
      console.log(`   密码: ${defaultAdmin.password}`);
      console.log(`   邮箱: ${defaultAdmin.email}`);
      console.log('⚠️ 请登录后立即修改默认密码！');

    } catch (error) {
      console.error('❌ 初始化失败:', error.message);
      throw error;
    } finally {
      await connection.end();
    }
  }

  async createAdminUsersTable(connection) {
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS admin_users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        real_name VARCHAR(50),
        role ENUM('super_admin', 'admin', 'operator') DEFAULT 'operator',
        status ENUM('active', 'inactive', 'locked') DEFAULT 'active',
        avatar VARCHAR(255),
        phone VARCHAR(20),
        last_login_at TIMESTAMP NULL,
        last_login_ip VARCHAR(45),
        login_count INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_username (username),
        INDEX idx_email (email),
        INDEX idx_role (role),
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;

    await connection.execute(createTableSQL);
    console.log('✅ admin_users表创建/检查完成');
  }

  async createAdminUser(connection, userData) {
    // 加密密码
    const hashedPassword = await bcrypt.hash(userData.password, 12);

    const insertSQL = `
      INSERT INTO admin_users (
        username, password, email, role_id, status, created_at
      ) VALUES (?, ?, ?, ?, ?, NOW())
    `;

    const values = [
      userData.username,
      hashedPassword,
      userData.email,
      userData.role_id,
      userData.status
    ];

    await connection.execute(insertSQL, values);
    console.log(`✅ 管理员用户 ${userData.username} 创建成功`);
  }

  async initRolesAndPermissions(connection) {
    // 创建角色表
    const createRolesTableSQL = `
      CREATE TABLE IF NOT EXISTS admin_roles (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(50) UNIQUE NOT NULL,
        display_name VARCHAR(100) NOT NULL,
        description TEXT,
        permissions JSON,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;

    await connection.execute(createRolesTableSQL);

    // 插入默认角色
    const defaultRoles = [
      {
        name: 'super_admin',
        display_name: '超级管理员',
        description: '拥有所有权限的超级管理员',
        permissions: JSON.stringify(['*'])
      },
      {
        name: 'admin',
        display_name: '管理员',
        description: '拥有大部分管理权限',
        permissions: JSON.stringify([
          'user.view', 'user.create', 'user.edit',
          'product.view', 'product.create', 'product.edit', 'product.delete',
          'order.view', 'order.edit',
          'category.view', 'category.create', 'category.edit', 'category.delete',
          'banner.view', 'banner.create', 'banner.edit', 'banner.delete',
          'stats.view'
        ])
      },
      {
        name: 'operator',
        display_name: '操作员',
        description: '基础操作权限',
        permissions: JSON.stringify([
          'product.view', 'product.edit',
          'order.view', 'order.edit',
          'category.view',
          'banner.view'
        ])
      }
    ];

    for (const role of defaultRoles) {
      const [existing] = await connection.execute(
        'SELECT id FROM admin_roles WHERE name = ?',
        [role.name]
      );

      if (existing.length === 0) {
        await connection.execute(
          'INSERT INTO admin_roles (name, display_name, description, permissions) VALUES (?, ?, ?, ?)',
          [role.name, role.display_name, role.description, role.permissions]
        );
        console.log(`✅ 角色 ${role.display_name} 创建成功`);
      }
    }
  }

  async resetAdminPassword(username, newPassword) {
    const connection = await this.createConnection();

    try {
      const hashedPassword = await bcrypt.hash(newPassword, 12);
      
      const [result] = await connection.execute(
        'UPDATE admin_users SET password = ?, updated_at = NOW() WHERE username = ?',
        [hashedPassword, username]
      );

      if (result.affectedRows > 0) {
        console.log(`✅ 用户 ${username} 密码重置成功`);
      } else {
        console.log(`❌ 用户 ${username} 不存在`);
      }
    } finally {
      await connection.end();
    }
  }
}

// 命令行参数处理
const args = process.argv.slice(2);
const command = args[0];

const initializer = new AdminUserInitializer();

switch (command) {
  case 'init':
    initializer.initAdminUser().catch(console.error);
    break;
  case 'reset-password':
    const username = args[1];
    const password = args[2];
    if (!username || !password) {
      console.log('用法: node init-admin-user.js reset-password <username> <new-password>');
      process.exit(1);
    }
    initializer.resetAdminPassword(username, password).catch(console.error);
    break;
  default:
    console.log('用法:');
    console.log('  初始化管理员: node init-admin-user.js init');
    console.log('  重置密码: node init-admin-user.js reset-password <username> <new-password>');
    break;
}

module.exports = AdminUserInitializer;
