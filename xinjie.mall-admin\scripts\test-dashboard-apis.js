const axios = require('axios');

const BASE_URL = 'http://localhost:8081/api/admin';

// 模拟管理员登录获取token
async function getAuthToken() {
  try {
    const response = await axios.post(`${BASE_URL}/login`, {
      username: 'admin',
      password: 'admin123'
    });
    
    if (response.data.success) {
      return response.data.data.token;
    } else {
      console.log('登录失败，使用模拟token');
      return 'mock-token-for-testing';
    }
  } catch (error) {
    console.log('登录请求失败，使用模拟token');
    return 'mock-token-for-testing';
  }
}

async function testDashboardAPIs() {
  console.log('🧪 测试仪表板真实数据API...\n');
  
  const token = await getAuthToken();
  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };
  
  const tests = [
    {
      name: '销售趋势数据 (7天)',
      url: `${BASE_URL}/order/sales-trend?days=7`,
      method: 'GET'
    },
    {
      name: '销售趋势数据 (30天)',
      url: `${BASE_URL}/order/sales-trend?days=30`,
      method: 'GET'
    },
    {
      name: '热销商品数据',
      url: `${BASE_URL}/product/hot?limit=8`,
      method: 'GET'
    },
    {
      name: '订单统计',
      url: `${BASE_URL}/order/statistics`,
      method: 'GET'
    },
    {
      name: '用户统计',
      url: `${BASE_URL}/user/statistics`,
      method: 'GET'
    }
  ];
  
  for (const test of tests) {
    try {
      console.log(`📡 测试 ${test.name}...`);
      const response = await axios({
        method: test.method,
        url: test.url,
        headers
      });
      
      if (response.data.success) {
        console.log(`✅ ${test.name} - 成功`);
        
        if (test.name.includes('销售趋势')) {
          const data = response.data.data;
          console.log(`   📊 趋势数据点: ${data.trend?.length || 0}个`);
          console.log(`   📊 总销售额: ¥${data.summary?.totalSales || 0}`);
          console.log(`   📊 总订单数: ${data.summary?.totalOrders || 0}个`);
          console.log(`   📊 平均订单价值: ¥${data.summary?.avgOrderValue || 0}`);
          
          // 显示前3天的数据示例
          if (data.trend && data.trend.length > 0) {
            console.log('   📈 数据示例:');
            data.trend.slice(0, 3).forEach(item => {
              console.log(`     ${item.date}: ¥${item.sales}, ${item.orders}单`);
            });
          }
        } else if (test.name.includes('热销商品')) {
          const products = response.data.data;
          console.log(`   📊 商品数量: ${products?.length || 0}个`);
          
          if (products && products.length > 0) {
            console.log('   🔥 热销商品示例:');
            products.slice(0, 3).forEach((product, index) => {
              console.log(`     ${index + 1}. ${product.name} - ¥${product.price} (销量: ${product.sales_count || 0})`);
            });
          }
        } else if (test.name.includes('统计')) {
          const stats = response.data.data;
          console.log(`   📊 统计数据:`, JSON.stringify(stats, null, 2));
        }
      } else {
        console.log(`❌ ${test.name} - 失败: ${response.data.message}`);
      }
    } catch (error) {
      if (error.response) {
        console.log(`❌ ${test.name} - HTTP ${error.response.status}: ${error.response.statusText}`);
        if (error.response.data) {
          console.log(`   错误详情: ${JSON.stringify(error.response.data)}`);
        }
      } else {
        console.log(`❌ ${test.name} - 网络错误: ${error.message}`);
      }
    }
    console.log('');
  }
  
  console.log('🎉 仪表板API测试完成！');
  console.log('💡 现在前端应该显示真实的数据库数据而不是模拟数据');
}

testDashboardAPIs();
