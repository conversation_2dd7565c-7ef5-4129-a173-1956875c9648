const axios = require('axios');

const BASE_URL = 'http://localhost:8081/api/admin';

// 模拟管理员登录获取token
async function getAuthToken() {
  try {
    const response = await axios.post(`${BASE_URL}/login`, {
      username: 'admin',
      password: 'admin123'
    });
    
    if (response.data.success) {
      return response.data.data.token;
    } else {
      console.log('登录失败，使用模拟token');
      return 'mock-token-for-testing';
    }
  } catch (error) {
    console.log('登录请求失败，使用模拟token');
    return 'mock-token-for-testing';
  }
}

async function testAPIs() {
  console.log('🧪 测试新创建的API接口...\n');
  
  const token = await getAuthToken();
  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };
  
  const tests = [
    {
      name: '系统通知',
      url: `${BASE_URL}/system/notifications?limit=5`,
      method: 'GET'
    },
    {
      name: '系统状态',
      url: `${BASE_URL}/system/status`,
      method: 'GET'
    },
    {
      name: '销售趋势',
      url: `${BASE_URL}/order/sales-trend?days=7`,
      method: 'GET'
    },
    {
      name: '热销商品',
      url: `${BASE_URL}/product/hot?limit=8`,
      method: 'GET'
    }
  ];
  
  for (const test of tests) {
    try {
      console.log(`📡 测试 ${test.name}...`);
      const response = await axios({
        method: test.method,
        url: test.url,
        headers
      });
      
      if (response.data.success) {
        console.log(`✅ ${test.name} - 成功`);
        if (test.name === '系统通知') {
          console.log(`   📊 通知数量: ${response.data.data.length}`);
          console.log(`   📊 未读数量: ${response.data.unreadCount}`);
        } else if (test.name === '热销商品') {
          console.log(`   📊 商品数量: ${response.data.data.length}`);
        } else if (test.name === '销售趋势') {
          console.log(`   📊 趋势数据: ${response.data.data.trend.length}天`);
          console.log(`   📊 总销售额: ¥${response.data.data.summary.totalSales}`);
        }
      } else {
        console.log(`❌ ${test.name} - 失败: ${response.data.message}`);
      }
    } catch (error) {
      if (error.response) {
        console.log(`❌ ${test.name} - HTTP ${error.response.status}: ${error.response.statusText}`);
      } else {
        console.log(`❌ ${test.name} - 网络错误: ${error.message}`);
      }
    }
    console.log('');
  }
  
  console.log('🎉 API测试完成！');
}

testAPIs();
