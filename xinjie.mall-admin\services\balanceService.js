const balanceModel = require('../models/balanceModel');
const db = require('../src/config/database');

/**
 * 余额服务层 - 处理所有余额相关的业务逻辑
 * 设计原则：
 * 1. 事务安全：所有余额操作都使用数据库事务
 * 2. 状态一致：确保余额和记录的一致性
 * 3. 错误处理：详细的错误信息和回滚机制
 * 4. 扩展性：为后续真实支付预留接口
 */
class BalanceService {
  
  /**
   * 获取用户余额信息
   * @param {number} userId - 用户ID
   * @returns {Object} 余额信息
   */
  async getUserBalanceInfo(userId) {
    try {
      const balance = await balanceModel.getUserBalance(userId);
      
      // 获取最近的余额变动记录
      const recentRecords = await this.getRecentBalanceRecords(userId, 5);
      
      return {
        userId,
        balance: parseFloat(balance).toFixed(2),
        balanceFloat: parseFloat(balance),
        recentRecords,
        canPay: parseFloat(balance) > 0,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      throw new Error(`获取用户余额失败: ${error.message}`);
    }
  }

  /**
   * 模拟用户充值（方案B - 开发测试用）
   * @param {number} userId - 用户ID
   * @param {number} amount - 充值金额
   * @param {string} paymentMethod - 支付方式
   * @param {string} remark - 备注
   * @returns {Object} 充值结果
   */
  async simulateUserRecharge(userId, amount, paymentMethod = 'wechat', remark = '') {
    const connection = await db.getConnection();
    
    try {
      await connection.beginTransaction();
      
      // 参数验证
      if (!userId || userId <= 0) {
        throw new Error('用户ID无效');
      }
      
      if (!amount || amount <= 0) {
        throw new Error('充值金额必须大于0');
      }
      
      if (amount > 10000) {
        throw new Error('单次充值金额不能超过10000元');
      }
      
      // 生成充值订单号
      const orderNo = this.generateRechargeOrderNo();
      
      // 创建充值记录
      const [rechargeResult] = await connection.query(`
        INSERT INTO recharge_records (
          user_id, order_no, amount, bonus_amount, total_amount,
          payment_method, payment_status, remark, created_at, paid_at
        ) VALUES (?, ?, ?, 0, ?, ?, 1, ?, NOW(), NOW())
      `, [
        userId, 
        orderNo, 
        amount, 
        amount, 
        this.getPaymentMethodCode(paymentMethod),
        remark || `模拟${paymentMethod}充值`
      ]);
      
      const rechargeId = rechargeResult.insertId;
      
      // 更新用户余额
      const newBalance = await this.updateUserBalanceWithTransaction(
        connection,
        userId,
        amount,
        1, // 增加
        1, // 充值来源
        rechargeId,
        `充值成功：${amount}元`
      );

      // 充值奖励积分
      const rechargeReward = this.calculateRechargeReward(amount);
      if (rechargeReward.points > 0) {
        const pointsService = require('./pointsService');
        await pointsService.addPointsForRecharge(connection, userId, rechargeReward.points, rechargeId);
      }

      await connection.commit();
      
      return {
        success: true,
        rechargeId,
        orderNo,
        amount: parseFloat(amount).toFixed(2),
        newBalance: parseFloat(newBalance).toFixed(2),
        paymentMethod,
        message: '充值成功'
      };
      
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 余额支付（核心功能）
   * @param {number} userId - 用户ID
   * @param {number} orderId - 订单ID
   * @param {number} amount - 支付金额
   * @param {string} remark - 备注
   * @returns {Object} 支付结果
   */
  async balancePayment(userId, orderId, amount, remark = '') {
    const connection = await db.getConnection();
    
    try {
      await connection.beginTransaction();
      
      // 参数验证
      if (!userId || !orderId || !amount || amount <= 0) {
        throw new Error('参数无效');
      }
      
      // 检查用户余额
      const [userResult] = await connection.query(
        'SELECT balance FROM users WHERE id = ? FOR UPDATE',
        [userId]
      );
      
      if (!userResult.length) {
        throw new Error('用户不存在');
      }
      
      const currentBalance = parseFloat(userResult[0].balance);
      
      if (currentBalance < amount) {
        throw new Error(`余额不足，当前余额：${currentBalance.toFixed(2)}元，需要：${amount.toFixed(2)}元`);
      }
      
      // 检查订单状态
      const [orderResult] = await connection.query(
        'SELECT id, order_no, total_amount, payment_status FROM orders WHERE id = ? AND user_id = ?',
        [orderId, userId]
      );
      
      if (!orderResult.length) {
        throw new Error('订单不存在或无权限');
      }
      
      const order = orderResult[0];
      
      if (order.payment_status === 1) {
        throw new Error('订单已支付');
      }
      
      // 扣除余额
      const newBalance = await this.updateUserBalanceWithTransaction(
        connection,
        userId,
        amount,
        2, // 减少
        2, // 订单支付来源
        orderId,
        remark || `订单${order.order_no}支付：${amount}元`
      );
      
      // 更新订单状态
      await connection.query(`
        UPDATE orders 
        SET payment_status = 1, payment_method = 'balance', payment_time = NOW(), 
            balance_used = ?, updated_at = NOW()
        WHERE id = ?
      `, [amount, orderId]);
      
      await connection.commit();
      
      return {
        success: true,
        orderId,
        orderNo: order.order_no,
        paidAmount: parseFloat(amount).toFixed(2),
        newBalance: parseFloat(newBalance).toFixed(2),
        paymentMethod: 'balance',
        message: '余额支付成功'
      };
      
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 混合支付（余额 + 第三方支付）
   * @param {number} userId - 用户ID
   * @param {number} orderId - 订单ID
   * @param {number} totalAmount - 订单总金额
   * @param {number} balanceAmount - 使用余额金额
   * @param {string} thirdPartyMethod - 第三方支付方式
   * @returns {Object} 支付结果
   */
  async mixedPayment(userId, orderId, totalAmount, balanceAmount, thirdPartyMethod = 'wechat') {
    try {
      // 参数验证
      if (balanceAmount < 0 || balanceAmount > totalAmount) {
        throw new Error('余额使用金额无效');
      }
      
      const thirdPartyAmount = totalAmount - balanceAmount;
      let result = {
        success: true,
        orderId,
        totalAmount: parseFloat(totalAmount).toFixed(2),
        balanceUsed: parseFloat(balanceAmount).toFixed(2),
        thirdPartyAmount: parseFloat(thirdPartyAmount).toFixed(2),
        paymentCompleted: false
      };
      
      // 如果使用余额
      if (balanceAmount > 0) {
        const balancePayResult = await this.balancePayment(
          userId, 
          orderId, 
          balanceAmount,
          `混合支付-余额部分：${balanceAmount}元`
        );
        
        result.balancePayment = balancePayResult;
        result.newBalance = balancePayResult.newBalance;
      }
      
      // 如果需要第三方支付
      if (thirdPartyAmount > 0.01) {
        // 这里为真实支付预留接口
        result.thirdPartyPayment = {
          amount: parseFloat(thirdPartyAmount).toFixed(2),
          method: thirdPartyMethod,
          // 在真实支付中，这里会返回支付参数
          paymentData: this.generateMockPaymentData(orderId, thirdPartyAmount, thirdPartyMethod)
        };
      } else {
        // 纯余额支付，订单完成
        result.paymentCompleted = true;
        result.message = '支付成功';
      }
      
      return result;
      
    } catch (error) {
      throw new Error(`混合支付失败: ${error.message}`);
    }
  }

  /**
   * 获取用户充值记录
   * @param {number} userId - 用户ID
   * @param {number} page - 页码
   * @param {number} pageSize - 每页数量
   * @returns {Object} 充值记录
   */
  async getUserRechargeHistory(userId, page = 1, pageSize = 10) {
    try {
      return await balanceModel.getUserRechargeHistory(userId, page, pageSize);
    } catch (error) {
      throw new Error(`获取充值记录失败: ${error.message}`);
    }
  }

  /**
   * 获取用户余额变动记录
   * @param {number} userId - 用户ID
   * @param {number} page - 页码
   * @param {number} pageSize - 每页数量
   * @returns {Object} 余额记录
   */
  async getUserBalanceHistory(userId, page = 1, pageSize = 10) {
    try {
      return await balanceModel.getUserBalanceHistory(userId, page, pageSize);
    } catch (error) {
      throw new Error(`获取余额记录失败: ${error.message}`);
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 在事务中更新用户余额
   */
  async updateUserBalanceWithTransaction(connection, userId, amount, type, source, sourceId, remark) {
    // 获取当前余额
    const [userResult] = await connection.query('SELECT balance FROM users WHERE id = ?', [userId]);
    const balanceBefore = parseFloat(userResult[0].balance);
    
    let balanceAfter;
    if (type === 1) { // 增加
      balanceAfter = balanceBefore + parseFloat(amount);
    } else { // 减少
      balanceAfter = balanceBefore - parseFloat(amount);
      if (balanceAfter < 0) {
        throw new Error('余额不足');
      }
    }
    
    // 更新用户余额
    await connection.query(
      'UPDATE users SET balance = ?, updated_at = NOW() WHERE id = ?',
      [balanceAfter, userId]
    );
    
    // 记录余额变动
    await connection.query(`
      INSERT INTO balance_records (
        user_id, type, amount, balance_before, balance_after, 
        source, source_id, remark, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `, [userId, type, amount, balanceBefore, balanceAfter, source, sourceId, remark]);
    
    return balanceAfter;
  }

  /**
   * 生成充值订单号
   */
  generateRechargeOrderNo() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `RC${timestamp}${random}`;
  }

  /**
   * 获取支付方式代码
   */
  getPaymentMethodCode(method) {
    const codes = {
      'wechat': 1,
      'alipay': 2,
      'balance': 3,
      'admin': 4
    };
    return codes[method] || 1;
  }

  /**
   * 获取最近的余额记录
   */
  async getRecentBalanceRecords(userId, limit = 5) {
    try {
      const [records] = await db.query(`
        SELECT type, amount, remark, created_at
        FROM balance_records 
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT ?
      `, [userId, limit]);
      
      return records || [];
    } catch (error) {
      return [];
    }
  }

  /**
   * 生成模拟支付数据（方案B用）
   */
  generateMockPaymentData(orderId, amount, method) {
    return {
      orderId,
      amount: parseFloat(amount).toFixed(2),
      method,
      mockPayment: true,
      timestamp: Date.now(),
      // 真实支付时，这里会返回微信/支付宝的支付参数
      message: '模拟支付数据，实际开发中会调用真实支付API'
    };
  }

  /**
   * 计算充值奖励
   */
  calculateRechargeReward(amount) {
    const amountFloat = parseFloat(amount);

    // 充值奖励规则
    const rewardRules = [
      { minAmount: 2000, points: 600, bonusRate: 0.075 }, // 充值2000+送600积分+7.5%奖励
      { minAmount: 1000, points: 250, bonusRate: 0.05 },  // 充值1000+送250积分+5%奖励
      { minAmount: 500, points: 100, bonusRate: 0.04 },   // 充值500+送100积分+4%奖励
      { minAmount: 300, points: 50, bonusRate: 0.03 },    // 充值300+送50积分+3%奖励
      { minAmount: 100, points: 15, bonusRate: 0.02 },    // 充值100+送15积分+2%奖励
      { minAmount: 50, points: 5, bonusRate: 0.01 }       // 充值50+送5积分+1%奖励
    ];

    // 找到匹配的奖励规则
    const matchedRule = rewardRules.find(rule => amountFloat >= rule.minAmount);

    if (matchedRule) {
      return {
        points: matchedRule.points,
        bonusAmount: parseFloat((amountFloat * matchedRule.bonusRate).toFixed(2)),
        description: `充值${amountFloat}元奖励${matchedRule.points}积分`
      };
    }

    return { points: 0, bonusAmount: 0, description: '' };
  }

  /**
   * 获取充值档位推荐
   */
  getRechargePackages() {
    return [
      {
        amount: 50,
        bonus: 0.5,
        points: 5,
        popular: false,
        description: '小试牛刀',
        label: '送5积分'
      },
      {
        amount: 100,
        bonus: 2,
        points: 15,
        popular: false,
        description: '品茶入门',
        label: '送15积分+2元'
      },
      {
        amount: 300,
        bonus: 9,
        points: 50,
        popular: true,
        description: '茶友推荐',
        label: '送50积分+9元'
      },
      {
        amount: 500,
        bonus: 20,
        points: 100,
        popular: false,
        description: '品质之选',
        label: '送100积分+20元'
      },
      {
        amount: 1000,
        bonus: 50,
        points: 250,
        popular: false,
        description: '尊享体验',
        label: '送250积分+50元'
      },
      {
        amount: 2000,
        bonus: 150,
        points: 600,
        popular: false,
        description: '至尊茶师',
        label: '送600积分+150元'
      }
    ];
  }
}

module.exports = new BalanceService();
