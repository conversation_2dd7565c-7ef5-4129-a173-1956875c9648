/**
 * 分销风控服务
 * 负责分销系统的风险控制、合规检查、异常检测等功能
 */

const { query } = require('../src/config/database');

class DistributionRiskService {
  
  constructor() {
    // 风控规则配置
    this.riskRules = {
      // 硬性合规规则
      compliance: {
        maxLevels: 2,                    // 最大层级
        enableThirdLevel: false,         // 禁用三级
        transparentRules: true,          // 规则透明
        allowOptOut: true               // 允许退出
      },
      
      // 防刷规则
      antiCheat: {
        maxDailyShares: 20,             // 每日最大分享次数
        maxDailyClicks: 100,            // 每日最大点击次数
        minOrderAmount: 10,             // 最小有效订单金额
        maxSelfPurchaseRate: 0,         // 自购比例（0=禁止）
        cooldownPeriod: 300,            // 点击冷却期（秒）
        maxCommissionPerOrder: 500      // 单笔最大佣金
      },
      
      // 异常检测
      anomalyDetection: {
        rapidRegistration: {
          threshold: 5,
          timeWindow: 3600
        },
        concentratedOrders: {
          threshold: 10,
          timeWindow: 1800
        },
        abnormalCommission: {
          dailyThreshold: 1000,
          monthlyThreshold: 10000
        }
      }
    };
  }

  /**
   * 分享前风控检查
   */
  async checkShareRisk(userId, shareType, targetId) {
    try {
      const checks = await Promise.all([
        this.checkDailyShareLimit(userId),
        this.checkUserStatus(userId),
        this.checkShareContent(shareType, targetId)
      ]);
      
      const failed = checks.find(check => !check.passed);
      
      if (failed) {
        return {
          passed: false,
          reason: failed.reason,
          action: failed.action || 'block'
        };
      }
      
      return { passed: true };
      
    } catch (error) {
      console.error('分享风控检查失败:', error);
      return {
        passed: false,
        reason: '风控检查异常',
        action: 'block'
      };
    }
  }

  /**
   * 订单佣金风控检查
   */
  async checkCommissionRisk(orderId, buyerUserId, distributorUserId) {
    try {
      const checks = await Promise.all([
        this.checkSelfPurchase(buyerUserId, distributorUserId),
        this.checkOrderValidity(orderId),
        this.checkCommissionLimit(distributorUserId),
        this.checkAbnormalPattern(buyerUserId, distributorUserId)
      ]);
      
      const failed = checks.find(check => !check.passed);
      
      if (failed) {
        return {
          passed: false,
          reason: failed.reason,
          action: failed.action || 'review'
        };
      }
      
      return { passed: true };
      
    } catch (error) {
      console.error('佣金风控检查失败:', error);
      return {
        passed: false,
        reason: '风控检查异常',
        action: 'block'
      };
    }
  }

  /**
   * 检查每日分享限制
   */
  async checkDailyShareLimit(userId) {
    try {
      const [result] = await query(`
        SELECT COUNT(*) as count
        FROM share_records
        WHERE sharer_user_id = ? AND DATE(created_at) = CURDATE()
      `, [userId]);
      
      const todayShares = result[0].count;
      const maxDailyShares = this.riskRules.antiCheat.maxDailyShares;
      
      if (todayShares >= maxDailyShares) {
        return {
          passed: false,
          reason: `每日最多可分享${maxDailyShares}次，今日已分享${todayShares}次`,
          action: 'block'
        };
      }
      
      return { passed: true };
      
    } catch (error) {
      return {
        passed: false,
        reason: '检查分享限制失败',
        action: 'block'
      };
    }
  }

  /**
   * 检查用户状态
   */
  async checkUserStatus(userId) {
    try {
      const [result] = await query(
        'SELECT distributor_status FROM users WHERE id = ?',
        [userId]
      );
      
      if (!result.length) {
        return {
          passed: false,
          reason: '用户不存在',
          action: 'block'
        };
      }
      
      const user = result[0];
      
      if (user.distributor_status !== 1) {
        return {
          passed: false,
          reason: '用户不是有效分销商',
          action: 'block'
        };
      }
      
      return { passed: true };
      
    } catch (error) {
      return {
        passed: false,
        reason: '检查用户状态失败',
        action: 'block'
      };
    }
  }

  /**
   * 检查分享内容
   */
  async checkShareContent(shareType, targetId) {
    try {
      // 检查分享类型是否有效
      if (![1, 2, 3].includes(shareType)) {
        return {
          passed: false,
          reason: '无效的分享类型',
          action: 'block'
        };
      }
      
      // 如果是商品分享，检查商品是否存在且有效
      if (shareType === 1 && targetId) {
        const [product] = await query(
          'SELECT status FROM products WHERE id = ?',
          [targetId]
        );
        
        if (!product.length || product[0].status !== 1) {
          return {
            passed: false,
            reason: '商品不存在或已下架',
            action: 'block'
          };
        }
      }
      
      return { passed: true };
      
    } catch (error) {
      return {
        passed: false,
        reason: '检查分享内容失败',
        action: 'block'
      };
    }
  }

  /**
   * 检查自购行为
   */
  async checkSelfPurchase(buyerUserId, distributorUserId) {
    try {
      // 直接自购检查
      if (buyerUserId === distributorUserId) {
        return {
          passed: false,
          reason: '禁止自购获得佣金',
          action: 'block'
        };
      }
      
      return { passed: true };
      
    } catch (error) {
      return {
        passed: false,
        reason: '检查自购行为失败',
        action: 'review'
      };
    }
  }

  /**
   * 检查订单有效性
   */
  async checkOrderValidity(orderId) {
    try {
      const [order] = await query(`
        SELECT id, total_amount, order_status, created_at
        FROM orders
        WHERE id = ?
      `, [orderId]);
      
      if (!order.length) {
        return {
          passed: false,
          reason: '订单不存在',
          action: 'block'
        };
      }
      
      const orderInfo = order[0];
      
      // 检查订单状态
      if (orderInfo.order_status < 2) {
        return {
          passed: false,
          reason: '订单未支付',
          action: 'block'
        };
      }
      
      // 检查最小订单金额
      const minOrderAmount = this.riskRules.antiCheat.minOrderAmount;
      if (parseFloat(orderInfo.total_amount) < minOrderAmount) {
        return {
          passed: false,
          reason: `订单金额低于${minOrderAmount}元`,
          action: 'block'
        };
      }
      
      return { passed: true };
      
    } catch (error) {
      return {
        passed: false,
        reason: '检查订单有效性失败',
        action: 'review'
      };
    }
  }

  /**
   * 检查佣金限制
   */
  async checkCommissionLimit(distributorUserId) {
    try {
      // 检查今日佣金总额
      const [todayCommission] = await query(`
        SELECT COALESCE(SUM(commission_amount), 0) as total
        FROM distributor_orders
        WHERE distributor_user_id = ? AND DATE(created_at) = CURDATE()
      `, [distributorUserId]);
      
      const dailyThreshold = this.riskRules.anomalyDetection.abnormalCommission.dailyThreshold;
      
      if (todayCommission[0].total >= dailyThreshold) {
        return {
          passed: false,
          reason: `今日佣金已达上限${dailyThreshold}元`,
          action: 'review'
        };
      }
      
      return { passed: true };
      
    } catch (error) {
      return {
        passed: false,
        reason: '检查佣金限制失败',
        action: 'review'
      };
    }
  }

  /**
   * 检查异常模式
   */
  async checkAbnormalPattern(buyerUserId, distributorUserId) {
    try {
      // 检查快速注册模式
      const [rapidRegistration] = await query(`
        SELECT COUNT(*) as count
        FROM distributor_relations dr
        JOIN users u ON dr.child_user_id = u.id
        WHERE dr.parent_user_id = ?
          AND dr.created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
      `, [distributorUserId]);

      const rapidThreshold = this.riskRules.anomalyDetection.rapidRegistration.threshold;

      if (rapidRegistration[0].count >= rapidThreshold) {
        // 记录风控事件
        await this.logRiskEvent('rapid_registration', distributorUserId, null,
          `1小时内推荐用户过多(${rapidRegistration[0].count}个)`, 'review');

        return {
          passed: false,
          reason: `1小时内推荐用户过多(${rapidRegistration[0].count}个)`,
          action: 'review'
        };
      }

      // 检查集中下单模式
      const [concentratedOrders] = await query(`
        SELECT COUNT(*) as count
        FROM orders o
        JOIN distributor_relations dr ON o.user_id = dr.child_user_id
        WHERE dr.parent_user_id = ?
          AND o.created_at >= DATE_SUB(NOW(), INTERVAL 30 MINUTE)
          AND o.order_status >= 2
      `, [distributorUserId]);

      const concentratedThreshold = this.riskRules.anomalyDetection.concentratedOrders.threshold;

      if (concentratedOrders[0].count >= concentratedThreshold) {
        await this.logRiskEvent('concentrated_orders', distributorUserId, null,
          `30分钟内下单过于集中(${concentratedOrders[0].count}个)`, 'review');

        return {
          passed: false,
          reason: `30分钟内下单过于集中(${concentratedOrders[0].count}个)`,
          action: 'review'
        };
      }

      // 检查循环推荐（A推荐B，B推荐A的情况）
      const [circularReference] = await query(`
        SELECT COUNT(*) as count
        FROM distributor_relations dr1
        JOIN distributor_relations dr2 ON dr1.parent_user_id = dr2.child_user_id
          AND dr1.child_user_id = dr2.parent_user_id
        WHERE dr1.parent_user_id = ? OR dr1.child_user_id = ?
      `, [distributorUserId, distributorUserId]);

      if (circularReference[0].count > 0) {
        await this.logRiskEvent('circular_reference', distributorUserId, null,
          '检测到循环推荐关系', 'block');

        return {
          passed: false,
          reason: '检测到循环推荐关系',
          action: 'block'
        };
      }

      return { passed: true };

    } catch (error) {
      console.error('检查异常模式失败:', error);
      return {
        passed: false,
        reason: '检查异常模式失败',
        action: 'review'
      };
    }
  }

  /**
   * 记录风控日志
   */
  async logRiskEvent(eventType, userId, orderId, reason, action, details = {}) {
    try {
      // 记录到数据库（如果有风控日志表）
      const logData = {
        event_type: eventType,
        user_id: userId,
        order_id: orderId,
        reason,
        action,
        details: JSON.stringify(details),
        ip_address: details.ip || null,
        user_agent: details.userAgent || null,
        created_at: new Date()
      };

      // 这里可以记录到专门的风控日志表
      console.log('🚨 分销风控事件:', logData);

      // 如果是严重风险，可以发送告警
      if (action === 'block') {
        console.error('🔴 严重风控事件 - 已阻止:', logData);
        // 这里可以集成告警系统，如邮件、短信、钉钉等
      }

    } catch (error) {
      console.error('记录风控日志失败:', error);
    }
  }

  /**
   * 获取用户风险评分
   */
  async getUserRiskScore(userId) {
    try {
      let riskScore = 0;
      const riskFactors = [];

      // 检查历史违规记录
      const [violations] = await query(`
        SELECT COUNT(*) as count
        FROM distributor_relations dr
        WHERE dr.parent_user_id = ? AND dr.status = 0
      `, [userId]);

      if (violations[0].count > 0) {
        riskScore += violations[0].count * 10;
        riskFactors.push(`历史违规${violations[0].count}次`);
      }

      // 检查异常行为频率
      const [abnormalBehavior] = await query(`
        SELECT
          COUNT(CASE WHEN sr.created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 1 END) as daily_shares,
          COUNT(CASE WHEN sr.created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) as hourly_shares
        FROM share_records sr
        WHERE sr.sharer_user_id = ?
      `, [userId]);

      const dailyShares = abnormalBehavior[0].daily_shares;
      const hourlyShares = abnormalBehavior[0].hourly_shares;

      if (dailyShares > 50) {
        riskScore += 20;
        riskFactors.push(`日分享过多(${dailyShares}次)`);
      }

      if (hourlyShares > 10) {
        riskScore += 15;
        riskFactors.push(`时分享过多(${hourlyShares}次)`);
      }

      // 检查账户活跃度异常
      const [accountActivity] = await query(`
        SELECT
          DATEDIFF(NOW(), u.created_at) as account_age_days,
          u.total_commission,
          u.total_customers
        FROM users u
        WHERE u.id = ?
      `, [userId]);

      if (accountActivity.length > 0) {
        const { account_age_days, total_commission, total_customers } = accountActivity[0];

        // 新账户高收益风险
        if (account_age_days < 7 && total_commission > 1000) {
          riskScore += 30;
          riskFactors.push('新账户高收益');
        }

        // 客户数量异常
        if (account_age_days < 30 && total_customers > 100) {
          riskScore += 25;
          riskFactors.push('短期内客户数量异常');
        }
      }

      const riskLevel = riskScore < 30 ? 'low' : riskScore < 70 ? 'medium' : 'high';

      return {
        userId,
        riskScore,
        riskLevel,
        riskFactors,
        lastUpdated: new Date()
      };

    } catch (error) {
      console.error('获取用户风险评分失败:', error);
      return {
        userId,
        riskScore: 50,
        riskLevel: 'medium',
        riskFactors: ['评分计算异常'],
        lastUpdated: new Date()
      };
    }
  }
}

module.exports = new DistributionRiskService();
