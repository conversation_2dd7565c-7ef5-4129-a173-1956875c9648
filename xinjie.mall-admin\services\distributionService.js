/**
 * 二级分销服务
 * 负责分销商管理、分享链接生成、关系绑定、佣金计算等核心功能
 */

const { query } = require('../src/config/database');
const crypto = require('crypto');

class DistributionService {
  
  constructor() {
    // 分销配置缓存
    this.configCache = new Map();
    this.cacheExpiry = 5 * 60 * 1000; // 5分钟缓存
    this.lastCacheUpdate = 0;
  }

  /**
   * 获取分销配置
   */
  async getDistributionConfig(key = null) {
    const now = Date.now();
    
    // 检查缓存是否过期
    if (now - this.lastCacheUpdate > this.cacheExpiry) {
      await this.refreshConfigCache();
    }
    
    if (key) {
      return this.configCache.get(key);
    }
    
    return Object.fromEntries(this.configCache);
  }

  /**
   * 刷新配置缓存
   */
  async refreshConfigCache() {
    try {
      const configs = await query(
        'SELECT config_key, config_value FROM distribution_config WHERE status = 1'
      );
      
      this.configCache.clear();
      configs.forEach(config => {
        let value = config.config_value;
        // 尝试转换数字类型
        if (!isNaN(value)) {
          value = parseFloat(value);
        }
        this.configCache.set(config.config_key, value);
      });
      
      this.lastCacheUpdate = Date.now();
    } catch (error) {
      console.error('刷新分销配置缓存失败:', error);
    }
  }

  /**
   * 申请成为分销商
   */
  async applyDistributor(userId, applyData = {}) {
    const db = require('../src/config/database');
    const connection = await db.getConnection();
    
    try {
      await connection.beginTransaction();
      
      // 检查用户是否存在且未申请过
      const [userInfo] = await connection.query(`
        SELECT 
          u.id, u.nickname, u.user_level, u.points, u.distributor_status,
          COUNT(o.id) as order_count,
          COALESCE(SUM(CASE WHEN o.order_status >= 2 THEN o.total_amount ELSE 0 END), 0) as total_spent
        FROM users u
        LEFT JOIN orders o ON u.id = o.user_id
        WHERE u.id = ?
        GROUP BY u.id
      `, [userId]);
      
      if (!userInfo.length) {
        throw new Error('用户不存在');
      }
      
      const user = userInfo[0];
      
      if (user.distributor_status > 0) {
        throw new Error('您已经是分销商了');
      }
      
      // 获取申请条件配置
      const minLevel = await this.getDistributionConfig('distributor_min_level') || 2;
      const minPurchase = await this.getDistributionConfig('distributor_min_purchase') || 100;
      
      // 检查申请条件
      if (user.user_level < minLevel) {
        throw new Error(`申请条件不满足：需要${minLevel === 2 ? 'VIP' : '钻石'}会员等级`);
      }
      
      if (user.order_count < 1) {
        throw new Error('申请条件不满足：需要至少有1笔订单');
      }
      
      if (user.total_spent < minPurchase) {
        throw new Error(`申请条件不满足：需要累计消费满${minPurchase}元`);
      }
      
      // 生成分销商编码
      const distributorCode = this.generateDistributorCode(userId);
      
      // 记录申请
      await connection.query(`
        INSERT INTO distributor_applications (
          user_id, application_reason, status, applied_at, reviewed_at
        ) VALUES (?, ?, 1, NOW(), NOW())
      `, [userId, applyData.reason || '申请成为分销商', 1]);
      
      // 更新用户状态（自动通过）
      await connection.query(`
        UPDATE users 
        SET distributor_status = 1,
            distributor_code = ?,
            distributor_apply_time = NOW(),
            distributor_approve_time = NOW(),
            updated_at = NOW()
        WHERE id = ?
      `, [distributorCode, userId]);
      
      await connection.commit();
      
      return {
        success: true,
        data: {
          distributorCode,
          userId,
          nickname: user.nickname
        },
        message: '恭喜您成为心洁茶叶分销商！'
      };
      
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 生成分享链接
   */
  async generateShareLink(userId, shareType, targetId = null) {
    try {
      // 检查用户分销商状态
      const [userInfo] = await query(
        'SELECT distributor_status, distributor_code, nickname FROM users WHERE id = ?',
        [userId]
      );
      
      if (!userInfo.length) {
        throw new Error('用户不存在');
      }
      
      const user = userInfo[0];
      
      if (user.distributor_status !== 1) {
        throw new Error('只有分销商才能生成分享链接');
      }
      
      // 检查每日分享限制
      const maxDailyShares = await this.getDistributionConfig('max_daily_shares') || 20;
      const [todayShares] = await query(`
        SELECT COUNT(*) as count 
        FROM share_records 
        WHERE sharer_user_id = ? AND DATE(created_at) = CURDATE()
      `, [userId]);
      
      if (todayShares[0].count >= maxDailyShares) {
        throw new Error(`每日最多可分享${maxDailyShares}次`);
      }
      
      // 生成分享码
      const shareCode = this.generateShareCode(userId, shareType, targetId);
      
      // 生成分享内容
      const shareContent = await this.generateShareContent(shareType, targetId, user.nickname);
      
      // 记录分享记录
      await query(`
        INSERT INTO share_records (
          sharer_user_id, share_type, share_target_id, share_code,
          share_title, share_desc, share_image, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
      `, [
        userId, shareType, targetId, shareCode,
        shareContent.title, shareContent.desc, shareContent.image
      ]);
      
      // 生成分享链接
      const shareUrls = this.generateShareUrls(shareCode, shareType, targetId);
      
      return {
        success: true,
        data: {
          shareCode,
          ...shareUrls,
          ...shareContent,
          distributorCode: user.distributor_code
        },
        message: '分享链接生成成功'
      };
      
    } catch (error) {
      throw error;
    }
  }

  /**
   * 处理分享链接点击
   */
  async handleShareClick(shareCode, visitorInfo = {}) {
    try {
      // 输入验证
      if (!shareCode || typeof shareCode !== 'string' || shareCode.length !== 12) {
        return { success: false, message: '分享码格式无效' };
      }

      // 查找分享记录（优化查询，只获取必要字段）
      const [shareRecords] = await query(`
        SELECT
          sr.id, sr.sharer_user_id, sr.share_type, sr.share_target_id,
          sr.share_title, sr.share_desc, sr.status,
          u.nickname as sharer_nickname, u.distributor_status
        FROM share_records sr
        JOIN users u ON sr.sharer_user_id = u.id
        WHERE sr.share_code = ? AND sr.status = 1 AND u.distributor_status = 1
        LIMIT 1
      `, [shareCode]);

      if (!shareRecords.length) {
        return { success: false, message: '分享链接无效或已失效' };
      }

      const shareRecord = shareRecords[0];

      // 异步更新点击次数（使用批量更新减少数据库压力）
      this.updateClickCountAsync(shareRecord.id);

      return {
        success: true,
        data: {
          shareInfo: {
            sharerId: shareRecord.sharer_user_id,
            sharerNickname: shareRecord.sharer_nickname,
            shareType: shareRecord.share_type,
            targetId: shareRecord.share_target_id,
            title: shareRecord.share_title,
            desc: shareRecord.share_desc
          }
        },
        message: '分享链接有效'
      };

    } catch (error) {
      console.error('处理分享点击失败:', error);
      return { success: false, message: '处理失败' };
    }
  }

  /**
   * 异步更新点击次数（批量处理）
   */
  updateClickCountAsync(shareRecordId) {
    if (!this.clickCountQueue) {
      this.clickCountQueue = new Map();
      this.clickCountTimer = null;
    }

    // 累加点击次数
    const currentCount = this.clickCountQueue.get(shareRecordId) || 0;
    this.clickCountQueue.set(shareRecordId, currentCount + 1);

    // 延迟批量更新
    if (this.clickCountTimer) {
      clearTimeout(this.clickCountTimer);
    }

    this.clickCountTimer = setTimeout(async () => {
      try {
        const updates = Array.from(this.clickCountQueue.entries());
        this.clickCountQueue.clear();

        // 批量更新
        for (const [recordId, count] of updates) {
          await query(
            'UPDATE share_records SET click_count = click_count + ? WHERE id = ?',
            [count, recordId]
          );
        }
      } catch (error) {
        console.error('批量更新点击次数失败:', error);
      }
    }, 5000); // 5秒后批量更新
  }

  /**
   * 绑定分销关系
   */
  async bindDistributorRelation(parentUserId, childUserId, shareCode) {
    const db = require('../src/config/database');
    const connection = await db.getConnection();
    
    try {
      await connection.beginTransaction();
      
      // 基础验证
      if (parentUserId === childUserId) {
        throw new Error('不能推荐自己');
      }
      
      // 检查子用户是否已有推荐关系
      const [existingRelation] = await connection.query(
        'SELECT id, parent_user_id FROM distributor_relations WHERE child_user_id = ?',
        [childUserId]
      );
      
      if (existingRelation.length > 0) {
        await connection.rollback();
        return { 
          success: false, 
          message: '该用户已有推荐关系',
          existingParent: existingRelation[0].parent_user_id
        };
      }
      
      // 检查父用户是否是有效分销商
      const [parentInfo] = await connection.query(
        'SELECT distributor_status, distributor_code FROM users WHERE id = ?',
        [parentUserId]
      );
      
      if (!parentInfo.length || parentInfo[0].distributor_status !== 1) {
        throw new Error('推荐人不是有效分销商');
      }
      
      // 建立一级关系
      await connection.query(`
        INSERT INTO distributor_relations (
          parent_user_id, child_user_id, level, bind_type, bind_source, created_at
        ) VALUES (?, ?, 1, 1, ?, NOW())
      `, [parentUserId, childUserId, shareCode]);
      
      const relationData = {
        level1: parentUserId,
        level2: null
      };
      
      // 查找是否需要建立二级关系
      const [grandParentRelation] = await connection.query(
        'SELECT parent_user_id FROM distributor_relations WHERE child_user_id = ? AND level = 1',
        [parentUserId]
      );
      
      if (grandParentRelation.length > 0) {
        const grandParentUserId = grandParentRelation[0].parent_user_id;
        
        // 检查祖父级用户是否是有效分销商
        const [grandParentInfo] = await connection.query(
          'SELECT distributor_status FROM users WHERE id = ?',
          [grandParentUserId]
        );
        
        if (grandParentInfo.length > 0 && grandParentInfo[0].distributor_status === 1) {
          // 建立二级关系
          await connection.query(`
            INSERT INTO distributor_relations (
              parent_user_id, child_user_id, level, bind_type, bind_source, created_at
            ) VALUES (?, ?, 2, 1, ?, NOW())
          `, [grandParentUserId, childUserId, shareCode]);
          
          relationData.level2 = grandParentUserId;
        }
      }
      
      // 更新分享记录的注册转化数
      await connection.query(
        'UPDATE share_records SET register_count = register_count + 1 WHERE share_code = ?',
        [shareCode]
      );
      
      // 更新分销商客户数
      await connection.query(
        'UPDATE users SET total_customers = total_customers + 1 WHERE id = ?',
        [parentUserId]
      );
      
      await connection.commit();
      
      return {
        success: true,
        data: {
          relations: relationData,
          bindSource: shareCode
        },
        message: '分销关系建立成功'
      };
      
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 生成分销商编码
   */
  generateDistributorCode(userId) {
    const timestamp = Date.now().toString().slice(-6);
    const userIdPadded = userId.toString().padStart(4, '0');
    const random = Math.random().toString(36).substr(2, 2).toUpperCase();
    return `D${userIdPadded}${timestamp}${random}`;
  }

  /**
   * 生成分享码
   */
  generateShareCode(userId, shareType, targetId) {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 6);
    const rawData = `${userId}_${shareType}_${targetId || 0}_${timestamp}_${random}`;
    return crypto.createHash('md5').update(rawData).digest('hex').substr(0, 12).toUpperCase();
  }

  /**
   * 生成分享内容
   */
  async generateShareContent(shareType, targetId, sharerNickname) {
    const baseUrl = process.env.MALL_BASE_URL || 'https://mall.xinjietea.com';
    
    switch (shareType) {
      case 1: // 商品分享
        const [product] = await query('SELECT name, main_image, price FROM products WHERE id = ?', [targetId]);
        if (product.length) {
          return {
            title: `${sharerNickname}推荐：${product[0].name}`,
            desc: `精选好茶，品质保证，现价￥${product[0].price}`,
            image: product[0].main_image || `${baseUrl}/images/default-product.jpg`
          };
        }
        break;
        
      case 2: // 商城分享
        return {
          title: `${sharerNickname}邀请您品茶`,
          desc: '心洁茗茶 - 发现好茶，品味生活',
          image: `${baseUrl}/images/mall-share.jpg`
        };
        
      case 3: // 活动分享
        return {
          title: `${sharerNickname}分享限时优惠`,
          desc: '限时特惠活动正在进行，好茶不等人！',
          image: `${baseUrl}/images/activity-share.jpg`
        };
    }
    
    // 默认分享内容
    return {
      title: `${sharerNickname}的茶叶推荐`,
      desc: '心洁茗茶，您的品茶首选',
      image: `${baseUrl}/images/default-share.jpg`
    };
  }

  /**
   * 生成分享链接
   */
  generateShareUrls(shareCode, shareType, targetId) {
    const baseUrl = process.env.MALL_BASE_URL || 'https://mall.xinjietea.com';
    
    let shareUrl, miniProgramPath;
    
    switch (shareType) {
      case 1: // 商品分享
        shareUrl = `${baseUrl}/product/${targetId}?share=${shareCode}`;
        miniProgramPath = `/pages/product/detail?id=${targetId}&share=${shareCode}`;
        break;
      case 2: // 商城分享
        shareUrl = `${baseUrl}/?share=${shareCode}`;
        miniProgramPath = `/pages/index/index?share=${shareCode}`;
        break;
      case 3: // 活动分享
        shareUrl = `${baseUrl}/activity/${targetId}?share=${shareCode}`;
        miniProgramPath = `/pages/activity/detail?id=${targetId}&share=${shareCode}`;
        break;
      default:
        shareUrl = `${baseUrl}/?share=${shareCode}`;
        miniProgramPath = `/pages/index/index?share=${shareCode}`;
    }
    
    return {
      shareUrl,
      miniProgramPath,
      qrCodeUrl: `${baseUrl}/api/qrcode/generate?data=${encodeURIComponent(shareUrl)}`
    };
  }

  /**
   * 计算分销佣金
   */
  async calculateDistributionCommission(orderId) {
    // 输入验证
    if (!orderId || !Number.isInteger(Number(orderId))) {
      return { success: false, message: '订单ID无效' };
    }

    const db = require('../src/config/database');
    const connection = await db.getConnection();

    try {
      await connection.beginTransaction();

      // 获取订单信息和买家分销关系（一次查询优化）
      const [orderWithRelations] = await connection.query(`
        SELECT
          o.id, o.user_id, o.total_amount, o.order_status, o.created_at,
          GROUP_CONCAT(
            CONCAT(dr.parent_user_id, ':', dr.level, ':', u.distributor_status)
            ORDER BY dr.level SEPARATOR '|'
          ) as relations_data
        FROM orders o
        LEFT JOIN distributor_relations dr ON o.user_id = dr.child_user_id AND dr.status = 1
        LEFT JOIN users u ON dr.parent_user_id = u.id AND u.distributor_status = 1
        WHERE o.id = ? AND o.order_status >= 2
        GROUP BY o.id
      `, [orderId]);

      if (!orderWithRelations.length) {
        await connection.rollback();
        return { success: false, message: '订单不存在或未支付' };
      }

      const order = orderWithRelations[0];
      const buyerUserId = order.user_id;
      const orderAmount = parseFloat(order.total_amount);

      // 检查最小订单金额
      const minOrderAmount = await this.getDistributionConfig('min_order_amount') || 10;
      if (orderAmount < minOrderAmount) {
        await connection.rollback();
        return { success: false, message: `订单金额低于${minOrderAmount}元，不产生佣金` };
      }

      // 解析分销关系数据
      const relations = [];
      if (order.relations_data) {
        const relationItems = order.relations_data.split('|');
        for (const item of relationItems) {
          const [parentUserId, level, distributorStatus] = item.split(':');
          if (distributorStatus === '1') { // 只处理有效分销商
            relations.push({
              parent_user_id: parseInt(parentUserId),
              level: parseInt(level)
            });
          }
        }
      }

      if (relations.length === 0) {
        await connection.rollback();
        return { success: false, message: '无有效分销关系' };
      }

      // 检查是否已经计算过佣金（优化查询）
      const [existingCommission] = await connection.query(
        'SELECT 1 FROM distributor_orders WHERE order_id = ? LIMIT 1',
        [orderId]
      );

      if (existingCommission.length > 0) {
        await connection.rollback();
        return { success: false, message: '该订单已计算过佣金' };
      }

      const commissionResults = [];

      // 获取佣金配置
      const level1Rate = await this.getDistributionConfig('level1_commission_rate') || 0.05;
      const level2Rate = await this.getDistributionConfig('level2_commission_rate') || 0.02;
      const level1Points = await this.getDistributionConfig('level1_points_rate') || 10;
      const level2Points = await this.getDistributionConfig('level2_points_rate') || 5;
      const maxCommissionPerOrder = await this.getDistributionConfig('max_commission_per_order') || 500;

      const commissionRates = { 1: level1Rate, 2: level2Rate };
      const pointsRates = { 1: level1Points, 2: level2Points };

      // 计算各级佣金
      for (const relation of relations) {
        const level = relation.level;
        const distributorUserId = relation.parent_user_id;

        const commissionRate = commissionRates[level];
        const pointsRate = pointsRates[level];

        if (!commissionRate) continue;

        let commissionAmount = parseFloat((orderAmount * commissionRate).toFixed(2));

        // 限制单笔最大佣金
        if (commissionAmount > maxCommissionPerOrder) {
          commissionAmount = maxCommissionPerOrder;
        }

        const pointsReward = Math.floor(orderAmount * pointsRate);

        // 记录分销订单
        await connection.query(`
          INSERT INTO distributor_orders (
            order_id, buyer_user_id, distributor_user_id, level,
            order_amount, commission_rate, commission_amount, points_reward,
            status, created_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 0, NOW())
        `, [orderId, buyerUserId, distributorUserId, level, orderAmount, commissionRate, commissionAmount, pointsReward]);

        // 记录佣金记录
        await connection.query(`
          INSERT INTO commission_records (
            user_id, order_id, commission_type, amount, points,
            status, remark, created_at
          ) VALUES (?, ?, ?, ?, ?, 0, ?, NOW())
        `, [distributorUserId, orderId, level, commissionAmount, pointsReward, `${level}级分销佣金`]);

        commissionResults.push({
          level,
          distributorUserId,
          commissionAmount,
          pointsReward
        });
      }

      // 更新分享记录的订单转化数和总佣金
      if (commissionResults.length > 0) {
        const totalCommission = commissionResults.reduce((sum, item) => sum + item.commissionAmount, 0);
        const level1DistributorId = commissionResults.find(r => r.level === 1)?.distributorUserId;

        if (level1DistributorId) {
          await connection.query(`
            UPDATE share_records
            SET order_count = order_count + 1,
                total_commission = total_commission + ?
            WHERE sharer_user_id = ?
          `, [totalCommission, level1DistributorId]);
        }
      }

      await connection.commit();

      return {
        success: true,
        data: {
          orderId,
          orderAmount,
          commissions: commissionResults,
          totalCommission: commissionResults.reduce((sum, item) => sum + item.commissionAmount, 0)
        },
        message: '佣金计算完成'
      };

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 结算佣金
   */
  async settleCommission(userId, settleType = 'auto') {
    const db = require('../src/config/database');
    const connection = await db.getConnection();

    try {
      await connection.beginTransaction();

      // 获取结算延迟天数
      const settleDelayDays = await this.getDistributionConfig('settle_delay_days') || 7;

      // 获取待结算佣金（订单完成且超过延迟期）
      const [pendingCommissions] = await connection.query(`
        SELECT cr.*, do.order_id, o.order_status, o.updated_at as order_updated_at
        FROM commission_records cr
        JOIN distributor_orders do ON cr.order_id = do.order_id AND cr.user_id = do.distributor_user_id
        JOIN orders o ON cr.order_id = o.id
        WHERE cr.user_id = ?
          AND cr.status = 0
          AND o.order_status = 3
          AND o.updated_at <= DATE_SUB(NOW(), INTERVAL ? DAY)
      `, [userId, settleDelayDays]);

      if (pendingCommissions.length === 0) {
        await connection.rollback();
        return { success: false, message: '暂无可结算佣金' };
      }

      let totalCommission = 0;
      let totalPoints = 0;
      const settledOrderIds = [];

      // 批量结算
      for (const commission of pendingCommissions) {
        totalCommission += parseFloat(commission.amount);
        totalPoints += commission.points;
        settledOrderIds.push(commission.order_id);

        // 更新佣金记录状态
        await connection.query(
          'UPDATE commission_records SET status = 1, settle_time = NOW() WHERE id = ?',
          [commission.id]
        );

        // 更新分销订单状态
        await connection.query(
          'UPDATE distributor_orders SET status = 1, settle_time = NOW() WHERE order_id = ? AND distributor_user_id = ?',
          [commission.order_id, userId]
        );
      }

      // 增加用户余额
      const balanceService = require('./balanceService');
      await balanceService.updateUserBalanceWithTransaction(
        connection,
        userId,
        totalCommission,
        1, // 增加
        5, // 分销佣金来源
        0,
        `分销佣金结算：${totalCommission}元，订单数：${pendingCommissions.length}`
      );

      // 增加积分
      if (totalPoints > 0) {
        const pointsService = require('./pointsService');
        await pointsService.addPointsForDistribution(connection, userId, totalPoints);
      }

      // 更新用户累计佣金
      await connection.query(
        'UPDATE users SET total_commission = total_commission + ?, updated_at = NOW() WHERE id = ?',
        [totalCommission, userId]
      );

      await connection.commit();

      return {
        success: true,
        data: {
          settledOrders: pendingCommissions.length,
          settledOrderIds,
          totalCommission: totalCommission.toFixed(2),
          totalPoints,
          settleTime: new Date()
        },
        message: '佣金结算成功'
      };

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }
}

module.exports = new DistributionService();
