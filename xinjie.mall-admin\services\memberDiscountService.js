const memberService = require('./memberService');
const db = require('../src/config/database');

/**
 * 会员折扣集成服务
 * 处理订单中的会员折扣计算和应用
 */
class MemberDiscountService {

  /**
   * 计算订单会员折扣
   * @param {number} userId - 用户ID
   * @param {Array} orderItems - 订单商品列表
   * @param {number} originalTotal - 原始总金额
   * @returns {Object} 折扣计算结果
   */
  async calculateOrderDiscount(userId, orderItems, originalTotal) {
    try {
      // 获取用户会员折扣信息
      const discountInfo = await memberService.calculateMemberDiscount(userId, originalTotal);

      // 计算商品级别的折扣分配
      const itemDiscounts = this.distributeDiscountToItems(orderItems, discountInfo.discountAmount);

      return {
        originalTotal: parseFloat(originalTotal).toFixed(2),
        memberDiscount: {
          levelName: discountInfo.levelName,
          discountRate: discountInfo.discountRate,
          discountAmount: discountInfo.discountAmount,
          discountText: discountInfo.discountText,
          hasDiscount: discountInfo.hasDiscount
        },
        finalTotal: discountInfo.finalAmount,
        itemDiscounts,
        savings: discountInfo.discountAmount
      };

    } catch (error) {
      // 如果计算失败，返回无折扣结果
      return {
        originalTotal: parseFloat(originalTotal).toFixed(2),
        memberDiscount: {
          levelName: '普通会员',
          discountRate: 1.00,
          discountAmount: '0.00',
          discountText: '无折扣',
          hasDiscount: false
        },
        finalTotal: parseFloat(originalTotal).toFixed(2),
        itemDiscounts: [],
        savings: '0.00'
      };
    }
  }

  /**
   * 应用会员折扣到订单
   * @param {number} userId - 用户ID
   * @param {Object} orderData - 订单数据
   * @returns {Object} 应用折扣后的订单数据
   */
  async applyMemberDiscountToOrder(userId, orderData) {
    try {
      const { items, originalTotal } = orderData;

      // 计算会员折扣
      const discountResult = await this.calculateOrderDiscount(userId, items, originalTotal);

      // 更新订单数据
      const updatedOrderData = {
        ...orderData,
        originalTotal: discountResult.originalTotal,
        memberDiscount: discountResult.memberDiscount,
        discountAmount: discountResult.memberDiscount.discountAmount,
        finalTotal: discountResult.finalTotal,
        items: items.map((item, index) => ({
          ...item,
          memberDiscount: discountResult.itemDiscounts[index] || 0,
          finalPrice: (parseFloat(item.price) - (discountResult.itemDiscounts[index] || 0)).toFixed(2)
        }))
      };

      return updatedOrderData;

    } catch (error) {
      console.error('应用会员折扣失败:', error);
      // 返回原始订单数据
      return orderData;
    }
  }

  /**
   * 获取用户可享受的折扣预览
   * @param {number} userId - 用户ID
   * @param {number} amount - 金额
   * @returns {Object} 折扣预览
   */
  async getDiscountPreview(userId, amount) {
    try {
      const discountInfo = await memberService.calculateMemberDiscount(userId, amount);

      return {
        canDiscount: discountInfo.hasDiscount,
        preview: {
          originalAmount: discountInfo.originalAmount,
          discountAmount: discountInfo.discountAmount,
          finalAmount: discountInfo.finalAmount,
          discountText: discountInfo.discountText,
          levelName: discountInfo.levelName,
          savingsText: discountInfo.hasDiscount ? `节省 ¥${discountInfo.discountAmount}` : '暂无优惠'
        }
      };

    } catch (error) {
      return {
        canDiscount: false,
        preview: {
          originalAmount: parseFloat(amount).toFixed(2),
          discountAmount: '0.00',
          finalAmount: parseFloat(amount).toFixed(2),
          discountText: '无折扣',
          levelName: '普通会员',
          savingsText: '暂无优惠'
        }
      };
    }
  }

  /**
   * 记录会员折扣使用
   * @param {number} userId - 用户ID
   * @param {number} orderId - 订单ID
   * @param {number} discountAmount - 折扣金额
   * @param {string} levelName - 会员等级名称
   */
  async recordMemberDiscountUsage(userId, orderId, discountAmount, levelName) {
    try {
      if (parseFloat(discountAmount) <= 0) {
        return; // 无折扣，不记录
      }

      await db.query(`
        INSERT INTO member_discount_records (
          user_id, order_id, discount_amount, level_name, created_at
        ) VALUES (?, ?, ?, ?, NOW())
      `, [userId, orderId, discountAmount, levelName]);

    } catch (error) {
      console.error('记录会员折扣使用失败:', error);
      // 不抛出错误，避免影响主流程
    }
  }

  /**
   * 获取用户会员折扣使用统计
   * @param {number} userId - 用户ID
   * @returns {Object} 使用统计
   */
  async getMemberDiscountStats(userId) {
    try {
      const [statsResult] = await db.query(`
        SELECT 
          COUNT(*) as usage_count,
          COALESCE(SUM(discount_amount), 0) as total_savings,
          MAX(created_at) as last_used
        FROM member_discount_records 
        WHERE user_id = ?
      `, [userId]);

      const stats = statsResult[0] || {};

      return {
        usageCount: stats.usage_count || 0,
        totalSavings: parseFloat(stats.total_savings || 0).toFixed(2),
        lastUsed: stats.last_used,
        hasUsed: (stats.usage_count || 0) > 0
      };

    } catch (error) {
      return {
        usageCount: 0,
        totalSavings: '0.00',
        lastUsed: null,
        hasUsed: false
      };
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 将折扣金额分配到各个商品
   * @param {Array} items - 商品列表
   * @param {number} totalDiscount - 总折扣金额
   * @returns {Array} 每个商品的折扣金额
   */
  distributeDiscountToItems(items, totalDiscount) {
    if (!items || items.length === 0 || parseFloat(totalDiscount) <= 0) {
      return [];
    }

    const totalAmount = items.reduce((sum, item) => sum + (parseFloat(item.price) * item.quantity), 0);
    const discountAmount = parseFloat(totalDiscount);

    return items.map(item => {
      const itemTotal = parseFloat(item.price) * item.quantity;
      const itemDiscountRatio = itemTotal / totalAmount;
      const itemDiscount = discountAmount * itemDiscountRatio;
      return parseFloat(itemDiscount.toFixed(2));
    });
  }

  /**
   * 验证折扣计算的准确性
   * @param {number} originalTotal - 原始总金额
   * @param {number} discountAmount - 折扣金额
   * @param {number} finalTotal - 最终金额
   * @returns {boolean} 是否准确
   */
  validateDiscountCalculation(originalTotal, discountAmount, finalTotal) {
    const calculatedFinal = parseFloat(originalTotal) - parseFloat(discountAmount);
    const actualFinal = parseFloat(finalTotal);
    const difference = Math.abs(calculatedFinal - actualFinal);
    
    // 允许0.01的误差（由于浮点数精度问题）
    return difference <= 0.01;
  }
}

module.exports = new MemberDiscountService();
