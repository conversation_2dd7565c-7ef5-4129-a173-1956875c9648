const db = require('../src/config/database');

/**
 * 会员服务层
 * 处理会员等级、积分、权益等业务逻辑
 */
class MemberService {

  /**
   * 获取用户会员信息
   * @param {number} userId - 用户ID
   * @returns {Object} 会员信息
   */
  async getUserMemberInfo(userId) {
    try {
      // 获取用户基本信息和会员信息
      const [userResult] = await db.query(`
        SELECT 
          u.id, u.nickname, u.phone, u.balance, u.points,
          u.user_level, u.member_expire_time, u.created_at,
          ml.level_name, ml.level_code, ml.discount_rate,
          ml.description as level_description,
          ml.min_points as level_min_points
        FROM users u
        LEFT JOIN member_levels ml ON u.user_level = ml.id
        WHERE u.id = ?
      `, [userId]);

      if (!userResult.length) {
        throw new Error('用户不存在');
      }

      const user = userResult[0];

      // 获取下一等级信息
      const nextLevel = await this.getNextMemberLevel(user.points);

      // 计算升级进度
      const upgradeProgress = this.calculateUpgradeProgress(user.points, user.user_level, nextLevel);

      // 获取会员权益
      const benefits = await this.getMemberBenefits(user.user_level);

      return {
        userId: user.id,
        nickname: user.nickname,
        phone: user.phone,
        balance: parseFloat(user.balance).toFixed(2),
        points: user.points,
        currentLevel: {
          id: user.user_level,
          code: user.level_code,
          name: user.level_name,
          discountRate: user.discount_rate,
          description: user.level_description,
          minPoints: user.level_min_points
        },
        nextLevel,
        upgradeProgress,
        benefits,
        memberExpireTime: user.member_expire_time,
        joinTime: user.created_at,
        isVip: user.user_level > 1
      };

    } catch (error) {
      throw new Error(`获取用户会员信息失败: ${error.message}`);
    }
  }

  /**
   * 获取所有会员等级
   * @returns {Array} 会员等级列表
   */
  async getAllMemberLevels() {
    try {
      const [levels] = await db.query(`
        SELECT 
          id, level_code, level_name, min_points, discount_rate,
          description, sort_order, status
        FROM member_levels 
        WHERE status = 1
        ORDER BY sort_order ASC, min_points ASC
      `);

      return levels.map(level => ({
        id: level.id,
        code: level.level_code,
        name: level.level_name,
        minPoints: level.min_points,
        discountRate: level.discount_rate,
        description: level.description,
        sortOrder: level.sort_order,
        discountText: `${((1 - level.discount_rate) * 100).toFixed(0)}折`
      }));

    } catch (error) {
      throw new Error(`获取会员等级失败: ${error.message}`);
    }
  }

  /**
   * 获取会员权益
   * @param {number} levelId - 会员等级ID
   * @returns {Array} 权益列表
   */
  async getMemberBenefits(levelId) {
    try {
      const [benefits] = await db.query(`
        SELECT 
          id, benefit_type, benefit_name, benefit_value,
          description, sort_order
        FROM member_benefits 
        WHERE level_id = ? AND status = 1
        ORDER BY sort_order ASC
      `, [levelId]);

      return benefits.map(benefit => ({
        id: benefit.id,
        type: benefit.benefit_type,
        name: benefit.benefit_name,
        value: benefit.benefit_value,
        description: benefit.description,
        icon: this.getBenefitIcon(benefit.benefit_type)
      }));

    } catch (error) {
      // 如果没有权益数据，返回默认权益
      return this.getDefaultBenefits(levelId);
    }
  }

  /**
   * 计算会员折扣
   * @param {number} userId - 用户ID
   * @param {number} originalAmount - 原始金额
   * @returns {Object} 折扣信息
   */
  async calculateMemberDiscount(userId, originalAmount) {
    try {
      const [userResult] = await db.query(`
        SELECT u.user_level, ml.discount_rate, ml.level_name
        FROM users u
        LEFT JOIN member_levels ml ON u.user_level = ml.id
        WHERE u.id = ?
      `, [userId]);

      if (!userResult.length) {
        throw new Error('用户不存在');
      }

      const user = userResult[0];
      const discountRate = user.discount_rate || 1.00;
      const discountAmount = originalAmount * (1 - discountRate);
      const finalAmount = originalAmount - discountAmount;

      return {
        originalAmount: parseFloat(originalAmount).toFixed(2),
        discountRate,
        discountAmount: parseFloat(discountAmount).toFixed(2),
        finalAmount: parseFloat(finalAmount).toFixed(2),
        levelName: user.level_name,
        hasDiscount: discountRate < 1.00,
        discountText: discountRate < 1.00 ? `${((1 - discountRate) * 100).toFixed(0)}折` : '无折扣'
      };

    } catch (error) {
      throw new Error(`计算会员折扣失败: ${error.message}`);
    }
  }

  /**
   * 获取用户积分记录
   * @param {number} userId - 用户ID
   * @param {number} page - 页码
   * @param {number} pageSize - 每页数量
   * @returns {Object} 积分记录
   */
  async getUserPointsHistory(userId, page = 1, pageSize = 10) {
    try {
      const offset = (page - 1) * pageSize;

      // 查询总数
      const [countResult] = await db.query(
        'SELECT COUNT(*) as total FROM points_records WHERE user_id = ?',
        [userId]
      );
      const total = countResult[0]?.total || 0;

      // 查询列表
      const [list] = await db.query(`
        SELECT 
          id, type, points, points_before, points_after,
          source, source_id, remark, created_at
        FROM points_records 
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
      `, [userId, pageSize, offset]);

      return {
        list: (list || []).map(record => ({
          id: record.id,
          type: record.type,
          typeText: record.type === 1 ? '获得' : '消费',
          points: record.points,
          pointsBefore: record.points_before,
          pointsAfter: record.points_after,
          source: record.source,
          sourceText: this.getPointsSourceText(record.source),
          remark: record.remark,
          createdAt: record.created_at
        })),
        total,
        page,
        pageSize
      };

    } catch (error) {
      // 如果积分记录表不存在，返回空数据
      return {
        list: [],
        total: 0,
        page,
        pageSize
      };
    }
  }

  /**
   * 检查并升级用户会员等级
   * @param {number} userId - 用户ID
   * @returns {Object} 升级结果
   */
  async checkAndUpgradeMember(userId) {
    const connection = await db.getConnection();

    try {
      await connection.beginTransaction();

      // 获取用户当前积分和等级
      const [userResult] = await connection.query(
        'SELECT id, points, user_level FROM users WHERE id = ? FOR UPDATE',
        [userId]
      );

      if (!userResult.length) {
        throw new Error('用户不存在');
      }

      const user = userResult[0];
      const currentPoints = user.points;
      const currentLevel = user.user_level;

      // 查找应该升级到的等级
      const [levelResult] = await connection.query(`
        SELECT id, level_name, min_points
        FROM member_levels 
        WHERE min_points <= ? AND status = 1
        ORDER BY min_points DESC
        LIMIT 1
      `, [currentPoints]);

      if (!levelResult.length) {
        await connection.commit();
        return { upgraded: false, message: '无可升级等级' };
      }

      const targetLevel = levelResult[0];

      // 如果等级有变化，执行升级
      if (targetLevel.id !== currentLevel) {
        await connection.query(
          'UPDATE users SET user_level = ?, updated_at = NOW() WHERE id = ?',
          [targetLevel.id, userId]
        );

        await connection.commit();

        return {
          upgraded: true,
          fromLevel: currentLevel,
          toLevel: targetLevel.id,
          levelName: targetLevel.level_name,
          message: `恭喜升级到${targetLevel.level_name}！`
        };
      }

      await connection.commit();
      return { upgraded: false, message: '等级无变化' };

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 获取下一等级信息
   */
  async getNextMemberLevel(currentPoints) {
    try {
      const [nextLevelResult] = await db.query(`
        SELECT id, level_name, min_points, discount_rate
        FROM member_levels 
        WHERE min_points > ? AND status = 1
        ORDER BY min_points ASC
        LIMIT 1
      `, [currentPoints]);

      if (!nextLevelResult.length) {
        return null; // 已是最高等级
      }

      return {
        id: nextLevelResult[0].id,
        name: nextLevelResult[0].level_name,
        minPoints: nextLevelResult[0].min_points,
        discountRate: nextLevelResult[0].discount_rate
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * 计算升级进度
   */
  calculateUpgradeProgress(currentPoints, currentLevelId, nextLevel) {
    if (!nextLevel) {
      return {
        progress: 100,
        needPoints: 0,
        progressText: '已达最高等级'
      };
    }

    const needPoints = nextLevel.minPoints - currentPoints;
    const progress = Math.min(100, (currentPoints / nextLevel.minPoints) * 100);

    return {
      progress: Math.round(progress),
      needPoints: Math.max(0, needPoints),
      progressText: needPoints > 0 ? `还需${needPoints}积分升级` : '可升级'
    };
  }

  /**
   * 获取权益图标
   */
  getBenefitIcon(benefitType) {
    const icons = {
      'discount': '💰',
      'shipping': '🚚',
      'service': '👥',
      'gift': '🎁',
      'points': '⭐',
      'exclusive': '👑'
    };
    return icons[benefitType] || '🎯';
  }

  /**
   * 获取默认权益
   */
  getDefaultBenefits(levelId) {
    const defaultBenefits = {
      1: [
        { name: '基础服务', description: '享受基础购物服务', icon: '🛍️' }
      ],
      2: [
        { name: '95折优惠', description: '全场商品95折', icon: '💰' },
        { name: '优先客服', description: '享受优先客服服务', icon: '👥' }
      ]
    };

    return defaultBenefits[levelId] || defaultBenefits[1];
  }

  /**
   * 获取积分来源文本
   */
  getPointsSourceText(source) {
    const sourceTexts = {
      1: '购物获得',
      2: '签到获得',
      3: '活动获得',
      4: '消费抵扣',
      5: '系统调整'
    };
    return sourceTexts[source] || '其他';
  }
}

module.exports = new MemberService();
