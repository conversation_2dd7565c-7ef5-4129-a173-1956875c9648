const balanceService = require('./balanceService');
const memberDiscountService = require('./memberDiscountService');
const pointsService = require('./pointsService');
const WechatPayServiceV2 = require('./wechatPayServiceV2');
const db = require('../src/config/database');

/**
 * 订单支付服务层
 * 设计原则：
 * 1. 支付方式统一：余额、第三方、混合支付统一接口
 * 2. 状态管理：完整的支付状态流转
 * 3. 扩展性：为真实支付预留完整接口
 * 4. 安全性：支付金额验证和防重复支付
 */
class OrderPaymentService {

  /**
   * 获取订单支付选项
   * @param {number} orderId - 订单ID
   * @param {number} userId - 用户ID
   * @returns {Object} 支付选项信息
   */
  async getPaymentOptions(orderId, userId) {
    try {
      // 获取订单信息
      const order = await this.getOrderInfo(orderId, userId);

      // 获取用户余额信息
      const balanceInfo = await balanceService.getUserBalanceInfo(userId);

      // 获取会员折扣预览
      const originalAmount = parseFloat(order.total_amount);
      const memberDiscount = await memberDiscountService.getDiscountPreview(userId, originalAmount);

      const orderAmount = parseFloat(memberDiscount.preview.finalAmount);
      const userBalance = balanceInfo.balanceFloat;
      const maxBalanceUse = Math.min(userBalance, orderAmount);
      
      return {
        success: true,
        data: {
          order: {
            id: order.id,
            orderNo: order.order_no,
            originalAmount: parseFloat(originalAmount).toFixed(2),
            finalAmount: parseFloat(orderAmount).toFixed(2),
            status: order.order_status,
            paymentStatus: order.payment_status
          },
          memberDiscount: memberDiscount.preview,
          balance: {
            available: balanceInfo.balance,
            maxUse: parseFloat(maxBalanceUse).toFixed(2),
            canFullPay: userBalance >= orderAmount
          },
          paymentMethods: [
            {
              code: 'balance',
              name: '余额支付',
              enabled: userBalance > 0,
              icon: '💰',
              description: `可用余额：${balanceInfo.balance}元`
            },
            {
              code: 'wechat',
              name: '微信支付',
              enabled: true,
              icon: '💚',
              description: '安全便捷的微信支付'
            },
            {
              code: 'alipay',
              name: '支付宝',
              enabled: true,
              icon: '💙',
              description: '支付宝快捷支付'
            },
            {
              code: 'mixed',
              name: '混合支付',
              enabled: userBalance > 0 && userBalance < orderAmount,
              icon: '🔄',
              description: '余额+第三方支付'
            }
          ]
        }
      };
      
    } catch (error) {
      throw new Error(`获取支付选项失败: ${error.message}`);
    }
  }

  /**
   * 创建支付订单
   * @param {Object} paymentData - 支付数据
   * @returns {Object} 支付结果
   */
  async createPayment(paymentData) {
    try {
      const {
        orderId,
        userId,
        paymentMethod,
        balanceAmount = 0,
        thirdPartyMethod = 'wechat'
      } = paymentData;

      // 验证订单
      const order = await this.getOrderInfo(orderId, userId);
      const orderAmount = parseFloat(order.final_amount || order.total_amount);

      // 根据支付方式处理
      switch (paymentMethod) {
        case 'balance':
          return await this.processBalancePayment(userId, orderId, orderAmount);
          
        case 'wechat':
        case 'alipay':
          return await this.processThirdPartyPayment(userId, orderId, orderAmount, paymentMethod);
          
        case 'mixed':
          return await this.processMixedPayment(userId, orderId, orderAmount, balanceAmount, thirdPartyMethod);
          
        default:
          throw new Error('不支持的支付方式');
      }
      
    } catch (error) {
      throw new Error(`创建支付失败: ${error.message}`);
    }
  }

  /**
   * 处理纯余额支付
   */
  async processBalancePayment(userId, orderId, amount) {
    try {
      const result = await balanceService.balancePayment(
        userId,
        orderId,
        amount,
        `订单余额支付：${amount}元`
      );

      return {
        success: true,
        paymentType: 'balance',
        paymentCompleted: true,
        data: {
          orderId: result.orderId,
          orderNo: result.orderNo,
          paidAmount: result.paidAmount,
          newBalance: result.newBalance,
          paymentMethod: 'balance',
          paymentTime: new Date().toISOString()
        },
        message: '余额支付成功'
      };
      
    } catch (error) {
      throw new Error(`余额支付失败: ${error.message}`);
    }
  }

  /**
   * 处理第三方支付（真实支付版本）
   */
  async processThirdPartyPayment(userId, orderId, amount, method) {
    try {
      const order = await this.getOrderInfo(orderId, userId);

      if (method === 'wechat') {
        // 使用真实的微信支付服务
        const wechatPayService = new WechatPayServiceV2();

        const paymentData = await wechatPayService.createPayOrder({
          userId,
          amount,
          orderNo: order.order_no,
          description: `心洁茶叶商城-订单支付`
        });

        return {
          success: true,
          paymentType: 'wechat',
          paymentCompleted: false, // 需要用户完成支付
          data: {
            orderId,
            orderNo: order.order_no,
            amount: parseFloat(amount).toFixed(2),
            paymentMethod: 'wechat',
            paymentData: paymentData
          },
          message: '微信支付订单创建成功，请完成支付'
        };

      } else if (method === 'alipay') {
        // 支付宝支付（暂时使用模拟数据）
        return {
          success: true,
          paymentType: 'alipay',
          paymentCompleted: false,
          data: {
            orderId,
            orderNo: order.order_no,
            amount: parseFloat(amount).toFixed(2),
            paymentMethod: 'alipay',
            paymentData: this.generateMockPaymentData(orderId, amount, 'alipay')
          },
          message: '支付宝支付订单创建成功，请完成支付'
        };
      } else {
        throw new Error('不支持的支付方式');
      }

    } catch (error) {
      console.error('第三方支付处理失败:', error);
      throw new Error(`第三方支付失败: ${error.message}`);
    }
  }

  /**
   * 处理混合支付
   */
  async processMixedPayment(userId, orderId, totalAmount, balanceAmount, thirdPartyMethod) {
    try {
      const result = await balanceService.mixedPayment(
        userId,
        orderId,
        totalAmount,
        balanceAmount,
        thirdPartyMethod
      );

      // 如果需要第三方支付
      if (!result.paymentCompleted) {
        // 在真实支付中，这里会返回第三方支付参数
        // 在方案B中，我们模拟第三方支付也成功
        const thirdPartyAmount = parseFloat(result.thirdPartyAmount);
        await this.completeThirdPartyPayment(orderId, thirdPartyMethod, `mock_mixed_${Date.now()}`);
        
        result.paymentCompleted = true;
        result.message = '混合支付成功';
      }

      return {
        success: true,
        paymentType: 'mixed',
        paymentCompleted: result.paymentCompleted,
        data: {
          orderId: result.orderId,
          totalAmount: result.totalAmount,
          balanceUsed: result.balanceUsed,
          thirdPartyAmount: result.thirdPartyAmount,
          newBalance: result.newBalance,
          paymentTime: new Date().toISOString(),
          mockPayment: true
        },
        message: result.message
      };
      
    } catch (error) {
      throw new Error(`混合支付失败: ${error.message}`);
    }
  }

  /**
   * 完成第三方支付（模拟回调处理）
   */
  async completeThirdPartyPayment(orderId, paymentMethod, transactionId) {
    const connection = await db.getConnection();

    try {
      await connection.beginTransaction();

      // 获取订单信息
      const [orderResult] = await connection.query(
        'SELECT user_id, final_amount, total_amount FROM orders WHERE id = ?',
        [orderId]
      );

      if (!orderResult.length) {
        throw new Error('订单不存在');
      }

      const order = orderResult[0];

      // 更新订单支付状态
      await connection.query(`
        UPDATE orders
        SET payment_status = 1,
            payment_method = ?,
            transaction_id = ?,
            payment_time = NOW(),
            updated_at = NOW()
        WHERE id = ? AND payment_status = 0
      `, [paymentMethod, transactionId, orderId]);

      await connection.commit();

      // 支付完成后处理积分奖励（异步处理，不影响支付流程）
      try {
        const orderAmount = parseFloat(order.final_amount || order.total_amount);
        await pointsService.earnPointsFromOrder(order.user_id, orderAmount, orderId);
      } catch (pointsError) {
        console.error('积分奖励处理失败:', pointsError);
      }

      // 支付完成后处理分销佣金（异步处理，不影响支付流程）
      try {
        const distributionService = require('./distributionService');
        await distributionService.calculateDistributionCommission(orderId);
      } catch (distributionError) {
        console.error('分销佣金计算失败:', distributionError);
      }

      return {
        success: true,
        orderId,
        paymentMethod,
        transactionId,
        message: '支付完成'
      };

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 查询支付状态
   */
  async getPaymentStatus(orderId, userId) {
    try {
      const order = await this.getOrderInfo(orderId, userId);
      
      return {
        success: true,
        data: {
          orderId: order.id,
          orderNo: order.order_no,
          paymentStatus: order.payment_status,
          orderStatus: order.order_status,
          totalAmount: parseFloat(order.total_amount).toFixed(2),
          finalAmount: parseFloat(order.final_amount || order.total_amount).toFixed(2),
          balanceUsed: parseFloat(order.balance_used || 0).toFixed(2),
          paymentMethod: order.payment_method,
          paymentTime: order.payment_time,
          transactionId: order.transaction_id,
          isPaid: order.payment_status === 1,
          statusText: this.getOrderStatusText(order.order_status, order.payment_status)
        }
      };
      
    } catch (error) {
      throw new Error(`查询支付状态失败: ${error.message}`);
    }
  }

  /**
   * 支付回调处理（为真实支付预留）
   */
  async handlePaymentCallback(callbackData) {
    try {
      const { orderNo, paymentStatus, transactionId, paymentMethod } = callbackData;
      
      if (paymentStatus === 'success') {
        // 查找订单
        const [orders] = await db.query(
          'SELECT id FROM orders WHERE order_no = ? AND payment_status = 0',
          [orderNo]
        );
        
        if (!orders.length) {
          throw new Error('订单不存在或已支付');
        }
        
        const orderId = orders[0].id;
        
        // 完成支付
        await this.completeThirdPartyPayment(orderId, paymentMethod, transactionId);
        
        return {
          success: true,
          orderId,
          message: '支付回调处理成功'
        };
      }
      
      return {
        success: false,
        message: '支付失败'
      };
      
    } catch (error) {
      throw new Error(`支付回调处理失败: ${error.message}`);
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 获取订单信息
   */
  async getOrderInfo(orderId, userId) {
    const [orders] = await db.query(`
      SELECT id, order_no, user_id, total_amount, final_amount, 
             payment_status, order_status, payment_method, 
             payment_time, transaction_id, balance_used
      FROM orders 
      WHERE id = ? AND user_id = ?
    `, [orderId, userId]);
    
    if (!orders.length) {
      throw new Error('订单不存在或无权限访问');
    }
    
    const order = orders[0];
    
    if (order.payment_status === 1) {
      throw new Error('订单已支付');
    }
    
    return order;
  }

  /**
   * 获取订单状态文本
   */
  getOrderStatusText(orderStatus, paymentStatus) {
    if (paymentStatus === 0) return '待支付';
    if (paymentStatus === 1 && orderStatus === 0) return '已支付';
    if (orderStatus === 1) return '待发货';
    if (orderStatus === 2) return '已发货';
    if (orderStatus === 3) return '已完成';
    if (orderStatus === -1) return '已取消';
    return '未知状态';
  }
}

module.exports = new OrderPaymentService();
