const db = require('../src/config/database');
const memberService = require('./memberService');

/**
 * 积分服务
 * 处理积分获得、消费、记录等业务逻辑
 */
class PointsService {

  /**
   * 用户消费获得积分
   * @param {number} userId - 用户ID
   * @param {number} orderAmount - 订单金额
   * @param {number} orderId - 订单ID
   * @returns {Object} 积分获得结果
   */
  async earnPointsFromOrder(userId, orderAmount, orderId) {
    const connection = await db.getConnection();

    try {
      await connection.beginTransaction();

      // 计算获得的积分（1元=1积分）
      const earnedPoints = Math.floor(parseFloat(orderAmount));

      if (earnedPoints <= 0) {
        await connection.commit();
        return {
          success: true,
          earnedPoints: 0,
          message: '订单金额不足，未获得积分'
        };
      }

      // 获取用户当前积分
      const [userResult] = await connection.query(
        'SELECT points FROM users WHERE id = ? FOR UPDATE',
        [userId]
      );

      if (!userResult.length) {
        throw new Error('用户不存在');
      }

      const currentPoints = userResult[0].points || 0;
      const newPoints = currentPoints + earnedPoints;

      // 更新用户积分
      await connection.query(
        'UPDATE users SET points = ?, updated_at = NOW() WHERE id = ?',
        [newPoints, userId]
      );

      // 记录积分变动
      await this.recordPointsChange(
        connection,
        userId,
        1, // 增加
        earnedPoints,
        currentPoints,
        newPoints,
        1, // 购物获得
        orderId,
        `订单消费获得积分：${earnedPoints}分`
      );

      await connection.commit();

      // 检查是否需要升级会员等级
      try {
        const upgradeResult = await memberService.checkAndUpgradeMember(userId);
        
        return {
          success: true,
          earnedPoints,
          newTotalPoints: newPoints,
          message: `获得${earnedPoints}积分`,
          upgrade: upgradeResult.upgraded ? upgradeResult : null
        };
      } catch (upgradeError) {
        console.error('检查会员升级失败:', upgradeError);
        return {
          success: true,
          earnedPoints,
          newTotalPoints: newPoints,
          message: `获得${earnedPoints}积分`,
          upgrade: null
        };
      }

    } catch (error) {
      await connection.rollback();
      throw new Error(`积分获得失败: ${error.message}`);
    } finally {
      connection.release();
    }
  }

  /**
   * 使用积分抵扣
   * @param {number} userId - 用户ID
   * @param {number} pointsToUse - 使用积分数量
   * @param {number} orderId - 订单ID
   * @returns {Object} 积分使用结果
   */
  async usePointsForDiscount(userId, pointsToUse, orderId) {
    const connection = await db.getConnection();

    try {
      await connection.beginTransaction();

      // 参数验证
      if (!pointsToUse || pointsToUse <= 0) {
        throw new Error('使用积分数量必须大于0');
      }

      // 获取用户当前积分
      const [userResult] = await connection.query(
        'SELECT points FROM users WHERE id = ? FOR UPDATE',
        [userId]
      );

      if (!userResult.length) {
        throw new Error('用户不存在');
      }

      const currentPoints = userResult[0].points || 0;

      if (currentPoints < pointsToUse) {
        throw new Error(`积分不足，当前积分：${currentPoints}，需要：${pointsToUse}`);
      }

      const newPoints = currentPoints - pointsToUse;

      // 计算抵扣金额（100积分=1元）
      const discountAmount = parseFloat((pointsToUse / 100).toFixed(2));

      // 更新用户积分
      await connection.query(
        'UPDATE users SET points = ?, updated_at = NOW() WHERE id = ?',
        [newPoints, userId]
      );

      // 记录积分变动
      await this.recordPointsChange(
        connection,
        userId,
        2, // 减少
        pointsToUse,
        currentPoints,
        newPoints,
        4, // 消费抵扣
        orderId,
        `积分抵扣：${pointsToUse}分抵扣${discountAmount}元`
      );

      await connection.commit();

      return {
        success: true,
        usedPoints: pointsToUse,
        discountAmount: discountAmount.toFixed(2),
        newTotalPoints: newPoints,
        message: `使用${pointsToUse}积分，抵扣${discountAmount}元`
      };

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 获取积分抵扣预览
   * @param {number} userId - 用户ID
   * @param {number} orderAmount - 订单金额
   * @returns {Object} 抵扣预览
   */
  async getPointsDiscountPreview(userId, orderAmount) {
    try {
      // 获取用户当前积分
      const [userResult] = await db.query('SELECT points FROM users WHERE id = ?', [userId]);

      if (!userResult.length) {
        throw new Error('用户不存在');
      }

      const currentPoints = userResult[0].points || 0;
      const orderAmountFloat = parseFloat(orderAmount);

      // 计算最大可抵扣金额（订单金额的50%）
      const maxDiscountAmount = orderAmountFloat * 0.5;
      
      // 计算最大可使用积分
      const maxPointsFromAmount = Math.floor(maxDiscountAmount * 100); // 100积分=1元
      const maxUsablePoints = Math.min(currentPoints, maxPointsFromAmount);
      
      // 计算实际抵扣金额
      const actualDiscountAmount = parseFloat((maxUsablePoints / 100).toFixed(2));

      return {
        currentPoints,
        maxUsablePoints,
        maxDiscountAmount: actualDiscountAmount.toFixed(2),
        exchangeRate: '100积分 = 1元',
        canUsePoints: maxUsablePoints > 0,
        suggestions: this.getPointsUsageSuggestions(currentPoints, maxUsablePoints, actualDiscountAmount)
      };

    } catch (error) {
      return {
        currentPoints: 0,
        maxUsablePoints: 0,
        maxDiscountAmount: '0.00',
        exchangeRate: '100积分 = 1元',
        canUsePoints: false,
        suggestions: []
      };
    }
  }

  /**
   * 签到获得积分
   * @param {number} userId - 用户ID
   * @returns {Object} 签到结果
   */
  async dailyCheckIn(userId) {
    const connection = await db.getConnection();

    try {
      await connection.beginTransaction();

      // 检查今天是否已签到
      const today = new Date().toISOString().split('T')[0];
      const [checkResult] = await connection.query(`
        SELECT id FROM points_records 
        WHERE user_id = ? AND source = 2 AND DATE(created_at) = ?
      `, [userId, today]);

      if (checkResult.length > 0) {
        await connection.commit();
        return {
          success: false,
          message: '今天已经签到过了'
        };
      }

      // 获取连续签到天数
      const consecutiveDays = await this.getConsecutiveCheckInDays(connection, userId);
      
      // 计算签到积分（基础5分，连续签到有奖励）
      const basePoints = 5;
      const bonusPoints = Math.min(Math.floor(consecutiveDays / 7) * 2, 10); // 每7天连续签到额外2分，最多10分
      const totalPoints = basePoints + bonusPoints;

      // 获取用户当前积分
      const [userResult] = await connection.query(
        'SELECT points FROM users WHERE id = ? FOR UPDATE',
        [userId]
      );

      if (!userResult.length) {
        throw new Error('用户不存在');
      }

      const currentPoints = userResult[0].points || 0;
      const newPoints = currentPoints + totalPoints;

      // 更新用户积分
      await connection.query(
        'UPDATE users SET points = ?, updated_at = NOW() WHERE id = ?',
        [newPoints, userId]
      );

      // 记录积分变动
      await this.recordPointsChange(
        connection,
        userId,
        1, // 增加
        totalPoints,
        currentPoints,
        newPoints,
        2, // 签到获得
        null,
        `每日签到获得积分：${totalPoints}分（连续${consecutiveDays + 1}天）`
      );

      await connection.commit();

      return {
        success: true,
        earnedPoints: totalPoints,
        consecutiveDays: consecutiveDays + 1,
        newTotalPoints: newPoints,
        message: `签到成功！获得${totalPoints}积分`
      };

    } catch (error) {
      await connection.rollback();
      throw new Error(`签到失败: ${error.message}`);
    } finally {
      connection.release();
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 记录积分变动
   */
  async recordPointsChange(connection, userId, type, points, pointsBefore, pointsAfter, source, sourceId, remark) {
    await connection.query(`
      INSERT INTO points_records (
        user_id, type, points, points_before, points_after,
        source, source_id, remark, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `, [userId, type, points, pointsBefore, pointsAfter, source, sourceId, remark]);
  }

  /**
   * 获取连续签到天数
   */
  async getConsecutiveCheckInDays(connection, userId) {
    try {
      const [records] = await connection.query(`
        SELECT DATE(created_at) as check_date
        FROM points_records 
        WHERE user_id = ? AND source = 2
        ORDER BY created_at DESC
        LIMIT 30
      `, [userId]);

      if (!records.length) {
        return 0;
      }

      let consecutiveDays = 0;
      const today = new Date();
      
      for (let i = 0; i < records.length; i++) {
        const checkDate = new Date(records[i].check_date);
        const expectedDate = new Date(today);
        expectedDate.setDate(today.getDate() - i);
        
        if (checkDate.toDateString() === expectedDate.toDateString()) {
          consecutiveDays++;
        } else {
          break;
        }
      }

      return consecutiveDays;
    } catch (error) {
      return 0;
    }
  }

  /**
   * 获取积分使用建议
   */
  getPointsUsageSuggestions(currentPoints, maxUsablePoints, discountAmount) {
    const suggestions = [];

    if (maxUsablePoints === 0) {
      suggestions.push('积分不足，无法抵扣');
      return suggestions;
    }

    if (maxUsablePoints < currentPoints) {
      suggestions.push(`本订单最多可使用${maxUsablePoints}积分`);
    }

    if (discountAmount > 0) {
      suggestions.push(`可抵扣${discountAmount}元`);
    }

    suggestions.push('积分抵扣不影响积分获得');

    return suggestions;
  }

  /**
   * 充值奖励积分
   * @param {Object} connection - 数据库连接
   * @param {number} userId - 用户ID
   * @param {number} points - 奖励积分数
   * @param {number} rechargeId - 充值记录ID
   */
  async addPointsForRecharge(connection, userId, points, rechargeId) {
    try {
      // 输入验证
      if (!userId || !points || points <= 0) {
        throw new Error('参数无效');
      }

      // 获取用户当前积分
      const [userResult] = await connection.query(
        'SELECT points FROM users WHERE id = ? FOR UPDATE',
        [userId]
      );

      if (!userResult.length) {
        throw new Error('用户不存在');
      }

      const currentPoints = userResult[0].points || 0;
      const newPoints = currentPoints + points;

      // 更新用户积分
      await connection.query(
        'UPDATE users SET points = ?, updated_at = NOW() WHERE id = ?',
        [newPoints, userId]
      );

      // 记录积分变动
      await this.recordPointsChange(
        connection,
        userId,
        1, // 增加
        points,
        currentPoints,
        newPoints,
        2, // 充值奖励
        rechargeId,
        `充值奖励积分：${points}分`
      );

      return {
        success: true,
        rewardPoints: points,
        newTotalPoints: newPoints,
        message: `充值奖励${points}积分`
      };

    } catch (error) {
      throw new Error(`充值积分奖励失败: ${error.message}`);
    }
  }

  /**
   * 分销积分奖励
   * @param {Object} connection - 数据库连接
   * @param {number} userId - 用户ID
   * @param {number} points - 奖励积分数
   */
  async addPointsForDistribution(connection, userId, points) {
    try {
      // 输入验证
      if (!userId || !points || points <= 0) {
        return { success: false, message: '参数无效' };
      }

      // 获取用户当前积分
      const [userResult] = await connection.query(
        'SELECT points FROM users WHERE id = ? FOR UPDATE',
        [userId]
      );

      if (!userResult.length) {
        throw new Error('用户不存在');
      }

      const currentPoints = userResult[0].points || 0;
      const newPoints = currentPoints + points;

      // 更新用户积分
      await connection.query(
        'UPDATE users SET points = ?, updated_at = NOW() WHERE id = ?',
        [newPoints, userId]
      );

      // 记录积分变动
      await this.recordPointsChange(
        connection,
        userId,
        1, // 增加
        points,
        currentPoints,
        newPoints,
        6, // 分销奖励（新增类型）
        0,
        `分销佣金积分奖励：${points}分`
      );

      return {
        success: true,
        rewardPoints: points,
        newTotalPoints: newPoints,
        message: `分销奖励${points}积分`
      };

    } catch (error) {
      console.error('分销积分奖励失败:', error);
      throw new Error(`分销积分奖励失败: ${error.message}`);
    }
  }
}

module.exports = new PointsService();
