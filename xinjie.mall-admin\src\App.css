/* 全局样式重置和基础设置 */
* {
  box-sizing: border-box;
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f3f4;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c8cd;
  border-radius: 4px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8b2ba;
}

::-webkit-scrollbar-corner {
  background: #f1f3f4;
}

body,
.ant-layout {
  background: #f5f7fa !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif !important;
  color: #2c3e50 !important;
  line-height: 1.6;
}

/* 侧边栏优化 - 现代浅绿渐变主题 */
.ant-layout-sider {
  background: linear-gradient(180deg, #10b981 0%, #059669 50%, #047857 100%) !important;
  box-shadow: 2px 0 20px rgba(16, 185, 129, 0.15);
  position: relative;
}

.ant-layout-sider::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="modern-pattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1.5" fill="rgba(255,255,255,0.04)"/><circle cx="10" cy="10" r="0.8" fill="rgba(255,255,255,0.02)"/><circle cx="30" cy="30" r="0.8" fill="rgba(255,255,255,0.02)"/></pattern></defs><rect width="100" height="100" fill="url(%23modern-pattern)"/></svg>') !important;
  pointer-events: none;
}

.ant-menu-dark,
.ant-menu-dark .ant-menu-sub {
  background: transparent !important;
  border-right: none !important;
}

.ant-menu-dark .ant-menu-item-selected {
  background: linear-gradient(90deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.2) 100%) !important;
  color: #fff !important;
  border-radius: 12px !important;
  margin: 6px 16px !important;
  width: calc(100% - 32px) !important;
  box-shadow: 0 4px 15px rgba(255,255,255, 0.25) !important;
  border-left: 4px solid rgba(255,255,255,0.8) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  backdrop-filter: blur(15px) !important;
  font-weight: 600;
  transform: translateX(4px);
}

.ant-menu-dark .ant-menu-item {
  color: rgba(255,255,255,0.9) !important;
  margin: 4px 12px !important;
  border-radius: 10px !important;
  width: calc(100% - 24px) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  padding: 0 20px !important;
  height: 48px !important;
  line-height: 48px !important;
  border: 1px solid transparent;
}

.ant-menu-dark .ant-menu-item:hover {
  background: rgba(255,255,255,0.15) !important;
  color: #fff !important;
  border-left: 3px solid rgba(255,255,255,0.7) !important;
  transform: translateX(6px);
  box-shadow: 0 6px 20px rgba(255,255,255, 0.2) !important;
  border: 1px solid rgba(255,255,255,0.2);
}

.ant-menu-dark .ant-menu-submenu-title {
  color: rgba(255,255,255,0.9) !important;
  margin: 4px 12px !important;
  border-radius: 10px !important;
  width: calc(100% - 24px) !important;
  padding: 0 20px !important;
  height: 48px !important;
  line-height: 48px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border: 1px solid transparent;
}

.ant-menu-dark .ant-menu-submenu-title:hover {
  background: rgba(255,255,255,0.15) !important;
  color: #fff !important;
  transform: translateX(4px);
  box-shadow: 0 4px 15px rgba(255,255,255, 0.15) !important;
  border: 1px solid rgba(255,255,255,0.2);
}

/* 菜单分组样式 - 茶叶主题 */
.ant-menu-item-group-title {
  padding: 0 !important;
  margin: 0 !important;
  line-height: 1 !important;
}

.ant-menu-item-group-title > div {
  margin: 0 !important;
}

.ant-menu-item-group-list {
  margin: 0 !important;
}

/* 子菜单样式优化 */
.ant-menu-dark .ant-menu-submenu-open > .ant-menu-submenu-title {
  background: rgba(255,255,255,0.1) !important;
}

.ant-menu-dark .ant-menu-sub {
  background: rgba(0,0,0,0.1) !important;
  margin: 4px 8px !important;
  border-radius: 6px !important;
  width: calc(100% - 16px) !important;
}

.ant-menu-dark .ant-menu-sub .ant-menu-item {
  margin: 2px 8px !important;
  width: calc(100% - 16px) !important;
  padding-left: 32px !important;
}

/* 菜单图标样式 */
.ant-menu-item .anticon,
.ant-menu-submenu-title .anticon {
  margin-right: 12px !important;
  font-size: 16px !important;
}

.ant-menu-sub .ant-menu-item .anticon {
  font-size: 14px !important;
  margin-right: 10px !important;
}

/* 自定义滚动条样式 - 优化版 */
.ant-layout-sider ::-webkit-scrollbar {
  width: 4px;
  background: transparent;
}

.ant-layout-sider ::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 2px;
  margin: 8px 0;
}

.ant-layout-sider ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  transition: all 0.3s ease;
  min-height: 20px;
}

.ant-layout-sider ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.4);
  width: 6px;
}

/* 侧边栏菜单区域滚动条 - Firefox */
.ant-menu-inline {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

/* 菜单容器滚动优化 */
.ant-layout-sider .ant-menu {
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-gutter: stable;
}

/* 滚动条在非悬停状态下几乎隐藏 */
.ant-layout-sider:not(:hover) ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  width: 2px;
}

/* 平滑滚动 */
.ant-layout-sider .ant-menu {
  scroll-behavior: smooth;
}

/* 滚动条角落处理 */
.ant-layout-sider ::-webkit-scrollbar-corner {
  background: transparent;
}

/* 滚动条按钮隐藏 */
.ant-layout-sider ::-webkit-scrollbar-button {
  display: none;
}

/* 侧边栏整体滚动优化 */
.ant-layout-sider {
  overflow: hidden;
}

.ant-layout-sider .ant-layout-sider-children {
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
}

/* 菜单项悬停时滚动条稍微明显一些 */
.ant-layout-sider:hover ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.25);
}

/* 滚动时的动画效果 */
.ant-layout-sider .ant-menu {
  transition: transform 0.1s ease-out;
}

/* 顶部操作区 */
.content-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 18px;
  margin-top: 8px;
  gap: 18px;
}
.content-title {
  font-size: 18px;
  font-weight: bold;
  color: #222;
  margin-right: 18px;
}
.primary-btn,
.ant-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: #fff !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 0 16px !important;
  height: 36px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;
  transition: all 0.3s ease !important;
  display: flex;
  align-items: center;
}

.primary-btn:hover,
.ant-btn-primary:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;
}

/* 顶部提示条 */
.ant-alert {
  border-radius: 6px !important;
  margin-bottom: 14px;
  box-shadow: none !important;
  font-size: 15px;
  background: #fff4f4 !important;
  border: 1px solid #ffd6d6 !important;
  color: #d93026 !important;
  padding: 12px 20px !important;
  display: flex;
  align-items: center;
}

/* 表格样式优化 */
.ant-table {
  background: transparent !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  box-shadow: none !important;
  margin-top: 16px !important;
}

.ant-table-thead > tr > th {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  color: #495057 !important;
  font-weight: 600 !important;
  border-bottom: 2px solid #dee2e6 !important;
  font-size: 14px !important;
  padding: 16px 12px !important;
  text-align: center !important;
  position: relative !important;
}

.ant-table-thead > tr > th::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.ant-table-tbody > tr > td {
  padding: 16px 12px !important;
  background: transparent !important;
  border-bottom: 1px solid #f1f3f4 !important;
  font-size: 14px !important;
  vertical-align: middle !important;
}

.ant-table-tbody > tr:nth-child(even) > td {
  background: #fafbfc !important;
}

.ant-table-tbody > tr:hover > td {
  background: linear-gradient(135deg, #f3e8ff 0%, #e0e7ff 100%) !important;
  transform: translateY(-1px) !important;
  transition: all 0.3s ease !important;
}

.ant-empty-description {
  color: #8c8c8c !important;
  font-size: 14px !important;
  text-align: center !important;
}

/* 卡片样式优化 */
.ant-card {
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06) !important;
  border: 1px solid #f0f0f0 !important;
}

.ant-card-head {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  border-bottom: 2px solid #dee2e6 !important;
  border-radius: 12px 12px 0 0 !important;
}

.ant-card-head-title {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #495057 !important;
}

/* 让内容区更紧凑 */
.ant-layout-content {
  padding: 0 24px 24px 24px !important;
}

/* 表格滚动优化 */
.ant-table-wrapper {
  overflow-x: auto !important;
}

.ant-table-body {
  overflow-x: auto !important;
}

/* 主内容区域滚动条优化 */
.ant-layout-content ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.ant-layout-content ::-webkit-scrollbar-track {
  background: #f1f3f4;
  border-radius: 3px;
}

.ant-layout-content ::-webkit-scrollbar-thumb {
  background: #c1c8cd;
  border-radius: 3px;
  transition: background 0.3s ease;
}

.ant-layout-content ::-webkit-scrollbar-thumb:hover {
  background: #a8b2ba;
}

/* 表格滚动条样式 */
.ant-table-wrapper ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.ant-table-wrapper ::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 3px;
}

.ant-table-wrapper ::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.ant-table-wrapper ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  transform: scale(1.1);
}

/* 固定列样式 */
.ant-table-fixed-right {
  box-shadow: -2px 0 8px rgba(0,0,0,0.1) !important;
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .content-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .ant-layout-sider {
    position: relative !important;
    width: 100% !important;
    max-width: 280px !important;
  }

  .ant-layout {
    margin-left: 0 !important;
  }
}

@media (max-width: 768px) {
  .ant-table {
    font-size: 12px !important;
  }

  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    padding: 8px 4px !important;
  }
}
