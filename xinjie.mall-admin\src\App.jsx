import React from 'react';
import { Layout } from 'antd';
import { Outlet } from 'react-router-dom';
import SidebarMenu from './components/SidebarMenu';
import Topbar from './components/Layout/Topbar';
import AuthMonitor from './components/AuthMonitor';
import './styles/dashboard.css';
import './styles/modern-theme.css';

const { Sider, Content } = Layout;

const App = () => {
  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* 认证状态监控组件 */}
      <AuthMonitor />

      <Sider
        width={280}
        style={{
          background: 'linear-gradient(180deg, #10b981 0%, #059669 50%, #047857 100%)',
          boxShadow: '2px 0 20px rgba(16, 185, 129, 0.15)',
          position: 'fixed',
          height: '100vh',
          left: 0,
          top: 0,
          zIndex: 100
        }}
      >
        <SidebarMenu />
      </Sider>

      <Layout style={{ marginLeft: 280 }}>
        <Topbar />
        <Content
          style={{
            margin: '20px',
            padding: '0',
            minHeight: 'calc(100vh - 84px)',
          }}
        >
          <div style={{
            background: '#fff',
            borderRadius: '12px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
            padding: '24px',
            minHeight: 'calc(100vh - 124px)',
          }}>
            <Outlet />
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default App;
