import React, { useEffect, useState } from 'react';
import {
  BrowserRouter,
  Routes,
  Route,
  Navigate,
  Outlet,
} from 'react-router-dom';
import { useSelector } from 'react-redux';
import App from './App';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import BannerListPage from './pages/BannerList';
import ProductListPage from './pages/ProductList';
import OrderListPage from './pages/OrderList';
import UserListPage from './pages/UserList';
import CategoryListPage from './pages/CategoryList';
import DiscountListPage from './pages/DiscountList';
import StatsDashboardPage from './pages/StatsDashboard';
import SettingsPageWrap from './pages/SettingsPage';
import RechargeManagementPage from './pages/RechargeManagement';
import MemberManagementPage from './pages/MemberManagement';
// 分销组件全部启用
import DistributionManagement from './pages/DistributionManagement';
import DistributionStatsPage from './pages/DistributionStats';
import DistributionCommission from './pages/DistributionCommission';
import DistributionConfig from './pages/DistributionConfig';
import ReturnRequestListPage from './pages/ReturnRequestList';
import AuthService from './utils/auth';

// 认证守卫组件
const RequireAuth = () => {
  const [authStatus, setAuthStatus] = useState({
    loading: true,
    authenticated: false,
    user: null
  });

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const status = await AuthService.getAuthStatus();
        setAuthStatus({
          loading: false,
          authenticated: status.authenticated,
          user: status.user
        });
      } catch (error) {
        console.error('认证检查失败:', error);
        setAuthStatus({
          loading: false,
          authenticated: false,
          user: null
        });
      }
    };

    checkAuth();
  }, []);

  // 显示加载状态
  if (authStatus.loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '16px',
        color: '#666'
      }}>
        正在验证登录状态...
      </div>
    );
  }

  // 未认证，跳转到登录页
  if (!authStatus.authenticated) {
    return <Navigate to='/login' replace />;
  }

  // 已认证，渲染子组件
  return <Outlet />;
};

// 登录页面守卫（已登录用户访问登录页时跳转到首页）
const LoginGuard = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const status = await AuthService.getAuthStatus();
        setIsAuthenticated(status.authenticated);
      } catch (error) {
        console.error('认证检查失败:', error);
        setIsAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '16px',
        color: '#666'
      }}>
        正在检查登录状态...
      </div>
    );
  }

  // 已登录用户跳转到首页
  if (isAuthenticated) {
    return <Navigate to='/' replace />;
  }

  // 未登录用户显示登录页
  return <Login />;
};

const AppRouter = () => (
  <BrowserRouter>
    <Routes>
      <Route path='/login' element={<LoginGuard />} />
      <Route element={<RequireAuth />}>
        <Route path='/' element={<App />}>
          <Route index element={<Dashboard />} />
          <Route path='banner' element={<BannerListPage />} />
          <Route path='product' element={<ProductListPage />} />
          <Route path='discount' element={<DiscountListPage />} />
          <Route path='order' element={<OrderListPage />} />
          <Route path='return' element={<ReturnRequestListPage />} />
          <Route path='user' element={<UserListPage />} />
          <Route path='user/recharge' element={<RechargeManagementPage />} />
          <Route path='user/member' element={<MemberManagementPage />} />
          <Route path='category' element={<CategoryListPage />} />
          {/* 分销路由全部启用 */}
          <Route path='distribution/distributors' element={<DistributionManagement />} />
          <Route path='distribution/stats' element={<DistributionStatsPage />} />
          <Route path='distribution/commission' element={<DistributionCommission />} />
          <Route path='distribution/config' element={<DistributionConfig />} />
          <Route path='stats' element={<StatsDashboardPage />} />
          <Route path='settings/basic' element={<SettingsPageWrap />} />
          <Route path='settings/payment' element={<SettingsPageWrap />} />
          <Route path='settings/shipping' element={<SettingsPageWrap />} />
          <Route path='settings/sms' element={<SettingsPageWrap />} />
          <Route path='settings/email' element={<SettingsPageWrap />} />
        </Route>
      </Route>
      {/* 404页面 */}
      <Route path='*' element={<Navigate to='/' replace />} />
    </Routes>
  </BrowserRouter>
);

export default AppRouter;
