import request from '../utils/request';

export const fetchBannerList = params =>
  request.get('/admin/banner/list', { params });
export const createBanner = data => request.post('/admin/banner/create', data);
export const updateBanner = (id, data) =>
  request.put(`/admin/banner/update/${id}`, data);
export const deleteBanner = id => request.delete(`/admin/banner/delete/${id}`);
export const uploadBannerImage = data =>
  request.post('/admin/banner/upload', data);
