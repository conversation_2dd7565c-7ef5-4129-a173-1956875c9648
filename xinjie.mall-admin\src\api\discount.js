import request from '../utils/request';

// 获取折扣列表
export const fetchDiscountList = (params) => {
  return request.get('/admin/discount/list', { params });
};

// 获取折扣详情
export const fetchDiscountDetail = (id) => {
  return request.get(`/admin/discount/detail/${id}`);
};

// 创建折扣
export const createDiscount = (data) => {
  return request.post('/admin/discount/create', data);
};

// 更新折扣
export const updateDiscount = (id, data) => {
  return request.put(`/admin/discount/update/${id}`, data);
};

// 删除折扣
export const deleteDiscount = (id) => {
  return request.delete(`/admin/discount/delete/${id}`);
};

// 更新折扣状态
export const updateDiscountStatus = (id, status) => {
  return request.put(`/admin/discount/status/${id}`, { status });
};

// 获取折扣统计信息
export const fetchDiscountStats = () => {
  return request.get('/admin/discount/stats');
};

// 获取折扣类型选项
export const fetchDiscountTypes = () => {
  return request.get('/admin/discount/types');
};

// 获取适用范围选项
export const fetchApplicableToOptions = () => {
  return request.get('/admin/discount/applicable-to-options');
};

// 获取可选商品列表
export const fetchAvailableProducts = (params) => {
  return request.get('/admin/discount/available-products', { params });
};

// 获取可选分类列表
export const fetchAvailableCategories = () => {
  return request.get('/admin/discount/available-categories');
};
