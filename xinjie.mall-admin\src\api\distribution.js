// 临时使用模拟数据，避免API调用失败导致系统崩溃
// import request from '../utils/request';

// 模拟数据
const mockDistributors = [
  {
    id: 1,
    nickname: '张三',
    phone: '13800138001',
    avatar: null,
    distributor_code: 'D001',
    distributor_level: 1,
    distributor_status: 1,
    today_commission: 50.00,
    today_orders: 3,
    month_commission: 1200.00,
    month_orders: 45,
    total_commission: 5600.00,
    total_customers: 28,
    total_orders: 156,
    distributor_apply_time: '2024-01-15T10:30:00Z'
  },
  {
    id: 2,
    nickname: '李四',
    phone: '13800138002',
    avatar: null,
    distributor_code: 'D002',
    distributor_level: 2,
    distributor_status: 1,
    today_commission: 80.00,
    today_orders: 5,
    month_commission: 2100.00,
    month_orders: 78,
    total_commission: 12300.00,
    total_customers: 56,
    total_orders: 289,
    distributor_apply_time: '2024-01-10T14:20:00Z'
  }
];

const mockCommissions = [
  {
    id: 1,
    nickname: '张三',
    phone: '13800138001',
    distributor_code: 'D001',
    order_no: 'ORD20240101001',
    order_amount: 299.00,
    commission_type: 1,
    amount: 14.95,
    points: 30,
    status: 0,
    created_at: '2024-01-20T10:30:00Z',
    settle_time: null
  }
];

const mockStats = {
  basicStats: {
    active_distributors: 156,
    today_new_distributors: 3
  },
  commissionStats: {
    total_commission: 45600.00,
    today_commission: 1200.00,
    total_orders: 2890,
    pending_commission: 3400.00
  },
  shareStats: {
    total_shares: 8900,
    today_shares: 45,
    total_clicks: 12300,
    total_registers: 890
  },
  trendData: [
    { date: '2024-01-15', orders: 12, commission: '560.00' },
    { date: '2024-01-16', orders: 15, commission: '720.00' },
    { date: '2024-01-17', orders: 18, commission: '890.00' }
  ],
  levelDistribution: [
    { distributor_level: 1, count: 89 },
    { distributor_level: 2, count: 67 }
  ],
  hotProducts: [
    {
      product_id: 1,
      product_name: '西湖龙井茶叶礼盒',
      share_count: 45,
      total_clicks: 234,
      total_orders: 23
    }
  ]
};

const mockConfig = {
  system: [
    { key: 'max_distribution_levels', value: '2', desc: '最大分销层级', type: 1 }
  ],
  business: [
    { key: 'level1_commission_rate', value: '0.05', desc: '一级分销佣金比例', type: 2 },
    { key: 'level2_commission_rate', value: '0.02', desc: '二级分销佣金比例', type: 2 },
    { key: 'level1_points_rate', value: '10', desc: '一级分销积分倍率', type: 2 },
    { key: 'level2_points_rate', value: '5', desc: '二级分销积分倍率', type: 2 },
    { key: 'max_commission_per_order', value: '500', desc: '单笔订单最大佣金', type: 2 },
    { key: 'settle_delay_days', value: '7', desc: '佣金结算延迟天数', type: 2 },
    { key: 'distributor_min_purchase', value: '100', desc: '成为分销商最低消费', type: 2 },
    { key: 'distributor_min_level', value: '2', desc: '成为分销商最低会员等级', type: 2 }
  ],
  risk: [
    { key: 'min_order_amount', value: '10', desc: '最小有效订单金额', type: 3 },
    { key: 'max_daily_shares', value: '20', desc: '每日最大分享次数', type: 3 }
  ]
};

// 模拟API延迟
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// 分销商管理
export const getDistributorList = async (params) => {
  await delay(500);
  return {
    success: true,
    data: {
      list: mockDistributors,
      pagination: {
        total: mockDistributors.length,
        current: params?.page || 1,
        pageSize: params?.pageSize || 10
      }
    }
  };
};

export const getDistributorDetail = async (id) => {
  await delay(300);
  const distributor = mockDistributors.find(d => d.id === parseInt(id));
  return {
    success: true,
    data: {
      distributor,
      commissionStats: {
        total_commission: 5600.00,
        settled_commission: 4200.00,
        pending_commission: 1400.00,
        total_orders: 156
      },
      relations: [
        {
          user_id: 101,
          nickname: '客户A',
          phone: '13900139001',
          level: 1,
          bind_time: '2024-01-16T09:30:00Z'
        }
      ]
    }
  };
};

export const updateDistributorStatus = async (id, data) => {
  await delay(300);
  return {
    success: true,
    message: '状态更新成功'
  };
};

export const getDistributionTree = async (userId) => {
  await delay(300);
  return {
    success: true,
    data: []
  };
};

// 佣金管理
export const getCommissionList = async (params) => {
  await delay(500);
  return {
    success: true,
    data: {
      list: mockCommissions,
      pagination: {
        total: mockCommissions.length,
        current: params?.page || 1,
        pageSize: params?.pageSize || 10
      },
      statistics: {
        total_amount: 45600.00,
        settled_amount: 32100.00,
        pending_amount: 13500.00,
        total_records: 1250
      }
    }
  };
};

export const settleCommission = async (data) => {
  await delay(800);
  return {
    success: true,
    message: `成功结算 ${data.commissionIds?.length || 0} 条佣金记录`
  };
};

// 数据统计
export const getDistributionStats = async (params) => {
  await delay(600);
  return {
    success: true,
    data: mockStats
  };
};

// 配置管理
export const getDistributionConfig = async () => {
  await delay(300);
  return {
    success: true,
    data: mockConfig
  };
};

export const updateDistributionConfig = async (data) => {
  await delay(500);
  return {
    success: true,
    message: '配置保存成功'
  };
};
