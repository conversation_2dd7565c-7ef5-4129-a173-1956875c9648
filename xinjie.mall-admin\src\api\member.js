import request from '../utils/request';

// 获取所有会员等级（用于下拉选择）
export const fetchAllMemberLevels = () => {
  return request.get('/admin/member/levels/all');
};

// 获取会员等级列表（分页）
export const fetchMemberLevelList = (params) => {
  return request.get('/admin/member/levels/list', { params });
};

// 获取会员等级详情
export const fetchMemberLevelDetail = (id) => {
  return request.get(`/admin/member/levels/detail/${id}`);
};

// 创建会员等级
export const createMemberLevel = (data) => {
  return request.post('/admin/member/levels/create', data);
};

// 更新会员等级
export const updateMemberLevel = (id, data) => {
  return request.put(`/admin/member/levels/update/${id}`, data);
};

// 删除会员等级
export const deleteMemberLevel = (id) => {
  return request.delete(`/admin/member/levels/delete/${id}`);
};

// 更新会员等级状态
export const updateMemberLevelStatus = (id, status) => {
  return request.put(`/admin/member/levels/status/${id}`, { status });
};

// 获取会员权益列表（分页）
export const fetchMemberBenefitList = (params) => {
  return request.get('/admin/member/benefits/list', { params });
};

// 创建会员权益
export const createMemberBenefit = (data) => {
  return request.post('/admin/member/benefits/create', data);
};

// 更新会员权益
export const updateMemberBenefit = (id, data) => {
  return request.put(`/admin/member/benefits/update/${id}`, data);
};

// 删除会员权益
export const deleteMemberBenefit = (id) => {
  return request.delete(`/admin/member/benefits/delete/${id}`);
};
