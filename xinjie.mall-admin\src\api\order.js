import request from '../utils/request';

export const fetchOrderList = params =>
  request.get('/admin/order/list', { params });

export const fetchOrderDetail = id =>
  request.get(`/admin/order/detail/${id}`);

export const updateOrderStatus = (id, data) =>
  request.put(`/admin/order/status/${id}`, data);

export const shipOrder = (id, data) =>
  request.post(`/admin/order/ship/${id}`, data);

export const handleRefund = (id, data) =>
  request.post(`/admin/order/refund/${id}`, data);

export const fetchOrderStats = params =>
  request.get('/admin/order/stats', { params });

export const createOrder = data =>
  request.post('/admin/order/create', data);

export const deleteOrder = id =>
  request.delete(`/admin/order/delete/${id}`);

export const batchUpdateStatus = (orderIds, status, remark) =>
  request.post('/admin/order/batch-status', { orderIds, status, remark });

export const batchShip = (orderIds, deliveryCompany, deliveryNo) =>
  request.post('/admin/order/batch-ship', { orderIds, deliveryCompany, deliveryNo });

export const exportOrders = params =>
  request.get('/admin/order/export', { params, responseType: 'blob' });
