import request from '../utils/request';

export const fetchProductList = params =>
  request.get('/admin/product/list', { params });
export const fetchProductDetail = id =>
  request.get(`/admin/product/detail/${id}`);
export const createProduct = data =>
  request.post('/admin/product/create', data);
export const updateProduct = (id, data) =>
  request.put(`/admin/product/update/${id}`, data);
export const deleteProduct = id =>
  request.delete(`/admin/product/delete/${id}`);
export const uploadProductImage = data =>
  request.post('/admin/product/upload', data);
