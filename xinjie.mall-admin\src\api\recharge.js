import request from '../utils/request';

// 获取充值记录列表
export const fetchRechargeList = (params) => {
  return request.get('/admin/recharge/list', { params });
};

// 获取充值记录详情
export const fetchRechargeDetail = (id) => {
  return request.get(`/admin/recharge/detail/${id}`);
};

// 后台充值
export const adminRecharge = (data) => {
  return request.post('/admin/recharge/admin-recharge', data);
};

// 更新充值记录
export const updateRecharge = (id, data) => {
  return request.put(`/admin/recharge/update/${id}`, data);
};

// 删除充值记录
export const deleteRecharge = (id) => {
  return request.delete(`/admin/recharge/delete/${id}`);
};

// 更新支付状态
export const updatePaymentStatus = (id, data) => {
  return request.put(`/admin/recharge/payment-status/${id}`, data);
};

// 获取充值统计数据
export const fetchRechargeStatistics = (params) => {
  return request.get('/admin/recharge/statistics', { params });
};

// 获取余额变动记录
export const fetchBalanceRecords = (params) => {
  return request.get('/admin/recharge/balance-records', { params });
};

// 后台调整余额
export const adjustBalance = (data) => {
  return request.post('/admin/recharge/adjust-balance', data);
};
