import request from '@/utils/request';

// 获取退货申请列表
export const fetchReturnRequestList = (params) => {
  return request.get('/admin/return', { params });
};

// 获取退货申请详情
export const fetchReturnRequestDetail = (id) => {
  return request.get(`/admin/return/detail/${id}`);
};

// 审核退货申请
export const approveReturnRequest = (id, data) => {
  return request.put(`/admin/return/approve/${id}`, data);
};

// 确认收货
export const confirmReceive = (id, data) => {
  return request.put(`/admin/return/receive/${id}`, data);
};

// 验收商品
export const inspectGoods = (id, data) => {
  return request.put(`/admin/return/inspect/${id}`, data);
};

// 处理退款
export const processRefund = (id, data) => {
  return request.put(`/admin/return/refund/${id}`, data);
};

// 获取退货统计数据
export const fetchReturnStatistics = (params) => {
  return request.get('/admin/return/statistics', { params });
};

// 批量处理退货申请
export const batchProcessReturns = (data) => {
  return request.post('/admin/return/batch', data);
};

// 导出退货数据
export const exportReturnData = (params) => {
  return request.get('/admin/return/export', { params });
};
