import request from '../utils/request';

export const fetchBasicSettings = () => request.get('/admin/settings/basic');
export const updateBasicSettings = data =>
  request.put('/admin/settings/basic', data);
export const fetchPaymentSettings = () =>
  request.get('/admin/settings/payment');
export const updatePaymentSettings = data =>
  request.put('/admin/settings/payment', data);
export const fetchShippingSettings = () =>
  request.get('/admin/settings/shipping');
export const updateShippingSettings = data =>
  request.put('/admin/settings/shipping', data);
export const fetchSmsSettings = () => request.get('/admin/settings/sms');
export const updateSmsSettings = data =>
  request.put('/admin/settings/sms', data);
export const fetchEmailSettings = () => request.get('/admin/settings/email');
export const updateEmailSettings = data =>
  request.put('/admin/settings/email', data);
