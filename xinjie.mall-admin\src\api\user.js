import request from '../utils/request';

export const fetchUserList = params =>
  request.get('/admin/user/list', { params });
export const fetchUserDetail = id => request.get(`/admin/user/detail/${id}`);
export const createUser = data => request.post('/admin/user/create', data);
export const updateUserStatus = (id, data) =>
  request.put(`/admin/user/status/${id}`, data);
export const updateUser = (id, data) =>
  request.put(`/admin/user/update/${id}`, data);
export const fetchAllUsers = () => request.get('/admin/user/list', { params: { pageSize: 1000 } });
