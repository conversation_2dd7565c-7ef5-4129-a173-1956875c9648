import React, { useEffect, useRef } from 'react';
import AuthService from '../utils/auth';
import authConfig from '../config/auth';

/**
 * 认证状态监控组件
 * 用于实时监控认证状态，自动处理token过期等问题
 */
const AuthMonitor = () => {
  const intervalRef = useRef(null);
  const visibilityHandlerRef = useRef(null);

  useEffect(() => {
    // 定期检查认证状态（每5分钟检查一次）
    const checkAuthStatus = async () => {
      try {
        if (AuthService.isAuthenticated()) {
          // 验证token有效性
          const isValid = await AuthService.validateToken();
          if (!isValid) {
            console.log('Token验证失败，清除认证信息');
            AuthService.clearAuth();
            
            // 如果不是登录页面，跳转到登录页
            if (!window.location.pathname.includes('/login')) {
              window.location.href = '/login';
            }
          }
        }
      } catch (error) {
        console.error('认证状态检查失败:', error);
      }
    };

    // 立即检查一次
    checkAuthStatus();

    // 设置定时检查
    intervalRef.current = setInterval(checkAuthStatus, authConfig.token.checkInterval);

    // 页面可见性变化时检查认证状态
    if (authConfig.security.visibilityCheck) {
      visibilityHandlerRef.current = () => {
        if (!document.hidden) {
          checkAuthStatus();
        }
      };

      document.addEventListener('visibilitychange', visibilityHandlerRef.current);
    }

    // 清理函数
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (authConfig.security.visibilityCheck && visibilityHandlerRef.current) {
        document.removeEventListener('visibilitychange', visibilityHandlerRef.current);
      }
    };
  }, []);

  // 这个组件不渲染任何内容，只负责监控
  return null;
};

export default AuthMonitor; 