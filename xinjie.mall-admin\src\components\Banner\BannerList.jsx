import React, { useEffect, useState } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  InputNumber,
  Switch,
  message,
} from 'antd';
import { PlusOutlined, UploadOutlined } from '@ant-design/icons';
import axios from 'axios';
import {
  fetchBannerList,
  createBanner,
  updateBanner,
  deleteBanner,
  uploadBannerImage,
} from '@/api/banner';

const BannerList = () => {
  const [data, setData] = useState([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [modalVisible, setModalVisible] = useState(false);
  const [editing, setEditing] = useState(null);
  const [form] = Form.useForm();
  const [uploading, setUploading] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [file, setFile] = useState(null); // 新增
  const [previewUrl, setPreviewUrl] = useState(''); // 新增
  const [title, setTitle] = useState(''); // 新增
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  // 使用API封装方法获取数据
  const fetchData = async (page = 1, pageSize = 10) => {
    setLoading(true);
    try {
      const res = await fetchBannerList({ page, pageSize });
      setData(res.data.list || res.data.data?.list || []);
      setTotal(res.data.total || res.data.data?.total || 0);
    } catch (e) {
      message.error('获取数据失败');
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchData(page, pageSize);
  }, [page, pageSize]);

  const handleAdd = () => {
    setEditing(null);
    setImageUrl('');
    setModalVisible(true);
    form.resetFields();
  };

  const handleEdit = record => {
    setEditing(record);
    setImageUrl(record.image_url);
    setModalVisible(true);
    form.setFieldsValue(record);
  };

  const handleDelete = async id => {
    Modal.confirm({
      title: '确认删除该轮播图？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        await deleteBanner(id);
        message.success('删除成功');
        fetchData(page, pageSize);
      },
    });
  };

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      values.image_url = imageUrl;
      // 强制将排序号转为数字类型
      values.sort_order = Number(values.sort_order);
      // 将状态转换为数字类型
      values.status = values.status ? 1 : 0;
      if (
        !values.sort_order ||
        isNaN(values.sort_order) ||
        values.sort_order < 1
      ) {
        message.error('排序号必须为大于0的数字');
        return;
      }
      if (!values.image_url) {
        message.error('请先上传图片');
        return;
      }
      if (editing) {
        await updateBanner(editing.id, values);
        message.success('更新成功');
      } else {
        await createBanner(values);
        message.success('添加成功');
      }
      setModalVisible(false);
      setTitle('');
      setFile(null);
      setPreviewUrl('');
      setImageUrl('');
      fetchData(page, pageSize); // 新增/编辑成功后刷新内容区
    } catch (e) {
      // 校验失败
    }
  };

  const handleFileChange = e => {
    const f = e.target.files[0];
    if (f && f.size > 20 * 1024 * 1024) {
      message.error('图片不能超过20MB');
      return;
    }
    setFile(f);
    if (f) {
      setPreviewUrl(URL.createObjectURL(f));
    } else {
      setPreviewUrl('');
    }
  };

  // 修复上传方法，使用uploadBannerImage
  const handleUpload = async () => {
    try {
      const values = await form.validateFields();
      if (!file) {
        message.error('请选择图片');
        return;
      }
      setUploading(true);
      const formData = new FormData();
      formData.append('file', file);
      formData.append('title', values.title);
      formData.append('sort_order', values.sort_order);
      // 1. 上传图片
      const res = await uploadBannerImage(formData);
      if (res.code === 0 && res.data && res.data.url) {
        // 2. 自动调用createBanner
        const bannerData = {
          ...values,
          image_url: res.data.url,
          sort_order: Number(values.sort_order),
          status: values.status ? 1 : 0, // 将 boolean 转换为数字
        };
        await createBanner(bannerData);
        message.success('轮播图添加成功');
        setModalVisible(false);
        setTitle('');
        setFile(null);
        setPreviewUrl('');
        setImageUrl('');
        fetchData(page, pageSize);
      } else {
        message.error(res.msg || '图片上传失败');
      }
    } catch (e) {
      // 校验失败或接口异常
      message.error(e?.message || '操作失败');
    }
    setUploading(false);
  };

  const columns = [
    { title: 'ID', dataIndex: 'sort_order', width: 60 },
    {
      title: '图片',
      dataIndex: 'image_url',
      render: url =>
        url ? <img src={url} alt='' style={{ width: 80 }} /> : '-',
    },
    { title: '标题', dataIndex: 'title' },
    { title: '排序', dataIndex: 'sort_order', width: 80 },
    {
      title: '状态',
      dataIndex: 'status',
      render: v => (v === 1 ? '启用' : '禁用'),
      width: 80,
    },
    {
      title: '操作',
      width: 180,
      render: (_, record) => (
        <>
          <Button type='link' onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Button type='link' danger onClick={() => handleDelete(record.id)}>
            删除
          </Button>
        </>
      ),
    },
  ];

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要删除的轮播图');
      return;
    }
    Modal.confirm({
      title: `确认删除选中的${selectedRowKeys.length}个轮播图？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        for (const id of selectedRowKeys) {
          await axios.delete(`/api/banners/delete/${id}`);
        }
        message.success('批量删除成功');
        setSelectedRowKeys([]);
        fetchData(page, pageSize);
      },
    });
  };

  // 全选/取消全选
  const handleSelectAll = () => {
    if (selectedRowKeys.length === data.length && data.length > 0) {
      setSelectedRowKeys([]);
    } else {
      setSelectedRowKeys(Array.isArray(data) ? data.map(item => item.id) : []);
    }
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
  };

  return (
    <>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: 12,
          marginBottom: 16,
        }}
      >
        <Button
          danger
          onClick={handleBatchDelete}
          disabled={selectedRowKeys.length === 0}
        >
          删除选中
        </Button>
        <Button onClick={handleSelectAll}>
          {selectedRowKeys.length === data.length && data.length > 0
            ? '取消全选'
            : '全选'}
        </Button>
        <Button
          type='primary'
          icon={<PlusOutlined />}
          size='middle'
          style={{ minWidth: 120 }}
          onClick={handleAdd}
        >
          新增轮播图
        </Button>
      </div>
      <Table
        rowKey='id'
        columns={columns}
        dataSource={data}
        loading={loading}
        rowSelection={rowSelection}
        pagination={{
          current: page,
          pageSize,
          total,
          onChange: (p, ps) => {
            setPage(p);
            setPageSize(ps);
          },
        }}
      />
      <Modal
        title={editing ? '编辑轮播图' : '新增轮播图'}
        open={modalVisible}
        footer={null}
        onCancel={() => setModalVisible(false)}
        destroyOnHidden
      >
        <Form
          form={form}
          layout='vertical'
          onValuesChange={(_, allValues) => setTitle(allValues.title)}
        >
          <Form.Item
            label='标题'
            name='title'
            rules={[{ required: true, message: '请输入标题' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item label='图片' required>
            {!file && (
              <Button
                icon={<UploadOutlined />}
                style={{ marginBottom: 8 }}
                onClick={() =>
                  document.getElementById('banner-file-input').click()
                }
              >
                选择图片
              </Button>
            )}
            <input
              id='banner-file-input'
              type='file'
              accept='image/*'
              style={{ display: 'none' }}
              onChange={handleFileChange}
            />
            {previewUrl && file && (
              <div style={{ marginTop: 8 }}>
                <img
                  src={previewUrl}
                  alt='预览'
                  style={{ width: 120, display: 'block', marginBottom: 8 }}
                />
                <div>文件名: {file?.name}</div>
                <div>
                  大小: {file ? (file.size / 1024).toFixed(2) + ' KB' : ''}
                </div>
                <div style={{ marginTop: 8, display: 'flex', gap: 8 }}>
                  <Button
                    size='small'
                    onClick={() =>
                      document.getElementById('banner-file-input').click()
                    }
                  >
                    更改图片
                  </Button>
                  <Button
                    size='small'
                    danger
                    onClick={() => {
                      setFile(null);
                      setPreviewUrl('');
                    }}
                  >
                    删除图片
                  </Button>
                </div>
              </div>
            )}
          </Form.Item>
          <Form.Item label='跳转链接' name='link_url'>
            <Input />
          </Form.Item>
          <Form.Item
            label='排序'
            name='sort_order'
            initialValue={''}
            rules={[
              { required: true, message: '请输入排序号' },
              { type: 'number', min: 1, message: '排序号必须大于0' },
            ]}
          >
            <InputNumber min={1} style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            label='状态'
            name='status'
            valuePropName='checked'
            initialValue={true}
          >
            <Switch checkedChildren='启用' unCheckedChildren='禁用' />
          </Form.Item>
          <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 8 }}>
            <Button onClick={() => setModalVisible(false)}>取消</Button>
            <Button
              type='primary'
              icon={<UploadOutlined />}
              loading={uploading}
              onClick={handleUpload}
              disabled={!title || !file || uploading}
            >
              上传图片
            </Button>
          </div>
        </Form>
      </Modal>
    </>
  );
};

export default BannerList;
