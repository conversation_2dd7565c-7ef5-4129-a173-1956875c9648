import React, { useEffect, useState } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  InputNumber,
  Switch,
  Upload,
  Select,
  message,
} from 'antd';
import { PlusOutlined, UploadOutlined } from '@ant-design/icons';
import axios from 'axios';
import {
  fetchCategoryList,
  createCategory,
  updateCategory,
  deleteCategory,
} from '@/api/category';

const { Option } = Select;

const CategoryList = () => {
  const [data, setData] = useState([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(6);
  const [modalVisible, setModalVisible] = useState(false);
  const [editing, setEditing] = useState(null);
  const [form] = Form.useForm();
  const [uploading, setUploading] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [searchName, setSearchName] = useState('');

  const fetchData = async (page = 1, pageSize = 6, name = '') => {
    setLoading(true);
    try {
      const res = await fetchCategoryList({ page, pageSize, name });
      console.log('完整API响应:', JSON.stringify(res, null, 2));

      // 根据API测试结果，axios拦截器返回response.data，数据结构为 { code, data, msg }
      let list = [];
      let total = 0;

      if (res && res.success && res.data && Array.isArray(res.data.list)) {
        list = res.data.list;
        total = res.data.pagination?.total || 0;
        console.log('成功解析数据:', { list: list.length, total, page });
      } else {
        console.error('API返回数据结构异常:', res);
        message.error(res.message || '数据格式错误');
      }

      setData(list);
      setTotal(total);

      console.log('最终设置的数据:', {
        listLength: list.length,
        total,
        currentPage: page,
        pageSize,
      });
    } catch (e) {
      message.error('获取数据失败');
      console.error('获取数据失败:', e);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchData(page, pageSize, searchName);
    // eslint-disable-next-line
  }, []);

  const handleSearch = () => {
    setPage(1);
    fetchData(1, pageSize, searchName);
  };

  const handleAdd = () => {
    setEditing(null);
    setImageUrl('');
    setModalVisible(true);
    form.resetFields();
  };

  const handleEdit = record => {
    setEditing(record);
    setImageUrl(record.image);
    setModalVisible(true);
    form.setFieldsValue(record);
  };

  const handleDelete = async id => {
    Modal.confirm({
      title: '确认删除该分类？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await deleteCategory(id);
          message.success('删除成功');

          // 删除后重新计算分页
          const newTotal = total - 1;
          const maxPage = Math.ceil(newTotal / pageSize);

          let targetPage = page;
          // 如果当前页超过了最大页数，跳转到最后一页
          if (page > maxPage && maxPage > 0) {
            targetPage = maxPage;
          }
          // 如果删除后没有数据了，跳转到第1页
          if (newTotal === 0) {
            targetPage = 1;
          }

          console.log('删除后计算:', {
            originalTotal: total,
            newTotal,
            currentPage: page,
            targetPage,
            maxPage,
          });

          setPage(targetPage);
          fetchData(targetPage, pageSize, searchName);
        } catch (error) {
          message.error('删除失败，请重试');
        }
      },
    });
  };

  // 状态切换处理函数
  const handleToggleStatus = async record => {
    try {
      const newStatus = record.status === 1 ? 0 : 1;
      await updateCategory(record.id, { ...record, status: newStatus });
      message.success(newStatus === 1 ? '已启用' : '已禁用');
      fetchData(page, pageSize, searchName);
    } catch (error) {
      message.error('状态切换失败');
    }
  };

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      values.image = imageUrl;
      values.parent_id = 0; // 设置为顶级分类
      if (!values.image) {
        message.error('请上传图片');
        return;
      }
      if (editing) {
        await updateCategory(editing.id, values);
        message.success('更新成功');
        // 编辑后保持当前页
        fetchData(page, pageSize, searchName);
      } else {
        // 创建分类
        await createCategory(values);
        message.success('添加成功');

        // 新分类会被排在最后，所以计算最后一页
        const newTotalCount = total + 1;
        const lastPage = Math.ceil(newTotalCount / pageSize);

        console.log('添加分类后计算:', {
          originalTotal: total,
          newTotalCount,
          pageSize,
          lastPage,
          currentPage: page,
        });

        // 跳转到最后一页（新分类所在页）
        setPage(lastPage);
        fetchData(lastPage, pageSize, searchName);
      }
      setModalVisible(false);
    } catch (e) {
      console.error('操作失败:', e);
      message.error('操作失败，请重试');
    }
  };

  const uploadProps = {
    name: 'file',
    action: '/api/admin/category/upload', // 需后端实现
    showUploadList: false,
    beforeUpload: () => {
      setUploading(true);
    },
    onChange(info) {
      if (info.file.status === 'done') {
        setImageUrl(info.file.response.url);
        setUploading(false);
        message.success('上传成功');
      } else if (info.file.status === 'error') {
        setUploading(false);
        message.error('上传失败');
      }
    },
  };

  const columns = [
    {
      title: '图片',
      dataIndex: 'image',
      align: 'center',
      render: url =>
        url ? <img src={url} alt='' style={{ width: 60 }} /> : '-',
    },
    {
      title: '名称',
      dataIndex: 'name',
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      align: 'center',
      render: (status, record) => (
        <Switch
          checked={status === 1}
          onChange={() => handleToggleStatus(record)}
          checkedChildren='启用'
          unCheckedChildren='禁用'
        />
      ),
    },
    {
      title: '操作',
      width: 180,
      align: 'center',
      render: (_, record) => (
        <>
          <Button type='link' onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Button type='link' danger onClick={() => handleDelete(record.id)}>
            删除
          </Button>
        </>
      ),
    },
  ];

  return (
    <div
      style={{
        padding: '0 24px',
        backgroundColor: '#fff',
        borderRadius: '0 0 6px 6px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
      }}
    >
      <div
        style={{
          marginBottom: 20,
          display: 'flex',
          gap: 12,
          padding: '20px 0 16px 0',
          borderBottom: '1px solid #f0f0f0',
        }}
      >
        <Input
          placeholder='分类名称'
          value={searchName}
          onChange={e => setSearchName(e.target.value)}
          style={{ width: 200 }}
        />
        <Button type='primary' onClick={handleSearch}>
          搜索
        </Button>
        <Button
          type='primary'
          icon={<PlusOutlined />}
          onClick={handleAdd}
          style={{ marginLeft: 'auto' }}
        >
          新增分类
        </Button>
      </div>
      <Table
        rowKey='id'
        columns={columns}
        dataSource={data}
        loading={loading}
        pagination={{
          current: page,
          pageSize,
          total,
          showSizeChanger: false,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条分类`,
          onChange: (p, ps) => {
            const newPageSize = ps || 6;
            setPage(p);
            setPageSize(newPageSize);
            fetchData(p, newPageSize, searchName);
          },
          pageSizeOptions: ['6', '12', '18', '24'],
          size: 'default',
        }}
        // 按sort_order升序排序
        sortDirections={['ascend', 'descend']}
        defaultSortOrder='ascend'
        defaultSortField='sort_order'
        style={{ paddingBottom: '20px' }}
      />
      <Modal
        title={editing ? '编辑分类' : '新增分类'}
        open={modalVisible}
        footer={null}
        onCancel={() => setModalVisible(false)}
        destroyOnHidden
      >
        <Form form={form} layout='vertical'>
          <Form.Item
            label='分类名称'
            name='name'
            rules={[{ required: true, message: '请输入分类名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item label='图片' required>
            <Upload {...uploadProps}>
              <Button icon={<UploadOutlined />} loading={uploading}>
                上传图片
              </Button>
            </Upload>
            {imageUrl && (
              <img src={imageUrl} alt='' style={{ width: 120, marginTop: 8 }} />
            )}
          </Form.Item>
          <Form.Item
            label='状态'
            name='status'
            valuePropName='checked'
            initialValue={true}
          >
            <Switch checkedChildren='启用' unCheckedChildren='禁用' />
          </Form.Item>
          <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 8 }}>
            <Button onClick={() => setModalVisible(false)}>取消</Button>
            <Button type='primary' onClick={handleOk}>
              确定
            </Button>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default CategoryList;
