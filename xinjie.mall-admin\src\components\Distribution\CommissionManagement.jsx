import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  DatePicker,
  Tag,
  Modal,
  message,
  Statistic,
  Row,
  Col,
  Tooltip
} from 'antd';
import {
  SearchOutlined,
  DollarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExportOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { getCommissionList, settleCommission } from '../../api/distribution';

const { RangePicker } = DatePicker;
const { Option } = Select;

const CommissionManagement = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [filters, setFilters] = useState({
    keyword: '',
    status: '',
    commissionType: '',
    startDate: '',
    endDate: ''
  });
  const [statistics, setStatistics] = useState({});
  const [detailModal, setDetailModal] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState(null);

  useEffect(() => {
    fetchData();
  }, [pagination.current, pagination.pageSize, filters]);

  const fetchData = async () => {
    setLoading(true);
    try {
      const params = {
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...filters
      };
      const response = await getCommissionList(params);
      
      if (response.success) {
        setData(response.data.list);
        setPagination(prev => ({
          ...prev,
          total: response.data.pagination.total
        }));
        setStatistics(response.data.statistics || {});
      }
    } catch (error) {
      message.error('获取佣金记录失败');
    } finally {
      setLoading(false);
    }
  };

  const handleBatchSettle = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要结算的佣金记录');
      return;
    }

    const pendingRecords = data.filter(record => 
      selectedRowKeys.includes(record.id) && record.status === 0
    );

    if (pendingRecords.length === 0) {
      message.warning('选中的记录中没有可结算的佣金');
      return;
    }

    const totalAmount = pendingRecords.reduce((sum, record) => sum + parseFloat(record.amount), 0);

    Modal.confirm({
      title: '确认批量结算',
      content: (
        <div>
          <p>确定要结算选中的 <strong>{pendingRecords.length}</strong> 条佣金记录吗？</p>
          <p>结算总金额：<span style={{ color: '#52c41a', fontWeight: 'bold' }}>¥{totalAmount.toFixed(2)}</span></p>
        </div>
      ),
      onOk: async () => {
        try {
          const response = await settleCommission({ 
            commissionIds: pendingRecords.map(r => r.id) 
          });
          if (response.success) {
            message.success(`成功结算 ${pendingRecords.length} 条佣金记录`);
            setSelectedRowKeys([]);
            fetchData();
          }
        } catch (error) {
          message.error('批量结算失败');
        }
      }
    });
  };

  const handleViewDetail = (record) => {
    setSelectedRecord(record);
    setDetailModal(true);
  };

  const handleExport = () => {
    message.info('导出功能开发中...');
  };

  const columns = [
    {
      title: '分销商信息',
      key: 'distributor_info',
      width: 200,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.nickname}</div>
          <div style={{ color: '#666', fontSize: '12px' }}>
            {record.phone}
          </div>
          <div style={{ color: '#999', fontSize: '12px' }}>
            {record.distributor_code}
          </div>
        </div>
      )
    },
    {
      title: '订单信息',
      key: 'order_info',
      width: 150,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.order_no}</div>
          <div style={{ color: '#666', fontSize: '12px' }}>
            金额：¥{record.order_amount}
          </div>
          <div style={{ color: '#999', fontSize: '12px' }}>
            {new Date(record.order_time).toLocaleDateString()}
          </div>
        </div>
      )
    },
    {
      title: '佣金类型',
      dataIndex: 'commission_type',
      key: 'commission_type',
      width: 100,
      render: (type) => (
        <Tag color={type === 1 ? 'blue' : 'green'}>
          {type === 1 ? '一级佣金' : '二级佣金'}
        </Tag>
      )
    },
    {
      title: '佣金金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      render: (amount) => (
        <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
          ¥{parseFloat(amount).toFixed(2)}
        </span>
      ),
      sorter: (a, b) => parseFloat(a.amount) - parseFloat(b.amount)
    },
    {
      title: '积分奖励',
      dataIndex: 'points',
      key: 'points',
      width: 100,
      render: (points) => points > 0 ? (
        <span style={{ color: '#1890ff' }}>{points}分</span>
      ) : '-'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => {
        const statusConfig = {
          0: { text: '待结算', color: 'orange', icon: <ClockCircleOutlined /> },
          1: { text: '已结算', color: 'green', icon: <CheckCircleOutlined /> },
          2: { text: '已取消', color: 'red', icon: null }
        };
        const config = statusConfig[status];
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        );
      }
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (time) => new Date(time).toLocaleString()
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      fixed: 'right',
      render: (_, record) => (
        <Tooltip title="查看详情">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          />
        </Tooltip>
      )
    }
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record) => ({
      disabled: record.status !== 0, // 只能选择待结算的记录
    }),
  };

  return (
    <div>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总佣金"
              value={statistics.total_amount || 0}
              prefix={<DollarOutlined />}
              suffix="元"
              precision={2}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已结算"
              value={statistics.settled_amount || 0}
              prefix={<CheckCircleOutlined />}
              suffix="元"
              precision={2}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待结算"
              value={statistics.pending_amount || 0}
              prefix={<ClockCircleOutlined />}
              suffix="元"
              precision={2}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="记录总数"
              value={statistics.total_records || 0}
              suffix="条"
            />
          </Card>
        </Col>
      </Row>

      <Card>
        {/* 筛选条件 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={5}>
            <Input
              placeholder="搜索分销商昵称/手机号"
              prefix={<SearchOutlined />}
              allowClear
              onChange={(e) => setFilters(prev => ({ ...prev, keyword: e.target.value }))}
            />
          </Col>
          <Col span={3}>
            <Select
              placeholder="佣金状态"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
            >
              <Option value="0">待结算</Option>
              <Option value="1">已结算</Option>
              <Option value="2">已取消</Option>
            </Select>
          </Col>
          <Col span={3}>
            <Select
              placeholder="佣金类型"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => setFilters(prev => ({ ...prev, commissionType: value }))}
            >
              <Option value="1">一级佣金</Option>
              <Option value="2">二级佣金</Option>
            </Select>
          </Col>
          <Col span={6}>
            <RangePicker
              style={{ width: '100%' }}
              onChange={(dates, dateStrings) => {
                setFilters(prev => ({
                  ...prev,
                  startDate: dateStrings[0],
                  endDate: dateStrings[1]
                }));
              }}
            />
          </Col>
          <Col span={7}>
            <Space>
              <Button
                type="primary"
                icon={<DollarOutlined />}
                onClick={handleBatchSettle}
                disabled={selectedRowKeys.length === 0}
              >
                批量结算 ({selectedRowKeys.length})
              </Button>
              <Button
                icon={<ExportOutlined />}
                onClick={handleExport}
              >
                导出
              </Button>
            </Space>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={data}
          rowKey="id"
          loading={loading}
          rowSelection={rowSelection}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
          onChange={(paginationInfo) => {
            setPagination(prev => ({
              ...prev,
              current: paginationInfo.current,
              pageSize: paginationInfo.pageSize
            }));
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 详情弹窗 */}
      <Modal
        title="佣金详情"
        open={detailModal}
        onCancel={() => setDetailModal(false)}
        footer={null}
        width={600}
      >
        {selectedRecord && (
          <div>
            <Row gutter={16}>
              <Col span={12}>
                <Card title="分销商信息" size="small">
                  <p><strong>昵称：</strong>{selectedRecord.nickname}</p>
                  <p><strong>手机：</strong>{selectedRecord.phone}</p>
                  <p><strong>编码：</strong>{selectedRecord.distributor_code}</p>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="订单信息" size="small">
                  <p><strong>订单号：</strong>{selectedRecord.order_no}</p>
                  <p><strong>订单金额：</strong>¥{selectedRecord.order_amount}</p>
                  <p><strong>下单时间：</strong>{new Date(selectedRecord.order_time).toLocaleString()}</p>
                </Card>
              </Col>
            </Row>
            <Card title="佣金信息" size="small" style={{ marginTop: 16 }}>
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic title="佣金类型" value={selectedRecord.commission_type === 1 ? '一级佣金' : '二级佣金'} />
                </Col>
                <Col span={8}>
                  <Statistic title="佣金金额" value={selectedRecord.amount} prefix="¥" precision={2} />
                </Col>
                <Col span={8}>
                  <Statistic title="积分奖励" value={selectedRecord.points || 0} suffix="分" />
                </Col>
              </Row>
              {selectedRecord.remark && (
                <div style={{ marginTop: 16 }}>
                  <strong>备注：</strong>{selectedRecord.remark}
                </div>
              )}
            </Card>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default CommissionManagement;
