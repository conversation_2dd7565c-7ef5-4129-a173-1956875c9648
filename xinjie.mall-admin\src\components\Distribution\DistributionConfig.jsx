import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  InputNumber,
  Button,
  message,
  Space,
  Typography,
  Row,
  Col,
  Alert,
  Divider
} from 'antd';
import {
  SettingOutlined,
  SaveOutlined,
  ReloadOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { getDistributionConfig, updateDistributionConfig } from '../../api/distribution';

const { Title, Text } = Typography;

const DistributionConfig = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [configData, setConfigData] = useState({});

  useEffect(() => {
    fetchConfig();
  }, []);

  const fetchConfig = async () => {
    setLoading(true);
    try {
      const response = await getDistributionConfig();
      if (response.success) {
        setConfigData(response.data);
        
        // 设置表单初始值
        const formValues = {};
        Object.entries(response.data).forEach(([key, value]) => {
          // 处理数值类型
          if (key.includes('rate')) {
            formValues[key] = parseFloat(value);
          } else if (key.includes('amount') || key.includes('days') || key.includes('level')) {
            formValues[key] = parseInt(value) || 0;
          } else {
            formValues[key] = value;
          }
        });
        form.setFieldsValue(formValues);
      }
    } catch (error) {
      message.error('获取配置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      setSaving(true);
      
      const response = await updateDistributionConfig(values);
      if (response.success) {
        message.success('配置保存成功');
        fetchConfig(); // 重新获取配置
      }
    } catch (error) {
      if (error.errorFields) {
        message.error('请检查表单填写是否正确');
      } else {
        message.error('配置保存失败');
      }
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    form.resetFields();
    fetchConfig();
  };

  return (
    <div>
      <Card style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Title level={4} style={{ margin: 0 }}>
              <SettingOutlined /> 分销系统配置
            </Title>
            <Text type="secondary">
              配置分销系统的各项参数，修改后立即生效
            </Text>
          </div>
          <Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={handleReset}
              loading={loading}
            >
              重置
            </Button>
            <Button 
              type="primary" 
              icon={<SaveOutlined />}
              onClick={handleSave}
              loading={saving}
            >
              保存配置
            </Button>
          </Space>
        </div>
      </Card>

      <Alert
        message="重要提示"
        description="分销配置修改后立即生效，请谨慎操作。建议在业务低峰期进行配置调整。"
        type="warning"
        icon={<InfoCircleOutlined />}
        style={{ marginBottom: 16 }}
        showIcon
      />

      <Form
        form={form}
        layout="vertical"
        loading={loading}
      >
        {/* 基础配置 */}
        <Card title="基础配置" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="max_distribution_levels"
                label="最大分销层级"
                rules={[
                  { required: true, message: '请输入最大分销层级' },
                  { type: 'number', min: 1, max: 2, message: '分销层级只能是1或2' }
                ]}
                extra="法律要求：最多只能设置2级分销"
              >
                <InputNumber
                  min={1}
                  max={2}
                  precision={0}
                  style={{ width: '100%' }}
                  disabled // 硬编码限制，不允许修改
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="min_order_amount"
                label="最小有效订单金额"
                rules={[
                  { required: true, message: '请输入最小订单金额' },
                  { type: 'number', min: 0, message: '金额不能小于0' }
                ]}
                extra="低于此金额的订单不产生分销佣金"
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  addonAfter="元"
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 佣金配置 */}
        <Card title="佣金配置" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="level1_commission_rate"
                label="一级分销佣金比例"
                rules={[
                  { required: true, message: '请输入一级佣金比例' },
                  { type: 'number', min: 0, max: 0.2, message: '佣金比例应在0-20%之间' }
                ]}
                extra="建议设置为3%-8%"
              >
                <InputNumber
                  min={0}
                  max={0.2}
                  step={0.01}
                  precision={4}
                  style={{ width: '100%' }}
                  formatter={value => `${(value * 100).toFixed(2)}%`}
                  parser={value => value.replace('%', '') / 100}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="level2_commission_rate"
                label="二级分销佣金比例"
                rules={[
                  { required: true, message: '请输入二级佣金比例' },
                  { type: 'number', min: 0, max: 0.1, message: '二级佣金比例应在0-10%之间' }
                ]}
                extra="建议设置为1%-3%"
              >
                <InputNumber
                  min={0}
                  max={0.1}
                  step={0.01}
                  precision={4}
                  style={{ width: '100%' }}
                  formatter={value => `${(value * 100).toFixed(2)}%`}
                  parser={value => value.replace('%', '') / 100}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="max_commission_per_order"
                label="单笔订单最大佣金"
                rules={[
                  { required: true, message: '请输入最大佣金限制' },
                  { type: 'number', min: 0, message: '金额不能小于0' }
                ]}
                extra="防止异常高额佣金"
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  addonAfter="元"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="settle_delay_days"
                label="佣金结算延迟天数"
                rules={[
                  { required: true, message: '请输入结算延迟天数' },
                  { type: 'number', min: 0, max: 30, message: '延迟天数应在0-30天之间' }
                ]}
                extra="订单完成后延迟结算的天数"
              >
                <InputNumber
                  min={0}
                  max={30}
                  precision={0}
                  style={{ width: '100%' }}
                  addonAfter="天"
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 积分配置 */}
        <Card title="积分配置" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="level1_points_rate"
                label="一级分销积分倍率"
                rules={[
                  { required: true, message: '请输入一级积分倍率' },
                  { type: 'number', min: 0, message: '倍率不能小于0' }
                ]}
                extra="基于订单金额的积分奖励倍率"
              >
                <InputNumber
                  min={0}
                  precision={0}
                  style={{ width: '100%' }}
                  addonAfter="倍"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="level2_points_rate"
                label="二级分销积分倍率"
                rules={[
                  { required: true, message: '请输入二级积分倍率' },
                  { type: 'number', min: 0, message: '倍率不能小于0' }
                ]}
                extra="二级分销的积分奖励倍率"
              >
                <InputNumber
                  min={0}
                  precision={0}
                  style={{ width: '100%' }}
                  addonAfter="倍"
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 风控配置 */}
        <Card title="风控配置" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="max_daily_shares"
                label="每日最大分享次数"
                rules={[
                  { required: true, message: '请输入每日分享限制' },
                  { type: 'number', min: 1, max: 100, message: '分享次数应在1-100之间' }
                ]}
                extra="防止恶意刷分享"
              >
                <InputNumber
                  min={1}
                  max={100}
                  precision={0}
                  style={{ width: '100%' }}
                  addonAfter="次"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="distributor_min_purchase"
                label="成为分销商最低消费"
                rules={[
                  { required: true, message: '请输入最低消费要求' },
                  { type: 'number', min: 0, message: '金额不能小于0' }
                ]}
                extra="用户申请成为分销商的最低消费门槛"
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  addonAfter="元"
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="distributor_min_level"
                label="成为分销商最低会员等级"
                rules={[
                  { required: true, message: '请输入最低会员等级' },
                  { type: 'number', min: 1, max: 10, message: '等级应在1-10之间' }
                ]}
                extra="申请分销商的最低会员等级要求"
              >
                <InputNumber
                  min={1}
                  max={10}
                  precision={0}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>
      </Form>

      {/* 配置说明 */}
      <Card title="配置说明">
        <div style={{ color: '#666', lineHeight: '1.8' }}>
          <p><strong>佣金比例：</strong>设置一级和二级分销的佣金比例，建议一级5%，二级2%，总和不超过10%</p>
          <p><strong>积分倍率：</strong>分销订单的积分奖励倍率，基于订单金额计算额外积分</p>
          <p><strong>最小订单金额：</strong>低于此金额的订单不产生分销佣金，防止刷单</p>
          <p><strong>每日分享限制：</strong>防止恶意刷分享，建议设置为20-50次</p>
          <p><strong>结算延迟天数：</strong>订单完成后延迟结算的天数，用于处理退款等情况</p>
          <p><strong>成为分销商条件：</strong>用户申请成为分销商需要满足的最低要求</p>
          <Divider />
          <Alert
            message="法律合规提醒"
            description="根据相关法律法规，分销层级最多只能设置2级，佣金比例应合理设置，避免涉嫌传销。"
            type="info"
            showIcon
          />
        </div>
      </Card>
    </div>
  );
};

export default DistributionConfig;
