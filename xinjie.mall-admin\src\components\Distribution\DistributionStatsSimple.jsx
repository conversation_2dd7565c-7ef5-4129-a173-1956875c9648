import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Select,
  Table,
  Progress,
  Tag
} from 'antd';
import {
  UserOutlined,
  DollarOutlined,
  ShoppingOutlined,
  ShareAltOutlined,
  RiseOutlined,
  TeamOutlined
} from '@ant-design/icons';
import { getDistributionStats } from '../../api/distribution';

const { Option } = Select;

const DistributionStatsSimple = () => {
  const [loading, setLoading] = useState(false);
  const [period, setPeriod] = useState('7');
  const [statsData, setStatsData] = useState({
    basicStats: {},
    commissionStats: {},
    shareStats: {},
    trendData: [],
    levelDistribution: [],
    hotProducts: []
  });

  useEffect(() => {
    fetchStats();
  }, [period]);

  const fetchStats = async () => {
    setLoading(true);
    try {
      const response = await getDistributionStats({ period });
      if (response.success) {
        setStatsData(response.data);
      }
    } catch (error) {
      console.error('获取分销统计失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 热门商品表格列
  const hotProductColumns = [
    {
      title: '商品名称',
      dataIndex: 'product_name',
      key: 'product_name',
      ellipsis: true
    },
    {
      title: '分享次数',
      dataIndex: 'share_count',
      key: 'share_count',
      render: (count) => <Tag color="blue">{count}</Tag>
    },
    {
      title: '点击次数',
      dataIndex: 'total_clicks',
      key: 'total_clicks',
      render: (clicks) => <Tag color="green">{clicks}</Tag>
    },
    {
      title: '转化订单',
      dataIndex: 'total_orders',
      key: 'total_orders',
      render: (orders) => <Tag color="orange">{orders}</Tag>
    },
    {
      title: '转化率',
      key: 'conversion_rate',
      render: (_, record) => {
        const rate = record.total_clicks > 0 
          ? ((record.total_orders / record.total_clicks) * 100).toFixed(2)
          : 0;
        return `${rate}%`;
      }
    }
  ];

  return (
    <div>
      {/* 时间筛选 */}
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <h3>分销数据统计</h3>
          </Col>
          <Col>
            <Select
              value={period}
              onChange={setPeriod}
              style={{ width: 120 }}
            >
              <Option value="7">近7天</Option>
              <Option value="30">近30天</Option>
              <Option value="90">近90天</Option>
            </Select>
          </Col>
        </Row>
      </Card>

      {/* 核心指标 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃分销商"
              value={statsData.basicStats.active_distributors || 0}
              prefix={<UserOutlined style={{ color: '#1890ff' }} />}
              suffix="人"
            />
            <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              今日新增: {statsData.basicStats.today_new_distributors || 0}人
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总佣金"
              value={statsData.commissionStats.total_commission || 0}
              prefix={<DollarOutlined style={{ color: '#52c41a' }} />}
              suffix="元"
              precision={2}
            />
            <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              今日佣金: ¥{statsData.commissionStats.today_commission || 0}
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="分销订单"
              value={statsData.commissionStats.total_orders || 0}
              prefix={<ShoppingOutlined style={{ color: '#722ed1' }} />}
              suffix="单"
            />
            <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              待结算: {statsData.commissionStats.pending_commission || 0}元
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="分享次数"
              value={statsData.shareStats.total_shares || 0}
              prefix={<ShareAltOutlined style={{ color: '#fa8c16' }} />}
              suffix="次"
            />
            <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              今日分享: {statsData.shareStats.today_shares || 0}次
            </div>
          </Card>
        </Col>
      </Row>

      {/* 简化的统计展示 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={16}>
          <Card title="分销趋势统计" loading={loading}>
            {statsData.trendData && statsData.trendData.length > 0 ? (
              <Row gutter={16}>
                {statsData.trendData.map((item, index) => (
                  <Col span={8} key={index}>
                    <Card size="small" style={{ marginBottom: 8 }}>
                      <Statistic
                        title={item.date}
                        value={item.orders}
                        suffix="单"
                        prefix={<ShoppingOutlined />}
                      />
                      <div style={{ marginTop: 8, color: '#52c41a' }}>
                        佣金: ¥{item.commission}
                      </div>
                    </Card>
                  </Col>
                ))}
              </Row>
            ) : (
              <div style={{ textAlign: 'center', padding: '50px 0', color: '#999' }}>
                暂无趋势数据
              </div>
            )}
          </Card>
        </Col>
        <Col span={8}>
          <Card title="分销商等级分布" loading={loading}>
            {statsData.levelDistribution && statsData.levelDistribution.length > 0 ? (
              <div>
                {statsData.levelDistribution.map(item => {
                  const total = statsData.levelDistribution.reduce((sum, i) => sum + i.count, 0);
                  const percentage = total > 0 ? ((item.count / total) * 100).toFixed(1) : 0;
                  return (
                    <div key={item.distributor_level} style={{ marginBottom: 16 }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                        <span>{item.distributor_level}级分销商</span>
                        <span>{item.count}人 ({percentage}%)</span>
                      </div>
                      <Progress 
                        percent={parseFloat(percentage)} 
                        size="small"
                        strokeColor={item.distributor_level === 1 ? '#1890ff' : '#52c41a'}
                      />
                    </div>
                  );
                })}
              </div>
            ) : (
              <div style={{ textAlign: 'center', padding: '50px 0', color: '#999' }}>
                暂无等级数据
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 分享效果统计 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={8}>
          <Card title="分享效果">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="总点击"
                  value={statsData.shareStats.total_clicks || 0}
                  prefix={<RiseOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="注册转化"
                  value={statsData.shareStats.total_registers || 0}
                  prefix={<TeamOutlined />}
                />
              </Col>
            </Row>
            <div style={{ marginTop: 16 }}>
              <div style={{ marginBottom: 8 }}>
                点击转化率: {
                  statsData.shareStats.total_shares > 0 
                    ? ((statsData.shareStats.total_clicks / statsData.shareStats.total_shares) * 100).toFixed(2)
                    : 0
                }%
              </div>
              <Progress 
                percent={
                  statsData.shareStats.total_shares > 0 
                    ? ((statsData.shareStats.total_clicks / statsData.shareStats.total_shares) * 100)
                    : 0
                }
                size="small"
              />
            </div>
          </Card>
        </Col>
        <Col span={16}>
          <Card title="热门分享商品" loading={loading}>
            <Table
              columns={hotProductColumns}
              dataSource={statsData.hotProducts}
              rowKey="product_id"
              pagination={false}
              size="small"
              scroll={{ y: 300 }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DistributionStatsSimple;
