import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Modal,
  Form,
  message,
  Tooltip,
  Avatar,
  Statistic,
  Row,
  Col
} from 'antd';
import {
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  UserOutlined,
  TeamOutlined,
  DollarOutlined,
  ShoppingOutlined
} from '@ant-design/icons';
import { getDistributorList, updateDistributorStatus, getDistributorDetail } from '../../api/distribution';

const { Option } = Select;
const { Search } = Input;

const DistributorList = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [filters, setFilters] = useState({
    keyword: '',
    status: '',
    level: ''
  });
  const [detailModal, setDetailModal] = useState(false);
  const [statusModal, setStatusModal] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState(null);
  const [distributorDetail, setDistributorDetail] = useState(null);
  const [statusForm] = Form.useForm();

  // 分销商状态配置
  const statusConfig = {
    0: { text: '普通用户', color: 'default' },
    1: { text: '正常', color: 'success' },
    2: { text: '暂停', color: 'warning' },
    3: { text: '禁用', color: 'error' }
  };

  // 分销商等级配置
  const levelConfig = {
    1: { text: '初级分销商', color: 'blue' },
    2: { text: '中级分销商', color: 'green' },
    3: { text: '高级分销商', color: 'gold' }
  };

  useEffect(() => {
    fetchData();
  }, [pagination.current, pagination.pageSize, filters]);

  const fetchData = async () => {
    setLoading(true);
    try {
      const params = {
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...filters
      };
      const response = await getDistributorList(params);
      
      if (response.success) {
        setData(response.data.list);
        setPagination(prev => ({
          ...prev,
          total: response.data.pagination.total
        }));
      }
    } catch (error) {
      message.error('获取分销商列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (value) => {
    setFilters(prev => ({ ...prev, keyword: value }));
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleTableChange = (paginationInfo) => {
    setPagination(prev => ({
      ...prev,
      current: paginationInfo.current,
      pageSize: paginationInfo.pageSize
    }));
  };

  const handleViewDetail = async (record) => {
    try {
      setSelectedRecord(record);
      const response = await getDistributorDetail(record.id);
      if (response.success) {
        setDistributorDetail(response.data);
        setDetailModal(true);
      }
    } catch (error) {
      message.error('获取分销商详情失败');
    }
  };

  const handleUpdateStatus = (record) => {
    setSelectedRecord(record);
    statusForm.setFieldsValue({
      status: record.distributor_status,
      reason: ''
    });
    setStatusModal(true);
  };

  const handleStatusSubmit = async () => {
    try {
      const values = await statusForm.validateFields();
      await updateDistributorStatus(selectedRecord.id, values);
      message.success('分销商状态更新成功');
      setStatusModal(false);
      fetchData();
    } catch (error) {
      message.error('更新分销商状态失败');
    }
  };

  const columns = [
    {
      title: '分销商信息',
      key: 'distributor_info',
      width: 200,
      render: (_, record) => (
        <Space>
          <Avatar 
            src={record.avatar} 
            icon={<UserOutlined />}
            size="large"
          />
          <div>
            <div style={{ fontWeight: 'bold' }}>{record.nickname}</div>
            <div style={{ color: '#666', fontSize: '12px' }}>
              {record.phone}
            </div>
            <div style={{ color: '#999', fontSize: '12px' }}>
              {record.distributor_code}
            </div>
          </div>
        </Space>
      )
    },
    {
      title: '等级状态',
      key: 'level_status',
      width: 120,
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <Tag color={levelConfig[record.distributor_level]?.color}>
            {levelConfig[record.distributor_level]?.text}
          </Tag>
          <Tag color={statusConfig[record.distributor_status]?.color}>
            {statusConfig[record.distributor_status]?.text}
          </Tag>
        </Space>
      )
    },
    {
      title: '今日数据',
      key: 'today_data',
      width: 120,
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <div>
            <DollarOutlined style={{ color: '#52c41a' }} />
            <span style={{ marginLeft: 4 }}>¥{record.today_commission || 0}</span>
          </div>
          <div>
            <ShoppingOutlined style={{ color: '#1890ff' }} />
            <span style={{ marginLeft: 4 }}>{record.today_orders || 0}单</span>
          </div>
        </Space>
      )
    },
    {
      title: '本月数据',
      key: 'month_data',
      width: 120,
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <div>
            <DollarOutlined style={{ color: '#52c41a' }} />
            <span style={{ marginLeft: 4 }}>¥{record.month_commission || 0}</span>
          </div>
          <div>
            <ShoppingOutlined style={{ color: '#1890ff' }} />
            <span style={{ marginLeft: 4 }}>{record.month_orders || 0}单</span>
          </div>
        </Space>
      )
    },
    {
      title: '累计数据',
      key: 'total_data',
      width: 150,
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <div>
            <DollarOutlined style={{ color: '#52c41a' }} />
            <span style={{ marginLeft: 4 }}>¥{record.total_commission || 0}</span>
          </div>
          <div>
            <TeamOutlined style={{ color: '#722ed1' }} />
            <span style={{ marginLeft: 4 }}>{record.total_customers || 0}人</span>
          </div>
          <div>
            <ShoppingOutlined style={{ color: '#1890ff' }} />
            <span style={{ marginLeft: 4 }}>{record.total_orders || 0}单</span>
          </div>
        </Space>
      )
    },
    {
      title: '申请时间',
      dataIndex: 'distributor_apply_time',
      key: 'distributor_apply_time',
      width: 120,
      render: (time) => time ? new Date(time).toLocaleDateString() : '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          <Tooltip title="修改状态">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleUpdateStatus(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div>
      <Card>
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Search
              placeholder="搜索分销商昵称/手机号/编码"
              allowClear
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="分销商状态"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => handleFilterChange('status', value)}
            >
              <Option value="1">正常</Option>
              <Option value="2">暂停</Option>
              <Option value="3">禁用</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="分销商等级"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => handleFilterChange('level', value)}
            >
              <Option value="1">初级分销商</Option>
              <Option value="2">中级分销商</Option>
              <Option value="3">高级分销商</Option>
            </Select>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={data}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 分销商详情弹窗 */}
      <Modal
        title="分销商详情"
        open={detailModal}
        onCancel={() => setDetailModal(false)}
        footer={null}
        width={800}
      >
        {distributorDetail && (
          <div>
            {/* 基本信息 */}
            <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic title="分销商编码" value={distributorDetail.distributor.distributor_code} />
                </Col>
                <Col span={8}>
                  <Statistic title="会员等级" value={distributorDetail.distributor.level_name || '普通会员'} />
                </Col>
                <Col span={8}>
                  <Statistic title="账户余额" value={distributorDetail.distributor.balance || 0} prefix="¥" />
                </Col>
              </Row>
            </Card>

            {/* 佣金统计 */}
            <Card title="佣金统计" size="small" style={{ marginBottom: 16 }}>
              <Row gutter={16}>
                <Col span={6}>
                  <Statistic 
                    title="总佣金" 
                    value={distributorDetail.commissionStats.total_commission || 0} 
                    prefix="¥" 
                  />
                </Col>
                <Col span={6}>
                  <Statistic 
                    title="已结算" 
                    value={distributorDetail.commissionStats.settled_commission || 0} 
                    prefix="¥" 
                  />
                </Col>
                <Col span={6}>
                  <Statistic 
                    title="待结算" 
                    value={distributorDetail.commissionStats.pending_commission || 0} 
                    prefix="¥" 
                  />
                </Col>
                <Col span={6}>
                  <Statistic 
                    title="订单数" 
                    value={distributorDetail.commissionStats.total_orders || 0} 
                  />
                </Col>
              </Row>
            </Card>

            {/* 推荐关系 */}
            <Card title="推荐关系" size="small">
              <Table
                columns={[
                  { title: '用户昵称', dataIndex: 'nickname', key: 'nickname' },
                  { title: '手机号', dataIndex: 'phone', key: 'phone' },
                  { title: '层级', dataIndex: 'level', key: 'level', render: (level) => `${level}级` },
                  { title: '绑定时间', dataIndex: 'bind_time', key: 'bind_time', render: (time) => new Date(time).toLocaleString() }
                ]}
                dataSource={distributorDetail.relations}
                rowKey="user_id"
                pagination={false}
                size="small"
              />
            </Card>
          </div>
        )}
      </Modal>

      {/* 状态修改弹窗 */}
      <Modal
        title="修改分销商状态"
        open={statusModal}
        onOk={handleStatusSubmit}
        onCancel={() => setStatusModal(false)}
      >
        <Form form={statusForm} layout="vertical">
          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select>
              <Option value={1}>正常</Option>
              <Option value={2}>暂停</Option>
              <Option value={3}>禁用</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="reason"
            label="操作原因"
          >
            <Input.TextArea rows={3} placeholder="请输入操作原因" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default DistributorList;
