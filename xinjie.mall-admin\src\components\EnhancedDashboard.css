/* 仪表板整体布局优化 */
.enhanced-dashboard {
  padding: 12px !important;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 欢迎卡片优化 */
.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  margin-bottom: 12px !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.15) !important;
}

.welcome-card .ant-card-body {
  padding: 16px 20px !important;
}

/* 统计卡片优化 */
.stats-card {
  transition: all 0.3s ease;
  height: 100%;
  border-radius: 8px !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06) !important;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.1) !important;
}

.stats-card .ant-card-body {
  padding: 16px !important;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 内容卡片优化 */
.content-card {
  border-radius: 8px !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06) !important;
  height: 100%;
}

.content-card .ant-card-body {
  padding: 16px !important;
}

.content-card .ant-card-head {
  padding: 0 16px !important;
  min-height: 48px !important;
}

/* 列表项优化 */
.order-list-item {
  padding: 12px 0 !important;
  border-bottom: 1px solid #f0f0f0;
}

.order-list-item:last-child {
  border-bottom: none;
}

/* 右侧面板优化 */
.side-panel {
  height: 100%;
}

.side-panel .ant-space-item {
  width: 100%;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .enhanced-dashboard {
    padding: 8px !important;
  }
  
  .welcome-card .ant-card-body {
    padding: 12px 16px !important;
  }
  
  .stats-card .ant-card-body {
    padding: 12px !important;
  }
  
  .content-card .ant-card-body {
    padding: 12px !important;
  }
}

/* 紧凑模式 */
.compact-mode {
  padding: 8px !important;
}

.compact-mode .welcome-card {
  margin-bottom: 8px !important;
}

.compact-mode .welcome-card .ant-card-body {
  padding: 12px 16px !important;
}

.compact-mode .stats-card .ant-card-body {
  padding: 12px !important;
}

.compact-mode .content-card .ant-card-body {
  padding: 12px !important;
}

/* 统计数字优化 */
.ant-statistic-content {
  font-weight: 600;
}

.ant-statistic-title {
  font-size: 13px !important;
  margin-bottom: 4px !important;
}

/* 趋势指示器 */
.trend-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  margin-top: 4px;
}

.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #ff4d4f;
}

.trend-neutral {
  color: #666;
}

/* 卡片标题图标 */
.card-title-icon {
  margin-right: 6px;
  font-size: 16px;
}

/* 快速操作按钮 */
.quick-action-btn {
  width: 100%;
  height: 36px;
  border-radius: 6px;
  font-size: 13px;
}

/* 列表优化 */
.ant-list-item {
  padding: 8px 0 !important;
}

.ant-list-item-meta-title {
  font-size: 14px !important;
  margin-bottom: 2px !important;
}

.ant-list-item-meta-description {
  font-size: 12px !important;
}

/* 空状态优化 */
.ant-empty {
  padding: 20px 0 !important;
}

.ant-empty-description {
  font-size: 13px !important;
  color: #999 !important;
}

/* 加载状态优化 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 标签优化 */
.status-tag {
  font-size: 11px !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
}

/* 按钮优化 */
.ant-btn-sm {
  height: 28px !important;
  padding: 0 12px !important;
  font-size: 12px !important;
}

/* 头像优化 */
.user-avatar {
  border: 2px solid rgba(255,255,255,0.3);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 渐变背景优化 - 现代浅绿色系 */
.gradient-bg-1 { background: linear-gradient(135deg, #10b981 0%, #059669 100%); }
.gradient-bg-2 { background: linear-gradient(135deg, #34d399 0%, #10b981 100%); }
.gradient-bg-3 { background: linear-gradient(135deg, #6ee7b7 0%, #34d399 100%); }
.gradient-bg-4 { background: linear-gradient(135deg, #a7f3d0 0%, #6ee7b7 100%); }

/* 滚动条优化 */
.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
