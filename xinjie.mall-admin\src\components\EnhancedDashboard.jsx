import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Space,
  Avatar,
  List,
  Tag,
  Button,
  Spin,
  Empty,
  message
} from 'antd';
import './EnhancedDashboard.css';
import {
  ShoppingCartOutlined,
  UserOutlined,
  ShopOutlined,
  DollarOutlined,
  ClockCircleOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import DashboardService from '../services/dashboardService';
import SystemNotifications from './SystemNotifications';
import QuickActions from './QuickActions';
import SystemStatus from './SystemStatus';
import SalesChart from './SalesChart';
import HotProducts from './HotProducts';
import { useRealTimeData } from '../hooks/useRealTimeData';

const { Title, Text } = Typography;

const EnhancedDashboard = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [userName] = useState('admin');

  // 使用实时数据Hook
  const {
    statistics,
    recentOrders,
    loading,
    refreshData,
    autoUpdate,
    toggleAutoUpdate,
    lastUpdate
  } = useRealTimeData(30000); // 30秒自动更新

  // 更新时间
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return '上午好';
    if (hour < 18) return '下午好';
    return '晚上好';
  };

  const statsConfig = [
    {
      key: 'orders',
      title: '今日订单',
      value: statistics.orders?.today || 0,
      total: statistics.orders?.total || 0,
      prefix: <ShoppingCartOutlined style={{ color: '#1890ff' }} />,
      suffix: '单',
      trend: statistics.orders?.trend || 0,
      color: '#1890ff',
      bgColor: '#e6f7ff'
    },
    {
      key: 'users',
      title: '用户总数',
      value: statistics.users?.total || 0,
      today: statistics.users?.today || 0,
      prefix: <UserOutlined style={{ color: '#52c41a' }} />,
      suffix: '人',
      trend: statistics.users?.trend || 0,
      color: '#52c41a',
      bgColor: '#f6ffed'
    },
    {
      key: 'products',
      title: '商品总数',
      value: statistics.products?.total || 0,
      active: statistics.products?.active || 0,
      prefix: <ShopOutlined style={{ color: '#fa8c16' }} />,
      suffix: '件',
      trend: statistics.products?.trend || 0,
      color: '#fa8c16',
      bgColor: '#fff7e6'
    },
    {
      key: 'sales',
      title: '今日销售额',
      value: statistics.sales?.today || 0,
      total: statistics.sales?.total || 0,
      prefix: <DollarOutlined style={{ color: '#eb2f96' }} />,
      suffix: '元',
      trend: statistics.sales?.trend || 0,
      color: '#eb2f96',
      bgColor: '#fff0f6'
    }
  ];

  const getStatusColor = (status) => {
    const statusMap = {
      '已完成': 'success',
      '待发货': 'warning',
      '已发货': 'processing',
      '待付款': 'default',
      '已取消': 'error'
    };
    return statusMap[status] || 'default';
  };

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '400px' 
      }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="enhanced-dashboard">
      {/* 欢迎区域 - 压缩高度 */}
      <Card
        className="welcome-card"
        style={{ color: 'white' }}
      >
        <Row align="middle" justify="space-between">
          <Col>
            <Space size="middle" align="center">
              <Avatar
                size={48}
                style={{
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  fontSize: '20px'
                }}
                icon={<UserOutlined />}
              />
              <div>
                <Title level={3} style={{ color: 'white', margin: 0, fontSize: '20px' }}>
                  {getGreeting()}，{userName}！
                </Title>
                <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: '14px' }}>
                  心洁茗茶后台管理系统
                </Text>
              </div>
            </Space>
          </Col>
          <Col>
            <div style={{ textAlign: 'right' }}>
              <Space direction="vertical" size={4}>
                <Button
                  type="text"
                  icon={<ReloadOutlined />}
                  onClick={refreshData}
                  style={{ color: 'white' }}
                  size="small"
                >
                  刷新数据
                </Button>
                <div style={{ fontSize: '12px', color: 'rgba(255,255,255,0.6)' }}>
                  自动更新: {autoUpdate ? '开启' : '关闭'}
                  <Button
                    type="text"
                    size="small"
                    onClick={toggleAutoUpdate}
                    style={{ color: 'rgba(255,255,255,0.8)', padding: '0 4px', marginLeft: '4px' }}
                  >
                    {autoUpdate ? '关闭' : '开启'}
                  </Button>
                </div>
                {lastUpdate && (
                  <div style={{ fontSize: '11px', color: 'rgba(255,255,255,0.5)' }}>
                    更新: {lastUpdate.toLocaleTimeString()}
                  </div>
                )}
              </Space>
              <div>
                <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: '14px' }}>
                  当前时间
                </Text>
                <div style={{ color: 'white', fontSize: '18px', fontWeight: 'bold' }}>
                  {currentTime.toLocaleString()}
                </div>
              </div>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 统计卡片 - 压缩间距 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
        {statsConfig.map((stat) => (
          <Col xs={24} sm={12} lg={6} key={stat.key}>
            <Card
              className="stats-card"
              style={{ background: stat.bgColor }}
              hoverable
            >
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <div style={{ flex: 1 }}>
                  <Text type="secondary" style={{ fontSize: '14px', marginBottom: '8px', display: 'block' }}>
                    {stat.title}
                  </Text>
                  <div style={{ display: 'flex', alignItems: 'baseline', gap: '8px', marginBottom: '4px' }}>
                    <span className="animated-number" style={{ fontSize: '28px', fontWeight: 'bold', color: stat.color }}>
                      {stat.value.toLocaleString()}
                    </span>
                    <span style={{ fontSize: '14px', color: '#666' }}>
                      {stat.suffix}
                    </span>
                  </div>
                  {stat.total && (
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      总计: {stat.total.toLocaleString()}{stat.suffix}
                    </Text>
                  )}
                  {stat.active && (
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      在售: {stat.active.toLocaleString()}{stat.suffix}
                    </Text>
                  )}
                  <div style={{ marginTop: '8px', display: 'flex', alignItems: 'center', gap: '4px' }}>
                    {stat.trend > 0 ? (
                      <ArrowUpOutlined style={{ color: '#52c41a', fontSize: '12px' }} />
                    ) : stat.trend < 0 ? (
                      <ArrowDownOutlined style={{ color: '#ff4d4f', fontSize: '12px' }} />
                    ) : null}
                    <Text style={{ 
                      fontSize: '12px', 
                      color: stat.trend > 0 ? '#52c41a' : stat.trend < 0 ? '#ff4d4f' : '#666'
                    }}>
                      {stat.trend !== 0 ? `${Math.abs(stat.trend)}%` : '无变化'}
                    </Text>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      较昨日
                    </Text>
                  </div>
                </div>
                <div style={{ 
                  fontSize: '32px', 
                  color: stat.color,
                  opacity: 0.8
                }}>
                  {stat.prefix}
                </div>
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      {/* 内容区域 - 压缩间距 */}
      <Row gutter={[16, 16]}>
        {/* 最近订单 */}
        <Col xs={24} lg={16}>
          <Card
            className="content-card"
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <ShoppingCartOutlined className="card-title-icon" style={{ color: '#1890ff' }} />
                <span>最近订单</span>
              </div>
            }
            extra={<Button type="link" href="/order" size="small">查看全部</Button>}
          >
            {recentOrders.length > 0 ? (
              <List
                dataSource={recentOrders}
                renderItem={(item) => (
                  <List.Item
                    style={{
                      padding: '16px 0',
                      borderBottom: '1px solid #f0f0f0'
                    }}
                    actions={[
                      <Tag color={getStatusColor(item.status)}>
                        {item.status}
                      </Tag>
                    ]}
                  >
                    <List.Item.Meta
                      avatar={
                        <Avatar
                          style={{ backgroundColor: '#1890ff' }}
                          icon={<UserOutlined />}
                        />
                      }
                      title={
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <span>订单 #{item.id}</span>
                          <Text strong style={{ color: '#1890ff' }}>
                            ¥{item.amount}
                          </Text>
                        </div>
                      }
                      description={
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <span>客户：{item.customer}</span>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            <ClockCircleOutlined style={{ marginRight: '4px' }} />
                            {item.time}
                          </Text>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            ) : (
              <Empty description="暂无订单数据" />
            )}
          </Card>
        </Col>

        {/* 右侧面板 - 压缩间距 */}
        <Col xs={24} lg={8}>
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            {/* 系统通知 */}
            <SystemNotifications />

            {/* 快捷操作 */}
            <QuickActions />

            {/* 系统状态 */}
            <SystemStatus />
          </Space>
        </Col>
      </Row>

      {/* 销售趋势图表和热销商品 - 压缩间距 */}
      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        <Col xs={24} lg={16}>
          <SalesChart />
        </Col>
        <Col xs={24} lg={8}>
          <HotProducts />
        </Col>
      </Row>
    </div>
  );
};

export default EnhancedDashboard;
