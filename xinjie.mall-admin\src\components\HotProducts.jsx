import React, { useState, useEffect } from 'react';
import { 
  Card, 
  List, 
  Avatar, 
  Typography, 
  Space, 
  Tag, 
  Button,
  Empty,
  Spin,
  Progress,
  message
} from 'antd';
import {
  FireOutlined,
  EyeOutlined,
  ShoppingCartOutlined,
  StarOutlined
} from '@ant-design/icons';
import DashboardService from '../services/dashboardService';

const { Text, Title } = Typography;

const HotProducts = () => {
  const [loading, setLoading] = useState(true);
  const [hotProducts, setHotProducts] = useState([]);

  useEffect(() => {
    loadHotProducts();
  }, []);

  const loadHotProducts = async () => {
    setLoading(true);
    try {
      const response = await DashboardService.getHotProducts(8);
      console.log('热销商品数据:', response);

      // 使用真实数据
      if (response && response.data && Array.isArray(response.data)) {
        const realData = response.data.map(product => ({
          ...product,
          image_url: product.image_url || product.main_image || '/images/default-product.svg',
          sales_count: product.sales_count || 0,
          rating: product.rating || 0,
          price: parseFloat(product.price || 0)
        }));
        setHotProducts(realData);
      } else if (Array.isArray(response)) {
        // 如果直接返回数组
        const realData = response.map(product => ({
          ...product,
          image_url: product.image_url || product.main_image || '/images/default-product.svg',
          sales_count: product.sales_count || 0,
          rating: product.rating || 0,
          price: parseFloat(product.price || 0)
        }));
        setHotProducts(realData);
      } else {
        // 没有数据时显示空列表
        setHotProducts([]);
      }
    } catch (error) {
      console.error('加载热销商品失败:', error);
      // 出错时显示空数据
      setHotProducts([]);
    } finally {
      setLoading(false);
    }
  };

  const generateMockData = () => {
    const products = [
      { name: '西湖龙井特级', category: '绿茶', price: 298 },
      { name: '铁观音浓香型', category: '乌龙茶', price: 188 },
      { name: '普洱茶饼357g', category: '普洱茶', price: 168 },
      { name: '碧螺春明前茶', category: '绿茶', price: 228 },
      { name: '大红袍岩茶', category: '乌龙茶', price: 388 },
      { name: '白毫银针', category: '白茶', price: 458 },
      { name: '祁门红茶', category: '红茶', price: 198 },
      { name: '茉莉花茶', category: '花茶', price: 128 }
    ];

    return products.map((product, index) => ({
      id: index + 1,
      name: product.name,
      category: product.category,
      price: product.price,
      sales_count: Math.floor(Math.random() * 500) + 100,
      image_url: `/images/tea-${index + 1}.jpg`,
      rating: (Math.random() * 1 + 4).toFixed(1),
      stock: Math.floor(Math.random() * 200) + 50
    }));
  };

  const getCategoryColor = (category) => {
    const colorMap = {
      '绿茶': 'green',
      '红茶': 'red',
      '乌龙茶': 'blue',
      '普洱茶': 'orange',
      '白茶': 'cyan',
      '花茶': 'purple'
    };
    return colorMap[category] || 'default';
  };

  const getPopularityLevel = (salesCount) => {
    if (salesCount >= 400) return { level: '爆款', color: '#ff4d4f', percent: 100 };
    if (salesCount >= 300) return { level: '热销', color: '#fa8c16', percent: 80 };
    if (salesCount >= 200) return { level: '畅销', color: '#52c41a', percent: 60 };
    return { level: '一般', color: '#1890ff', percent: 40 };
  };

  if (loading) {
    return (
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <FireOutlined style={{ color: '#ff4d4f' }} />
            <span>热销商品</span>
          </div>
        }
        style={{
          borderRadius: '12px',
          border: 'none',
          boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
        }}
      >
        <div className="loading-container">
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <FireOutlined style={{ color: '#ff4d4f' }} />
          <span>热销商品</span>
        </div>
      }
      extra={
        <Button type="link" size="small" onClick={loadHotProducts}>
          刷新
        </Button>
      }
      style={{
        borderRadius: '12px',
        border: 'none',
        boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
      }}
    >
      {hotProducts.length > 0 ? (
        <List
          dataSource={hotProducts}
          renderItem={(item, index) => {
            const popularity = getPopularityLevel(item.sales_count);
            return (
              <List.Item
                style={{ 
                  padding: '16px 0',
                  borderBottom: index === hotProducts.length - 1 ? 'none' : '1px solid #f0f0f0',
                  position: 'relative'
                }}
              >
                {/* 排名标识 */}
                <div style={{
                  position: 'absolute',
                  left: '-8px',
                  top: '16px',
                  width: '24px',
                  height: '24px',
                  borderRadius: '50%',
                  background: index < 3 ? 
                    (index === 0 ? '#ffd700' : index === 1 ? '#c0c0c0' : '#cd7f32') : 
                    '#f0f0f0',
                  color: index < 3 ? 'white' : '#666',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '12px',
                  fontWeight: 'bold',
                  zIndex: 1
                }}>
                  {index + 1}
                </div>

                <List.Item.Meta
                  avatar={
                    <Avatar 
                      size={48}
                      shape="square"
                      style={{ 
                        backgroundColor: '#f0f0f0',
                        border: '2px solid #fff',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                      }}
                      src={item.image_url}
                      icon={<ShoppingCartOutlined />}
                    />
                  }
                  title={
                    <div style={{ marginLeft: '16px' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '4px' }}>
                        <Text strong style={{ fontSize: '14px' }}>
                          {item.name}
                        </Text>
                        <Tag color={getCategoryColor(item.category)} size="small">
                          {item.category}
                        </Tag>
                      </div>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '8px' }}>
                        <Text strong style={{ color: '#ff4d4f', fontSize: '16px' }}>
                          ¥{item.price}
                        </Text>
                        <Space size={4}>
                          <StarOutlined style={{ color: '#fadb14', fontSize: '12px' }} />
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {item.rating}
                          </Text>
                        </Space>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          库存: {item.stock}
                        </Text>
                      </div>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          销量: {item.sales_count}
                        </Text>
                        <Tag color={popularity.color} size="small">
                          {popularity.level}
                        </Tag>
                      </div>
                      <div style={{ marginTop: '8px', width: '200px' }}>
                        <Progress
                          percent={popularity.percent}
                          strokeColor={popularity.color}
                          showInfo={false}
                          size="small"
                        />
                      </div>
                    </div>
                  }
                />

                {/* 操作按钮 */}
                <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                  <Button 
                    type="text" 
                    size="small" 
                    icon={<EyeOutlined />}
                    style={{ fontSize: '12px' }}
                  >
                    查看
                  </Button>
                  <Button 
                    type="text" 
                    size="small" 
                    icon={<ShoppingCartOutlined />}
                    style={{ fontSize: '12px' }}
                  >
                    编辑
                  </Button>
                </div>
              </List.Item>
            );
          }}
        />
      ) : (
        <Empty 
          description="暂无热销商品数据" 
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          style={{ padding: '40px 20px' }}
        />
      )}
    </Card>
  );
};

export default HotProducts;
