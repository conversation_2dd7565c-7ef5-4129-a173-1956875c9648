import React, { useEffect, useState } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Tag,
  message,
  Row,
  Col,
  Statistic,
  Card,
  DatePicker,
  InputNumber,
  Space,
  Tooltip,
  Popconfirm,
} from 'antd';
import {
  DownloadOutlined,
  PrinterOutlined,
  PlusOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  SendOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import {
  fetchOrderList,
  updateOrderStatus,
  shipOrder,
  deleteOrder,
  createOrder,
  fetchOrderStats,
  fetchOrderDetail,
  batchUpdateStatus,
  batchShip,
  exportOrders,
} from '@/api/order';
import { fetchAllUsers } from '@/api/user';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

const statusMap = {
  0: { text: '待付款', color: 'orange' },
  1: { text: '待发货', color: 'blue' },
  2: { text: '待收货', color: 'cyan' },
  3: { text: '已完成', color: 'green' },
  4: { text: '已取消', color: 'red' },
};

const OrderList = () => {
  const [data, setData] = useState([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchNo, setSearchNo] = useState('');
  const [searchReceiver, setSearchReceiver] = useState('');
  const [searchStatus, setSearchStatus] = useState('');
  const [searchDateRange, setSearchDateRange] = useState(null);
  const [detailModal, setDetailModal] = useState(false);
  const [shipModal, setShipModal] = useState(false);
  const [currentOrder, setCurrentOrder] = useState(null);
  const [detail, setDetail] = useState(null);
  const [shipForm] = Form.useForm();
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [batchShipModal, setBatchShipModal] = useState(false);
  const [batchStatusModal, setBatchStatusModal] = useState(false);
  const [batchShipForm] = Form.useForm();
  const [batchStatusForm] = Form.useForm();
  const [batchStatus, setBatchStatus] = useState('');
  const [stats, setStats] = useState({ statusStats: [], totalStats: {}, trendStats: [] });
  const [addModal, setAddModal] = useState(false);
  const [addForm] = Form.useForm();
  const [users, setUsers] = useState([]);

  const fetchData = async (page = 1, pageSize = 10) => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: pageSize,
        orderNo: searchNo,
        receiverName: searchReceiver,
        orderStatus: searchStatus,
      };

      // 添加时间范围参数
      if (searchDateRange && searchDateRange.length === 2) {
        params.startDate = searchDateRange[0].format('YYYY-MM-DD');
        params.endDate = searchDateRange[1].format('YYYY-MM-DD');
      }

      const res = await fetchOrderList(params);
      if (res && res.code === 200 && res.data) {
        setData(res.data.list || []);
        setTotal(res.data.total || 0);
        setStats({
          statusStats: res.data.statusStats || [],
          totalStats: res.data.totalStats || {},
          trendStats: res.data.trendStats || []
        });
      } else {
        setData([]);
        setTotal(0);
        message.error(res.message || '数据格式错误');
      }
    } catch (e) {
      console.error('获取订单列表失败:', e);
      message.error('获取数据失败');
    }
    setLoading(false);
  };

  const fetchOrderStatsData = async () => {
    try {
      const res = await fetchOrderStats();
      if (res && res.code === 200 && res.data) {
        setStats(res.data);
      } else {
        // 设置默认值，避免undefined导致的问题
        setStats({
          statusStats: [],
          totalStats: {},
          trendStats: []
        });
      }
    } catch (e) {
      console.error('获取统计数据失败:', e);
      // 设置默认值，避免undefined导致的问题
      setStats({
        statusStats: [],
        totalStats: {},
        trendStats: []
      });
    }
  };

  // 初始化数据加载
  useEffect(() => {
    fetchOrderStatsData();
    fetchUsers();
  }, []); // 只在组件挂载时执行一次

  // 分页数据加载
  useEffect(() => {
    fetchData(page, pageSize);
  }, [page, pageSize]);

  const handleSearch = () => {
    setPage(1);
    fetchData(1, pageSize);
  };

  const handleReset = () => {
    setSearchNo('');
    setSearchReceiver('');
    setSearchStatus('');
    setSearchDateRange(null);
    setPage(1);
    fetchData(1, pageSize);
  };

  const showDetail = async record => {
    setCurrentOrder(record);
    try {
      const res = await fetchOrderDetail(record.id);
      if (res && res.code === 200) {
        setDetail(res.data);
        setDetailModal(true);
      }
    } catch (e) {
      message.error('获取订单详情失败');
    }
  };

  const showShip = record => {
    setCurrentOrder(record);
    shipForm.resetFields();
    setShipModal(true);
  };

  const handleShip = async () => {
    try {
      const values = await shipForm.validateFields();
      await shipOrder(currentOrder.id, values);
      message.success('发货成功');
      setShipModal(false);
      fetchData(page, pageSize);
    } catch (e) {
      console.error('发货失败:', e);
    }
  };

  const handleStatus = async (record, status) => {
    try {
      await updateOrderStatus(record.id, { orderStatus: status });
      message.success('状态更新成功');
      fetchData(page, pageSize);
    } catch (e) {
      message.error('状态更新失败');
    }
  };

  const handleAdd = () => {
    setAddModal(true);
    addForm.resetFields();
  };

  const handleAddOk = async () => {
    try {
      const values = await addForm.validateFields();
      const orderData = {
        ...values,
        total_amount: values.pay_amount + (values.freight_amount || 0),
        pay_amount: values.pay_amount,
        freight_amount: values.freight_amount || 0,
        discount_amount: values.discount_amount || 0,
      };

      await createOrder(orderData);
      message.success('订单创建成功');
      setAddModal(false);
      fetchData(page, pageSize);
    } catch (e) {
      message.error('创建订单失败');
    }
  };

  const handleDelete = async (id) => {
    try {
      await deleteOrder(id);
      message.success('删除成功');
      fetchData(page, pageSize);
    } catch (e) {
      message.error('删除失败');
    }
  };

  const handleBatchShip = async () => {
    try {
      const values = await batchShipForm.validateFields();
      await batchShip(selectedRowKeys, values.deliveryCompany, values.deliveryNo);
      message.success('批量发货成功');
      setBatchShipModal(false);
      setSelectedRowKeys([]);
      fetchData(page, pageSize);
    } catch (e) {
      message.error('批量发货失败');
    }
  };

  const handleBatchStatus = async () => {
    try {
      const values = await batchStatusForm.validateFields();
      await batchUpdateStatus(selectedRowKeys, values.status, values.remark);
      message.success('批量状态更新成功');
      setBatchStatusModal(false);
      setSelectedRowKeys([]);
      fetchData(page, pageSize);
    } catch (e) {
      message.error('批量状态更新失败');
    }
  };

  const handleExport = async () => {
    try {
      const params = {
        orderNo: searchNo,
        receiverName: searchReceiver,
        orderStatus: searchStatus,
      };

      if (searchDateRange && searchDateRange.length === 2) {
        params.startDate = searchDateRange[0].format('YYYY-MM-DD');
        params.endDate = searchDateRange[1].format('YYYY-MM-DD');
      }

      const response = await exportOrders(params);
      const blob = new Blob([response], { type: 'application/vnd.ms-excel' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `订单数据_${new Date().toISOString().split('T')[0]}.xlsx`;
      link.click();
      window.URL.revokeObjectURL(url);
      message.success('导出成功');
    } catch (e) {
      message.error('导出失败');
    }
  };

  const fetchUsers = async () => {
    try {
      const res = await fetchAllUsers();
      if (res && res.success && res.data && Array.isArray(res.data.list)) {
        setUsers(res.data.list);
      } else {
        setUsers([]); // 设置默认空数组
      }
    } catch (error) {
      console.error('获取用户列表失败:', error);
      setUsers([]); // 设置默认空数组
    }
  };

  const columns = [
    {
      title: '订单号',
      dataIndex: 'order_no',
      key: 'order_no',
      width: 180,
      render: (text) => (
        <Tooltip title={text}>
          <span style={{ cursor: 'pointer', color: '#1890ff' }}>{text}</span>
        </Tooltip>
      ),
    },
    {
      title: '关联用户',
      dataIndex: 'user_id',
      key: 'user_id',
      width: 120,
      render: (userId) => {
        if (!userId) return '-';
        const user = users.find(u => u.id === userId);
        return user ? (
          <Tooltip title={`${user.nickname} (${user.phone})`}>
            <span>{user.nickname}</span>
          </Tooltip>
        ) : `用户ID: ${userId}`;
      },
    },
    {
      title: '收货人',
      dataIndex: 'receiver_name',
      key: 'receiver_name',
      width: 100,
    },
    {
      title: '手机号',
      dataIndex: 'receiver_phone',
      key: 'receiver_phone',
      width: 120,
    },
    {
      title: '金额',
      dataIndex: 'pay_amount',
      key: 'pay_amount',
      width: 100,
      render: (text) => `¥${text}`,
    },
    {
      title: '状态',
      dataIndex: 'order_status',
      key: 'order_status',
      width: 100,
      render: (status) => (
        <Tag color={statusMap[status]?.color}>{statusMap[status]?.text}</Tag>
      ),
    },
    {
      title: '下单时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 160,
      render: (text) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="link" 
            size="small" 
            icon={<EyeOutlined />}
            onClick={() => showDetail(record)}
          >
            详情
          </Button>
          <Button 
            type="link" 
            size="small" 
            icon={<SendOutlined />}
            onClick={() => showShip(record)}
            disabled={record.order_status !== 1}
          >
            发货
          </Button>
          <Popconfirm
            title="确定要删除这个订单吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              type="link" 
              size="small" 
              danger 
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总订单数"
              value={stats.totalStats?.total_orders || 0}
              prefix="📦"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总金额"
              value={stats.totalStats?.total_amount || 0}
              prefix="¥"
              precision={2}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待发货"
              value={stats.statusStats?.find(s => s.order_status === 1)?.count || 0}
              prefix="🚚"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待收货"
              value={stats.statusStats?.find(s => s.order_status === 2)?.count || 0}
              prefix="📦"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索栏 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <Input
              placeholder="订单号"
              value={searchNo}
              onChange={(e) => setSearchNo(e.target.value)}
              allowClear
            />
          </Col>
          <Col span={6}>
            <Input
              placeholder="收货人"
              value={searchReceiver}
              onChange={(e) => setSearchReceiver(e.target.value)}
              allowClear
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="订单状态"
              value={searchStatus}
              onChange={setSearchStatus}
              allowClear
              style={{ width: '100%' }}
            >
              {Object.entries(statusMap).map(([key, value]) => (
                <Option key={key} value={key}>
                  {value.text}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={6}>
            <RangePicker
              value={searchDateRange}
              onChange={setSearchDateRange}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={2}>
            <Space>
              <Button type="primary" onClick={handleSearch}>
                搜索
              </Button>
              <Button onClick={handleReset}>
                重置
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 操作栏 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={handleAdd}
              >
                新增订单
              </Button>
              <Button 
                icon={<DownloadOutlined />}
                onClick={handleExport}
              >
                导出Excel
              </Button>
              <Button 
                icon={<SendOutlined />}
                onClick={() => setBatchShipModal(true)}
                disabled={selectedRowKeys.length === 0}
              >
                批量发货
              </Button>
              <Button 
                icon={<EditOutlined />}
                onClick={() => setBatchStatusModal(true)}
                disabled={selectedRowKeys.length === 0}
              >
                批量状态变更
              </Button>
            </Space>
          </Col>
          <Col>
            <Button 
              icon={<ReloadOutlined />}
              onClick={() => fetchData(page, pageSize)}
            >
              刷新
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 订单列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={data}
          rowKey="id"
          loading={loading}
          pagination={{
            current: page,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
            onChange: (page, pageSize) => {
              setPage(page);
              setPageSize(pageSize);
            },
          }}
          rowSelection={rowSelection}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 订单详情弹窗 */}
      <Modal
        title="订单详情"
        open={detailModal}
        onCancel={() => setDetailModal(false)}
        footer={null}
        width={800}
      >
        {detail && (
          <div>
            <Row gutter={16}>
              <Col span={12}>
                <p><strong>订单号：</strong>{detail.order_no}</p>
                <p><strong>关联用户：</strong>
                  {detail.user_id ? (
                    (() => {
                      const user = users.find(u => u.id === detail.user_id);
                      return user ? `${user.nickname} (${user.phone})` : `用户ID: ${detail.user_id}`;
                    })()
                  ) : '未关联用户'}
                </p>
                <p><strong>收货人：</strong>{detail.receiver_name}</p>
                <p><strong>手机号：</strong>{detail.receiver_phone}</p>
                <p><strong>收货地址：</strong>{detail.receiver_address}</p>
              </Col>
              <Col span={12}>
                <p><strong>订单金额：</strong>¥{detail.pay_amount}</p>
                <p><strong>订单状态：</strong>
                  <Tag color={statusMap[detail.order_status]?.color}>
                    {statusMap[detail.order_status]?.text}
                  </Tag>
                </p>
                <p><strong>下单时间：</strong>{new Date(detail.created_at).toLocaleString()}</p>
                {detail.delivery_company && (
                  <p><strong>物流信息：</strong>{detail.delivery_company} {detail.delivery_no}</p>
                )}
              </Col>
            </Row>
            
            {detail.orderItems && detail.orderItems.length > 0 && (
              <div style={{ marginTop: '16px' }}>
                <h4>商品信息</h4>
                <Table
                  columns={[
                    { title: '商品名称', dataIndex: 'product_name' },
                    { title: '单价', dataIndex: 'price', render: (text) => `¥${text}` },
                    { title: '数量', dataIndex: 'quantity' },
                    { title: '小计', dataIndex: 'total_amount', render: (text) => `¥${text}` },
                  ]}
                  dataSource={detail.orderItems}
                  rowKey="id"
                  pagination={false}
                  size="small"
                />
              </div>
            )}

            {detail.logs && detail.logs.length > 0 && (
              <div style={{ marginTop: '16px' }}>
                <h4>订单日志</h4>
                <div>
                  {detail.logs.map((log, index) => (
                    <div key={index} style={{ marginBottom: '8px' }}>
                      <span style={{ color: '#666' }}>
                        {new Date(log.time).toLocaleString()}
                      </span>
                      <span style={{ marginLeft: '8px' }}>
                        {log.action} - {log.user}
                      </span>
                      {log.description && (
                        <span style={{ marginLeft: '8px', color: '#999' }}>
                          ({log.description})
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 发货弹窗 */}
      <Modal
        title="订单发货"
        open={shipModal}
        onOk={handleShip}
        onCancel={() => setShipModal(false)}
      >
        <Form form={shipForm} layout="vertical">
          <Form.Item
            name="deliveryCompany"
            label="物流公司"
            rules={[{ required: true, message: '请输入物流公司' }]}
          >
            <Select placeholder="请选择物流公司">
              <Option value="顺丰速运">顺丰速运</Option>
              <Option value="圆通速递">圆通速递</Option>
              <Option value="中通快递">中通快递</Option>
              <Option value="申通快递">申通快递</Option>
              <Option value="韵达快递">韵达快递</Option>
              <Option value="百世快递">百世快递</Option>
              <Option value="德邦快递">德邦快递</Option>
              <Option value="京东物流">京东物流</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="deliveryNo"
            label="快递单号"
            rules={[{ required: true, message: '请输入快递单号' }]}
          >
            <Input placeholder="请输入快递单号" />
          </Form.Item>
          <Form.Item name="remark" label="备注">
            <TextArea rows={3} placeholder="请输入备注信息" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 批量发货弹窗 */}
      <Modal
        title="批量发货"
        open={batchShipModal}
        onOk={handleBatchShip}
        onCancel={() => setBatchShipModal(false)}
      >
        <Form form={batchShipForm} layout="vertical">
          <Form.Item
            name="deliveryCompany"
            label="物流公司"
            rules={[{ required: true, message: '请输入物流公司' }]}
          >
            <Select placeholder="请选择物流公司">
              <Option value="顺丰速运">顺丰速运</Option>
              <Option value="圆通速递">圆通速递</Option>
              <Option value="中通快递">中通快递</Option>
              <Option value="申通快递">申通快递</Option>
              <Option value="韵达快递">韵达快递</Option>
              <Option value="百世快递">百世快递</Option>
              <Option value="德邦快递">德邦快递</Option>
              <Option value="京东物流">京东物流</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="deliveryNo"
            label="快递单号"
            rules={[{ required: true, message: '请输入快递单号' }]}
          >
            <Input placeholder="请输入快递单号" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 批量状态变更弹窗 */}
      <Modal
        title="批量状态变更"
        open={batchStatusModal}
        onOk={handleBatchStatus}
        onCancel={() => setBatchStatusModal(false)}
      >
        <Form form={batchStatusForm} layout="vertical">
          <Form.Item
            name="status"
            label="订单状态"
            rules={[{ required: true, message: '请选择订单状态' }]}
          >
            <Select placeholder="请选择订单状态">
              {Object.entries(statusMap).map(([key, value]) => (
                <Option key={key} value={parseInt(key)}>
                  {value.text}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="remark" label="备注">
            <TextArea rows={3} placeholder="请输入备注信息" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 新增订单弹窗 */}
      <Modal
        title="新增订单"
        open={addModal}
        onOk={handleAddOk}
        onCancel={() => setAddModal(false)}
        width={600}
      >
        <Form form={addForm} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="user_id"
                label="关联用户"
              >
                <Select
                  placeholder="请选择关联用户（可选）"
                  allowClear
                  showSearch
                  optionFilterProp="children"
                >
                  {users.map(user => (
                    <Option key={user.id} value={user.id}>
                      {user.nickname} ({user.phone})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="receiver_name"
                label="收货人"
                rules={[{ required: true, message: '请输入收货人' }]}
              >
                <Input placeholder="请输入收货人" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="receiver_phone"
                label="手机号"
                rules={[{ required: true, message: '请输入手机号' }]}
              >
                <Input placeholder="请输入手机号" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="pay_amount"
                label="实付金额"
                rules={[{ required: true, message: '请输入实付金额' }]}
              >
                <InputNumber
                  placeholder="请输入实付金额"
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="receiver_address"
            label="收货地址"
            rules={[{ required: true, message: '请输入收货地址' }]}
          >
            <TextArea rows={3} placeholder="请输入收货地址" />
          </Form.Item>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="freight_amount" label="运费">
                <InputNumber
                  placeholder="请输入运费"
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="discount_amount" label="优惠金额">
                <InputNumber
                  placeholder="请输入优惠金额"
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="pay_type" label="支付方式">
                <Select placeholder="请选择支付方式">
                  <Option value={1}>微信支付</Option>
                  <Option value={2}>支付宝</Option>
                  <Option value={3}>银行卡</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item name="remark" label="备注">
            <TextArea rows={3} placeholder="请输入备注信息" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default OrderList;
