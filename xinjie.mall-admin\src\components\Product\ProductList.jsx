import React, { useEffect, useState } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  Switch,
  Upload,
  message,
} from 'antd';
import {
  PlusOutlined,
  UploadOutlined,
  DownloadOutlined,
} from '@ant-design/icons';
import axios from 'axios';
import {
  fetchProductList,
  createProduct,
  updateProduct,
  deleteProduct,
} from '@/api/product';
import { fetchCategoryList } from '@/api/category';
import RichTextEditor from '@/components/common/RichTextEditor';

const { Option } = Select;

const ProductList = () => {
  const [data, setData] = useState([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [modalVisible, setModalVisible] = useState(false);
  const [editing, setEditing] = useState(null);
  const [form] = Form.useForm();
  const [uploading, setUploading] = useState(false);
  const [mainImage, setMainImage] = useState('');
  const [categories, setCategories] = useState([]);
  const [searchName, setSearchName] = useState('');
  const [searchCategory, setSearchCategory] = useState('');
  const [importModal, setImportModal] = useState(false);
  const [importing, setImporting] = useState(false);
  const [importResult, setImportResult] = useState(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const fetchCategories = async () => {
    try {
      const res = await fetchCategoryList({ page: 1, pageSize: 1000 });
      console.log('获取分类数据:', res);

      // 根据之前修复的经验，使用正确的数据路径
      if (res && res.success && res.data && Array.isArray(res.data.list)) {
        setCategories(res.data.list);
        console.log('分类数据加载成功:', res.data.list.length, '个分类');
      } else {
        console.error('分类数据格式异常:', res);
        setCategories([]);
      }
    } catch (error) {
      console.error('获取分类数据失败:', error);
      message.error('获取分类数据失败');
      setCategories([]);
    }
  };

  const fetchData = async (
    page = 1,
    pageSize = 10,
    name = '',
    category_id = ''
  ) => {
    setLoading(true);
    try {
      const res = await fetchProductList({ page, pageSize, name, category_id });
      if (res && res.success && res.data && Array.isArray(res.data.list)) {
        setData(res.data.list);
        setTotal(res.data.pagination?.total || 0);
      } else {
        setData([]);
        setTotal(0);
        message.error(res.message || '数据格式错误');
      }
    } catch (e) {
      message.error('获取数据失败');
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchCategories();
    fetchData(page, pageSize, searchName, searchCategory);
    // eslint-disable-next-line
  }, [page, pageSize]);

  const handleSearch = () => {
    setPage(1);
    fetchData(1, pageSize, searchName, searchCategory);
  };

  const handleAdd = () => {
    setEditing(null);
    setMainImage('');
    setModalVisible(true);
    form.resetFields();
  };

  const handleEdit = record => {
    setEditing(record);
    setMainImage(record.main_image);
    setModalVisible(true);
    form.setFieldsValue(record);
  };

  const handleDelete = async id => {
    Modal.confirm({
      title: '确认删除该商品？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        await deleteProduct(id);
        message.success('删除成功');
        fetchData(page, pageSize, searchName, searchCategory);
      },
    });
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要删除的商品');
      return;
    }
    Modal.confirm({
      title: `确认删除选中的${selectedRowKeys.length}个商品？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          for (const id of selectedRowKeys) {
            await deleteProduct(id);
          }
          message.success('批量删除成功');
          setSelectedRowKeys([]);
          fetchData(page, pageSize, searchName, searchCategory);
        } catch (error) {
          message.error('批量删除失败');
        }
      },
    });
  };

  // 批量下架
  const handleBatchOffline = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要下架的商品');
      return;
    }
    Modal.confirm({
      title: `确认下架选中的${selectedRowKeys.length}个商品？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          for (const id of selectedRowKeys) {
            const product = data.find(item => item.id === id);
            if (product) {
              await updateProduct(id, { ...product, status: 0 });
            }
          }
          message.success('批量下架成功');
          setSelectedRowKeys([]);
          fetchData(page, pageSize, searchName, searchCategory);
        } catch (error) {
          message.error('批量下架失败');
        }
      },
    });
  };

  // 状态切换处理函数
  const handleToggleStatus = async record => {
    try {
      const newStatus = record.status === 1 ? 0 : 1;
      await updateProduct(record.id, { ...record, status: newStatus });
      message.success(newStatus === 1 ? '商品已上架' : '商品已下架');
      fetchData(page, pageSize, searchName, searchCategory);
    } catch (error) {
      message.error('状态切换失败');
    }
  };

  // 全选/取消全选
  const handleSelectAll = () => {
    if (selectedRowKeys.length === data.length && data.length > 0) {
      setSelectedRowKeys([]);
    } else {
      setSelectedRowKeys(Array.isArray(data) ? data.map(item => item.id) : []);
    }
  };

  const handleOk = async () => {
    try {
      const values = await form.validateFields();

      // 数据处理和验证
      values.main_image = mainImage;
      if (!values.main_image) {
        message.error('请上传主图');
        return;
      }

      // 确保数据类型正确
      values.price = parseFloat(values.price) || 0;
      values.stock = parseInt(values.stock) || 0;
      values.category_id = parseInt(values.category_id) || 0;
      values.status = values.status ? 1 : 0; // 将boolean转换为数字

      // 验证必填字段
      if (!values.name || !values.category_id) {
        message.error('请填写完整的商品信息');
        return;
      }

      if (editing) {
        await updateProduct(editing.id, values);
        message.success('更新成功');
      } else {
        const result = await createProduct(values);
        message.success('添加成功');
      }
      setModalVisible(false);
      fetchData(page, pageSize, searchName, searchCategory);
    } catch (e) {
      console.error('提交失败:', e);
      message.error('提交失败：' + (e.message || '未知错误'));
    }
  };

  const uploadProps = {
    name: 'file',
    action: '/api/admin/product/upload',
    showUploadList: false,
    beforeUpload: () => {
      setUploading(true);
    },
    onChange(info) {
      if (info.file.status === 'done') {
        setMainImage(info.file.response.url);
        setUploading(false);
        message.success('上传成功');
      } else if (info.file.status === 'error') {
        setUploading(false);
        message.error('上传失败');
      }
    },
  };

  const columns = [
    {
      title: '主图',
      dataIndex: 'main_image',
      render: url =>
        url ? <img src={url} alt='' style={{ width: 60 }} /> : '-',
    },
    { title: '名称', dataIndex: 'name', width: 200 },
    { title: '分类', dataIndex: 'category_name', width: 120 },
    { title: '价格', dataIndex: 'price', width: 100 },
    { title: '库存', dataIndex: 'stock', width: 80 },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render: (status, record) => (
        <Switch
          checked={status === 1}
          onChange={() => handleToggleStatus(record)}
          checkedChildren='上架'
          unCheckedChildren='下架'
        />
      ),
    },
    {
      title: '操作',
      width: 180,
      render: (_, record) => (
        <>
          <Button type='link' onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Button type='link' danger onClick={() => handleDelete(record.id)}>
            删除
          </Button>
        </>
      ),
    },
  ];

  return (
    <>
      <div style={{ marginBottom: 16, display: 'flex', gap: 8 }}>
        <Input
          placeholder='商品名称'
          value={searchName}
          onChange={e => setSearchName(e.target.value)}
          style={{ width: 180 }}
        />
        <Select
          placeholder='选择分类'
          value={searchCategory}
          onChange={v => setSearchCategory(v)}
          allowClear
          style={{ width: 160 }}
        >
          {Array.isArray(categories) &&
            categories.map(c => (
              <Option key={c.id} value={c.id}>
                {c.name}
              </Option>
            ))}
        </Select>
        <Button type='primary' onClick={handleSearch}>
          搜索
        </Button>
        <Button
          danger
          onClick={handleBatchDelete}
          disabled={selectedRowKeys.length === 0}
          style={{ marginLeft: 'auto' }}
        >
          批量删除
        </Button>
        <Button
          onClick={handleBatchOffline}
          disabled={selectedRowKeys.length === 0}
          style={{ marginLeft: 8 }}
        >
          批量下架
        </Button>
        <Button onClick={handleSelectAll} style={{ marginLeft: 8 }}>
          {selectedRowKeys.length === data.length && data.length > 0
            ? '取消全选'
            : '全选'}
        </Button>
        <Button
          type='primary'
          icon={<PlusOutlined />}
          onClick={handleAdd}
          style={{ marginLeft: 8 }}
        >
          新增商品
        </Button>
        <Button onClick={() => setImportModal(true)} style={{ marginLeft: 8 }}>
          批量导入
        </Button>
      </div>
      <Table
        rowKey='id'
        columns={columns}
        dataSource={data}
        loading={loading}
        rowSelection={{
          selectedRowKeys,
          onChange: setSelectedRowKeys,
        }}
        pagination={{
          current: page,
          pageSize,
          total,
          onChange: (p, ps) => {
            setPage(p);
            setPageSize(ps);
          },
        }}
      />
      <Modal
        title={editing ? '编辑商品' : '新增商品'}
        open={modalVisible}
        footer={null}
        onCancel={() => setModalVisible(false)}
        destroyOnHidden
      >
        <Form form={form} layout='vertical'>
          <Form.Item
            label='商品名称'
            name='name'
            rules={[
              { required: true, message: '请输入商品名称' },
              { min: 2, message: '商品名称至少2个字符' },
              { max: 100, message: '商品名称不能超过100个字符' },
            ]}
          >
            <Input placeholder='请输入商品名称' showCount maxLength={100} />
          </Form.Item>
          <Form.Item
            label='分类'
            name='category_id'
            rules={[{ required: true, message: '请选择分类' }]}
          >
            <Select
              placeholder='请选择分类'
              allowClear
              showSearch
              filterOption={(input, option) =>
                option?.children?.toLowerCase().indexOf(input.toLowerCase()) >=
                0
              }
            >
              {Array.isArray(categories) &&
                categories.map(c => (
                  <Option key={c.id} value={c.id}>
                    {c.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
          <Form.Item
            label='价格'
            name='price'
            rules={[
              { required: true, message: '请输入价格' },
              { type: 'number', min: 0.01, message: '价格必须大于0' },
            ]}
          >
            <InputNumber
              min={0.01}
              step={0.01}
              precision={2}
              style={{ width: '100%' }}
              placeholder='请输入商品价格'
            />
          </Form.Item>
          <Form.Item
            label='库存'
            name='stock'
            rules={[
              { required: true, message: '请输入库存' },
              { type: 'number', min: 0, message: '库存不能为负数' },
            ]}
          >
            <InputNumber
              min={0}
              style={{ width: '100%' }}
              placeholder='请输入商品库存'
            />
          </Form.Item>
          <Form.Item label='主图' required>
            <Upload {...uploadProps}>
              <Button icon={<UploadOutlined />} loading={uploading}>
                上传主图
              </Button>
            </Upload>
            {mainImage && (
              <img
                src={mainImage}
                alt=''
                style={{ width: 120, marginTop: 8 }}
              />
            )}
          </Form.Item>
          <Form.Item
            label='描述'
            name='description'
            rules={[{ max: 2000, message: '描述不能超过2000个字符' }]}
          >
            <RichTextEditor placeholder='请输入商品描述（可选）' height={300} />
          </Form.Item>
          <Form.Item
            label='状态'
            name='status'
            valuePropName='checked'
            initialValue={true}
          >
            <Switch checkedChildren='上架' unCheckedChildren='下架' />
          </Form.Item>
          <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 8 }}>
            <Button onClick={() => setModalVisible(false)}>取消</Button>
            <Button type='primary' onClick={handleOk}>
              确定
            </Button>
          </div>
        </Form>
      </Modal>
      <Modal
        title='批量导入商品'
        open={importModal}
        onCancel={() => {
          setImportModal(false);
          setImportResult(null);
        }}
        footer={null}
        destroyOnHidden
      >
        <div
          style={{
            marginBottom: 12,
            display: 'flex',
            justifyContent: 'space-between',
          }}
        >
          <span>请下载模板，按要求填写后上传：</span>
          <Button
            icon={<DownloadOutlined />}
            size='small'
            onClick={() => window.open('/api/admin/product/template')}
          >
            模板下载
          </Button>
        </div>
        <Upload.Dragger
          name='file'
          accept='.xlsx,.xls,.csv'
          action='/api/admin/product/import'
          showUploadList={false}
          headers={{ Authorization: localStorage.getItem('token') }}
          beforeUpload={() => {
            setImporting(true);
            setImportResult(null);
          }}
          onChange={info => {
            if (info.file.status === 'done') {
              setImporting(false);
              const res = info.file.response;
              if (res.code === 0) {
                message.success(res.msg || '批量导入成功');
                setImportResult(res.results);
                fetchData(page, pageSize, searchName, searchCategory);
              } else {
                message.error(res.msg || '批量导入失败');
              }
            } else if (info.file.status === 'error') {
              setImporting(false);
              message.error('批量导入失败');
            }
          }}
          disabled={importing}
        >
          <p className='ant-upload-drag-icon'>
            <UploadOutlined />
          </p>
          <p className='ant-upload-text'>
            点击或拖拽上传Excel文件（.xlsx/.xls/.csv）
          </p>
        </Upload.Dragger>
        {importResult && Array.isArray(importResult) && (
          <div style={{ marginTop: 16, maxHeight: 200, overflow: 'auto' }}>
            <b>导入结果：</b>
            <ul>
              {importResult.map((r, idx) => (
                <li key={idx} style={{ color: r.success ? 'green' : 'red' }}>
                  第{r.row}行：{r.success ? '成功' : `失败（${r.reason}）`}
                </li>
              ))}
            </ul>
          </div>
        )}
      </Modal>
    </>
  );
};

export default ProductList;
