import React from 'react';
import { Card, Space, Button } from 'antd';
import {
  PlusOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  BarChartOutlined,
  SettingOutlined,
  FileTextOutlined,
  GiftOutlined,
  BankOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const QuickActions = () => {
  const navigate = useNavigate();

  const quickActions = [
    {
      key: 'add-product',
      title: '添加商品',
      icon: <PlusOutlined />,
      color: '#1890ff',
      gradient: 'linear-gradient(135deg, #1890ff 0%, #36cfc9 100%)',
      path: '/product/add'
    },
    {
      key: 'orders',
      title: '订单管理',
      icon: <ShoppingCartOutlined />,
      color: '#52c41a',
      gradient: 'linear-gradient(135deg, #52c41a 0%, #73d13d 100%)',
      path: '/order'
    },
    {
      key: 'users',
      title: '用户管理',
      icon: <UserOutlined />,
      color: '#fa8c16',
      gradient: 'linear-gradient(135deg, #fa8c16 0%, #ffc53d 100%)',
      path: '/user'
    },
    {
      key: 'statistics',
      title: '数据统计',
      icon: <BarChartOutlined />,
      color: '#eb2f96',
      gradient: 'linear-gradient(135deg, #eb2f96 0%, #f759ab 100%)',
      path: '/statistics'
    },
    {
      key: 'categories',
      title: '分类管理',
      icon: <FileTextOutlined />,
      color: '#722ed1',
      gradient: 'linear-gradient(135deg, #722ed1 0%, #b37feb 100%)',
      path: '/category'
    },
    {
      key: 'promotions',
      title: '优惠活动',
      icon: <GiftOutlined />,
      color: '#13c2c2',
      gradient: 'linear-gradient(135deg, #13c2c2 0%, #36cfc9 100%)',
      path: '/promotion'
    },
    {
      key: 'finance',
      title: '财务管理',
      icon: <BankOutlined />,
      color: '#f5222d',
      gradient: 'linear-gradient(135deg, #f5222d 0%, #ff7875 100%)',
      path: '/finance'
    },
    {
      key: 'settings',
      title: '系统设置',
      icon: <SettingOutlined />,
      color: '#666666',
      gradient: 'linear-gradient(135deg, #666666 0%, #8c8c8c 100%)',
      path: '/settings'
    }
  ];

  const handleActionClick = (path) => {
    navigate(path);
  };

  return (
    <Card
      title="快捷操作"
      style={{
        borderRadius: '12px',
        border: 'none',
        boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
      }}
    >
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', 
        gap: '16px' 
      }}>
        {quickActions.map((action) => (
          <Button
            key={action.key}
            type="text"
            onClick={() => handleActionClick(action.path)}
            style={{
              height: '80px',
              borderRadius: '12px',
              background: action.gradient,
              border: 'none',
              color: 'white',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '8px',
              fontSize: '13px',
              fontWeight: '500',
              transition: 'all 0.3s ease',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
            }}
            className="quick-action-btn"
          >
            <div style={{ fontSize: '20px' }}>
              {action.icon}
            </div>
            <span>{action.title}</span>
          </Button>
        ))}
      </div>
      
      <style>{`
        .quick-action-btn:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(0,0,0,0.15) !important;
        }
      `}</style>
    </Card>
  );
};

export default QuickActions;
