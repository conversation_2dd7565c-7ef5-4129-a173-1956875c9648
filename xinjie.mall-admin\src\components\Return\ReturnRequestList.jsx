import React, { useEffect, useState } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Tag,
  message,
  Row,
  Col,
  Statistic,
  Card,
  DatePicker,
  Space,
  Tooltip,
  Popconfirm,
  Image,
  Descriptions,
  Steps,
  Timeline,
  InputNumber,
  Radio,
} from 'antd';
import {
  EyeOutlined,
  CheckOutlined,
  CloseOutlined,
  ReloadOutlined,
  DownloadOutlined,
  ExclamationCircleOutlined,
  DollarOutlined,
  TruckOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import {
  fetchReturnRequestList,
  fetchReturnRequestDetail,
  approveReturnRequest,
  confirmReceive,
  inspectGoods,
  processRefund,
  fetchReturnStatistics,
  batchProcessReturns,
  exportReturnData,
} from '@/api/returnRequest';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;
const { Step } = Steps;

// 退货状态映射
const statusMap = {
  0: { text: '待审核', color: 'orange' },
  1: { text: '审核通过', color: 'blue' },
  2: { text: '审核拒绝', color: 'red' },
  3: { text: '待寄回', color: 'cyan' },
  4: { text: '已寄回', color: 'purple' },
  5: { text: '验收中', color: 'geekblue' },
  6: { text: '验收通过', color: 'green' },
  7: { text: '验收不通过', color: 'volcano' },
  8: { text: '退款完成', color: 'lime' },
  9: { text: '已取消', color: 'default' },
};

// 退货类型映射
const returnTypeMap = {
  1: '仅退款',
  2: '退货退款',
  3: '换货',
};

const ReturnRequestList = () => {
  const [data, setData] = useState([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchReturnNo, setSearchReturnNo] = useState('');
  const [searchOrderNo, setSearchOrderNo] = useState('');
  const [searchStatus, setSearchStatus] = useState('');
  const [searchDateRange, setSearchDateRange] = useState(null);
  const [detailModal, setDetailModal] = useState(false);
  const [approveModal, setApproveModal] = useState(false);
  const [receiveModal, setReceiveModal] = useState(false);
  const [inspectModal, setInspectModal] = useState(false);
  const [refundModal, setRefundModal] = useState(false);
  const [currentReturn, setCurrentReturn] = useState(null);
  const [detail, setDetail] = useState(null);
  const [approveForm] = Form.useForm();
  const [receiveForm] = Form.useForm();
  const [inspectForm] = Form.useForm();
  const [refundForm] = Form.useForm();
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [stats, setStats] = useState({ statusStats: [], totalStats: {} });

  // 获取数据
  const fetchData = async (page = 1, pageSize = 10) => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: pageSize,
        returnNo: searchReturnNo,
        orderNo: searchOrderNo,
        status: searchStatus,
      };

      // 添加时间范围参数
      if (searchDateRange && searchDateRange.length === 2) {
        params.startDate = searchDateRange[0].format('YYYY-MM-DD');
        params.endDate = searchDateRange[1].format('YYYY-MM-DD');
      }

      const res = await fetchReturnRequestList(params);
      if (res.code === 200) {
        setData(res.data.list || []);
        setTotal(res.data.total || 0);
        
        // 设置统计数据
        if (res.data.statusStats) {
          setStats(prev => ({ ...prev, statusStats: res.data.statusStats }));
        }
      }
    } catch (error) {
      console.error('获取退货申请列表失败:', error);
      message.error('获取退货申请列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取统计数据
  const fetchStats = async () => {
    try {
      const res = await fetchReturnStatistics();
      if (res.code === 200) {
        setStats(res.data);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  };

  useEffect(() => {
    fetchData(page, pageSize);
    fetchStats();
  }, [page, pageSize]);

  // 搜索
  const handleSearch = () => {
    setPage(1);
    fetchData(1, pageSize);
  };

  // 重置搜索
  const handleReset = () => {
    setSearchReturnNo('');
    setSearchOrderNo('');
    setSearchStatus('');
    setSearchDateRange(null);
    setPage(1);
    fetchData(1, pageSize);
  };

  // 查看详情
  const handleViewDetail = async (record) => {
    try {
      const res = await fetchReturnRequestDetail(record.id);
      if (res.code === 200) {
        setDetail(res.data);
        setCurrentReturn(record);
        setDetailModal(true);
      }
    } catch (error) {
      message.error('获取详情失败');
    }
  };

  // 审核退货申请
  const handleApprove = (record) => {
    setCurrentReturn(record);
    setApproveModal(true);
    approveForm.resetFields();
  };

  // 提交审核
  const handleApproveSubmit = async () => {
    try {
      const values = await approveForm.validateFields();
      const res = await approveReturnRequest(currentReturn.id, values);
      if (res.code === 200) {
        message.success(values.approved ? '审核通过' : '审核拒绝');
        setApproveModal(false);
        fetchData(page, pageSize);
      }
    } catch (error) {
      message.error('审核失败');
    }
  };

  // 确认收货
  const handleReceive = (record) => {
    setCurrentReturn(record);
    setReceiveModal(true);
    receiveForm.resetFields();
  };

  // 提交确认收货
  const handleReceiveSubmit = async () => {
    try {
      const values = await receiveForm.validateFields();
      const res = await confirmReceive(currentReturn.id, values);
      if (res.code === 200) {
        message.success('确认收货成功');
        setReceiveModal(false);
        fetchData(page, pageSize);
      }
    } catch (error) {
      message.error('确认收货失败');
    }
  };

  // 验收商品
  const handleInspect = (record) => {
    setCurrentReturn(record);
    setInspectModal(true);
    inspectForm.resetFields();
  };

  // 提交验收
  const handleInspectSubmit = async () => {
    try {
      const values = await inspectForm.validateFields();
      const res = await inspectGoods(currentReturn.id, values);
      if (res.code === 200) {
        message.success(values.inspectResult ? '验收通过' : '验收不通过');
        setInspectModal(false);
        fetchData(page, pageSize);
      }
    } catch (error) {
      message.error('验收失败');
    }
  };

  // 处理退款
  const handleRefund = (record) => {
    setCurrentReturn(record);
    setRefundModal(true);
    refundForm.setFieldsValue({
      refundAmount: record.return_amount,
      refundType: 2, // 默认余额退款
    });
  };

  // 提交退款
  const handleRefundSubmit = async () => {
    try {
      const values = await refundForm.validateFields();
      const res = await processRefund(currentReturn.id, values);
      if (res.code === 200) {
        message.success('退款处理成功');
        setRefundModal(false);
        fetchData(page, pageSize);
      }
    } catch (error) {
      message.error('退款处理失败');
    }
  };

  // 导出数据
  const handleExport = async () => {
    try {
      const params = {
        status: searchStatus,
        returnNo: searchReturnNo,
        orderNo: searchOrderNo,
      };

      if (searchDateRange && searchDateRange.length === 2) {
        params.startDate = searchDateRange[0].format('YYYY-MM-DD');
        params.endDate = searchDateRange[1].format('YYYY-MM-DD');
      }

      const res = await exportReturnData(params);
      if (res.code === 200) {
        message.success('导出成功');
        // 这里可以处理文件下载
      }
    } catch (error) {
      message.error('导出失败');
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '退货单号',
      dataIndex: 'return_no',
      key: 'return_no',
      width: 150,
      render: (text) => (
        <span style={{ fontFamily: 'monospace', fontSize: '12px' }}>{text}</span>
      ),
    },
    {
      title: '订单号',
      dataIndex: 'order_no',
      key: 'order_no',
      width: 150,
      render: (text) => (
        <span style={{ fontFamily: 'monospace', fontSize: '12px' }}>{text}</span>
      ),
    },
    {
      title: '用户信息',
      key: 'user',
      width: 120,
      render: (_, record) => (
        <div>
          <div>{record.user?.nickname || record.user?.username || '-'}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.user?.phone || '-'}
          </div>
        </div>
      ),
    },
    {
      title: '退货类型',
      dataIndex: 'return_type',
      key: 'return_type',
      width: 100,
      render: (type) => returnTypeMap[type] || '-',
    },
    {
      title: '退货原因',
      dataIndex: 'return_reason',
      key: 'return_reason',
      width: 150,
      ellipsis: {
        showTitle: false,
      },
      render: (text) => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '退货金额',
      dataIndex: 'return_amount',
      key: 'return_amount',
      width: 100,
      render: (amount) => (
        <span style={{ color: '#f50', fontWeight: 'bold' }}>
          ¥{parseFloat(amount).toFixed(2)}
        </span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={statusMap[status]?.color}>
          {statusMap[status]?.text}
        </Tag>
      ),
    },
    {
      title: '申请时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (time) => new Date(time).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>

          {record.status === 0 && (
            <Tooltip title="审核">
              <Button
                type="link"
                size="small"
                icon={<CheckOutlined />}
                onClick={() => handleApprove(record)}
              />
            </Tooltip>
          )}

          {record.status === 4 && (
            <Tooltip title="确认收货">
              <Button
                type="link"
                size="small"
                icon={<TruckOutlined />}
                onClick={() => handleReceive(record)}
              />
            </Tooltip>
          )}

          {record.status === 5 && (
            <Tooltip title="验收商品">
              <Button
                type="link"
                size="small"
                icon={<SearchOutlined />}
                onClick={() => handleInspect(record)}
              />
            </Tooltip>
          )}

          {record.status === 6 && (
            <Tooltip title="处理退款">
              <Button
                type="link"
                size="small"
                icon={<DollarOutlined />}
                onClick={() => handleRefund(record)}
              />
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record) => ({
      disabled: ![0, 1].includes(record.status), // 只有待审核和审核通过的可以批量操作
    }),
  };

  return (
    <div>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总申请数"
              value={stats.totalStats?.total_count || 0}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待审核"
              value={stats.statusStats?.find(s => s.status === 0)?.count || 0}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="处理中"
              value={stats.statusStats?.filter(s => [1,3,4,5,6].includes(s.status))
                .reduce((sum, s) => sum + parseInt(s.count), 0) || 0}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已完成"
              value={stats.statusStats?.find(s => s.status === 8)?.count || 0}
              valueStyle={{ color: '#13c2c2' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索表单 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Input
              placeholder="退货单号"
              value={searchReturnNo}
              onChange={(e) => setSearchReturnNo(e.target.value)}
              onPressEnter={handleSearch}
            />
          </Col>
          <Col span={6}>
            <Input
              placeholder="订单号"
              value={searchOrderNo}
              onChange={(e) => setSearchOrderNo(e.target.value)}
              onPressEnter={handleSearch}
            />
          </Col>
          <Col span={6}>
            <Select
              placeholder="退货状态"
              value={searchStatus}
              onChange={setSearchStatus}
              allowClear
              style={{ width: '100%' }}
            >
              {Object.entries(statusMap).map(([key, value]) => (
                <Option key={key} value={key}>
                  {value.text}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={6}>
            <RangePicker
              value={searchDateRange}
              onChange={setSearchDateRange}
              style={{ width: '100%' }}
            />
          </Col>
        </Row>
        <Row style={{ marginTop: 16 }}>
          <Col>
            <Space>
              <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                搜索
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReset}>
                重置
              </Button>
              <Button icon={<DownloadOutlined />} onClick={handleExport}>
                导出
              </Button>
              {selectedRowKeys.length > 0 && (
                <>
                  <Button
                    type="primary"
                    onClick={() => {
                      Modal.confirm({
                        title: '批量审核通过',
                        content: `确定要批量审核通过选中的 ${selectedRowKeys.length} 个退货申请吗？`,
                        onOk: async () => {
                          try {
                            const res = await batchProcessReturns({
                              returnIds: selectedRowKeys,
                              action: 'approve',
                              data: { remark: '批量审核通过' }
                            });
                            if (res.code === 200) {
                              message.success('批量审核成功');
                              setSelectedRowKeys([]);
                              fetchData(page, pageSize);
                            }
                          } catch (error) {
                            message.error('批量审核失败');
                          }
                        }
                      });
                    }}
                  >
                    批量通过
                  </Button>
                  <Button
                    danger
                    onClick={() => {
                      Modal.confirm({
                        title: '批量审核拒绝',
                        content: `确定要批量审核拒绝选中的 ${selectedRowKeys.length} 个退货申请吗？`,
                        onOk: async () => {
                          try {
                            const res = await batchProcessReturns({
                              returnIds: selectedRowKeys,
                              action: 'reject',
                              data: {
                                refuseReason: '批量拒绝',
                                remark: '批量审核拒绝'
                              }
                            });
                            if (res.code === 200) {
                              message.success('批量拒绝成功');
                              setSelectedRowKeys([]);
                              fetchData(page, pageSize);
                            }
                          } catch (error) {
                            message.error('批量拒绝失败');
                          }
                        }
                      });
                    }}
                  >
                    批量拒绝
                  </Button>
                </>
              )}
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 数据表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={data}
          rowKey="id"
          loading={loading}
          rowSelection={rowSelection}
          scroll={{ x: 1200 }}
          pagination={{
            current: page,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => {
              setPage(page);
              setPageSize(pageSize);
            },
          }}
        />
      </Card>

      {/* 详情模态框 */}
      <Modal
        title="退货申请详情"
        open={detailModal}
        onCancel={() => setDetailModal(false)}
        footer={null}
        width={800}
      >
        {detail && (
          <div>
            <Descriptions title="基本信息" bordered column={2}>
              <Descriptions.Item label="退货单号">{detail.return_no}</Descriptions.Item>
              <Descriptions.Item label="订单号">{detail.order_no}</Descriptions.Item>
              <Descriptions.Item label="用户">
                {detail.user?.nickname || detail.user?.username}
              </Descriptions.Item>
              <Descriptions.Item label="联系电话">{detail.contact_phone}</Descriptions.Item>
              <Descriptions.Item label="退货类型">
                {returnTypeMap[detail.return_type]}
              </Descriptions.Item>
              <Descriptions.Item label="退货金额">
                <span style={{ color: '#f50', fontWeight: 'bold' }}>
                  ¥{parseFloat(detail.return_amount).toFixed(2)}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="退货原因" span={2}>
                {detail.return_reason}
              </Descriptions.Item>
              {detail.return_description && (
                <Descriptions.Item label="详细说明" span={2}>
                  {detail.return_description}
                </Descriptions.Item>
              )}
              <Descriptions.Item label="申请时间" span={2}>
                {new Date(detail.created_at).toLocaleString()}
              </Descriptions.Item>
            </Descriptions>

            {/* 退货商品 */}
            {detail.returnItems && detail.returnItems.length > 0 && (
              <div style={{ marginTop: 16 }}>
                <h4>退货商品</h4>
                {detail.returnItems.map((item, index) => (
                  <Card key={index} size="small" style={{ marginBottom: 8 }}>
                    <Row gutter={16} align="middle">
                      <Col span={4}>
                        <Image
                          width={60}
                          height={60}
                          src={item.product_image}
                          fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                        />
                      </Col>
                      <Col span={12}>
                        <div>{item.product_name}</div>
                        <div style={{ color: '#666', fontSize: '12px' }}>
                          单价: ¥{parseFloat(item.product_price).toFixed(2)}
                        </div>
                      </Col>
                      <Col span={4}>
                        <div>退货数量: {item.return_quantity}</div>
                      </Col>
                      <Col span={4}>
                        <div style={{ color: '#f50', fontWeight: 'bold' }}>
                          ¥{parseFloat(item.return_amount).toFixed(2)}
                        </div>
                      </Col>
                    </Row>
                  </Card>
                ))}
              </div>
            )}

            {/* 退货凭证图片 */}
            {detail.return_images && detail.return_images.length > 0 && (
              <div style={{ marginTop: 16 }}>
                <h4>退货凭证</h4>
                <Image.PreviewGroup>
                  {detail.return_images.map((img, index) => (
                    <Image
                      key={index}
                      width={100}
                      height={100}
                      src={img}
                      style={{ marginRight: 8, marginBottom: 8 }}
                    />
                  ))}
                </Image.PreviewGroup>
              </div>
            )}

            {/* 状态时间线 */}
            {detail.statusLogs && detail.statusLogs.length > 0 && (
              <div style={{ marginTop: 16 }}>
                <h4>处理记录</h4>
                <Timeline>
                  {detail.statusLogs.map((log, index) => (
                    <Timeline.Item key={index}>
                      <div>
                        <strong>{log.status_text}</strong>
                        <span style={{ marginLeft: 8, color: '#666' }}>
                          {log.operator_name} - {new Date(log.created_at).toLocaleString()}
                        </span>
                      </div>
                      {log.remark && (
                        <div style={{ color: '#666', fontSize: '12px', marginTop: 4 }}>
                          {log.remark}
                        </div>
                      )}
                    </Timeline.Item>
                  ))}
                </Timeline>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 审核模态框 */}
      <Modal
        title="审核退货申请"
        open={approveModal}
        onOk={handleApproveSubmit}
        onCancel={() => setApproveModal(false)}
        okText="提交"
        cancelText="取消"
      >
        <Form form={approveForm} layout="vertical">
          <Form.Item
            name="approved"
            label="审核结果"
            rules={[{ required: true, message: '请选择审核结果' }]}
          >
            <Radio.Group>
              <Radio value={true}>审核通过</Radio>
              <Radio value={false}>审核拒绝</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.approved !== currentValues.approved
            }
          >
            {({ getFieldValue }) =>
              getFieldValue('approved') === false && (
                <Form.Item
                  name="refuseReason"
                  label="拒绝原因"
                  rules={[{ required: true, message: '请输入拒绝原因' }]}
                >
                  <TextArea rows={3} placeholder="请输入拒绝原因" />
                </Form.Item>
              )
            }
          </Form.Item>

          <Form.Item name="adminRemark" label="备注">
            <TextArea rows={3} placeholder="请输入备注信息" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 确认收货模态框 */}
      <Modal
        title="确认收货"
        open={receiveModal}
        onOk={handleReceiveSubmit}
        onCancel={() => setReceiveModal(false)}
        okText="确认收货"
        cancelText="取消"
      >
        <Form form={receiveForm} layout="vertical">
          <Form.Item name="remark" label="备注">
            <TextArea rows={3} placeholder="请输入收货备注" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 验收模态框 */}
      <Modal
        title="验收商品"
        open={inspectModal}
        onOk={handleInspectSubmit}
        onCancel={() => setInspectModal(false)}
        okText="提交验收结果"
        cancelText="取消"
      >
        <Form form={inspectForm} layout="vertical">
          <Form.Item
            name="inspectResult"
            label="验收结果"
            rules={[{ required: true, message: '请选择验收结果' }]}
          >
            <Radio.Group>
              <Radio value={true}>验收通过</Radio>
              <Radio value={false}>验收不通过</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item name="inspectRemark" label="验收备注">
            <TextArea rows={4} placeholder="请输入验收备注" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 退款模态框 */}
      <Modal
        title="处理退款"
        open={refundModal}
        onOk={handleRefundSubmit}
        onCancel={() => setRefundModal(false)}
        okText="确认退款"
        cancelText="取消"
      >
        <Form form={refundForm} layout="vertical">
          <Form.Item
            name="refundAmount"
            label="退款金额"
            rules={[{ required: true, message: '请输入退款金额' }]}
          >
            <InputNumber
              min={0}
              precision={2}
              style={{ width: '100%' }}
              formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value.replace(/¥\s?|(,*)/g, '')}
            />
          </Form.Item>

          <Form.Item
            name="refundType"
            label="退款方式"
            rules={[{ required: true, message: '请选择退款方式' }]}
          >
            <Select>
              <Option value={1}>原路退回</Option>
              <Option value={2}>余额退款</Option>
              <Option value={3}>线下退款</Option>
            </Select>
          </Form.Item>

          <Form.Item name="remark" label="退款备注">
            <TextArea rows={3} placeholder="请输入退款备注" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ReturnRequestList;
