import React, { useState, useEffect } from 'react';
import { Card, Select, Row, Col, Statistic, Spin } from 'antd';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts';
import {
  RiseOutlined,
  DollarOutlined,
  ShoppingCartOutlined
} from '@ant-design/icons';
import DashboardService from '../services/dashboardService';

const { Option } = Select;

const SalesChart = () => {
  const [loading, setLoading] = useState(true);
  const [chartData, setChartData] = useState([]);
  const [period, setPeriod] = useState('7');
  const [totalSales, setTotalSales] = useState(0);
  const [totalOrders, setTotalOrders] = useState(0);
  const [avgOrderValue, setAvgOrderValue] = useState(0);

  useEffect(() => {
    loadChartData();
  }, [period]);

  const loadChartData = async () => {
    setLoading(true);
    try {
      const response = await DashboardService.getSalesTrend(parseInt(period));
      console.log('销售趋势数据:', response);

      // 使用真实数据
      if (response && response.data && response.data.trend) {
        const realData = response.data.trend;
        setChartData(realData);
        setTotalSales(response.data.summary?.totalSales || 0);
        setTotalOrders(response.data.summary?.totalOrders || 0);
        setAvgOrderValue(response.data.summary?.avgOrderValue || 0);
      } else if (Array.isArray(response)) {
        // 如果直接返回数组
        setChartData(response);
        const sales = response.reduce((sum, item) => sum + (item.sales || 0), 0);
        const orders = response.reduce((sum, item) => sum + (item.orders || 0), 0);
        setTotalSales(sales);
        setTotalOrders(orders);
        setAvgOrderValue(orders > 0 ? sales / orders : 0);
      } else {
        // 没有数据时显示空图表
        setChartData([]);
        setTotalSales(0);
        setTotalOrders(0);
        setAvgOrderValue(0);
      }

    } catch (error) {
      console.error('加载销售趋势失败:', error);
      // 出错时显示空数据
      setChartData([]);
      setTotalSales(0);
      setTotalOrders(0);
      setAvgOrderValue(0);
    } finally {
      setLoading(false);
    }
  };

  const generateMockData = (days) => {
    const data = [];
    const today = new Date();
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      
      const baseOrders = Math.floor(Math.random() * 50) + 20;
      const baseSales = baseOrders * (Math.random() * 200 + 100);
      
      data.push({
        date: date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }),
        fullDate: date.toISOString().split('T')[0],
        sales: Math.round(baseSales),
        orders: baseOrders,
        avgOrderValue: Math.round(baseSales / baseOrders)
      });
    }
    
    return data;
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="custom-tooltip">
          <p style={{ margin: 0, marginBottom: '4px', fontWeight: 'bold' }}>{label}</p>
          <p style={{ margin: 0, color: '#1890ff' }}>
            销售额: ¥{payload[0]?.value?.toLocaleString()}
          </p>
          <p style={{ margin: 0, color: '#52c41a' }}>
            订单数: {payload[1]?.value}单
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <Card
      title="销售趋势分析"
      extra={
        <Select
          value={period}
          onChange={setPeriod}
          style={{ width: 120 }}
        >
          <Option value="7">近7天</Option>
          <Option value="15">近15天</Option>
          <Option value="30">近30天</Option>
        </Select>
      }
      style={{
        borderRadius: '12px',
        border: 'none',
        boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
      }}
    >
      {loading ? (
        <div className="loading-container">
          <Spin size="large" />
        </div>
      ) : (
        <>
          {/* 统计概览 */}
          <Row gutter={24} style={{ marginBottom: '24px' }}>
            <Col span={8}>
              <Statistic
                title="总销售额"
                value={totalSales}
                precision={0}
                valueStyle={{ color: '#1890ff' }}
                prefix={<DollarOutlined />}
                suffix="元"
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="总订单数"
                value={totalOrders}
                valueStyle={{ color: '#52c41a' }}
                prefix={<ShoppingCartOutlined />}
                suffix="单"
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="平均客单价"
                value={avgOrderValue}
                precision={0}
                valueStyle={{ color: '#fa8c16' }}
                prefix={<RiseOutlined />}
                suffix="元"
              />
            </Col>
          </Row>

          {/* 图表区域 */}
          <div className="chart-container" style={{ height: '300px', padding: 0 }}>
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={chartData}>
                <defs>
                  <linearGradient id="salesGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#1890ff" stopOpacity={0.3}/>
                    <stop offset="95%" stopColor="#1890ff" stopOpacity={0}/>
                  </linearGradient>
                  <linearGradient id="ordersGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#52c41a" stopOpacity={0.3}/>
                    <stop offset="95%" stopColor="#52c41a" stopOpacity={0}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis 
                  dataKey="date" 
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#666' }}
                />
                <YAxis 
                  yAxisId="sales"
                  orientation="left"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#666' }}
                />
                <YAxis 
                  yAxisId="orders"
                  orientation="right"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#666' }}
                />
                <Tooltip content={<CustomTooltip />} />
                <Area
                  yAxisId="sales"
                  type="monotone"
                  dataKey="sales"
                  stroke="#1890ff"
                  strokeWidth={2}
                  fill="url(#salesGradient)"
                />
                <Line
                  yAxisId="orders"
                  type="monotone"
                  dataKey="orders"
                  stroke="#52c41a"
                  strokeWidth={2}
                  dot={{ fill: '#52c41a', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: '#52c41a', strokeWidth: 2 }}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>

          {/* 数据表格 */}
          <div style={{ marginTop: '16px', fontSize: '12px', color: '#666' }}>
            <p style={{ margin: 0 }}>
              * 蓝色区域表示销售额趋势，绿色线条表示订单数量变化
            </p>
          </div>
        </>
      )}
    </Card>
  );
};

export default SalesChart;
