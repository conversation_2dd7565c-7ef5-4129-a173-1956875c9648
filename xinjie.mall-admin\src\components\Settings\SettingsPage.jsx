import React, { useEffect, useState } from 'react';
import { Tabs, Form, Input, Button, message, Upload, Spin } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import axios from 'axios';
import request from '../../utils/request';

const { TabPane } = Tabs;

const SettingsPage = () => {
  const [loading, setLoading] = useState(false);
  const [basicForm] = Form.useForm();
  const [paymentForm] = Form.useForm();
  const [shippingForm] = Form.useForm();
  const [smsForm] = Form.useForm();
  const [emailForm] = Form.useForm();
  const [logoUrl, setLogoUrl] = useState('');

  // 默认设置数据
  const defaultSettings = {
    basic: {
      siteName: '心洁茗茶管理系统',
      siteDescription: '专业的茗茶销售管理平台',
      logo: '',
      contactPhone: '************',
      contactEmail: '<EMAIL>',
      address: '福建省福州市茗茶批发市场'
    },
    payment: {
      alipayEnabled: true,
      wechatEnabled: true,
      alipayAppId: '',
      alipayPrivateKey: '',
      wechatAppId: '',
      wechatMchId: '',
      wechatApiKey: ''
    },
    shipping: {
      freeShippingAmount: 99,
      defaultShippingFee: 10,
      expressCompanies: ['顺丰速运', '圆通快递', '中通快递', '申通快递', '韵达快递']
    },
    sms: {
      provider: 'aliyun',
      accessKeyId: '',
      accessKeySecret: '',
      signName: '心洁茗茶',
      templateCode: ''
    },
    email: {
      smtpHost: 'smtp.qq.com',
      smtpPort: 587,
      smtpUser: '',
      smtpPassword: '',
      fromName: '心洁茗茶'
    }
  };

  // 获取各项设置
  const fetchSettings = async () => {
    setLoading(true);
    try {
      // 尝试获取设置，如果失败则使用默认值
      const settingsPromises = [
        request.get('/admin/settings/basic').catch(() => ({ data: defaultSettings.basic })),
        request.get('/admin/settings/payment').catch(() => ({ data: defaultSettings.payment })),
        request.get('/admin/settings/shipping').catch(() => ({ data: defaultSettings.shipping })),
        request.get('/admin/settings/sms').catch(() => ({ data: defaultSettings.sms })),
        request.get('/admin/settings/email').catch(() => ({ data: defaultSettings.email }))
      ];

      const [basic, payment, shipping, sms, email] = await Promise.all(settingsPromises);

      basicForm.setFieldsValue(basic.data);
      setLogoUrl(basic.data.logo || '');
      paymentForm.setFieldsValue(payment.data);
      shippingForm.setFieldsValue(shipping.data);
      smsForm.setFieldsValue(sms.data);
      emailForm.setFieldsValue(email.data);
    } catch (error) {
      console.error('获取设置失败:', error);
      message.warning('部分设置加载失败，已使用默认配置');

      // 使用默认设置
      basicForm.setFieldsValue(defaultSettings.basic);
      paymentForm.setFieldsValue(defaultSettings.payment);
      shippingForm.setFieldsValue(defaultSettings.shipping);
      smsForm.setFieldsValue(defaultSettings.sms);
      emailForm.setFieldsValue(defaultSettings.email);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchSettings();
    // eslint-disable-next-line
  }, []);

  const handleSave = async type => {
    let form, url;
    switch (type) {
      case 'basic':
        form = basicForm;
        url = '/admin/settings/basic';
        break;
      case 'payment':
        form = paymentForm;
        url = '/admin/settings/payment';
        break;
      case 'shipping':
        form = shippingForm;
        url = '/admin/settings/shipping';
        break;
      case 'sms':
        form = smsForm;
        url = '/admin/settings/sms';
        break;
      case 'email':
        form = emailForm;
        url = '/admin/settings/email';
        break;
      default:
        return;
    }

    try {
      const values = await form.validateFields();
      if (type === 'basic') values.logo = logoUrl;

      // 尝试保存设置
      await request.put(url, values);
      message.success('保存成功');
    } catch (error) {
      console.error('保存设置失败:', error);
      if (error.response && error.response.status === 500) {
        message.warning('后端接口暂未实现，设置已保存到本地');
        // 可以在这里添加本地存储逻辑
        localStorage.setItem(`settings_${type}`, JSON.stringify(values));
      } else if (error.name === 'ValidationError') {
        message.error('请检查表单填写是否正确');
      } else {
        message.error('保存失败，请稍后重试');
      }
    }
  };

  const uploadProps = {
    name: 'file',
    action: '/api/admin/banner/upload', // 保持上传接口路径
    showUploadList: false,
    onChange(info) {
      if (info.file.status === 'done') {
        setLogoUrl(info.file.response.url);
        message.success('上传成功');
      } else if (info.file.status === 'error') {
        message.error('上传失败');
      }
    },
  };

  return (
    <Spin spinning={loading}>
      <Tabs defaultActiveKey='basic'>
        <TabPane tab='基本设置' key='basic'>
          <Form form={basicForm} layout='vertical' style={{ maxWidth: 500 }}>
            <Form.Item
              label='商城名称'
              name='name'
              rules={[{ required: true, message: '请输入商城名称' }]}
            >
              <Input />
            </Form.Item>
            <Form.Item label='Logo'>
              <Upload {...uploadProps}>
                <Button icon={<UploadOutlined />}>上传Logo</Button>
              </Upload>
              {logoUrl && (
                <img
                  src={logoUrl}
                  alt='logo'
                  style={{ width: 100, marginTop: 8 }}
                />
              )}
            </Form.Item>
            <Form.Item label='客服信息' name='service'>
              <Input />
            </Form.Item>
            <Button type='primary' onClick={() => handleSave('basic')}>
              保存
            </Button>
          </Form>
        </TabPane>
        <TabPane tab='支付设置' key='payment'>
          <Form form={paymentForm} layout='vertical' style={{ maxWidth: 500 }}>
            <Form.Item label='支付AppID' name='appid'>
              <Input />
            </Form.Item>
            <Form.Item label='支付商户号' name='mchid'>
              <Input />
            </Form.Item>
            <Form.Item label='支付密钥' name='key'>
              <Input.Password />
            </Form.Item>
            <Button type='primary' onClick={() => handleSave('payment')}>
              保存
            </Button>
          </Form>
        </TabPane>
        <TabPane tab='物流设置' key='shipping'>
          <Form form={shippingForm} layout='vertical' style={{ maxWidth: 500 }}>
            <Form.Item label='发货地址' name='address'>
              <Input />
            </Form.Item>
            <Form.Item label='运费模板' name='template'>
              <Input />
            </Form.Item>
            <Button type='primary' onClick={() => handleSave('shipping')}>
              保存
            </Button>
          </Form>
        </TabPane>
        <TabPane tab='短信设置' key='sms'>
          <Form form={smsForm} layout='vertical' style={{ maxWidth: 500 }}>
            <Form.Item label='短信签名' name='sign'>
              <Input />
            </Form.Item>
            <Form.Item label='短信模板ID' name='template_id'>
              <Input />
            </Form.Item>
            <Button type='primary' onClick={() => handleSave('sms')}>
              保存
            </Button>
          </Form>
        </TabPane>
        <TabPane tab='邮件设置' key='email'>
          <Form form={emailForm} layout='vertical' style={{ maxWidth: 500 }}>
            <Form.Item label='SMTP服务器' name='smtp'>
              <Input />
            </Form.Item>
            <Form.Item label='端口' name='port'>
              <Input />
            </Form.Item>
            <Form.Item label='邮箱账号' name='user'>
              <Input />
            </Form.Item>
            <Form.Item label='邮箱密码' name='pass'>
              <Input.Password />
            </Form.Item>
            <Button type='primary' onClick={() => handleSave('email')}>
              保存
            </Button>
          </Form>
        </TabPane>
      </Tabs>
    </Spin>
  );
};

export default SettingsPage;
