import React, { useEffect, useState } from 'react';
import { Card, Row, Col, Statistic, Table, Tag } from 'antd';
import axios from 'axios';

const statusMap = {
  0: { text: '待付款', color: 'orange' },
  1: { text: '待发货', color: 'blue' },
  2: { text: '待收货', color: 'cyan' },
  3: { text: '已完成', color: 'green' },
  4: { text: '已取消', color: 'red' },
};

const StatsDashboard = () => {
  const [sales, setSales] = useState({ daily: [], monthly: [] });
  const [products, setProducts] = useState({ hot: [] });
  const [orders, setOrders] = useState({ total: 0, status: [], conversion: 0 });

  useEffect(() => {
    axios.get('/api/admin/stats/sales').then(res => setSales(res.data.data));
    axios
      .get('/api/admin/stats/products')
      .then(res => setProducts(res.data.data));
    axios.get('/api/admin/stats/orders').then(res => setOrders(res.data.data));
  }, []);

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Statistic title='订单总量' value={orders.total} />
        </Col>
        <Col span={6}>
          <Statistic
            title='转化率'
            value={orders.conversion}
            suffix='%'
            precision={2}
            valueStyle={{ color: '#52c41a' }}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title='待付款'
            value={orders.status.find(s => s.order_status === 0)?.count || 0}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title='已完成'
            value={orders.status.find(s => s.order_status === 3)?.count || 0}
          />
        </Col>
      </Row>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={12}>
          <Card title='日销售统计' size='small'>
            <Table
              rowKey='date'
              columns={[
                { title: '日期', dataIndex: 'date' },
                {
                  title: '销售额',
                  dataIndex: 'total',
                  render: value => `¥${value}`,
                },
              ]}
              dataSource={sales.daily}
              size='small'
              pagination={false}
              scroll={{ y: 200 }}
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card title='月销售统计' size='small'>
            <Table
              rowKey='month'
              columns={[
                { title: '月份', dataIndex: 'month' },
                {
                  title: '销售额',
                  dataIndex: 'total',
                  render: value => `¥${value}`,
                },
              ]}
              dataSource={sales.monthly}
              size='small'
              pagination={false}
              scroll={{ y: 200 }}
            />
          </Card>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={12}>
          <Card title='热销商品' size='small'>
            <Table
              rowKey='id'
              columns={[
                { title: '商品名', dataIndex: 'name' },
                { title: '销量', dataIndex: 'sales' },
              ]}
              dataSource={products.hot}
              size='small'
              pagination={false}
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card title='订单状态分布' size='small'>
            <Table
              rowKey='order_status'
              columns={[
                {
                  title: '状态',
                  dataIndex: 'order_status',
                  render: v => (
                    <Tag color={statusMap[v]?.color}>{statusMap[v]?.text}</Tag>
                  ),
                },
                { title: '数量', dataIndex: 'count' },
              ]}
              dataSource={orders.status}
              size='small'
              pagination={false}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default StatsDashboard;
