import React, { useState, useEffect } from 'react';
import { 
  Card, 
  List, 
  Typography, 
  Space, 
  Button,
  Badge,
  Empty,
  Spin,
  message
} from 'antd';
import {
  BellOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import DashboardService from '../services/dashboardService';

const { Text } = Typography;

const SystemNotifications = () => {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadNotifications();
  }, []);

  const loadNotifications = async () => {
    setLoading(true);
    try {
      const data = await DashboardService.getNotifications(10);
      setNotifications(data);
    } catch (error) {
      console.error('加载通知失败:', error);
      message.error('加载通知失败');
    } finally {
      setLoading(false);
    }
  };

  const getNotificationIcon = (type) => {
    const iconMap = {
      'info': <InfoCircleOutlined style={{ color: '#1890ff' }} />,
      'warning': <WarningOutlined style={{ color: '#fa8c16' }} />,
      'success': <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      'error': <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
    };
    return iconMap[type] || iconMap['info'];
  };

  const getNotificationColor = (type) => {
    const colorMap = {
      'info': '#1890ff',
      'warning': '#fa8c16',
      'success': '#52c41a',
      'error': '#ff4d4f'
    };
    return colorMap[type] || colorMap['info'];
  };

  if (loading) {
    return (
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <BellOutlined style={{ color: '#fa8c16' }} />
            <span>系统通知</span>
          </div>
        }
        style={{
          borderRadius: '12px',
          border: 'none',
          boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
        }}
      >
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  return (
    <Card
      className="content-card"
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <BellOutlined className="card-title-icon" style={{ color: '#fa8c16' }} />
          <span>系统通知</span>
          {notifications.length > 0 && (
            <Badge count={notifications.length} style={{ marginLeft: '8px' }} />
          )}
        </div>
      }
      extra={
        <Button type="link" size="small" onClick={loadNotifications}>
          刷新
        </Button>
      }
    >
      {notifications.length > 0 ? (
        <List
          dataSource={notifications}
          renderItem={(item) => (
            <List.Item 
              style={{ 
                padding: '12px 0', 
                border: 'none',
                borderLeft: `3px solid ${getNotificationColor(item.type)}`,
                paddingLeft: '12px',
                marginBottom: '8px',
                backgroundColor: '#fafafa',
                borderRadius: '6px'
              }}
            >
              <List.Item.Meta
                avatar={getNotificationIcon(item.type)}
                title={
                  <Text style={{ fontSize: '14px', fontWeight: '500' }}>
                    {item.title || item.message}
                  </Text>
                }
                description={
                  <Space direction="vertical" size={4}>
                    {item.content && (
                      <Text type="secondary" style={{ fontSize: '13px' }}>
                        {item.content}
                      </Text>
                    )}
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {item.time || item.created_at}
                    </Text>
                  </Space>
                }
              />
            </List.Item>
          )}
        />
      ) : (
        <Empty 
          description="暂无系统通知" 
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          style={{ padding: '20px 0' }}
        />
      )}
    </Card>
  );
};

export default SystemNotifications;
