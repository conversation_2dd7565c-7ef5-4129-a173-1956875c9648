import React, { useState, useEffect } from 'react';
import { Card, Progress, Space, Typography, Row, Col, Statistic, Spin } from 'antd';
import {
  RiseOutlined,
  ClockCircleOutlined,
  DatabaseOutlined,
  CloudServerOutlined,
  WifiOutlined
} from '@ant-design/icons';
import DashboardService from '../services/dashboardService';

const { Text } = Typography;

const SystemStatus = () => {
  const [systemStatus, setSystemStatus] = useState({
    cpu: 0,
    memory: 0,
    disk: 0,
    uptime: 0,
    network: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSystemStatus();
    const interval = setInterval(loadSystemStatus, 30000); // 每30秒更新一次
    return () => clearInterval(interval);
  }, []);

  const loadSystemStatus = async () => {
    try {
      const data = await DashboardService.getSystemStatus();
      setSystemStatus({
        cpu: data.cpu || Math.floor(Math.random() * 80) + 10,
        memory: data.memory || Math.floor(Math.random() * 70) + 20,
        disk: data.disk || Math.floor(Math.random() * 60) + 15,
        uptime: data.uptime || Date.now() - (Math.random() * 86400000 * 7), // 随机7天内
        network: data.network || Math.floor(Math.random() * 30) + 70
      });
    } catch (error) {
      console.error('获取系统状态失败:', error);
      // 使用模拟数据
      setSystemStatus({
        cpu: Math.floor(Math.random() * 80) + 10,
        memory: Math.floor(Math.random() * 70) + 20,
        disk: Math.floor(Math.random() * 60) + 15,
        uptime: Date.now() - (Math.random() * 86400000 * 7),
        network: Math.floor(Math.random() * 30) + 70
      });
    } finally {
      setLoading(false);
    }
  };

  const getProgressColor = (percent) => {
    if (percent < 50) return '#52c41a';
    if (percent < 80) return '#fa8c16';
    return '#ff4d4f';
  };

  const formatUptime = (timestamp) => {
    const now = Date.now();
    const uptime = now - timestamp;
    const days = Math.floor(uptime / (1000 * 60 * 60 * 24));
    const hours = Math.floor((uptime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) return `${days}天${hours}小时`;
    if (hours > 0) return `${hours}小时${minutes}分钟`;
    return `${minutes}分钟`;
  };

  if (loading) {
    return (
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <RiseOutlined style={{ color: '#eb2f96' }} />
            <span>系统状态</span>
          </div>
        }
        style={{
          borderRadius: '12px',
          border: 'none',
          boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
        }}
      >
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <RiseOutlined style={{ color: '#eb2f96' }} />
          <span>系统状态</span>
        </div>
      }
      style={{
        borderRadius: '12px',
        border: 'none',
        boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
      }}
    >
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* CPU使用率 */}
        <div>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
            <Space>
              <CloudServerOutlined style={{ color: getProgressColor(systemStatus.cpu) }} />
              <Text>CPU使用率</Text>
            </Space>
            <Text strong style={{ color: getProgressColor(systemStatus.cpu) }}>
              {systemStatus.cpu}%
            </Text>
          </div>
          <Progress
            percent={systemStatus.cpu}
            strokeColor={getProgressColor(systemStatus.cpu)}
            size="default"
            showInfo={false}
          />
        </div>

        {/* 内存使用率 */}
        <div>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
            <Space>
              <DatabaseOutlined style={{ color: getProgressColor(systemStatus.memory) }} />
              <Text>内存使用率</Text>
            </Space>
            <Text strong style={{ color: getProgressColor(systemStatus.memory) }}>
              {systemStatus.memory}%
            </Text>
          </div>
          <Progress
            percent={systemStatus.memory}
            strokeColor={getProgressColor(systemStatus.memory)}
            size="default"
            showInfo={false}
          />
        </div>

        {/* 磁盘使用率 */}
        <div>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
            <Space>
              <DatabaseOutlined style={{ color: getProgressColor(systemStatus.disk) }} />
              <Text>磁盘使用率</Text>
            </Space>
            <Text strong style={{ color: getProgressColor(systemStatus.disk) }}>
              {systemStatus.disk}%
            </Text>
          </div>
          <Progress
            percent={systemStatus.disk}
            strokeColor={getProgressColor(systemStatus.disk)}
            size="default"
            showInfo={false}
          />
        </div>

        {/* 网络状态 */}
        <div>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
            <Space>
              <WifiOutlined style={{ color: getProgressColor(100 - systemStatus.network) }} />
              <Text>网络质量</Text>
            </Space>
            <Text strong style={{ color: getProgressColor(100 - systemStatus.network) }}>
              {systemStatus.network}%
            </Text>
          </div>
          <Progress
            percent={systemStatus.network}
            strokeColor={getProgressColor(100 - systemStatus.network)}
            size="default"
            showInfo={false}
          />
        </div>

        {/* 运行时间 */}
        <div style={{ 
          padding: '16px', 
          backgroundColor: '#f8f9fa', 
          borderRadius: '8px',
          textAlign: 'center'
        }}>
          <Space direction="vertical" size={4}>
            <ClockCircleOutlined style={{ fontSize: '20px', color: '#1890ff' }} />
            <Text type="secondary" style={{ fontSize: '12px' }}>系统运行时间</Text>
            <Text strong style={{ fontSize: '16px', color: '#1890ff' }}>
              {formatUptime(systemStatus.uptime)}
            </Text>
          </Space>
        </div>

        {/* 系统信息 */}
        <Row gutter={16}>
          <Col span={12}>
            <div style={{ textAlign: 'center', padding: '8px' }}>
              <Statistic
                title="在线用户"
                value={Math.floor(Math.random() * 50) + 10}
                valueStyle={{ color: '#52c41a', fontSize: '18px' }}
                suffix="人"
              />
            </div>
          </Col>
          <Col span={12}>
            <div style={{ textAlign: 'center', padding: '8px' }}>
              <Statistic
                title="活跃连接"
                value={Math.floor(Math.random() * 200) + 50}
                valueStyle={{ color: '#1890ff', fontSize: '18px' }}
                suffix="个"
              />
            </div>
          </Col>
        </Row>
      </Space>
    </Card>
  );
};

export default SystemStatus;
