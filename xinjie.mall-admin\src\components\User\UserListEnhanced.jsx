// 增强的用户列表组件 - 修复日期问题并添加删除功能
import React, { useEffect, useState } from 'react';
import { Table, Button, Modal, Input, Tag, Switch, Form, message, Popconfirm, Space, DatePicker } from 'antd';
import { PlusOutlined, DeleteOutlined, EditOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import axios from 'axios';
import moment from 'moment';

const { confirm } = Modal;

const statusMap = {
  1: { text: '正常', color: 'green' },
  0: { text: '禁用', color: 'red' },
  '-1': { text: '已删除', color: 'gray' },
};

const genderMap = {
  1: '男',
  0: '女'
};

const UserListEnhanced = () => {
  const [data, setData] = useState([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchNickname, setSearchNickname] = useState('');
  const [searchPhone, setSearchPhone] = useState('');
  const [searchStatus, setSearchStatus] = useState('');
  const [addModal, setAddModal] = useState(false);
  const [editModal, setEditModal] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [form] = Form.useForm();
  const [editForm] = Form.useForm();
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  // 获取用户列表
  const fetchData = async (page = 1, pageSize = 10) => {
    setLoading(true);
    try {
      const res = await axios.get('/api/admin/user/list', {
        params: {
          page,
          pageSize,
          nickname: searchNickname,
          phone: searchPhone,
          status: searchStatus,
        }
      });
      
      if (res.data && res.data.success && res.data.data && Array.isArray(res.data.data.list)) {
        setData(res.data.data.list);
        setTotal(res.data.data.pagination?.total || 0);
      } else {
        setData([]);
        setTotal(0);
        message.error(res.data?.message || '数据格式错误');
      }
    } catch (e) {
      message.error('获取数据失败');
      console.error('获取用户列表失败:', e);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchData(page, pageSize);
  }, [page, pageSize]);

  const handleSearch = () => {
    setPage(1);
    fetchData(1, pageSize);
  };

  // 创建用户
  const handleAdd = () => {
    setAddModal(true);
    form.resetFields();
  };

  const handleAddOk = async () => {
    try {
      const values = await form.validateFields();
      
      // 处理日期格式
      if (values.birthday) {
        values.birthday = moment(values.birthday).format('YYYY-MM-DD');
      }
      
      console.log('提交的数据:', values);
      
      await axios.post('/api/admin/user/create', values);
      message.success('创建用户成功');
      setAddModal(false);
      fetchData(page, pageSize);
    } catch (e) {
      console.error('创建用户失败:', e);
      message.error('创建用户失败: ' + (e.response?.data?.message || e.message));
    }
  };

  // 编辑用户
  const handleEdit = (record) => {
    setEditingUser(record);
    editForm.setFieldsValue({
      openid: record.openid,
      unionid: record.unionid,
      nickname: record.nickname,
      avatar: record.avatar,
      phone: record.phone,
      gender: record.gender,
      birthday: record.birthday ? moment(record.birthday) : null,
      status: record.status
    });
    setEditModal(true);
  };

  const handleEditOk = async () => {
    try {
      const values = await editForm.validateFields();
      
      // 处理日期格式
      if (values.birthday) {
        values.birthday = moment(values.birthday).format('YYYY-MM-DD');
      }
      
      console.log('更新的数据:', values);
      
      await axios.put(`/api/admin/user/update/${editingUser.id}`, values);
      message.success('用户信息更新成功');
      setEditModal(false);
      setEditingUser(null);
      fetchData(page, pageSize);
    } catch (e) {
      console.error('更新用户失败:', e);
      message.error('更新用户失败: ' + (e.response?.data?.message || e.message));
    }
  };

  // 删除单个用户
  const handleDelete = async (record) => {
    try {
      await axios.delete(`/api/admin/user/delete/${record.id}`);
      message.success('用户删除成功');
      fetchData(page, pageSize);
    } catch (e) {
      console.error('删除用户失败:', e);
      message.error('删除用户失败: ' + (e.response?.data?.message || e.message));
    }
  };

  // 批量删除
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的用户');
      return;
    }

    confirm({
      title: '确认批量删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除选中的 ${selectedRowKeys.length} 个用户吗？此操作不可恢复。`,
      onOk: async () => {
        try {
          await axios.post('/api/admin/user/batch-delete', {
            ids: selectedRowKeys
          });
          message.success('批量删除成功');
          setSelectedRowKeys([]);
          fetchData(page, pageSize);
        } catch (e) {
          console.error('批量删除失败:', e);
          message.error('批量删除失败: ' + (e.response?.data?.message || e.message));
        }
      }
    });
  };

  // 更新状态
  const handleStatusChange = async (record, checked) => {
    try {
      await axios.put(`/api/admin/user/status/${record.id}`, {
        status: checked ? 1 : 0
      });
      message.success('状态更新成功');
      fetchData(page, pageSize);
    } catch (e) {
      console.error('状态更新失败:', e);
      message.error('状态更新失败');
    }
  };

  const columns = [
    {
      title: '头像',
      dataIndex: 'avatar',
      width: 80,
      render: url =>
        url ? (
          <img src={url} alt='' style={{ width: 40, height: 40, borderRadius: 20, objectFit: 'cover' }} />
        ) : (
          <div style={{ width: 40, height: 40, borderRadius: 20, backgroundColor: '#f0f0f0', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            无
          </div>
        ),
    },
    { title: 'OpenID', dataIndex: 'openid', width: 120, ellipsis: true },
    { title: '昵称', dataIndex: 'nickname', width: 120 },
    { title: '手机号', dataIndex: 'phone', width: 120 },
    { 
      title: '性别', 
      dataIndex: 'gender', 
      width: 80,
      render: gender => genderMap[gender] || '未知'
    },
    { 
      title: '生日', 
      dataIndex: 'birthday', 
      width: 120,
      render: birthday => birthday ? moment(birthday).format('YYYY-MM-DD') : '-'
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render: v => <Tag color={statusMap[v]?.color}>{statusMap[v]?.text}</Tag>,
    },
    {
      title: '操作',
      width: 280,
      render: (_, record) => (
        <Space>
          <Button type='link' icon={<EditOutlined />} onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Switch
            checked={record.status === 1}
            checkedChildren='启用'
            unCheckedChildren='禁用'
            onChange={(checked) => handleStatusChange(record, checked)}
            size="small"
          />
          <Popconfirm
            title="确定要删除这个用户吗？"
            description="此操作不可恢复"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button type='link' danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
  };

  return (
    <div>
      {/* 搜索和操作栏 */}
      <div style={{ marginBottom: 16, display: 'flex', gap: 8, flexWrap: 'wrap' }}>
        <Input
          placeholder='昵称'
          value={searchNickname}
          onChange={e => setSearchNickname(e.target.value)}
          style={{ width: 160 }}
        />
        <Input
          placeholder='手机号'
          value={searchPhone}
          onChange={e => setSearchPhone(e.target.value)}
          style={{ width: 160 }}
        />
        <Input
          placeholder='状态(1正常,0禁用)'
          value={searchStatus}
          onChange={e => setSearchStatus(e.target.value)}
          style={{ width: 120 }}
        />
        <Button type='primary' onClick={handleSearch}>
          搜索
        </Button>
        <Button
          type='primary'
          icon={<PlusOutlined />}
          onClick={handleAdd}
        >
          新增用户
        </Button>
        <Button
          type='primary'
          danger
          icon={<DeleteOutlined />}
          onClick={handleBatchDelete}
          disabled={selectedRowKeys.length === 0}
        >
          批量删除 ({selectedRowKeys.length})
        </Button>
      </div>

      {/* 用户表格 */}
      <Table
        rowKey='id'
        columns={columns}
        dataSource={data}
        loading={loading}
        rowSelection={rowSelection}
        pagination={{
          current: page,
          pageSize,
          total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          onChange: (p, ps) => {
            setPage(p);
            setPageSize(ps);
          },
        }}
        scroll={{ x: 1200 }}
      />

      {/* 新增用户弹窗 */}
      <Modal
        title='新增用户'
        open={addModal}
        onOk={handleAddOk}
        onCancel={() => setAddModal(false)}
        destroyOnClose
        width={600}
      >
        <Form form={form} layout='vertical'>
          <Form.Item
            label='微信OpenID'
            name='openid'
            rules={[
              { required: true, message: '请输入微信OpenID' },
              { max: 100, message: 'OpenID不能超过100个字符' },
            ]}
          >
            <Input placeholder='请输入微信OpenID' />
          </Form.Item>
          <Form.Item
            label='微信UnionID'
            name='unionid'
            rules={[
              { max: 100, message: 'UnionID不能超过100个字符' },
            ]}
          >
            <Input placeholder='请输入微信UnionID（可选）' />
          </Form.Item>
          <Form.Item
            label='昵称'
            name='nickname'
            rules={[
              { required: true, message: '请输入昵称' },
              { max: 50, message: '昵称不能超过50个字符' },
            ]}
          >
            <Input placeholder='请输入昵称' />
          </Form.Item>
          <Form.Item
            label='头像URL'
            name='avatar'
            rules={[
              { max: 500, message: '头像URL不能超过500个字符' },
            ]}
          >
            <Input placeholder='请输入头像URL（可选）' />
          </Form.Item>
          <Form.Item
            label='手机号'
            name='phone'
            rules={[
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' },
            ]}
          >
            <Input placeholder='请输入手机号（可选）' />
          </Form.Item>
          <Form.Item
            label='性别'
            name='gender'
            initialValue={0}
            rules={[{ required: true, message: '请选择性别' }]}
          >
            <Switch
              checkedChildren='男'
              unCheckedChildren='女'
            />
          </Form.Item>
          <Form.Item
            label='生日'
            name='birthday'
          >
            <DatePicker placeholder='请选择生日（可选）' style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item label='状态' name='status' initialValue={1}>
            <Switch
              checkedChildren='正常'
              unCheckedChildren='禁用'
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑用户弹窗 */}
      <Modal
        title="编辑用户信息"
        open={editModal}
        onOk={handleEditOk}
        onCancel={() => {
          setEditModal(false);
          setEditingUser(null);
          editForm.resetFields();
        }}
        width={600}
      >
        <Form form={editForm} layout="vertical">
          <Form.Item
            name="openid"
            label="微信OpenID"
            rules={[
              { required: true, message: '请输入微信OpenID' },
              { max: 100, message: 'OpenID不能超过100个字符' }
            ]}
          >
            <Input placeholder="请输入微信OpenID" />
          </Form.Item>
          <Form.Item
            name="unionid"
            label="微信UnionID"
            rules={[
              { max: 100, message: 'UnionID不能超过100个字符' }
            ]}
          >
            <Input placeholder="请输入微信UnionID（可选）" />
          </Form.Item>
          <Form.Item
            name="nickname"
            label="昵称"
            rules={[
              { required: true, message: '请输入昵称' },
              { min: 2, max: 50, message: '昵称长度在2-50个字符之间' }
            ]}
          >
            <Input placeholder="请输入昵称" />
          </Form.Item>
          <Form.Item
            name="avatar"
            label="头像URL"
            rules={[
              { max: 500, message: '头像URL不能超过500个字符' }
            ]}
          >
            <Input placeholder="请输入头像URL（可选）" />
          </Form.Item>
          <Form.Item
            name="phone"
            label="手机号"
            rules={[
              { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' }
            ]}
          >
            <Input placeholder="请输入手机号（可选）" />
          </Form.Item>
          <Form.Item
            name="gender"
            label="性别"
            rules={[{ required: true, message: '请选择性别' }]}
          >
            <Switch
              checkedChildren="男"
              unCheckedChildren="女"
            />
          </Form.Item>
          <Form.Item
            name="birthday"
            label="生日"
          >
            <DatePicker placeholder="请选择生日（可选）" style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Switch
              checkedChildren="启用"
              unCheckedChildren="禁用"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UserListEnhanced;
