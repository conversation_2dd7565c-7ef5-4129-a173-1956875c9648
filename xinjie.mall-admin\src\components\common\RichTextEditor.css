/* 富文本编辑器样式 */
.rich-text-editor {
  position: relative;
}

.rich-text-editor .ql-container {
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue',
    <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
    'Segoe UI Symbol', 'Noto Color Emoji';
  font-size: 14px;
  line-height: 1.5;
}

.rich-text-editor .ql-editor {
  min-height: 200px;
  max-height: 600px;
  overflow-y: auto;
  padding: 12px 15px;
}

.rich-text-editor .ql-editor.ql-blank::before {
  color: #bfbfbf;
  font-style: normal;
  left: 15px;
}

/* 工具栏样式优化 */
.rich-text-editor .ql-toolbar {
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
  padding: 8px;
}

.rich-text-editor .ql-toolbar .ql-formats {
  margin-right: 15px;
}

.rich-text-editor .ql-toolbar button {
  width: 28px;
  height: 28px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  margin: 0 1px;
}

.rich-text-editor .ql-toolbar button:hover {
  background: #e6f7ff;
  border-color: #91d5ff;
}

.rich-text-editor .ql-toolbar button.ql-active {
  background: #1890ff;
  color: white;
}

/* 下拉选择器样式 */
.rich-text-editor .ql-toolbar .ql-picker {
  color: #595959;
}

.rich-text-editor .ql-toolbar .ql-picker-options {
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  max-height: 200px;
  overflow-y: auto;
}

.rich-text-editor .ql-toolbar .ql-picker-item {
  padding: 5px 12px;
}

.rich-text-editor .ql-toolbar .ql-picker-item:hover {
  background: #f5f5f5;
}

/* 编辑器内容样式 */
.rich-text-editor .ql-editor h1 {
  font-size: 32px;
  font-weight: 600;
  margin: 16px 0;
}

.rich-text-editor .ql-editor h2 {
  font-size: 24px;
  font-weight: 600;
  margin: 14px 0;
}

.rich-text-editor .ql-editor h3 {
  font-size: 20px;
  font-weight: 600;
  margin: 12px 0;
}

.rich-text-editor .ql-editor h4 {
  font-size: 18px;
  font-weight: 600;
  margin: 10px 0;
}

.rich-text-editor .ql-editor h5 {
  font-size: 16px;
  font-weight: 600;
  margin: 8px 0;
}

.rich-text-editor .ql-editor h6 {
  font-size: 14px;
  font-weight: 600;
  margin: 6px 0;
}

.rich-text-editor .ql-editor p {
  margin: 8px 0;
}

.rich-text-editor .ql-editor ul,
.rich-text-editor .ql-editor ol {
  margin: 8px 0;
  padding-left: 20px;
}

.rich-text-editor .ql-editor li {
  margin: 4px 0;
}

.rich-text-editor .ql-editor blockquote {
  border-left: 4px solid #1890ff;
  margin: 16px 0;
  padding: 8px 0 8px 16px;
  background: #f6f8fa;
}

.rich-text-editor .ql-editor pre {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  margin: 16px 0;
  overflow-x: auto;
}

.rich-text-editor .ql-editor code {
  background: #f6f8fa;
  border-radius: 3px;
  padding: 2px 4px;
  font-family:
    'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}

.rich-text-editor .ql-editor img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 8px 0;
}

.rich-text-editor .ql-editor a {
  color: #1890ff;
  text-decoration: none;
}

.rich-text-editor .ql-editor a:hover {
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .rich-text-editor .ql-toolbar {
    padding: 4px;
  }

  .rich-text-editor .ql-toolbar .ql-formats {
    margin-right: 8px;
  }

  .rich-text-editor .ql-toolbar button {
    width: 24px;
    height: 24px;
  }
}
