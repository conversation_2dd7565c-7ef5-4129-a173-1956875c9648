import React from 'react';
import { Input } from 'antd';

const { TextArea } = Input;

const RichTextEditor = ({
  value,
  onChange,
  placeholder = '请输入内容...',
  height = 200,
}) => {
  return (
    <TextArea
      value={value}
      onChange={e => onChange && onChange(e.target.value)}
      placeholder={placeholder}
      rows={Math.floor(height / 24)}
      style={{ minHeight: height }}
    />
  );
};

export default RichTextEditor;
