import React from 'react';
import { Upload, Button, Image, message } from 'antd';
import { UploadOutlined } from '@ant-design/icons';

const UploadImage = ({ value, onChange, action, ...rest }) => {
  const uploadProps = {
    name: 'file',
    action,
    showUploadList: false,
    onChange(info) {
      if (info.file.status === 'done') {
        onChange?.(info.file.response.url);
        message.success('上传成功');
      } else if (info.file.status === 'error') {
        message.error('上传失败');
      }
    },
    ...rest,
  };
  return (
    <div>
      <Upload {...uploadProps}>
        <Button icon={<UploadOutlined />}>上传图片</Button>
      </Upload>
      {value && (
        <Image src={value} alt='' style={{ width: 120, marginTop: 8 }} />
      )}
    </div>
  );
};

export default UploadImage;
