/**
 * 认证相关配置
 */

const authConfig = {
  // Token配置
  token: {
    // Token过期时间（毫秒）
    expiryTime: 24 * 60 * 60 * 1000, // 24小时
    
    // Token刷新时间（毫秒）- 在过期前30分钟开始刷新
    refreshTime: 30 * 60 * 1000, // 30分钟
    
    // Token检查间隔（毫秒）
    checkInterval: 5 * 60 * 1000, // 5分钟
    
    // Token存储键名
    storageKeys: {
      token: 'token',
      refreshToken: 'refreshToken',
      tokenExpiry: 'tokenExpiry',
      userInfo: 'userInfo'
    }
  },

  // API配置
  api: {
    // 登录接口
    login: '/api/admin/login',
    
    // 登出接口
    logout: '/api/admin/logout',
    
    // Token验证接口
    check: '/api/admin/check',
    
    // Token刷新接口
    refresh: '/api/admin/refresh-token',
    
    // 用户信息接口
    profile: '/api/admin/profile'
  },

  // 路由配置
  routes: {
    // 登录页面路径
    login: '/login',
    
    // 首页路径
    home: '/',
    
    // 需要认证的路径前缀
    protectedPrefixes: ['/banner', '/product', '/order', '/user', '/category', '/stats', '/settings']
  },

  // 安全配置
  security: {
    // 是否启用自动刷新
    autoRefresh: true,
    
    // 是否启用定期检查
    periodicCheck: true,
    
    // 是否在页面可见性变化时检查
    visibilityCheck: true,
    
    // 最大重试次数
    maxRetries: 3,
    
    // 重试间隔（毫秒）
    retryInterval: 1000
  },

  // 错误处理配置
  errorHandling: {
    // 是否自动跳转到登录页
    autoRedirect: true,
    
    // 是否显示错误消息
    showErrorMessages: true,
    
    // 错误消息显示时间（毫秒）
    messageDuration: 3000
  },

  // 开发环境配置
  development: {
    // 是否启用调试日志
    debug: process.env.NODE_ENV === 'development',
    
    // 是否跳过某些安全检查（仅开发环境）
    skipSecurityChecks: false
  }
};

export default authConfig; 