const mysql = require('mysql2/promise');
const config = require('./index');

// 创建连接池
const pool = mysql.createPool(config.database);

// 执行SQL查询
async function query(sql, params = []) {
  console.log('执行SQL:', sql, '\n参数:', params);
  const start = Date.now();
  try {
    const [rows] = await pool.execute(sql, params);
    const duration = Date.now() - start;
    console.log(`SQL执行耗时: ${duration}ms`);
    return rows;
  } catch (error) {
    const duration = Date.now() - start;
    console.error(`SQL执行错误，耗时: ${duration}ms`, error);
    throw error;
  }
}

// 分页查询辅助函数
function buildPaginationQuery(baseSql, params = [], page = 1, pageSize = 10) {
  const offset = parseInt((page - 1) * pageSize) || 0;
  const limit = parseInt(pageSize) || 10;
  if (params.length === 0) {
    // 没有where参数，直接拼接数字
    const limitSql = `${baseSql} LIMIT ${offset}, ${limit}`;
    return { sql: limitSql, params: [] };
  } else {
    // 有where参数，LIMIT用?占位符
    const limitSql = `${baseSql} LIMIT ?, ?`;
    const limitParams = [...params, offset, limit];
    return { sql: limitSql, params: limitParams };
  }
}

// 数据库连接测试函数
async function testConnection() {
  try {
    const connection = await pool.getConnection();
    console.log('✅ 数据库连接成功');
    connection.release();
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    console.error('请检查数据库配置和服务状态');
    return false;
  }
}

module.exports = {
  pool,
  query,
  buildPaginationQuery,
  testConnection,
};
