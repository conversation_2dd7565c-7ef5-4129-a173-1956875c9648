const path = require('path');
const fs = require('fs');

// 根据NODE_ENV加载不同的.env文件
const env = process.env.NODE_ENV || 'development';
let envFile = '.env';
if (env === 'development') envFile = '.env.development';
else if (env === 'staging') envFile = '.env.staging';
else if (env === 'production') envFile = '.env.production';

const envPath = path.resolve(__dirname, '../../', envFile);
if (fs.existsSync(envPath)) {
  require('dotenv').config({ path: envPath });
} else {
  require('dotenv').config(); // 默认.env
}

// 默认配置
const defaultConfig = {
  // 服务器配置
  server: {
    port: process.env.PORT || 8081,
    host: process.env.HOST || 'localhost',
    env: env,
  },

  // 数据库配置
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'xinjie_mall',
    charset: 'utf8mb4',
    timezone: '+08:00',
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
  },

  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'xinjie-mall-secret-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
  },

  // 文件上传配置
  upload: {
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif'],
    uploadDir: path.join(__dirname, '../../public/uploads'),
    tempDir: path.join(__dirname, '../../public/uploads/temp'),
  },

  // 日志配置
  logger: {
    level: process.env.LOG_LEVEL || 'info',
    dir: path.join(__dirname, '../../logs'),
  },

  // 安全配置
  security: {
    bcryptRounds: 12,
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 100, // 限制每个IP 15分钟内最多100个请求
    },
  },
};

// 环境特定配置
const envConfigs = {
  development: {
    server: {
      port: 3000,
    },
    database: {
      connectionLimit: 5,
    },
    logger: {
      level: 'debug',
    },
  },

  staging: {
    server: {
      port: 3000,
    },
    database: {
      connectionLimit: 20,
    },
    logger: {
      level: 'info',
    },
  },

  production: {
    server: {
      port: process.env.PORT || 3000,
    },
    database: {
      connectionLimit: 50,
    },
    logger: {
      level: 'warn',
    },
    security: {
      rateLimit: {
        windowMs: 15 * 60 * 1000,
        max: 50,
      },
    },
  },
};

// 调试环境变量
console.log('环境变量调试:');
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_USER:', process.env.DB_USER);
console.log('DB_PASSWORD:', process.env.DB_PASSWORD);
console.log('DB_NAME:', process.env.DB_NAME);

// 合并配置（深度合并）
function deepMerge(target, source) {
  for (const key in source) {
    if (
      source[key] &&
      typeof source[key] === 'object' &&
      !Array.isArray(source[key])
    ) {
      target[key] = deepMerge(target[key] || {}, source[key]);
    } else {
      target[key] = source[key];
    }
  }
  return target;
}

const config = deepMerge({ ...defaultConfig }, envConfigs[env] || {});
console.log('最终数据库配置:', config.database);

module.exports = config;
