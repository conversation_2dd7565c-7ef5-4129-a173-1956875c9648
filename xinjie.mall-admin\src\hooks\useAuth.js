import { useState, useEffect, useCallback } from 'react';
import AuthService from '../utils/auth';

/**
 * 认证相关的React Hook
 * @returns {Object} 认证状态和操作方法
 */
const useAuth = () => {
  const [authState, setAuthState] = useState({
    loading: true,
    authenticated: false,
    user: null,
    token: null
  });

  // 检查认证状态
  const checkAuth = useCallback(async () => {
    try {
      setAuthState(prev => ({ ...prev, loading: true }));
      
      const status = await AuthService.getAuthStatus();
      
      setAuthState({
        loading: false,
        authenticated: status.authenticated,
        user: status.user,
        token: status.token
      });
    } catch (error) {
      console.error('认证状态检查失败:', error);
      setAuthState({
        loading: false,
        authenticated: false,
        user: null,
        token: null
      });
    }
  }, []);

  // 登录
  const login = useCallback(async (credentials) => {
    try {
      setAuthState(prev => ({ ...prev, loading: true }));
      
      const response = await fetch('/api/admin/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      const data = await response.json();

      if (data.success) {
        AuthService.saveAuth(data.data);
        await checkAuth();
        return { success: true };
      } else {
        return { success: false, message: data.message };
      }
    } catch (error) {
      console.error('登录失败:', error);
      return { success: false, message: '网络错误' };
    } finally {
      setAuthState(prev => ({ ...prev, loading: false }));
    }
  }, [checkAuth]);

  // 登出
  const logout = useCallback(async () => {
    try {
      await AuthService.logout();
      setAuthState({
        loading: false,
        authenticated: false,
        user: null,
        token: null
      });
      return { success: true };
    } catch (error) {
      console.error('登出失败:', error);
      return { success: false, message: '登出失败' };
    }
  }, []);

  // 刷新token
  const refreshToken = useCallback(async () => {
    try {
      const success = await AuthService.refreshToken();
      if (success) {
        await checkAuth();
      }
      return success;
    } catch (error) {
      console.error('Token刷新失败:', error);
      return false;
    }
  }, [checkAuth]);

  // 验证token
  const validateToken = useCallback(async () => {
    try {
      return await AuthService.validateToken();
    } catch (error) {
      console.error('Token验证失败:', error);
      return false;
    }
  }, []);

  // 初始化时检查认证状态
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  return {
    // 状态
    ...authState,
    
    // 方法
    login,
    logout,
    refreshToken,
    validateToken,
    checkAuth,
    
    // 便捷方法
    isAuthenticated: authState.authenticated,
    isLoading: authState.loading,
    user: authState.user,
    token: authState.token
  };
};

export default useAuth; 