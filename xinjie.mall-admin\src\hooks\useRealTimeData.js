import { useState, useEffect, useCallback } from 'react';
import DashboardService from '../services/dashboardService';

// 实时数据更新Hook
export const useRealTimeData = (updateInterval = 30000) => {
  const [data, setData] = useState({
    statistics: {},
    recentOrders: [],
    notifications: [],
    systemStatus: {},
    hotProducts: [],
    loading: true,
    lastUpdate: null
  });

  const [autoUpdate, setAutoUpdate] = useState(true);

  // 加载所有数据
  const loadAllData = useCallback(async () => {
    try {
      setData(prev => ({ ...prev, loading: true }));

      const [
        statisticsData,
        ordersData,
        notificationsData,
        systemStatusData,
        hotProductsData
      ] = await Promise.all([
        DashboardService.getStatistics(),
        DashboardService.getRecentOrders(5),
        DashboardService.getNotifications(5),
        DashboardService.getSystemStatus(),
        DashboardService.getHotProducts(8)
      ]);

      setData({
        statistics: statisticsData,
        recentOrders: ordersData,
        notifications: notificationsData,
        systemStatus: systemStatusData,
        hotProducts: hotProductsData,
        loading: false,
        lastUpdate: new Date()
      });

      return true;
    } catch (error) {
      console.error('加载数据失败:', error);
      setData(prev => ({ ...prev, loading: false }));
      return false;
    }
  }, []);

  // 手动刷新数据
  const refreshData = useCallback(async () => {
    return await loadAllData();
  }, [loadAllData]);

  // 切换自动更新
  const toggleAutoUpdate = useCallback(() => {
    setAutoUpdate(prev => !prev);
  }, []);

  // 初始化数据加载
  useEffect(() => {
    loadAllData();
  }, [loadAllData]);

  // 自动更新定时器
  useEffect(() => {
    if (!autoUpdate) return;

    const interval = setInterval(() => {
      loadAllData();
    }, updateInterval);

    return () => clearInterval(interval);
  }, [autoUpdate, updateInterval, loadAllData]);

  return {
    ...data,
    refreshData,
    autoUpdate,
    toggleAutoUpdate
  };
};

// 统计数据Hook
export const useStatistics = () => {
  const [statistics, setStatistics] = useState({});
  const [loading, setLoading] = useState(true);

  const loadStatistics = useCallback(async () => {
    setLoading(true);
    try {
      const data = await DashboardService.getStatistics();
      setStatistics(data);
    } catch (error) {
      console.error('加载统计数据失败:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadStatistics();
  }, [loadStatistics]);

  return { statistics, loading, refresh: loadStatistics };
};

// 最近订单Hook
export const useRecentOrders = (limit = 5) => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);

  const loadOrders = useCallback(async () => {
    setLoading(true);
    try {
      const data = await DashboardService.getRecentOrders(limit);
      setOrders(data);
    } catch (error) {
      console.error('加载最近订单失败:', error);
    } finally {
      setLoading(false);
    }
  }, [limit]);

  useEffect(() => {
    loadOrders();
  }, [loadOrders]);

  return { orders, loading, refresh: loadOrders };
};

// 系统通知Hook
export const useNotifications = (limit = 5) => {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);

  const loadNotifications = useCallback(async () => {
    setLoading(true);
    try {
      const data = await DashboardService.getNotifications(limit);
      setNotifications(data);
    } catch (error) {
      console.error('加载系统通知失败:', error);
    } finally {
      setLoading(false);
    }
  }, [limit]);

  useEffect(() => {
    loadNotifications();
  }, [loadNotifications]);

  return { notifications, loading, refresh: loadNotifications };
};

// 系统状态Hook
export const useSystemStatus = () => {
  const [status, setStatus] = useState({});
  const [loading, setLoading] = useState(true);

  const loadStatus = useCallback(async () => {
    setLoading(true);
    try {
      const data = await DashboardService.getSystemStatus();
      setStatus(data);
    } catch (error) {
      console.error('加载系统状态失败:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadStatus();
    const interval = setInterval(loadStatus, 30000); // 每30秒更新一次
    return () => clearInterval(interval);
  }, [loadStatus]);

  return { status, loading, refresh: loadStatus };
};

// 热销商品Hook
export const useHotProducts = (limit = 8) => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);

  const loadProducts = useCallback(async () => {
    setLoading(true);
    try {
      const data = await DashboardService.getHotProducts(limit);
      setProducts(data);
    } catch (error) {
      console.error('加载热销商品失败:', error);
    } finally {
      setLoading(false);
    }
  }, [limit]);

  useEffect(() => {
    loadProducts();
  }, [loadProducts]);

  return { products, loading, refresh: loadProducts };
};

export default useRealTimeData;
