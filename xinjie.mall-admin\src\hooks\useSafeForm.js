import { Form } from 'antd';
import { useRef, useEffect } from 'react';

/**
 * 安全的 useForm Hook，避免在组件卸载后操作表单实例
 */
export const useSafeForm = () => {
  const [form] = Form.useForm();
  const mountedRef = useRef(true);

  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  // 包装表单方法，添加安全检查
  const safeForm = {
    ...form,
    setFieldsValue: (values) => {
      if (mountedRef.current) {
        try {
          return form.setFieldsValue(values);
        } catch (error) {
          console.warn('设置表单值失败，可能组件已卸载:', error);
        }
      }
    },
    resetFields: (fields) => {
      if (mountedRef.current) {
        try {
          return form.resetFields(fields);
        } catch (error) {
          console.warn('重置表单失败，可能组件已卸载:', error);
        }
      }
    },
    getFieldValue: (name) => {
      if (mountedRef.current) {
        try {
          return form.getFieldValue(name);
        } catch (error) {
          console.warn('获取表单值失败，可能组件已卸载:', error);
          return undefined;
        }
      }
      return undefined;
    }
  };

  return [safeForm];
};

export default useSafeForm;
