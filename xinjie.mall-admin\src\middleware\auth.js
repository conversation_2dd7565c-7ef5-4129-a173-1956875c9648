const jwt = require('jsonwebtoken');
const config = require('../config');

function requireAuth(req, res, next) {
  // 1. 优先用 Authorization 头
  const authHeader = req.headers['authorization'];
  let token = null;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    token = authHeader.slice(7);
  } else if (req.session && req.session.token) {
    // 2. 兼容 session 里的 token
    token = req.session.token;
  }
  if (!token) {
    return res.status(401).json({ success: false, message: '未提供认证令牌' });
  }
  try {
    const decoded = jwt.verify(token, config.jwt.secret);
    req.user = decoded;
    next();
  } catch (err) {
    return res
      .status(401)
      .json({ success: false, message: '认证令牌无效或已过期' });
  }
}

module.exports = { requireAuth };
