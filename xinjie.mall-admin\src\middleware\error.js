const config = require('../config');

// 统一错误处理中间件
function errorHandler(err, req, res, next) {
  console.error('错误详情:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  // 根据错误类型返回不同的响应
  if (err.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: '参数验证失败',
      errors: err.errors,
    });
  }

  if (err.name === 'UnauthorizedError') {
    return res.status(401).json({
      success: false,
      message: '认证失败',
    });
  }

  if (err.code === 'ER_DUP_ENTRY') {
    return res.status(400).json({
      success: false,
      message: '数据已存在',
    });
  }

  if (err.code === 'ER_NO_REFERENCED_ROW_2') {
    return res.status(400).json({
      success: false,
      message: '关联数据不存在',
    });
  }

  // 默认错误响应
  const statusCode = err.statusCode || 500;
  const message =
    config.server.env === 'production' ? '服务器内部错误' : err.message;

  res.status(statusCode).json({
    success: false,
    message,
    ...(config.server.env !== 'production' && { stack: err.stack }),
  });
}

// 404错误处理
function notFoundHandler(req, res) {
  res.status(404).json({
    success: false,
    message: '请求的资源不存在',
  });
}

// 自定义错误类
class AppError extends Error {
  constructor(message, statusCode = 500) {
    super(message);
    this.statusCode = statusCode;
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
  }
}

// 参数验证错误
class ValidationError extends AppError {
  constructor(message, errors = []) {
    super(message, 400);
    this.errors = errors;
  }
}

// 业务逻辑错误
class BusinessError extends AppError {
  constructor(message, statusCode = 400) {
    super(message, statusCode);
  }
}

module.exports = {
  errorHandler,
  notFoundHandler,
  AppError,
  ValidationError,
  BusinessError,
};
