import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Space,
  Tag,
  Avatar,
  message,
  Popconfirm,
  Row,
  Col,
  Card,
  Statistic
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  UserOutlined,
  LockOutlined,
  UnlockOutlined,
  KeyOutlined
} from '@ant-design/icons';
import { request } from '../utils/request';

const { Option } = Select;

const AdminManagement = () => {
  const [adminList, setAdminList] = useState([]);
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [resetPasswordModalVisible, setResetPasswordModalVisible] = useState(false);
  const [editingAdmin, setEditingAdmin] = useState(null);
  const [currentAdmin, setCurrentAdmin] = useState(null);
  const [form] = Form.useForm();
  const [resetPasswordForm] = Form.useForm();
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [searchParams, setSearchParams] = useState({
    username: '',
    role_id: '',
    status: ''
  });

  // 获取管理员列表
  const fetchAdminList = async (params = {}) => {
    setLoading(true);
    try {
      const response = await request.get('/admin/admin/list', {
        params: {
          page: pagination.current,
          limit: pagination.pageSize,
          ...searchParams,
          ...params
        }
      });

      if (response.code === 200) {
        setAdminList(response.data.list);
        setPagination(prev => ({
          ...prev,
          total: response.data.total
        }));
      }
    } catch (error) {
      message.error('获取管理员列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取角色列表
  const fetchRoles = async () => {
    try {
      const response = await request.get('/admin/role/all');
      if (response.code === 200) {
        setRoles(response.data);
      }
    } catch (error) {
      message.error('获取角色列表失败');
    }
  };

  useEffect(() => {
    fetchAdminList();
    fetchRoles();
  }, []);

  // 处理搜索
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchAdminList();
  };

  // 处理分页变化
  const handleTableChange = (paginationInfo) => {
    setPagination(prev => ({
      ...prev,
      current: paginationInfo.current,
      pageSize: paginationInfo.pageSize
    }));
    fetchAdminList();
  };

  // 显示创建/编辑弹窗
  const showModal = (admin = null) => {
    setEditingAdmin(admin);
    setModalVisible(true);
    if (admin) {
      form.setFieldsValue({
        username: admin.username,
        real_name: admin.real_name,
        email: admin.email,
        phone: admin.phone,
        role_id: admin.role_id,
        department: admin.department,
        position: admin.position
      });
    } else {
      form.resetFields();
    }
  };

  // 隐藏弹窗
  const hideModal = () => {
    setModalVisible(false);
    setEditingAdmin(null);
    form.resetFields();
  };

  // 显示重置密码弹窗
  const showResetPasswordModal = (admin) => {
    setCurrentAdmin(admin);
    setResetPasswordModalVisible(true);
    resetPasswordForm.resetFields();
  };

  // 隐藏重置密码弹窗
  const hideResetPasswordModal = () => {
    setResetPasswordModalVisible(false);
    setCurrentAdmin(null);
    resetPasswordForm.resetFields();
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingAdmin) {
        // 更新管理员
        const response = await request.put(`/admin/admin/update/${editingAdmin.id}`, values);
        if (response.code === 200) {
          message.success('更新成功');
          hideModal();
          fetchAdminList();
        }
      } else {
        // 创建管理员
        const response = await request.post('/admin/admin/create', values);
        if (response.code === 200) {
          message.success('创建成功');
          hideModal();
          fetchAdminList();
        }
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 重置密码
  const handleResetPassword = async () => {
    try {
      const values = await resetPasswordForm.validateFields();
      
      const response = await request.put(`/admin/admin/reset-password/${currentAdmin.id}`, {
        new_password: values.new_password
      });
      
      if (response.code === 200) {
        message.success('密码重置成功');
        hideResetPasswordModal();
      }
    } catch (error) {
      message.error('密码重置失败');
    }
  };

  // 删除管理员
  const handleDelete = async (admin) => {
    try {
      const response = await request.delete(`/admin/admin/delete/${admin.id}`);
      if (response.code === 200) {
        message.success('删除成功');
        fetchAdminList();
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 更新状态
  const handleStatusChange = async (admin) => {
    try {
      const newStatus = admin.status === 1 ? 0 : 1;
      const response = await request.put(`/admin/admin/status/${admin.id}`, {
        status: newStatus
      });
      
      if (response.code === 200) {
        message.success('状态更新成功');
        fetchAdminList();
      }
    } catch (error) {
      message.error('状态更新失败');
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '头像',
      dataIndex: 'avatar',
      key: 'avatar',
      width: 60,
      render: (avatar) => (
        <Avatar 
          src={avatar} 
          icon={<UserOutlined />}
          size={40}
        />
      )
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      width: 120
    },
    {
      title: '真实姓名',
      dataIndex: 'real_name',
      key: 'real_name',
      width: 120
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: 180
    },
    {
      title: '角色',
      dataIndex: ['Role', 'name'],
      key: 'role',
      width: 120,
      render: (roleName) => (
        <Tag color="blue">{roleName}</Tag>
      )
    },
    {
      title: '部门',
      dataIndex: 'department',
      key: 'department',
      width: 120
    },
    {
      title: '职位',
      dataIndex: 'position',
      key: 'position',
      width: 120
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={status === 1 ? 'green' : 'red'}>
          {status === 1 ? '正常' : '禁用'}
        </Tag>
      )
    },
    {
      title: '最后登录',
      dataIndex: 'last_login_time',
      key: 'last_login_time',
      width: 160,
      render: (time) => time ? new Date(time).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="link" 
            size="small"
            icon={<EditOutlined />}
            onClick={() => showModal(record)}
          >
            编辑
          </Button>
          <Button 
            type="link" 
            size="small"
            icon={<KeyOutlined />}
            onClick={() => showResetPasswordModal(record)}
          >
            重置密码
          </Button>
          <Button 
            type="link" 
            size="small"
            icon={record.status === 1 ? <LockOutlined /> : <UnlockOutlined />}
            onClick={() => handleStatusChange(record)}
          >
            {record.status === 1 ? '禁用' : '启用'}
          </Button>
          <Popconfirm
            title="确定要删除这个管理员吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              type="link" 
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div className="admin-management">
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总管理员数"
              value={pagination.total}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="正常状态"
              value={adminList.filter(admin => admin.status === 1).length}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="禁用状态"
              value={adminList.filter(admin => admin.status === 0).length}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="角色数量"
              value={roles.length}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作栏 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <Input.Search
              placeholder="搜索用户名/姓名"
              value={searchParams.username}
              onChange={(e) => setSearchParams(prev => ({ ...prev, username: e.target.value }))}
              onSearch={handleSearch}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="角色筛选"
              value={searchParams.role_id}
              onChange={(value) => setSearchParams(prev => ({ ...prev, role_id: value }))}
              allowClear
              style={{ width: '100%' }}
            >
              {roles.map(role => (
                <Option key={role.id} value={role.id}>{role.name}</Option>
              ))}
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="状态筛选"
              value={searchParams.status}
              onChange={(value) => setSearchParams(prev => ({ ...prev, status: value }))}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="1">正常</Option>
              <Option value="0">禁用</Option>
            </Select>
          </Col>
          <Col span={10}>
            <Space>
              <Button 
                type="primary" 
                icon={<PlusOutlined />} 
                onClick={() => showModal()}
              >
                添加管理员
              </Button>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={() => fetchAdminList()}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 管理员列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={adminList}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 创建/编辑管理员弹窗 */}
      <Modal
        title={editingAdmin ? '编辑管理员' : '添加管理员'}
        visible={modalVisible}
        onOk={handleSubmit}
        onCancel={hideModal}
        width={600}
        destroyOnClose
      >
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="username"
                label="用户名"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, message: '用户名至少3个字符' }
                ]}
              >
                <Input 
                  placeholder="请输入用户名" 
                  disabled={!!editingAdmin}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="real_name"
                label="真实姓名"
                rules={[{ required: true, message: '请输入真实姓名' }]}
              >
                <Input placeholder="请输入真实姓名" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { type: 'email', message: '请输入正确的邮箱格式' }
                ]}
              >
                <Input placeholder="请输入邮箱" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="手机号"
                rules={[
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
                ]}
              >
                <Input placeholder="请输入手机号" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="role_id"
                label="角色"
                rules={[{ required: true, message: '请选择角色' }]}
              >
                <Select placeholder="请选择角色">
                  {roles.map(role => (
                    <Option key={role.id} value={role.id}>
                      {role.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="department"
                label="部门"
              >
                <Input placeholder="请输入部门" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="position"
                label="职位"
              >
                <Input placeholder="请输入职位" />
              </Form.Item>
            </Col>
            {!editingAdmin && (
              <Col span={12}>
                <Form.Item
                  name="password"
                  label="密码"
                  rules={[
                    { required: true, message: '请输入密码' },
                    { min: 8, message: '密码至少8个字符' },
                    { 
                      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
                      message: '密码必须包含大小写字母、数字和特殊字符'
                    }
                  ]}
                >
                  <Input.Password placeholder="请输入密码" />
                </Form.Item>
              </Col>
            )}
          </Row>
        </Form>
      </Modal>

      {/* 重置密码弹窗 */}
      <Modal
        title="重置密码"
        visible={resetPasswordModalVisible}
        onOk={handleResetPassword}
        onCancel={hideResetPasswordModal}
        width={400}
        destroyOnClose
      >
        <Form form={resetPasswordForm} layout="vertical">
          <Form.Item
            name="new_password"
            label="新密码"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 8, message: '密码至少8个字符' },
              { 
                pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
                message: '密码必须包含大小写字母、数字和特殊字符'
              }
            ]}
          >
            <Input.Password placeholder="请输入新密码" />
          </Form.Item>
          <Form.Item
            name="confirm_password"
            label="确认密码"
            dependencies={['new_password']}
            rules={[
              { required: true, message: '请确认密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('new_password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password placeholder="请确认密码" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AdminManagement; 