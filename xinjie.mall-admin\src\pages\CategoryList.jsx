import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Upload,
  message,
  Popconfirm,
  Image,
  Space,
  Tag,
  InputNumber,
  Switch
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, UploadOutlined } from '@ant-design/icons';
import request from '../utils/request';
import axios from 'axios';

const { TextArea } = Input;

const CategoryList = () => {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [form] = Form.useForm();
  const [uploading, setUploading] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);

  // 获取分类列表
  const fetchCategories = async (currentPage = page, currentPageSize = pageSize) => {
    setLoading(true);
    try {
      const response = await request.get('/admin/category/list', {
        params: {
          page: currentPage,
          pageSize: currentPageSize
        }
      });
      if (response.success) {
        setCategories(response.data.list || response.data);
        setTotal(response.data.total || response.data.length || 0);
      } else {
        message.error('获取分类列表失败');
      }
    } catch (error) {
      message.error('获取分类列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();

    // 清理函数
    return () => {
      // 清理表单实例
      form.resetFields();
    };
  }, []);

  // 监听分页变化
  useEffect(() => {
    fetchCategories(page, pageSize);
  }, [page, pageSize]);

  // 上传图片
  const uploadImage = async (file) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', 'category');

    try {
      // 使用分类图片上传接口
      const response = await axios.post('/api/admin/category/upload', formData, {
        timeout: 30000, // 增加超时时间
      });

      if (response.data.code === 0) {
        return response.data.data.url;
      } else {
        throw new Error(response.data.msg);
      }
    } catch (error) {
      message.error('图片上传失败: ' + error.message);
      return null;
    }
  };

  // 处理表单提交
  const handleSubmit = async (values) => {
    setUploading(true);
    try {
      let imageUrl = values.imageUrl;

      // 如果有新上传的文件
      if (values.image && values.image[0] && values.image[0].originFileObj) {
        const uploadedUrl = await uploadImage(values.image[0].originFileObj);
        if (!uploadedUrl) {
          setUploading(false);
          return;
        }
        imageUrl = uploadedUrl;
      }

      const categoryData = {
        name: values.name,
        description: values.description,
        image: imageUrl,
        sort_order: values.sortOrder || 0,
        status: values.status ? 1 : 0
      };

      if (editingCategory) {
        // 编辑
        await request.put(`/admin/category/update/${editingCategory.id}`, categoryData);
        message.success('分类更新成功');
      } else {
        // 新增
        await request.post('/admin/category/create', categoryData);
        message.success('分类添加成功');
      }

      setModalVisible(false);
      form.resetFields();
      setEditingCategory(null);
      fetchCategories();
    } catch (error) {
      message.error('操作失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setUploading(false);
    }
  };

  // 删除分类
  const handleDelete = async (id) => {
    try {
      await request.delete(`/admin/category/delete/${id}`);
      message.success('删除成功');
      fetchCategories();
    } catch (error) {
      message.error('删除失败: ' + (error.response?.data?.message || error.message));
    }
  };

  // 编辑分类
  const handleEdit = (record) => {
    setEditingCategory(record);
    form.setFieldsValue({
      name: record.name,
      description: record.description,
      imageUrl: record.image,
      sortOrder: record.sort_order,
      status: record.status === 1
    });
    setModalVisible(true);
  };

  // 表格列定义
  const columns = [
    {
      title: '图片',
      dataIndex: 'image',
      key: 'image',
      render: (url, record) => (
        <Image
          width={60}
          height={60}
          src={url || record.image_url || ''}
          fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
          style={{ objectFit: 'cover', borderRadius: '4px' }}
        />
      ),
      width: 100,
    },
    {
      title: '分类名称',
      dataIndex: 'name',
      key: 'name',
      width: 180,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 250,
      ellipsis: true,
    },
    {
      title: '排序',
      dataIndex: 'sort_order',
      key: 'sort_order',
      width: 80,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={status === 1 ? 'green' : 'red'}>
          {status === 1 ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '商品数量',
      dataIndex: 'product_count',
      key: 'product_count',
      width: 120,
      render: (count) => count || 0,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (date) => new Date(date).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 160,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个分类吗？"
            description="删除分类将同时删除该分类下的所有商品！"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card
        title="分类管理"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setEditingCategory(null);
              form.resetFields();
              setModalVisible(true);
            }}
          >
            添加分类
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={categories}
          rowKey="id"
          loading={loading}
          pagination={{
            current: page,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条记录`,
            onChange: (page, pageSize) => {
              setPage(page);
              setPageSize(pageSize);
            },
          }}
        />
      </Card>

      <Modal
        title={editingCategory ? '编辑分类' : '添加分类'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingCategory(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            status: true,
            sortOrder: 0
          }}
        >
          <Form.Item
            name="name"
            label="分类名称"
            rules={[{ required: true, message: '请输入分类名称' }]}
          >
            <Input placeholder="请输入分类名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="分类描述"
          >
            <TextArea
              rows={3}
              placeholder="请输入分类描述"
            />
          </Form.Item>

          <Form.Item
            name="image"
            label="分类图片"
            rules={[
              {
                required: !editingCategory,
                message: '请上传分类图片'
              }
            ]}
            valuePropName="fileList"
            getValueFromEvent={(e) => {
              if (Array.isArray(e)) {
                return e;
              }
              return e?.fileList;
            }}
          >
            <Upload
              listType="picture-card"
              maxCount={1}
              beforeUpload={() => false}
              accept="image/*"
            >
              <div>
                <UploadOutlined />
                <div style={{ marginTop: 8 }}>上传图片</div>
              </div>
            </Upload>
          </Form.Item>

          {editingCategory && (
            <Form.Item
              name="imageUrl"
              label="当前图片"
            >
              <Input disabled />
            </Form.Item>
          )}

          <Form.Item
            name="sortOrder"
            label="排序"
          >
            <InputNumber
              min={0}
              style={{ width: '100%' }}
              placeholder="数字越小越靠前"
            />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={uploading}>
                {editingCategory ? '更新' : '添加'}
              </Button>
              <Button
                onClick={() => {
                  setModalVisible(false);
                  setEditingCategory(null);
                  form.resetFields();
        }}
      >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
);
};

export default CategoryList;
