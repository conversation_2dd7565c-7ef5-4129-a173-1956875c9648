import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  Switch,
  DatePicker,
  message,
  Space,
  Tag,
  Popconfirm,
  Card,
  Row,
  Col,
  Checkbox,
  Alert,
  Tooltip,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import {
  fetchDiscountList,
  fetchDiscountDetail,
  createDiscount,
  updateDiscount,
  deleteDiscount,
  updateDiscountStatus,
  fetchAvailableProducts,
  fetchAvailableCategories,
} from '../api/discount';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

const DiscountList = () => {
  const [discounts, setDiscounts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingDiscount, setEditingDiscount] = useState(null);
  const [form] = Form.useForm();
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchParams, setSearchParams] = useState({});
  const [availableProducts, setAvailableProducts] = useState([]);
  const [availableCategories, setAvailableCategories] = useState([]);
  const [selectedApplicableTo, setSelectedApplicableTo] = useState(1);
  const [optionsLoading, setOptionsLoading] = useState(false);

  // 获取折扣列表
  const fetchData = async (params = {}) => {
    setLoading(true);
    try {
      const response = await fetchDiscountList({
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...searchParams,
        ...params,
      });
      
      if (response.success) {
        setDiscounts(response.data.list);
        setPagination(prev => ({
          ...prev,
          total: response.data.total,
          current: response.data.page,
        }));
      }
    } catch (error) {
      message.error('获取折扣列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取可选商品和分类
  const fetchOptions = async () => {
    setOptionsLoading(true);
    try {
      const [productsRes, categoriesRes] = await Promise.all([
        fetchAvailableProducts({ pageSize: 100 }),
        fetchAvailableCategories(),
      ]);

      console.log('商品数据响应:', productsRes);
      console.log('分类数据响应:', categoriesRes);

      if (productsRes && productsRes.success) {
        setAvailableProducts(productsRes.data?.list || []);
      } else {
        console.warn('获取商品数据失败:', productsRes);
        setAvailableProducts([]);
      }

      if (categoriesRes && categoriesRes.success) {
        console.log('分类数据类型:', typeof categoriesRes.data, categoriesRes.data);
        const categories = Array.isArray(categoriesRes.data) ? categoriesRes.data : [];
        setAvailableCategories(categories);
      } else {
        console.warn('获取分类数据失败:', categoriesRes);
        setAvailableCategories([]);
      }
    } catch (error) {
      console.error('获取选项数据失败:', error);
      setAvailableProducts([]);
      setAvailableCategories([]);
      message.error('获取商品和分类数据失败，请检查网络连接');
    } finally {
      setOptionsLoading(false);
    }
  };

  useEffect(() => {
    // 确保状态初始化为数组
    if (!Array.isArray(availableProducts)) {
      setAvailableProducts([]);
    }
    if (!Array.isArray(availableCategories)) {
      setAvailableCategories([]);
    }

    fetchData();
    fetchOptions();
  }, []);

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '折扣名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type) => {
        const typeMap = {
          1: '百分比折扣',
          2: '固定金额折扣',
          3: '满减折扣',
        };
        return typeMap[type] || '未知';
      },
    },
    {
      title: '折扣值',
      dataIndex: 'value',
      key: 'value',
      width: 120,
      render: (value, record) => {
        switch (record.type) {
          case 1:
            return `${value}%`;
          case 2:
            return `¥${value}`;
          case 3:
            return `满¥${record.min_amount || 0}减¥${value}`;
          default:
            return value;
        }
      },
    },
    {
      title: '适用范围',
      dataIndex: 'applicable_to',
      key: 'applicable_to',
      width: 200,
      render: (applicableTo, record) => {
        const rangeMap = {
          1: { text: '全部商品', color: 'blue' },
          2: { text: '指定商品', color: 'green' },
          3: { text: '指定分类', color: 'orange' },
        };
        const config = rangeMap[applicableTo] || { text: '未知', color: 'default' };

        let detail = '';
        if (applicableTo === 2 && record.products && record.products.length > 0) {
          detail = `(${record.products.length}个商品)`;
        } else if (applicableTo === 3 && record.categories && record.categories.length > 0) {
          detail = `(${record.categories.length}个分类)`;
        }

        return (
          <div>
            <Tag color={config.color}>{config.text}</Tag>
            {detail && <span style={{ fontSize: '12px', color: '#666' }}>{detail}</span>}
          </div>
        );
      },
    },
    {
      title: '有效期',
      key: 'validity',
      width: 200,
      render: (_, record) => (
        <div>
          <div>{dayjs(record.start_time).format('YYYY-MM-DD HH:mm')}</div>
          <div>至</div>
          <div>{dayjs(record.end_time).format('YYYY-MM-DD HH:mm')}</div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status, record) => {
        const now = dayjs();
        const startTime = dayjs(record.start_time);
        const endTime = dayjs(record.end_time);
        
        let statusInfo = { color: 'default', text: '未知' };
        
        if (endTime.isBefore(now)) {
          statusInfo = { color: 'default', text: '已过期' };
        } else if (startTime.isAfter(now)) {
          statusInfo = { color: 'orange', text: '未开始' };
        } else if (status === 0) {
          statusInfo = { color: 'red', text: '已禁用' };
        } else if (record.usage_limit && record.used_count >= record.usage_limit) {
          statusInfo = { color: 'default', text: '已用完' };
        } else {
          statusInfo = { color: 'green', text: '进行中' };
        }
        
        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
      },
    },
    {
      title: '使用情况',
      key: 'usage',
      width: 120,
      render: (_, record) => {
        const used = record.used_count || 0;
        const limit = record.usage_limit;
        return limit ? `${used}/${limit}` : `${used}/无限制`;
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space>
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            size="small"
            onClick={() => handleToggleStatus(record)}
          >
            {record.status ? '禁用' : '启用'}
          </Button>
          <Popconfirm
            title="确定要删除这个折扣吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              danger
              size="small"
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 处理添加
  const handleAdd = () => {
    setEditingDiscount(null);
    setSelectedApplicableTo(1); // 默认选择全部商品
    form.resetFields();
    setModalVisible(true);
  };

  // 处理编辑
  const handleEdit = async (record) => {
    try {
      const response = await fetchDiscountDetail(record.id);
      if (response.success) {
        const discount = response.data;
        setEditingDiscount(discount);
        setSelectedApplicableTo(discount.applicable_to || 1);

        // 填充表单
        form.setFieldsValue({
          ...discount,
          time_range: [dayjs(discount.start_time), dayjs(discount.end_time)],
          status: discount.status === 1,
          applicable_product_ids: discount.products?.map(p => p.id) || [],
          applicable_category_ids: discount.categories?.map(c => c.id) || [],
        });

        setModalVisible(true);
      }
    } catch (error) {
      message.error('获取折扣详情失败');
    }
  };

  // 处理删除
  const handleDelete = async (id) => {
    try {
      const response = await deleteDiscount(id);
      if (response.success) {
        message.success('删除成功');
        fetchData();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 处理状态切换
  const handleToggleStatus = async (record) => {
    try {
      const newStatus = record.status ? 0 : 1;
      const response = await updateDiscountStatus(record.id, newStatus);
      if (response.success) {
        message.success(newStatus ? '启用成功' : '禁用成功');
        fetchData();
      } else {
        message.error(response.message || '操作失败');
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 处理表单提交
  const handleSubmit = async (values) => {
    try {
      // 验证适用范围相关数据
      if (values.applicable_to === 2 && (!values.applicable_product_ids || values.applicable_product_ids.length === 0)) {
        message.error('选择指定商品时，必须至少选择一个商品');
        return;
      }

      if (values.applicable_to === 3 && (!values.applicable_category_ids || values.applicable_category_ids.length === 0)) {
        message.error('选择指定分类时，必须至少选择一个分类');
        return;
      }

      const [startTime, endTime] = values.time_range;

      // 验证时间范围
      if (endTime.isBefore(startTime)) {
        message.error('结束时间不能早于开始时间');
        return;
      }

      if (startTime.isBefore(dayjs(), 'minute')) {
        message.error('开始时间不能早于当前时间');
        return;
      }

      const submitData = {
        ...values,
        start_time: startTime.format('YYYY-MM-DD HH:mm:ss'),
        end_time: endTime.format('YYYY-MM-DD HH:mm:ss'),
        status: values.status ? 1 : 0,
        product_ids: values.applicable_product_ids || [],
        category_ids: values.applicable_category_ids || [],
      };

      delete submitData.time_range;
      delete submitData.applicable_product_ids;
      delete submitData.applicable_category_ids;
      
      let response;
      if (editingDiscount) {
        response = await updateDiscount(editingDiscount.id, submitData);
      } else {
        response = await createDiscount(submitData);
      }
      
      if (response.success) {
        message.success(editingDiscount ? '更新成功' : '创建成功');
        setModalVisible(false);
        fetchData();
      } else {
        message.error(response.message || '操作失败');
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  return (
    <div>
      <Card
        title="折扣管理"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            添加折扣
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={discounts}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            onChange: (page, pageSize) => {
              setPagination(prev => ({ ...prev, current: page, pageSize }));
              fetchData({ page, pageSize });
            },
          }}
        />
      </Card>

      <Modal
        title={editingDiscount ? '编辑折扣' : '添加折扣'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        <Alert
          message="使用说明"
          description="选择适用范围后，可以指定具体的商品或分类。指定商品时只对选中商品生效，指定分类时对该分类下所有商品生效。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            status: true,
            priority: 0,
            applicable_to: 1,
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="折扣名称"
                rules={[{ required: true, message: '请输入折扣名称' }]}
              >
                <Input placeholder="请输入折扣名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="折扣类型"
                rules={[{ required: true, message: '请选择折扣类型' }]}
              >
                <Select placeholder="请选择折扣类型">
                  <Option value={1}>百分比折扣</Option>
                  <Option value={2}>固定金额折扣</Option>
                  <Option value={3}>满减折扣</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="value"
                label="折扣值"
                rules={[{ required: true, message: '请输入折扣值' }]}
              >
                <InputNumber
                  min={0}
                  step={0.01}
                  style={{ width: '100%' }}
                  placeholder="请输入折扣值"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="min_amount"
                label="最低消费金额"
              >
                <InputNumber
                  min={0}
                  step={0.01}
                  style={{ width: '100%' }}
                  placeholder="满减折扣的最低消费金额"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="折扣描述"
          >
            <TextArea rows={3} placeholder="请输入折扣描述" />
          </Form.Item>

          <Form.Item
            name="time_range"
            label="有效期"
            rules={[{ required: true, message: '请选择有效期' }]}
          >
            <RangePicker
              showTime
              style={{ width: '100%' }}
              placeholder={['开始时间', '结束时间']}
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="usage_limit"
                label="总使用次数限制"
              >
                <InputNumber
                  min={1}
                  style={{ width: '100%' }}
                  placeholder="留空表示无限制"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="user_limit"
                label="单用户使用次数限制"
              >
                <InputNumber
                  min={1}
                  style={{ width: '100%' }}
                  placeholder="留空表示无限制"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="applicable_to"
            label={
              <span>
                适用范围
                <Tooltip title="全部商品：折扣应用于所有商品；指定商品：只对选中的商品生效；指定分类：对选中分类下的所有商品生效">
                  <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
                </Tooltip>
              </span>
            }
            rules={[{ required: true, message: '请选择适用范围' }]}
          >
            <Select
              placeholder="请选择适用范围"
              onChange={(value) => {
                setSelectedApplicableTo(value);
                // 清空相关字段
                if (value === 1) {
                  form.setFieldsValue({
                    applicable_product_ids: undefined,
                    applicable_category_ids: undefined
                  });
                } else if (value === 2) {
                  form.setFieldsValue({ applicable_category_ids: undefined });
                } else if (value === 3) {
                  form.setFieldsValue({ applicable_product_ids: undefined });
                }
              }}
            >
              <Option value={1}>全部商品</Option>
              <Option value={2}>指定商品</Option>
              <Option value={3}>指定分类</Option>
            </Select>
          </Form.Item>

          {/* 指定商品选择 */}
          {selectedApplicableTo === 2 && (
            <Form.Item
              name="applicable_product_ids"
              label="选择商品"
              rules={[{ required: true, message: '请选择至少一个商品' }]}
            >
              <Select
                mode="multiple"
                placeholder={optionsLoading ? "正在加载商品..." : "请选择要应用折扣的商品"}
                showSearch
                optionFilterProp="children"
                style={{ width: '100%' }}
                loading={optionsLoading}
                notFoundContent={optionsLoading ? "加载中..." : "暂无可选商品"}
              >
                {Array.isArray(availableProducts) ? availableProducts.map(product => (
                  <Option key={product.id} value={product.id}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      {product.image && (
                        <img
                          src={product.image}
                          alt={product.name}
                          style={{
                            width: 20,
                            height: 20,
                            marginRight: 8,
                            objectFit: 'cover',
                            borderRadius: 2
                          }}
                        />
                      )}
                      <span>{product.name}</span>
                      <span style={{ marginLeft: 8, color: '#999', fontSize: '12px' }}>
                        ¥{parseFloat(product.price).toFixed(2)}
                      </span>
                    </div>
                  </Option>
                )) : []}
              </Select>
            </Form.Item>
          )}

          {/* 指定分类选择 */}
          {selectedApplicableTo === 3 && (
            <Form.Item
              name="applicable_category_ids"
              label="选择分类"
              rules={[{ required: true, message: '请选择至少一个分类' }]}
            >
              <Select
                mode="multiple"
                placeholder={optionsLoading ? "正在加载分类..." : "请选择要应用折扣的分类"}
                showSearch
                optionFilterProp="children"
                style={{ width: '100%' }}
                loading={optionsLoading}
                notFoundContent={optionsLoading ? "加载中..." : "暂无可选分类"}
              >
                {Array.isArray(availableCategories) ? availableCategories.map(category => (
                  <Option key={category.id} value={category.id}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      {category.icon && (
                        <img
                          src={category.icon}
                          alt={category.name}
                          style={{
                            width: 20,
                            height: 20,
                            marginRight: 8,
                            objectFit: 'cover',
                            borderRadius: 2
                          }}
                        />
                      )}
                      <span>{category.name}</span>
                    </div>
                  </Option>
                )) : []}
              </Select>
            </Form.Item>
          )}

          <Form.Item
            name="status"
            label="状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                保存
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default DiscountList;
