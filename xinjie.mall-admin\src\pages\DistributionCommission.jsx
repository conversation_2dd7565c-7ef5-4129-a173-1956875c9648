import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  DatePicker,
  Tag,
  Modal,
  message,
  Statistic,
  Row,
  Col
} from 'antd';
import {
  SearchOutlined,
  DollarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { getCommissionList, settleCommission } from '../api/distribution';

const { RangePicker } = DatePicker;
const { Option } = Select;

const DistributionCommission = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [filters, setFilters] = useState({
    distributorId: '',
    status: '',
    startDate: '',
    endDate: ''
  });
  const [statistics, setStatistics] = useState({});

  useEffect(() => {
    fetchData();
  }, [pagination.current, pagination.pageSize, filters]);

  const fetchData = async () => {
    setLoading(true);
    try {
      const params = {
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...filters
      };
      const response = await getCommissionList(params);
      
      if (response.success) {
        setData(response.data.list);
        setPagination(prev => ({
          ...prev,
          total: response.data.pagination.total
        }));
        setStatistics(response.data.statistics);
      }
    } catch (error) {
      message.error('获取佣金记录失败');
    } finally {
      setLoading(false);
    }
  };

  const handleBatchSettle = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要结算的佣金记录');
      return;
    }

    Modal.confirm({
      title: '确认结算',
      content: `确定要结算选中的 ${selectedRowKeys.length} 条佣金记录吗？`,
      onOk: async () => {
        try {
          const response = await settleCommission({ commissionIds: selectedRowKeys });
          if (response.success) {
            message.success(response.message);
            setSelectedRowKeys([]);
            fetchData();
          }
        } catch (error) {
          message.error('批量结算失败');
        }
      }
    });
  };

  const columns = [
    {
      title: '分销商信息',
      key: 'distributor_info',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.nickname}</div>
          <div style={{ color: '#666', fontSize: '12px' }}>
            {record.phone} | {record.distributor_code}
          </div>
        </div>
      )
    },
    {
      title: '订单信息',
      key: 'order_info',
      render: (_, record) => (
        <div>
          <div>{record.order_no}</div>
          <div style={{ color: '#666', fontSize: '12px' }}>
            订单金额：¥{record.order_amount}
          </div>
        </div>
      )
    },
    {
      title: '佣金类型',
      dataIndex: 'commission_type',
      key: 'commission_type',
      render: (type) => (
        <Tag color={type === 1 ? 'blue' : 'green'}>
          {type === 1 ? '一级佣金' : '二级佣金'}
        </Tag>
      )
    },
    {
      title: '佣金金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount) => (
        <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
          ¥{parseFloat(amount).toFixed(2)}
        </span>
      )
    },
    {
      title: '积分奖励',
      dataIndex: 'points',
      key: 'points',
      render: (points) => points > 0 ? `${points}分` : '-'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusConfig = {
          0: { text: '待结算', color: 'orange', icon: <ClockCircleOutlined /> },
          1: { text: '已结算', color: 'green', icon: <CheckCircleOutlined /> },
          2: { text: '已取消', color: 'red', icon: null }
        };
        const config = statusConfig[status];
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        );
      }
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time) => new Date(time).toLocaleString()
    },
    {
      title: '结算时间',
      dataIndex: 'settle_time',
      key: 'settle_time',
      render: (time) => time ? new Date(time).toLocaleString() : '-'
    }
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record) => ({
      disabled: record.status !== 0, // 只能选择待结算的记录
    }),
  };

  return (
    <div>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总佣金"
              value={statistics.total_amount || 0}
              prefix={<DollarOutlined />}
              suffix="元"
              precision={2}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已结算"
              value={statistics.settled_amount || 0}
              prefix={<CheckCircleOutlined />}
              suffix="元"
              precision={2}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待结算"
              value={statistics.pending_amount || 0}
              prefix={<ClockCircleOutlined />}
              suffix="元"
              precision={2}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="记录总数"
              value={statistics.total_records || 0}
              suffix="条"
            />
          </Card>
        </Col>
      </Row>

      <Card>
        {/* 筛选条件 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Input
              placeholder="搜索分销商"
              prefix={<SearchOutlined />}
              allowClear
              onChange={(e) => setFilters(prev => ({ ...prev, distributorId: e.target.value }))}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="佣金状态"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
            >
              <Option value="0">待结算</Option>
              <Option value="1">已结算</Option>
              <Option value="2">已取消</Option>
            </Select>
          </Col>
          <Col span={8}>
            <RangePicker
              style={{ width: '100%' }}
              onChange={(dates, dateStrings) => {
                setFilters(prev => ({
                  ...prev,
                  startDate: dateStrings[0],
                  endDate: dateStrings[1]
                }));
              }}
            />
          </Col>
          <Col span={6}>
            <Space>
              <Button
                type="primary"
                icon={<DollarOutlined />}
                onClick={handleBatchSettle}
                disabled={selectedRowKeys.length === 0}
              >
                批量结算 ({selectedRowKeys.length})
              </Button>
            </Space>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={data}
          rowKey="id"
          loading={loading}
          rowSelection={rowSelection}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
          onChange={(paginationInfo) => {
            setPagination(prev => ({
              ...prev,
              current: paginationInfo.current,
              pageSize: paginationInfo.pageSize
            }));
          }}
        />
      </Card>
    </div>
  );
};

export default DistributionCommission;
