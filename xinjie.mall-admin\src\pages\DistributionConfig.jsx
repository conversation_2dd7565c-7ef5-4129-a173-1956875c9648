import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  InputNumber,
  Button,
  message,
  Divider,
  Space,
  Typography,
  Row,
  Col,
  Switch
} from 'antd';
import {
  SettingOutlined,
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { getDistributionConfig, updateDistributionConfig } from '../api/distribution';

const { Title, Text } = Typography;

const DistributionConfig = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [configData, setConfigData] = useState({
    system: [],
    business: [],
    risk: []
  });

  useEffect(() => {
    fetchConfig();
  }, []);

  const fetchConfig = async () => {
    setLoading(true);
    try {
      const response = await getDistributionConfig();
      if (response.success) {
        setConfigData(response.data);
        
        // 设置表单初始值
        const formValues = {};
        Object.values(response.data).flat().forEach(config => {
          formValues[config.key] = isNaN(config.value) ? config.value : Number(config.value);
        });
        form.setFieldsValue(formValues);
      }
    } catch (error) {
      message.error('获取配置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      setSaving(true);
      
      // 转换为配置数组格式
      const configs = Object.entries(values).map(([key, value]) => ({
        key,
        value: value.toString()
      }));

      const response = await updateDistributionConfig({ configs });
      if (response.success) {
        message.success('配置保存成功');
      }
    } catch (error) {
      message.error('配置保存失败');
    } finally {
      setSaving(false);
    }
  };

  const renderConfigSection = (title, configs, icon) => (
    <Card
      title={
        <Space>
          {icon}
          <span>{title}</span>
        </Space>
      }
      style={{ marginBottom: 16 }}
    >
      <Row gutter={16}>
        {configs && configs.length > 0 ? configs.map(config => (
          <Col span={12} key={config.key} style={{ marginBottom: 16 }}>
            <Form.Item
              name={config.key}
              label={config.desc}
              rules={[
                { required: true, message: `请输入${config.desc}` }
              ]}
            >
              {config.key.includes('rate') ? (
                <InputNumber
                  min={0}
                  max={1}
                  step={0.01}
                  precision={4}
                  style={{ width: '100%' }}
                  formatter={value => `${(value * 100).toFixed(2)}%`}
                  parser={value => value.replace('%', '') / 100}
                />
              ) : config.key.includes('amount') || config.key.includes('commission') ? (
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  addonAfter="元"
                />
              ) : config.key.includes('days') || config.key.includes('count') || config.key.includes('level') ? (
                <InputNumber
                  min={0}
                  precision={0}
                  style={{ width: '100%' }}
                />
              ) : (
                <Input />
              )}
            </Form.Item>
          </Col>
        )) : (
          <Col span={24}>
            <div style={{ textAlign: 'center', color: '#999', padding: '20px 0' }}>
              暂无配置项
            </div>
          </Col>
        )}
      </Row>
    </Card>
  );

  return (
    <div>
      <Card style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Title level={4} style={{ margin: 0 }}>
              <SettingOutlined /> 分销系统配置
            </Title>
            <Text type="secondary">
              配置分销系统的各项参数，修改后立即生效
            </Text>
          </div>
          <Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={fetchConfig}
              loading={loading}
            >
              刷新
            </Button>
            <Button 
              type="primary" 
              icon={<SaveOutlined />}
              onClick={handleSave}
              loading={saving}
            >
              保存配置
            </Button>
          </Space>
        </div>
      </Card>

      <Form
        form={form}
        layout="vertical"
      >
        {/* 系统配置 */}
        {renderConfigSection(
          '系统配置', 
          configData.system, 
          <SettingOutlined style={{ color: '#1890ff' }} />
        )}

        {/* 业务配置 */}
        {renderConfigSection(
          '业务配置', 
          configData.business, 
          <SettingOutlined style={{ color: '#52c41a' }} />
        )}

        {/* 风控配置 */}
        {renderConfigSection(
          '风控配置', 
          configData.risk, 
          <SettingOutlined style={{ color: '#fa8c16' }} />
        )}
      </Form>

      {/* 配置说明 */}
      <Card title="配置说明">
        <div style={{ color: '#666' }}>
          <p><strong>佣金比例：</strong>设置一级和二级分销的佣金比例，建议一级5%，二级2%</p>
          <p><strong>积分倍率：</strong>分销订单的积分奖励倍率，基于订单金额计算</p>
          <p><strong>最小订单金额：</strong>低于此金额的订单不产生分销佣金</p>
          <p><strong>每日分享限制：</strong>防止恶意刷分享，建议设置为20次</p>
          <p><strong>结算延迟天数：</strong>订单完成后延迟结算的天数，用于处理退款等情况</p>
          <p><strong>成为分销商条件：</strong>用户申请成为分销商的最低要求</p>
        </div>
      </Card>
    </div>
  );
};

export default DistributionConfig;
