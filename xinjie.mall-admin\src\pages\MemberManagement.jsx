import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  message,
  Tag,
  Popconfirm,
  InputNumber,
  Switch,
  Tabs,
  Descriptions,
  Badge
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  EditOutlined,
  DeleteOutlined,
  CrownOutlined,
  GiftOutlined
} from '@ant-design/icons';
import {
  fetchMemberLevelList,
  fetchMemberLevelDetail,
  createMemberLevel,
  updateMemberLevel,
  deleteMemberLevel,
  updateMemberLevelStatus,
  fetchMemberBenefitList,
  createMemberBenefit,
  updateMemberBenefit,
  deleteMemberBenefit
} from '../api/member';

const { Option } = Select;
const { TabPane } = Tabs;

const MemberManagement = () => {
  const [loading, setLoading] = useState(false);
  const [levelList, setLevelList] = useState([]);
  const [benefitList, setBenefitList] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [searchParams, setSearchParams] = useState({});
  const [levelModalVisible, setLevelModalVisible] = useState(false);
  const [benefitModalVisible, setBenefitModalVisible] = useState(false);
  const [editingLevel, setEditingLevel] = useState(null);
  const [editingBenefit, setEditingBenefit] = useState(null);
  const [activeTab, setActiveTab] = useState('levels');

  const [levelForm] = Form.useForm();
  const [benefitForm] = Form.useForm();

  // 获取会员等级列表
  const fetchLevelData = async (params = {}) => {
    setLoading(true);
    try {
      const response = await fetchMemberLevelList({
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...searchParams,
        ...params
      });

      if (response.success) {
        setLevelList(response.data.list);
        setPagination(prev => ({
          ...prev,
          total: response.data.total,
          current: response.data.page
        }));
      }
    } catch (error) {
      message.error('获取会员等级列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取会员权益列表
  const fetchBenefitData = async (params = {}) => {
    setLoading(true);
    try {
      const response = await fetchMemberBenefitList({
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...searchParams,
        ...params
      });

      if (response.success) {
        setBenefitList(response.data.list);
        setPagination(prev => ({
          ...prev,
          total: response.data.total,
          current: response.data.page
        }));
      }
    } catch (error) {
      message.error('获取会员权益列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (activeTab === 'levels') {
      fetchLevelData();
    } else if (activeTab === 'benefits') {
      fetchBenefitData();
    }
  }, [activeTab]);

  // 搜索
  const handleSearch = (values) => {
    setSearchParams(values);
    setPagination(prev => ({ ...prev, current: 1 }));
    if (activeTab === 'levels') {
      fetchLevelData({ ...values, page: 1 });
    } else if (activeTab === 'benefits') {
      fetchBenefitData({ ...values, page: 1 });
    }
  };

  // 重置搜索
  const handleReset = () => {
    setSearchParams({});
    setPagination(prev => ({ ...prev, current: 1 }));
    if (activeTab === 'levels') {
      fetchLevelData({ page: 1 });
    } else if (activeTab === 'benefits') {
      fetchBenefitData({ page: 1 });
    }
  };

  // 添加会员等级
  const handleAddLevel = () => {
    setEditingLevel(null);
    levelForm.resetFields();
    setLevelModalVisible(true);
  };

  // 编辑会员等级
  const handleEditLevel = async (record) => {
    try {
      const response = await fetchMemberLevelDetail(record.id);
      if (response.success) {
        setEditingLevel(response.data);
        levelForm.setFieldsValue({
          ...response.data,
          status: response.data.status === 1
        });
        setLevelModalVisible(true);
      }
    } catch (error) {
      message.error('获取会员等级详情失败');
    }
  };

  // 删除会员等级
  const handleDeleteLevel = async (id) => {
    try {
      const response = await deleteMemberLevel(id);
      if (response.success) {
        message.success('删除成功');
        fetchLevelData();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 切换会员等级状态
  const handleToggleLevelStatus = async (record) => {
    try {
      const newStatus = record.status === 1 ? 0 : 1;
      const response = await updateMemberLevelStatus(record.id, newStatus);
      if (response.success) {
        message.success(newStatus ? '启用成功' : '禁用成功');
        fetchLevelData();
      } else {
        message.error(response.message || '操作失败');
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 提交会员等级表单
  const handleLevelSubmit = async (values) => {
    try {
      const submitData = {
        ...values,
        status: values.status ? 1 : 0
      };

      let response;
      if (editingLevel) {
        response = await updateMemberLevel(editingLevel.id, submitData);
      } else {
        response = await createMemberLevel(submitData);
      }

      if (response.success) {
        message.success(editingLevel ? '更新成功' : '创建成功');
        setLevelModalVisible(false);
        fetchLevelData();
      } else {
        message.error(response.message || '操作失败');
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 添加会员权益
  const handleAddBenefit = () => {
    setEditingBenefit(null);
    benefitForm.resetFields();
    setBenefitModalVisible(true);
  };

  // 编辑会员权益
  const handleEditBenefit = (record) => {
    setEditingBenefit(record);
    benefitForm.setFieldsValue({
      ...record,
      status: record.status === 1
    });
    setBenefitModalVisible(true);
  };

  // 删除会员权益
  const handleDeleteBenefit = async (id) => {
    try {
      const response = await deleteMemberBenefit(id);
      if (response.success) {
        message.success('删除成功');
        fetchBenefitData();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 提交会员权益表单
  const handleBenefitSubmit = async (values) => {
    try {
      const submitData = {
        ...values,
        status: values.status ? 1 : 0
      };

      let response;
      if (editingBenefit) {
        response = await updateMemberBenefit(editingBenefit.id, submitData);
      } else {
        response = await createMemberBenefit(submitData);
      }

      if (response.success) {
        message.success(editingBenefit ? '更新成功' : '创建成功');
        setBenefitModalVisible(false);
        fetchBenefitData();
      } else {
        message.error(response.message || '操作失败');
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 会员等级表格列
  const levelColumns = [
    {
      title: '等级代码',
      dataIndex: 'level_code',
      key: 'level_code',
      width: 100
    },
    {
      title: '等级名称',
      dataIndex: 'level_name',
      key: 'level_name',
      render: (text, record) => (
        <Space>
          <CrownOutlined style={{ color: '#faad14' }} />
          <span>{text}</span>
        </Space>
      )
    },
    {
      title: '升级条件',
      key: 'upgrade_condition',
      render: (_, record) => (
        <div>
          <div>积分: {record.min_points}</div>
          <div>消费: ¥{parseFloat(record.min_amount).toFixed(2)}</div>
        </div>
      )
    },
    {
      title: '折扣率',
      dataIndex: 'discount_rate',
      key: 'discount_rate',
      render: (rate) => `${(parseFloat(rate) * 100).toFixed(0)}%`
    },
    {
      title: '积分倍率',
      dataIndex: 'points_ratio',
      key: 'points_ratio',
      render: (ratio) => `${parseFloat(ratio)}倍`
    },
    {
      title: '免邮门槛',
      dataIndex: 'free_shipping_threshold',
      key: 'free_shipping_threshold',
      render: (threshold) => threshold > 0 ? `¥${parseFloat(threshold).toFixed(2)}` : '全场免邮'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => (
        <Switch
          checked={status === 1}
          onChange={() => handleToggleLevelStatus(record)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      )
    },
    {
      title: '排序',
      dataIndex: 'sort_order',
      key: 'sort_order',
      width: 80
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEditLevel(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除这个会员等级吗？"
            onConfirm={() => handleDeleteLevel(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div>
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="会员等级" key="levels">
            {/* 搜索表单 */}
            <Form
              layout="inline"
              onFinish={handleSearch}
              style={{ marginBottom: 16 }}
            >
              <Form.Item name="levelName">
                <Input placeholder="等级名称" style={{ width: 200 }} />
              </Form.Item>
              <Form.Item name="status">
                <Select placeholder="状态" style={{ width: 120 }} allowClear>
                  <Option value={1}>启用</Option>
                  <Option value={0}>禁用</Option>
                </Select>
              </Form.Item>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                    搜索
                  </Button>
                  <Button onClick={handleReset} icon={<ReloadOutlined />}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>

            {/* 操作按钮 */}
            <div style={{ marginBottom: 16 }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddLevel}
              >
                添加等级
              </Button>
            </div>

            {/* 会员等级表格 */}
            <Table
              columns={levelColumns}
              dataSource={levelList}
              rowKey="id"
              loading={loading}
              pagination={{
                ...pagination,
                onChange: (page, pageSize) => {
                  setPagination(prev => ({ ...prev, current: page, pageSize }));
                  fetchLevelData({ page, pageSize });
                },
              }}
            />
          </TabPane>

          <TabPane tab="会员权益" key="benefits">
            {/* 权益管理内容将在下一部分添加 */}
            <div>会员权益管理功能开发中...</div>
          </TabPane>
        </Tabs>
      </Card>

      {/* 会员等级模态框 */}
      <Modal
        title={editingLevel ? '编辑会员等级' : '添加会员等级'}
        open={levelModalVisible}
        onCancel={() => setLevelModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={levelForm}
          layout="vertical"
          onFinish={handleLevelSubmit}
          initialValues={{
            status: true,
            sort_order: 0,
            discount_rate: 1.00,
            points_ratio: 1.00,
            free_shipping_threshold: 0,
            birthday_discount: 0
          }}
        >
          <Form.Item
            name="level_code"
            label="等级代码"
            rules={[{ required: true, message: '请输入等级代码' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={1}
              placeholder="请输入等级代码（数字）"
              disabled={!!editingLevel}
            />
          </Form.Item>

          <Form.Item
            name="level_name"
            label="等级名称"
            rules={[{ required: true, message: '请输入等级名称' }]}
          >
            <Input placeholder="请输入等级名称" />
          </Form.Item>

          <Form.Item
            name="level_icon"
            label="等级图标"
          >
            <Input placeholder="请输入等级图标URL（可选）" />
          </Form.Item>

          <Form.Item
            name="min_points"
            label="升级所需积分"
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              placeholder="请输入升级所需最低积分"
            />
          </Form.Item>

          <Form.Item
            name="min_amount"
            label="升级所需消费金额"
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={2}
              placeholder="请输入升级所需最低消费金额"
            />
          </Form.Item>

          <Form.Item
            name="discount_rate"
            label="折扣率"
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0.1}
              max={1}
              step={0.01}
              precision={2}
              placeholder="请输入折扣率（0.95表示95折）"
            />
          </Form.Item>

          <Form.Item
            name="points_ratio"
            label="积分倍率"
          >
            <InputNumber
              style={{ width: '100%' }}
              min={1}
              step={0.1}
              precision={1}
              placeholder="请输入积分倍率（1.5表示1.5倍积分）"
            />
          </Form.Item>

          <Form.Item
            name="free_shipping_threshold"
            label="免邮门槛"
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={2}
              placeholder="请输入免邮门槛（0表示全场免邮）"
            />
          </Form.Item>

          <Form.Item
            name="birthday_discount"
            label="生日折扣"
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              max={1}
              step={0.01}
              precision={2}
              placeholder="请输入生日折扣（0.1表示额外9折）"
            />
          </Form.Item>

          <Form.Item
            name="sort_order"
            label="排序"
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              placeholder="请输入排序值"
            />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingLevel ? '更新' : '创建'}
              </Button>
              <Button onClick={() => setLevelModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default MemberManagement;
