import React, { useState, useEffect } from 'react';
import { Card, Button, Table, message, Modal, Progress, Tag, Space, Typography } from 'antd';
import { ReloadOutlined, ToolOutlined, CheckCircleOutlined, ExclamationCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import axios from 'axios';

const { Title, Text } = Typography;

const ProductImageManager = () => {
  const [loading, setLoading] = useState(false);
  const [validationData, setValidationData] = useState(null);
  const [fixing, setFixing] = useState(false);

  // 验证商品图片
  const validateImages = async () => {
    setLoading(true);
    try {
      const response = await axios.post('/api/admin/product/validate-images');
      if (response.data.success) {
        setValidationData(response.data.data);
        message.success('商品图片验证完成');
      } else {
        message.error(response.data.message || '验证失败');
      }
    } catch (error) {
      console.error('验证图片失败:', error);
      message.error('验证图片失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 批量修复图片
  const fixImages = async () => {
    Modal.confirm({
      title: '确认批量修复',
      content: '这将修复所有商品图片路径问题，为缺失图片的商品设置默认图片。确定要继续吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        setFixing(true);
        try {
          const response = await axios.post('/api/admin/product/fix-images');
          if (response.data.success) {
            message.success(response.data.message);
            // 重新验证
            await validateImages();
          } else {
            message.error(response.data.message || '修复失败');
          }
        } catch (error) {
          console.error('修复图片失败:', error);
          message.error('修复图片失败: ' + (error.response?.data?.message || error.message));
        } finally {
          setFixing(false);
        }
      }
    });
  };

  // 计算统计信息
  const getStatistics = () => {
    if (!validationData) return null;
    
    const { total, valid, invalid, missing, fixed } = validationData;
    const totalIssues = invalid + missing;
    const healthRate = total > 0 ? ((valid / total) * 100).toFixed(1) : 0;
    
    return {
      total,
      valid,
      invalid,
      missing,
      fixed,
      totalIssues,
      healthRate
    };
  };

  const stats = getStatistics();

  // 表格列定义
  const columns = [
    {
      title: '商品ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '商品名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '图片路径',
      dataIndex: 'path',
      key: 'path',
      ellipsis: true,
      render: (text, record) => {
        const path = record.path || record.original;
        return (
          <Text code style={{ fontSize: '12px' }}>
            {path || '无图片'}
          </Text>
        );
      }
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      render: (_, record) => {
        if (record.reason) {
          return <Tag color="red" icon={<CloseCircleOutlined />}>无效</Tag>;
        }
        if (record.fixed) {
          return <Tag color="orange" icon={<ExclamationCircleOutlined />}>可修复</Tag>;
        }
        return <Tag color="green" icon={<CheckCircleOutlined />}>正常</Tag>;
      }
    },
    {
      title: '问题描述',
      key: 'issue',
      render: (_, record) => {
        if (record.reason) {
          return <Text type="danger">{record.reason}</Text>;
        }
        if (record.fixed) {
          return (
            <Space direction="vertical" size="small">
              <Text type="warning">路径格式不正确</Text>
              <Text type="secondary">可修复为: {record.fixed}</Text>
            </Space>
          );
        }
        return <Text type="success">图片正常</Text>;
      }
    }
  ];

  // 获取表格数据
  const getTableData = () => {
    if (!validationData?.details) return [];
    
    const { valid, invalid, missing, fixed } = validationData.details;
    return [
      ...valid.map(item => ({ ...item, key: `valid_${item.id}` })),
      ...invalid.map(item => ({ ...item, key: `invalid_${item.id}` })),
      ...missing.map(item => ({ ...item, key: `missing_${item.id}` })),
      ...fixed.map(item => ({ ...item, key: `fixed_${item.id}` }))
    ];
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
          <Title level={3} style={{ margin: 0 }}>
            <ToolOutlined style={{ marginRight: '8px' }} />
            商品图片管理
          </Title>
          <Space>
            <Button 
              type="primary" 
              icon={<ReloadOutlined />}
              loading={loading}
              onClick={validateImages}
            >
              验证图片
            </Button>
            <Button 
              type="default" 
              icon={<ToolOutlined />}
              loading={fixing}
              disabled={!validationData || (stats?.totalIssues === 0)}
              onClick={fixImages}
            >
              批量修复
            </Button>
          </Space>
        </div>

        {stats && (
          <div style={{ marginBottom: '24px' }}>
            <Card size="small" style={{ marginBottom: '16px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div>
                  <Text strong>图片健康度: </Text>
                  <Text type={stats.healthRate >= 90 ? 'success' : stats.healthRate >= 70 ? 'warning' : 'danger'}>
                    {stats.healthRate}%
                  </Text>
                </div>
                <Progress 
                  percent={parseFloat(stats.healthRate)} 
                  status={stats.healthRate >= 90 ? 'success' : stats.healthRate >= 70 ? 'normal' : 'exception'}
                  style={{ width: '200px' }}
                />
              </div>
            </Card>

            <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
              <Card size="small" style={{ flex: 1, minWidth: '120px' }}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                    {stats.valid}
                  </div>
                  <Text type="secondary">正常图片</Text>
                </div>
              </Card>
              <Card size="small" style={{ flex: 1, minWidth: '120px' }}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#faad14' }}>
                    {stats.fixed}
                  </div>
                  <Text type="secondary">可修复</Text>
                </div>
              </Card>
              <Card size="small" style={{ flex: 1, minWidth: '120px' }}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ff4d4f' }}>
                    {stats.totalIssues}
                  </div>
                  <Text type="secondary">需要处理</Text>
                </div>
              </Card>
              <Card size="small" style={{ flex: 1, minWidth: '120px' }}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                    {stats.total}
                  </div>
                  <Text type="secondary">总商品数</Text>
                </div>
              </Card>
            </div>
          </div>
        )}

        {validationData && (
          <Table
            columns={columns}
            dataSource={getTableData()}
            pagination={{
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
            }}
            scroll={{ x: 800 }}
            size="small"
          />
        )}

        {!validationData && (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <Text type="secondary">点击"验证图片"开始检查商品图片状态</Text>
          </div>
        )}
      </Card>
    </div>
  );
};

export default ProductImageManager; 