import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Upload,
  message,
  Popconfirm,
  Image,
  Space,
  Tag,
  Select,
  InputNumber,
  Switch,
  Checkbox
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  UploadOutlined,
  ImportOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import axios from 'axios';

const { TextArea } = Input;
const { Option } = Select;

const ProductList = () => {
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingProduct, setEditingProduct] = useState(null);
  const [form] = Form.useForm();
  const [uploading, setUploading] = useState(false);
  
  // 批量操作相关状态
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [batchDeleteLoading, setBatchDeleteLoading] = useState(false);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importLoading, setImportLoading] = useState(false);

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条记录`,
  });

  // 获取商品列表
  const fetchProducts = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/admin/product');
      if (response.data.success) {
        const productList = response.data.data;
        setProducts(productList);

        // 更新分页总数
        setPagination(prev => ({
          ...prev,
          total: productList.length
        }));
      }
    } catch (error) {
      message.error('获取商品列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      const response = await axios.get('/api/admin/category');
      if (response.data.success) {
        setCategories(response.data.data);
      }
    } catch (error) {
      console.error('获取分类列表失败:', error);
    }
  };

  useEffect(() => {
    fetchProducts();
    fetchCategories();
    
    // 清理函数
    return () => {
      // 清理表单实例
      form.resetFields();
    };
  }, []);

  // 上传图片
  const uploadImage = async (file) => {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await axios.post('/api/admin/product/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.code === 0) {
        return response.data.data.url;
      } else {
        throw new Error(response.data.msg);
      }
    } catch (error) {
      message.error('图片上传失败: ' + error.message);
      return null;
    }
  };

  // 处理表单提交
  const handleSubmit = async (values) => {
    setUploading(true);
    try {
      console.log('表单提交值:', values);
      let imageUrl = values.imageUrl;

      // 如果有新上传的文件
      if (values.image && values.image.length > 0) {
        const imageFile = values.image[0];
        console.log('图片文件信息:', imageFile);
        
        // 检查上传状态和获取URL
        if (imageFile.status === 'done' && imageFile.response && imageFile.response.success) {
          // 自动上传已完成，从响应中获取URL
          imageUrl = imageFile.response.data.url;
        } else if (imageFile.url) {
          // 如果已经有URL，直接使用
          imageUrl = imageFile.url;
        } else if (imageFile.originFileObj) {
          // 如果还没有上传，手动上传
          const uploadedUrl = await uploadImage(imageFile.originFileObj);
          if (!uploadedUrl) {
            setUploading(false);
            return;
          }
          imageUrl = uploadedUrl;
        }
      }

      console.log('最终图片URL:', imageUrl);

      // 验证必填字段
      if (!values.name || !values.price || !values.categoryId || !imageUrl) {
        const missingFields = [];
        if (!values.name) missingFields.push('商品名称');
        if (!values.price) missingFields.push('价格');
        if (!values.categoryId) missingFields.push('分类');
        if (!imageUrl) missingFields.push('图片');
        
        console.log('验证失败:', { name: values.name, price: values.price, categoryId: values.categoryId, imageUrl });
        message.error(`请填写完整信息：${missingFields.join('、')}`);
        setUploading(false);
        return;
      }

      const productData = {
        name: values.name,
        description: values.description,
        price: values.price,
        original_price: values.originalPrice || values.price,
        stock: values.stock,
        category_id: values.categoryId,
        image_url: imageUrl,
        images: values.images || [],
        status: values.status ? 1 : 0,
        is_hot: values.isHot ? 1 : 0,
        is_recommend: values.isRecommend ? 1 : 0,
        sort_order: values.sortOrder || 0
      };

      if (editingProduct) {
        // 编辑
        await axios.put(`/api/admin/product/${editingProduct.id}`, productData);
        message.success('商品更新成功');
      } else {
        // 新增
        await axios.post('/api/admin/product', productData);
        message.success('商品添加成功');

        // 新增商品后，跳转到第一页显示新添加的商品
        setPagination(prev => ({
          ...prev,
          current: 1
        }));
      }

      setModalVisible(false);
      form.resetFields();
      setEditingProduct(null);
      fetchProducts();
    } catch (error) {
      message.error('操作失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setUploading(false);
    }
  };

  // 删除商品
  const handleDelete = async (id) => {
    try {
      await axios.delete(`/api/admin/product/${id}`);
      message.success('删除成功');
      fetchProducts();
    } catch (error) {
      message.error('删除失败: ' + (error.response?.data?.message || error.message));
    }
  };

  // 编辑商品
  const handleEdit = (record) => {
    setEditingProduct(record);
    form.setFieldsValue({
      name: record.name,
      description: record.description,
      price: record.price,
      originalPrice: record.original_price,
      stock: record.stock,
      categoryId: record.category_id,
      imageUrl: record.image_url,
      status: record.status === 1,
      isHot: record.is_hot === 1,
      isRecommend: record.is_recommend === 1,
      sortOrder: record.sort_order
    });
    setModalVisible(true);
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的商品');
      return;
    }

    try {
      setBatchDeleteLoading(true);
      await axios.post('/api/admin/product/batch-delete', {
        ids: selectedRowKeys
      });
      message.success(`成功删除 ${selectedRowKeys.length} 个商品`);
      setSelectedRowKeys([]);
      fetchProducts();
    } catch (error) {
      message.error('批量删除失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setBatchDeleteLoading(false);
    }
  };

  // 批量导入
  const handleBatchImport = async (file) => {
    const formData = new FormData();
    formData.append('file', file);
    
    try {
      setImportLoading(true);
      const response = await axios.post('/api/admin/product/batch-import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      if (response.data.success) {
        message.success(`成功导入 ${response.data.data.count} 个商品`);
        setImportModalVisible(false);
        fetchProducts();
      } else {
        throw new Error(response.data.message);
      }
    } catch (error) {
      message.error('批量导入失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setImportLoading(false);
    }
  };

  // 下载模板
  const downloadTemplate = () => {
    const template = [
      ['商品名称', '商品描述', '价格', '原价', '库存', '分类ID', '状态', '热门', '推荐', '排序'],
      ['示例商品', '商品描述', '99.00', '129.00', '100', '1', '1', '0', '0', '0']
    ];
    
    const csvContent = template.map(row => row.join(',')).join('\n');
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', '商品导入模板.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 表格列定义 - 优化后的布局，突出关键信息
  const columns = [
    {
      title: '商品信息',
      key: 'product_info',
      width: 280,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <Image
            width={50}
            height={50}
            src={record.image_url}
            fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
            style={{ objectFit: 'cover', borderRadius: '6px', border: '1px solid #f0f0f0' }}
          />
          <div style={{ flex: 1, minWidth: 0 }}>
            <div
              style={{
                fontWeight: '600',
                fontSize: '14px',
                color: '#262626',
                marginBottom: '4px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
              title={record.name}
            >
              {record.name}
            </div>
            <div style={{
              fontSize: '12px',
              color: '#8c8c8c',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <span>{record.category_name}</span>
              {record.brand && (
                <>
                  <span>•</span>
                  <span>{record.brand}</span>
                </>
              )}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '价格信息',
      key: 'price_info',
      width: 120,
      align: 'center',
      render: (_, record) => (
        <div style={{ textAlign: 'center' }}>
          <div style={{
            fontSize: '16px',
            fontWeight: '600',
            color: '#ff4d4f',
            marginBottom: '2px'
          }}>
            ¥{record.price}
          </div>
          {record.original_price && record.original_price > record.price && (
            <div style={{
              fontSize: '12px',
              color: '#8c8c8c',
              textDecoration: 'line-through'
            }}>
              ¥{record.original_price}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '库存',
      dataIndex: 'stock',
      key: 'stock',
      width: 80,
      align: 'center',
      render: (stock) => (
        <span style={{
          color: stock > 10 ? '#52c41a' : stock > 0 ? '#faad14' : '#ff4d4f',
          fontWeight: '600'
        }}>
          {stock}
        </span>
      ),
    },
    {
      title: '销量',
      dataIndex: 'sales',
      key: 'sales',
      width: 80,
      align: 'center',
      render: (sales) => (
        <span style={{ fontWeight: '500' }}>
          {sales || 0}
        </span>
      ),
    },
    {
      title: '状态',
      key: 'status_info',
      width: 120,
      align: 'center',
      render: (_, record) => (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px', alignItems: 'center' }}>
          <Tag color={record.status === 1 ? 'green' : 'red'} size="small">
            {record.status === 1 ? '上架' : '下架'}
          </Tag>
          <div style={{ display: 'flex', gap: '4px' }}>
            {record.is_hot === 1 && (
              <Tag color="orange" size="small">热门</Tag>
            )}
            {record.is_recommend === 1 && (
              <Tag color="blue" size="small">推荐</Tag>
            )}
          </div>
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 140,
      align: 'center',
      render: (date) => (
        <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
          {new Date(date).toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
          })}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      align: 'center',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            style={{ padding: '4px 8px' }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个商品吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
              style={{ padding: '4px 8px' }}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card
        title="商品管理"
        extra={
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingProduct(null);
                form.resetFields();
                setModalVisible(true);
              }}
            >
              添加商品
            </Button>
            <Button
              icon={<ImportOutlined />}
              onClick={() => setImportModalVisible(true)}
            >
              批量导入
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={downloadTemplate}
            >
              下载模板
            </Button>
            <Popconfirm
              title={`确定要删除选中的 ${selectedRowKeys.length} 个商品吗？`}
              onConfirm={handleBatchDelete}
              okText="确定"
              cancelText="取消"
              disabled={selectedRowKeys.length === 0}
            >
              <Button
                danger
                loading={batchDeleteLoading}
                disabled={selectedRowKeys.length === 0}
              >
                批量删除 ({selectedRowKeys.length})
              </Button>
            </Popconfirm>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={products}
          rowKey="id"
          loading={loading}
          rowSelection={{
            selectedRowKeys,
            onChange: (selectedRowKeys, selectedRows) => {
              setSelectedRowKeys(selectedRowKeys);
            },
            getCheckboxProps: (record) => ({
              disabled: false,
            }),
          }}
          pagination={{
            ...pagination,
            onChange: (page, pageSize) => {
              setPagination(prev => ({
                ...prev,
                current: page,
                pageSize: pageSize
              }));
            },
            onShowSizeChange: (current, size) => {
              setPagination(prev => ({
                ...prev,
                current: 1,
                pageSize: size
              }));
            }
          }}
        />
      </Card>

      <Modal
        title={editingProduct ? '编辑商品' : '添加商品'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingProduct(null);
          form.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            status: true,
            isHot: false,
            isRecommend: false,
            sortOrder: 0
          }}
        >
          <Form.Item
            name="name"
            label="商品名称"
            rules={[{ required: true, message: '请输入商品名称' }]}
          >
            <Input placeholder="请输入商品名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="商品描述"
          >
            <TextArea
              rows={4}
              placeholder="请输入商品描述"
            />
          </Form.Item>

          <Form.Item
            name="categoryId"
            label="商品分类"
            rules={[{ required: true, message: '请选择商品分类' }]}
          >
            <Select placeholder="请选择商品分类">
              {categories.map(category => (
                <Option key={category.id} value={category.id}>
                  {category.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="image"
            label="商品图片"
            rules={[
              {
                required: !editingProduct,
                message: '请上传商品图片'
              }
            ]}
          >
            <Upload
              listType="picture-card"
              maxCount={1}
              accept="image/*"
              action="/api/admin/product/upload"
              name="file"
              headers={{
                'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
                'X-Requested-With': 'XMLHttpRequest'
              }}
              withCredentials={true}
              onChange={(info) => {
                console.log('Upload onChange:', info);
                if (info.file.status === 'uploading') {
                  console.log('文件上传中...');
                } else if (info.file.status === 'done') {
                  console.log('文件上传完成:', info.file);
                  // 设置图片URL到表单
                  if (info.file.response && info.file.response.code === 0) {
                    const imageUrl = info.file.response.data.url;
                    console.log('设置图片URL:', imageUrl);
                    // 设置到隐藏字段
                    form.setFieldsValue({ 
                      imageUrl: imageUrl,
                      image: [{
                        ...info.file,
                        url: imageUrl,
                        status: 'done',
                        response: info.file.response
                      }]
                    });
                    message.success('图片上传成功');
                  }
                } else if (info.file.status === 'error') {
                  console.log('文件上传失败:', info.file);
                  message.error('图片上传失败');
                }
              }}
              beforeUpload={(file) => {
                console.log('准备上传文件:', file);
                return true; // 允许上传
              }}
            >
              <div>
                <UploadOutlined />
                <div style={{ marginTop: 8 }}>上传图片</div>
              </div>
            </Upload>
          </Form.Item>

          {/* 隐藏字段存储图片URL */}
          <Form.Item name="imageUrl" hidden>
            <Input />
          </Form.Item>

          {editingProduct && (
            <Form.Item
              name="imageUrl"
              label="当前图片"
            >
              <Input disabled />
            </Form.Item>
          )}

          <Form.Item
            name="price"
            label="售价"
            rules={[{ required: true, message: '请输入售价' }]}
          >
            <InputNumber
              min={0}
              precision={2}
              style={{ width: '100%' }}
              placeholder="请输入售价"
            />
          </Form.Item>

          <Form.Item
            name="originalPrice"
            label="原价"
          >
            <InputNumber
              min={0}
              precision={2}
              style={{ width: '100%' }}
              placeholder="请输入原价"
            />
          </Form.Item>

          <Form.Item
            name="stock"
            label="库存"
            rules={[{ required: true, message: '请输入库存' }]}
          >
            <InputNumber
              min={0}
              style={{ width: '100%' }}
              placeholder="请输入库存"
            />
          </Form.Item>

          <Form.Item
            name="sortOrder"
            label="排序"
          >
            <InputNumber
              min={0}
              style={{ width: '100%' }}
              placeholder="数字越小越靠前"
            />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="上架" unCheckedChildren="下架" />
          </Form.Item>

          <Form.Item
            name="isHot"
            label="热门商品"
            valuePropName="checked"
          >
            <Switch checkedChildren="是" unCheckedChildren="否" />
          </Form.Item>

          <Form.Item
            name="isRecommend"
            label="推荐商品"
            valuePropName="checked"
          >
            <Switch checkedChildren="是" unCheckedChildren="否" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={uploading}>
                {editingProduct ? '更新' : '添加'}
              </Button>
              <Button
                onClick={() => {
                  setModalVisible(false);
                  setEditingProduct(null);
                  form.resetFields();
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 批量导入Modal */}
      <Modal
        title="批量导入商品"
        open={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        footer={null}
        width={600}
      >
        <div style={{ marginBottom: 16 }}>
          <p>请按照以下格式准备CSV文件：</p>
          <ul>
            <li>第一行为表头：商品名称,商品描述,价格,原价,库存,分类ID,状态,热门,推荐,排序</li>
            <li>状态、热门、推荐：1表示是，0表示否</li>
            <li>分类ID：请先查看分类管理中的分类ID</li>
            <li>价格和原价：支持小数，如99.00</li>
          </ul>
          <Button 
            type="link" 
            icon={<DownloadOutlined />} 
            onClick={downloadTemplate}
            style={{ padding: 0 }}
          >
            下载CSV模板
          </Button>
        </div>
        
        <Upload
          accept=".csv"
          beforeUpload={(file) => {
            handleBatchImport(file);
            return false; // 阻止自动上传
          }}
          showUploadList={false}
        >
          <Button 
            type="primary" 
            icon={<UploadOutlined />} 
            loading={importLoading}
            block
          >
            选择CSV文件并导入
          </Button>
        </Upload>
      </Modal>
    </div>
);
};

export default ProductList;
