import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  message,
  Tag,
  Popconfirm,
  Row,
  Col,
  Statistic,
  InputNumber,
  Tabs
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  DollarOutlined,
  UserOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import {
  fetchRechargeList,
  fetchRechargeDetail,
  adminRecharge,
  updateRecharge,
  deleteRecharge,
  updatePaymentStatus,
  fetchRechargeStatistics,
  fetchBalanceRecords,
  adjustBalance
} from '../api/recharge';
import { fetchUserList } from '../api/user';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

const RechargeManagement = () => {
  const [loading, setLoading] = useState(false);
  const [rechargeList, setRechargeList] = useState([]);
  const [balanceRecords, setBalanceRecords] = useState([]);
  const [statistics, setStatistics] = useState({
    total_count: 0,
    total_amount: 0,
    today_count: 0,
    today_amount: 0
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [searchParams, setSearchParams] = useState({});
  const [modalVisible, setModalVisible] = useState(false);
  const [adjustModalVisible, setAdjustModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [userList, setUserList] = useState([]);
  const [activeTab, setActiveTab] = useState('recharge');

  const [form] = Form.useForm();
  const [adjustForm] = Form.useForm();

  // 获取充值记录列表
  const fetchData = async (params = {}) => {
    setLoading(true);
    try {
      const response = await fetchRechargeList({
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...searchParams,
        ...params
      });

      if (response.success) {
        setRechargeList(response.data.list);
        setPagination(prev => ({
          ...prev,
          total: response.data.total,
          current: response.data.page
        }));
      }
    } catch (error) {
      message.error('获取充值记录失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取余额变动记录
  const fetchBalanceData = async (params = {}) => {
    setLoading(true);
    try {
      const response = await fetchBalanceRecords({
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...searchParams,
        ...params
      });

      if (response.success) {
        setBalanceRecords(response.data.list);
        setPagination(prev => ({
          ...prev,
          total: response.data.total,
          current: response.data.page
        }));
      }
    } catch (error) {
      message.error('获取余额记录失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取统计数据
  const fetchStats = async () => {
    try {
      const response = await fetchRechargeStatistics();
      if (response.success) {
        setStatistics(response.data);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  };

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      const response = await fetchUserList({ pageSize: 1000 });
      if (response.success) {
        setUserList(response.data.list || []);
      }
    } catch (error) {
      console.error('获取用户列表失败:', error);
    }
  };

  useEffect(() => {
    if (activeTab === 'recharge') {
      fetchData();
    } else if (activeTab === 'balance') {
      fetchBalanceData();
    }
    fetchStats();
    fetchUsers();
  }, [activeTab]);

  // 搜索
  const handleSearch = (values) => {
    setSearchParams(values);
    setPagination(prev => ({ ...prev, current: 1 }));
    if (activeTab === 'recharge') {
      fetchData({ ...values, page: 1 });
    } else if (activeTab === 'balance') {
      fetchBalanceData({ ...values, page: 1 });
    }
  };

  // 重置搜索
  const handleReset = () => {
    setSearchParams({});
    setPagination(prev => ({ ...prev, current: 1 }));
    if (activeTab === 'recharge') {
      fetchData({ page: 1 });
    } else if (activeTab === 'balance') {
      fetchBalanceData({ page: 1 });
    }
  };

  // 添加充值
  const handleAdd = () => {
    setEditingRecord(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 编辑充值记录
  const handleEdit = async (record) => {
    try {
      const response = await fetchRechargeDetail(record.id);
      if (response.success) {
        setEditingRecord(response.data);
        form.setFieldsValue({
          ...response.data,
          user_id: response.data.user_id
        });
        setModalVisible(true);
      }
    } catch (error) {
      message.error('获取充值记录详情失败');
    }
  };

  // 删除充值记录
  const handleDelete = async (id) => {
    try {
      const response = await deleteRecharge(id);
      if (response.success) {
        message.success('删除成功');
        fetchData();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 提交表单
  const handleSubmit = async (values) => {
    try {
      let response;
      if (editingRecord) {
        response = await updateRecharge(editingRecord.id, values);
      } else {
        response = await adminRecharge(values);
      }

      if (response.success) {
        message.success(editingRecord ? '更新成功' : '充值成功');
        setModalVisible(false);
        fetchData();
        fetchStats();
      } else {
        message.error(response.message || '操作失败');
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 调整余额
  const handleAdjustBalance = () => {
    adjustForm.resetFields();
    setAdjustModalVisible(true);
  };

  // 提交余额调整
  const handleAdjustSubmit = async (values) => {
    try {
      const response = await adjustBalance(values);
      if (response.success) {
        message.success('余额调整成功');
        setAdjustModalVisible(false);
        if (activeTab === 'balance') {
          fetchBalanceData();
        }
        fetchStats();
      } else {
        message.error(response.message || '调整失败');
      }
    } catch (error) {
      message.error('调整失败');
    }
  };

  // 充值记录表格列
  const rechargeColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80
    },
    {
      title: '用户',
      dataIndex: 'nickname',
      key: 'nickname',
      render: (text, record) => (
        <div>
          <div>{text || '未知用户'}</div>
          <div style={{ fontSize: '12px', color: '#999' }}>{record.phone}</div>
        </div>
      )
    },
    {
      title: '订单号',
      dataIndex: 'order_no',
      key: 'order_no'
    },
    {
      title: '充值金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount) => `¥${parseFloat(amount).toFixed(2)}`
    },
    {
      title: '赠送金额',
      dataIndex: 'bonus_amount',
      key: 'bonus_amount',
      render: (amount) => amount > 0 ? `¥${parseFloat(amount).toFixed(2)}` : '-'
    },
    {
      title: '到账金额',
      dataIndex: 'total_amount',
      key: 'total_amount',
      render: (amount) => `¥${parseFloat(amount).toFixed(2)}`
    },
    {
      title: '支付方式',
      dataIndex: 'payment_method_text',
      key: 'payment_method_text'
    },
    {
      title: '支付状态',
      dataIndex: 'payment_status',
      key: 'payment_status',
      render: (status) => {
        const statusMap = {
          0: { color: 'orange', text: '待支付' },
          1: { color: 'green', text: '已支付' },
          2: { color: 'red', text: '支付失败' },
          3: { color: 'blue', text: '已退款' }
        };
        const config = statusMap[status] || { color: 'default', text: '未知' };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time) => new Date(time).toLocaleString()
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" size="small" onClick={() => handleEdit(record)}>
            编辑
          </Button>
          {record.payment_status === 0 && (
            <Popconfirm
              title="确定删除这条记录吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" size="small" danger>
                删除
              </Button>
            </Popconfirm>
          )}
        </Space>
      )
    }
  ];

  return (
    <div>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总充值次数"
              value={statistics.total_count || 0}
              prefix={<DollarOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总充值金额"
              value={statistics.total_amount || 0}
              precision={2}
              prefix="¥"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总赠送金额"
              value={statistics.total_bonus || 0}
              precision={2}
              prefix="¥"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均充值金额"
              value={statistics.avg_amount || 0}
              precision={2}
              prefix="¥"
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="充值记录" key="recharge">
            {/* 搜索表单 */}
            <Form
              layout="inline"
              onFinish={handleSearch}
              style={{ marginBottom: 16 }}
            >
              <Form.Item name="userId">
                <Select
                  placeholder="选择用户"
                  style={{ width: 200 }}
                  showSearch
                  optionFilterProp="children"
                  allowClear
                >
                  {userList.map(user => (
                    <Option key={user.id} value={user.id}>
                      {user.nickname || user.phone || `用户${user.id}`}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
              <Form.Item name="paymentStatus">
                <Select placeholder="支付状态" style={{ width: 120 }} allowClear>
                  <Option value={0}>待支付</Option>
                  <Option value={1}>已支付</Option>
                  <Option value={2}>支付失败</Option>
                  <Option value={3}>已退款</Option>
                </Select>
              </Form.Item>
              <Form.Item name="dateRange">
                <RangePicker placeholder={['开始日期', '结束日期']} />
              </Form.Item>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                    搜索
                  </Button>
                  <Button onClick={handleReset} icon={<ReloadOutlined />}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>

            {/* 操作按钮 */}
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                >
                  后台充值
                </Button>
                <Button
                  icon={<DollarOutlined />}
                  onClick={handleAdjustBalance}
                >
                  调整余额
                </Button>
              </Space>
            </div>

            {/* 充值记录表格 */}
            <Table
              columns={rechargeColumns}
              dataSource={rechargeList}
              rowKey="id"
              loading={loading}
              pagination={{
                ...pagination,
                onChange: (page, pageSize) => {
                  setPagination(prev => ({ ...prev, current: page, pageSize }));
                  fetchData({ page, pageSize });
                },
              }}
            />
          </TabPane>

          <TabPane tab="余额记录" key="balance">
            {/* 搜索表单 */}
            <Form
              layout="inline"
              onFinish={handleSearch}
              style={{ marginBottom: 16 }}
            >
              <Form.Item name="userId">
                <Select
                  placeholder="选择用户"
                  style={{ width: 200 }}
                  showSearch
                  optionFilterProp="children"
                  allowClear
                >
                  {userList.map(user => (
                    <Option key={user.id} value={user.id}>
                      {user.nickname || user.phone || `用户${user.id}`}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
              <Form.Item name="type">
                <Select placeholder="变动类型" style={{ width: 120 }} allowClear>
                  <Option value={1}>增加</Option>
                  <Option value={2}>减少</Option>
                </Select>
              </Form.Item>
              <Form.Item name="source">
                <Select placeholder="变动来源" style={{ width: 120 }} allowClear>
                  <Option value={1}>充值</Option>
                  <Option value={2}>消费</Option>
                  <Option value={3}>退款</Option>
                  <Option value={4}>后台调整</Option>
                </Select>
              </Form.Item>
              <Form.Item name="dateRange">
                <RangePicker placeholder={['开始日期', '结束日期']} />
              </Form.Item>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                    搜索
                  </Button>
                  <Button onClick={handleReset} icon={<ReloadOutlined />}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>

            {/* 余额记录表格 */}
            <Table
              columns={[
                {
                  title: 'ID',
                  dataIndex: 'id',
                  key: 'id',
                  width: 80
                },
                {
                  title: '用户',
                  dataIndex: 'nickname',
                  key: 'nickname',
                  render: (text, record) => (
                    <div>
                      <div>{text || '未知用户'}</div>
                      <div style={{ fontSize: '12px', color: '#999' }}>{record.phone}</div>
                    </div>
                  )
                },
                {
                  title: '变动类型',
                  dataIndex: 'type_text',
                  key: 'type_text',
                  render: (text, record) => (
                    <Tag color={record.type === 1 ? 'green' : 'red'}>
                      {text}
                    </Tag>
                  )
                },
                {
                  title: '变动金额',
                  dataIndex: 'amount',
                  key: 'amount',
                  render: (amount, record) => (
                    <span style={{ color: record.type === 1 ? '#52c41a' : '#ff4d4f' }}>
                      {record.type === 1 ? '+' : '-'}¥{parseFloat(amount).toFixed(2)}
                    </span>
                  )
                },
                {
                  title: '变动前余额',
                  dataIndex: 'balance_before',
                  key: 'balance_before',
                  render: (amount) => `¥${parseFloat(amount).toFixed(2)}`
                },
                {
                  title: '变动后余额',
                  dataIndex: 'balance_after',
                  key: 'balance_after',
                  render: (amount) => `¥${parseFloat(amount).toFixed(2)}`
                },
                {
                  title: '变动来源',
                  dataIndex: 'source_text',
                  key: 'source_text'
                },
                {
                  title: '备注',
                  dataIndex: 'remark',
                  key: 'remark',
                  ellipsis: true
                },
                {
                  title: '创建时间',
                  dataIndex: 'created_at',
                  key: 'created_at',
                  render: (time) => new Date(time).toLocaleString()
                }
              ]}
              dataSource={balanceRecords}
              rowKey="id"
              loading={loading}
              pagination={{
                ...pagination,
                onChange: (page, pageSize) => {
                  setPagination(prev => ({ ...prev, current: page, pageSize }));
                  fetchBalanceData({ page, pageSize });
                },
              }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 充值/编辑模态框 */}
      <Modal
        title={editingRecord ? '编辑充值记录' : '后台充值'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="user_id"
            label="选择用户"
            rules={[{ required: true, message: '请选择用户' }]}
          >
            <Select
              placeholder="选择用户"
              showSearch
              optionFilterProp="children"
            >
              {userList.map(user => (
                <Option key={user.id} value={user.id}>
                  {user.nickname || user.phone || `用户${user.id}`}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="amount"
            label="充值金额"
            rules={[{ required: true, message: '请输入充值金额' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0.01}
              precision={2}
              placeholder="请输入充值金额"
            />
          </Form.Item>

          <Form.Item
            name="bonus_amount"
            label="赠送金额"
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={2}
              placeholder="请输入赠送金额（可选）"
            />
          </Form.Item>

          <Form.Item
            name="remark"
            label="备注"
          >
            <Input.TextArea
              rows={3}
              placeholder="请输入备注信息（可选）"
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingRecord ? '更新' : '充值'}
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 调整余额模态框 */}
      <Modal
        title="调整用户余额"
        open={adjustModalVisible}
        onCancel={() => setAdjustModalVisible(false)}
        footer={null}
        width={500}
      >
        <Form
          form={adjustForm}
          layout="vertical"
          onFinish={handleAdjustSubmit}
        >
          <Form.Item
            name="user_id"
            label="选择用户"
            rules={[{ required: true, message: '请选择用户' }]}
          >
            <Select
              placeholder="选择用户"
              showSearch
              optionFilterProp="children"
            >
              {userList.map(user => (
                <Option key={user.id} value={user.id}>
                  {user.nickname || user.phone || `用户${user.id}`}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="type"
            label="调整类型"
            rules={[{ required: true, message: '请选择调整类型' }]}
          >
            <Select placeholder="选择调整类型">
              <Option value={1}>增加余额</Option>
              <Option value={2}>减少余额</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="amount"
            label="调整金额"
            rules={[{ required: true, message: '请输入调整金额' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0.01}
              precision={2}
              placeholder="请输入调整金额"
            />
          </Form.Item>

          <Form.Item
            name="remark"
            label="调整原因"
            rules={[{ required: true, message: '请输入调整原因' }]}
          >
            <Input.TextArea
              rows={3}
              placeholder="请输入调整原因"
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                确认调整
              </Button>
              <Button onClick={() => setAdjustModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default RechargeManagement;
