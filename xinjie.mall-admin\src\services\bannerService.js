const bannerModel = require('../../models/bannerModel');
const { ValidationError } = require('../middleware/error');

class BannerService {
  // 获取轮播图列表
  async getBannerList(params) {
    const { page = 1, pageSize = 10, title = '', status } = params;

    // 参数验证
    if (page < 1) {
      throw new ValidationError('页码必须大于0');
    }
    if (pageSize < 1 || pageSize > 100) {
      throw new ValidationError('每页数量必须在1-100之间');
    }

    return await bannerModel.findAll({ page, pageSize, title, status });
  }

  // 创建轮播图
  async createBanner(data) {
    const {
      title,
      image_url,
      link_url,
      status = 1,
      start_time,
      end_time,
    } = data;
    let sort_order = data.sort_order;
    sort_order = Number(sort_order);
    if (!sort_order || isNaN(sort_order) || sort_order < 1) {
      throw new ValidationError('排序号必须为大于0的数字');
    }

    // 参数验证
    if (!title || title.trim().length === 0) {
      throw new ValidationError('轮播图标题不能为空');
    }
    if (!image_url || image_url.trim().length === 0) {
      throw new ValidationError('轮播图图片不能为空');
    }
    if (sort_order < 0) {
      throw new ValidationError('排序值不能小于0');
    }
    if (status !== 0 && status !== 1) {
      throw new ValidationError('状态值无效');
    }

    const bannerId = await bannerModel.create({
      title: title.trim(),
      image_url: image_url.trim(),
      link_url: link_url ? link_url.trim() : '',
      sort_order,
      status,
      start_time,
      end_time,
    });

    return { id: bannerId };
  }

  // 更新轮播图
  async updateBanner(id, data) {
    if (!id || id <= 0) {
      throw new ValidationError('轮播图ID无效');
    }

    const {
      title,
      image_url,
      link_url,
      sort_order,
      status,
      start_time,
      end_time,
    } = data;

    // 参数验证
    if (title !== undefined && title.trim().length === 0) {
      throw new ValidationError('轮播图标题不能为空');
    }
    if (image_url !== undefined && image_url.trim().length === 0) {
      throw new ValidationError('轮播图图片不能为空');
    }
    if (sort_order !== undefined && sort_order < 0) {
      throw new ValidationError('排序值不能小于0');
    }
    if (status !== undefined && status !== 0 && status !== 1) {
      throw new ValidationError('状态值无效');
    }

    await bannerModel.update(id, {
      title: title ? title.trim() : undefined,
      image_url: image_url ? image_url.trim() : undefined,
      link_url: link_url ? link_url.trim() : undefined,
      sort_order,
      status,
      start_time,
      end_time,
    });

    return { success: true };
  }

  // 删除轮播图
  async deleteBanner(id) {
    if (!id || id <= 0) {
      throw new ValidationError('轮播图ID无效');
    }

    await bannerModel.delete(id);
    return { success: true };
  }

  // 批量更新轮播图状态
  async batchUpdateStatus(ids, status) {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new ValidationError('轮播图ID列表不能为空');
    }
    if (status !== 0 && status !== 1) {
      throw new ValidationError('状态值无效');
    }

    const validIds = ids.filter(id => id > 0);
    if (validIds.length === 0) {
      throw new ValidationError('没有有效的轮播图ID');
    }

    // 这里可以添加批量更新逻辑
    for (const id of validIds) {
      await bannerModel.update(id, { status });
    }

    return { success: true, updatedCount: validIds.length };
  }
}

module.exports = new BannerService();
