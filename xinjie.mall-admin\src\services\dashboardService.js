import request from '../utils/request';

// 仪表板数据服务
class DashboardService {
  // 获取统计数据
  static async getStatistics() {
    try {
      const [
        ordersResponse,
        usersResponse,
        productsResponse,
        salesResponse
      ] = await Promise.all([
        request.get('/admin/order/statistics'),
        request.get('/admin/user/statistics'),
        request.get('/admin/product/statistics'),
        request.get('/admin/order/sales-statistics')
      ]);

      return {
        orders: ordersResponse.data || { total: 0, today: 0, trend: 0 },
        users: usersResponse.data || { total: 0, today: 0, trend: 0 },
        products: productsResponse.data || { total: 0, active: 0, trend: 0 },
        sales: salesResponse.data || { total: 0, today: 0, trend: 0 }
      };
    } catch (error) {
      console.error('获取统计数据失败:', error);
      return {
        orders: { total: 0, today: 0, trend: 0 },
        users: { total: 0, today: 0, trend: 0 },
        products: { total: 0, active: 0, trend: 0 },
        sales: { total: 0, today: 0, trend: 0 }
      };
    }
  }

  // 获取最近订单
  static async getRecentOrders(limit = 5) {
    try {
      const response = await request.get('/admin/order/recent', {
        params: { limit }
      });
      return response.data?.list || [];
    } catch (error) {
      console.error('获取最近订单失败:', error);
      return [];
    }
  }

  // 获取系统通知
  static async getNotifications(limit = 5) {
    try {
      const response = await request.get('/admin/system/notifications', {
        params: { limit }
      });
      return response.data?.list || [];
    } catch (error) {
      console.error('获取系统通知失败:', error);
      return [];
    }
  }

  // 获取系统状态
  static async getSystemStatus() {
    try {
      const response = await request.get('/admin/system/status');
      return response.data || {
        cpu: 0,
        memory: 0,
        disk: 0,
        uptime: 0
      };
    } catch (error) {
      console.error('获取系统状态失败:', error);
      return {
        cpu: 0,
        memory: 0,
        disk: 0,
        uptime: 0
      };
    }
  }

  // 获取热销商品
  static async getHotProducts(limit = 5) {
    try {
      const response = await request.get('/admin/product/hot', {
        params: { limit }
      });
      console.log('热销商品API响应:', response);
      return response; // 直接返回完整响应，让组件处理
    } catch (error) {
      console.error('获取热销商品失败:', error);
      return { data: [] };
    }
  }

  // 获取用户增长趋势
  static async getUserGrowthTrend(days = 7) {
    try {
      const response = await request.get('/admin/user/growth-trend', {
        params: { days }
      });
      return response.data || [];
    } catch (error) {
      console.error('获取用户增长趋势失败:', error);
      return [];
    }
  }

  // 获取销售趋势
  static async getSalesTrend(days = 7) {
    try {
      const response = await request.get('/admin/order/sales-trend', {
        params: { days }
      });
      console.log('销售趋势API响应:', response);
      return response; // 直接返回完整响应，让组件处理
    } catch (error) {
      console.error('获取销售趋势失败:', error);
      return { data: { trend: [], summary: {} } };
    }
  }
}

export default DashboardService;
