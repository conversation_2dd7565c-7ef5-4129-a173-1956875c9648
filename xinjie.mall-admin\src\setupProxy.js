const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function (app) {
  // 注释掉代理配置，因为后台管理系统的API直接在同一个服务器上
  // 如果需要代理到其他服务，请根据实际情况修改target

  // app.use(
  //   '/api',
  //   createProxyMiddleware({
  //     target: 'http://localhost:8081', // 修改为正确的后台服务地址
  //     changeOrigin: true,
  //   })
  // );

  // app.use(
  //   '/uploads',
  //   createProxyMiddleware({
  //     target: 'http://localhost:8081', // 修改为正确的后台服务地址
  //     changeOrigin: true,
  //   })
  // );
};
