import { configureStore, createSlice } from '@reduxjs/toolkit';

const userSlice = createSlice({
  name: 'user',
  initialState: {
    info: null,
    token: localStorage.getItem('token') || '',
  },
  reducers: {
    setUserInfo(state, action) {
      state.info = action.payload;
    },
    setToken(state, action) {
      state.token = action.payload;
      localStorage.setItem('token', action.payload);
    },
    logout(state) {
      state.info = null;
      state.token = '';
      localStorage.removeItem('token');
    },
  },
});

export const { setUserInfo, setToken, logout } = userSlice.actions;

const store = configureStore({
  reducer: {
    user: userSlice.reducer,
  },
});

export default store;
