/* Dashboard 全局样式优化 */

/* 卡片悬浮效果 */
.ant-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ant-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
}

/* 统计卡片动画 */
.stats-card {
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.stats-card:hover::before {
  left: 100%;
}

/* 快捷操作按钮样式 */
.quick-action-btn {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.quick-action-btn:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.quick-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.quick-action-btn:hover::before {
  left: 100%;
}

/* 进度条样式优化 */
.ant-progress-bg {
  border-radius: 10px !important;
}

.ant-progress-inner {
  border-radius: 10px !important;
  background-color: rgba(0, 0, 0, 0.06) !important;
}

/* 列表项悬浮效果 */
.ant-list-item {
  transition: all 0.2s ease;
  border-radius: 8px;
  margin-bottom: 8px;
  padding: 12px !important;
}

.ant-list-item:hover {
  background-color: #f8f9fa !important;
  transform: translateX(4px);
}

/* 头像样式优化 */
.ant-avatar {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.ant-avatar:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* 标签样式优化 */
.ant-tag {
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 500;
  border: none;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* 按钮样式优化 */
.ant-btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #36cfc9 100%);
  border: none;
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #5cdbd3 100%);
}

/* 欢迎区域动画 */
.welcome-card {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #4facfe 100%);
  position: relative;
  overflow: hidden;
}

.welcome-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 数字动画 */
.animated-number {
  display: inline-block;
  transition: all 0.3s ease;
}

.animated-number:hover {
  transform: scale(1.05);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .stats-card {
    margin-bottom: 16px;
  }
  
  .quick-action-btn {
    height: 60px !important;
    font-size: 12px !important;
  }
  
  .welcome-card .ant-typography h2 {
    font-size: 20px !important;
  }
}

@media (max-width: 576px) {
  .ant-card-body {
    padding: 16px !important;
  }
  
  .stats-card .ant-statistic-title {
    font-size: 12px !important;
  }
  
  .stats-card .ant-statistic-content-value {
    font-size: 20px !important;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 加载动画 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.ant-spin-dot-item {
  background-color: #1890ff;
}

/* 空状态样式 */
.ant-empty {
  padding: 40px 20px;
}

.ant-empty-description {
  color: #8c8c8c;
  font-size: 14px;
}

/* 通知样式优化 */
.notification-item {
  border-left: 3px solid;
  padding-left: 12px;
  margin-bottom: 8px;
  background-color: #fafafa;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.notification-item:hover {
  background-color: #f0f0f0;
  transform: translateX(2px);
}

.notification-item.info {
  border-left-color: #1890ff;
}

.notification-item.warning {
  border-left-color: #fa8c16;
}

.notification-item.success {
  border-left-color: #52c41a;
}

.notification-item.error {
  border-left-color: #ff4d4f;
}

/* 系统状态指示器 */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
  animation: pulse 2s infinite;
}

.status-indicator.good {
  background-color: #52c41a;
}

.status-indicator.warning {
  background-color: #fa8c16;
}

.status-indicator.danger {
  background-color: #ff4d4f;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}

/* 图表容器 */
.chart-container {
  padding: 20px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 工具提示样式 */
.custom-tooltip {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
