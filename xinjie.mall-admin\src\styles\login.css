/* 现代化登录页面样式 */

/* 全局登录页面样式 */
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

/* 登录卡片优化 */
.login-card {
  width: 100%;
  max-width: 420px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

/* 现代化输入框样式 */
.modern-input {
  height: 50px !important;
  border-radius: 8px !important;
  border: 1px solid #e5e7eb !important;
  background-color: #ffffff !important;
  font-size: 15px !important;
  transition: all 0.2s ease !important;
  box-shadow: none !important;
}

.modern-input:hover {
  border-color: #d1d5db !important;
}

.modern-input:focus,
.modern-input.ant-input-focused {
  border-color: #10b981 !important;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
  outline: none !important;
}

/* 密码输入框特殊处理 */
.ant-input-password.modern-input {
  padding-right: 40px !important;
}

.ant-input-password .ant-input {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

.ant-input-password:hover {
  border-color: #d1d5db !important;
}

.ant-input-password:focus-within,
.ant-input-password.ant-input-affix-wrapper-focused {
  border-color: #10b981 !important;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
}

/* 图标颜色统一 */
.modern-input .anticon {
  color: #10b981 !important;
}

/* 现代化按钮样式 */
.modern-login-btn {
  width: 100% !important;
  height: 50px !important;
  border-radius: 8px !important;
  background: #10b981 !important;
  border: none !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  box-shadow: none !important;
  transition: all 0.2s ease !important;
}

.modern-login-btn:hover,
.modern-login-btn:focus {
  background: #059669 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3) !important;
}

.modern-login-btn:active {
  transform: translateY(0) !important;
}

/* 表单项间距优化 */
.ant-form-item {
  margin-bottom: 20px !important;
}

.ant-form-item:last-child {
  margin-bottom: 0 !important;
}

/* 错误信息样式 */
.ant-form-item-explain-error {
  font-size: 13px !important;
  margin-top: 4px !important;
}

/* 记住我复选框样式 */
.ant-checkbox-wrapper {
  font-size: 14px !important;
  color: #6b7280 !important;
}

.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #10b981 !important;
  border-color: #10b981 !important;
}

.ant-checkbox:hover .ant-checkbox-inner {
  border-color: #10b981 !important;
}

/* 忘记密码链接 */
.forgot-password-link {
  font-size: 14px !important;
  color: #10b981 !important;
  text-decoration: none !important;
  font-weight: 500 !important;
  transition: color 0.2s ease !important;
}

.forgot-password-link:hover {
  color: #059669 !important;
}

/* 移除多余的边框和阴影 */
.ant-input-affix-wrapper {
  box-shadow: none !important;
}

.ant-input-affix-wrapper:hover {
  box-shadow: none !important;
}

.ant-input-affix-wrapper-focused {
  box-shadow: none !important;
}

/* 占位符文字颜色 */
.modern-input::placeholder {
  color: #9ca3af !important;
  font-size: 14px !important;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-card {
    margin: 10px;
    border-radius: 12px;
  }
  
  .modern-input {
    height: 48px !important;
    font-size: 14px !important;
  }
  
  .modern-login-btn {
    height: 48px !important;
    font-size: 15px !important;
  }
}

/* 加载状态优化 */
.ant-btn-loading-icon {
  margin-right: 8px !important;
}

/* 表单验证状态 */
.ant-form-item-has-error .modern-input {
  border-color: #ef4444 !important;
}

.ant-form-item-has-error .modern-input:focus {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

/* 成功状态 */
.ant-form-item-has-success .modern-input {
  border-color: #10b981 !important;
}

/* 禁用状态 */
.modern-input:disabled {
  background-color: #f9fafb !important;
  border-color: #e5e7eb !important;
  color: #9ca3af !important;
  cursor: not-allowed !important;
}

/* 清除默认的focus outline */
.modern-input:focus-visible {
  outline: none !important;
}

/* 输入框内容对齐 */
.ant-input-affix-wrapper .ant-input {
  padding: 0 !important;
}

/* 前缀图标间距 */
.ant-input-prefix {
  margin-right: 12px !important;
}

/* 后缀图标间距 */
.ant-input-suffix {
  margin-left: 12px !important;
}

/* 密码可见性图标 */
.ant-input-password-icon {
  color: #10b981 !important;
  transition: color 0.2s ease !important;
}

.ant-input-password-icon:hover {
  color: #059669 !important;
}

/* 表单标题样式优化 */
.login-title {
  font-size: 28px !important;
  font-weight: 700 !important;
  color: #1f2937 !important;
  margin-bottom: 8px !important;
  text-align: center !important;
}

.login-subtitle {
  font-size: 15px !important;
  color: #6b7280 !important;
  text-align: center !important;
  margin-bottom: 32px !important;
}

/* 卡片内边距优化 */
.ant-card-body {
  padding: 40px 32px !important;
}

@media (max-width: 480px) {
  .ant-card-body {
    padding: 32px 24px !important;
  }
}

/* 去除不必要的动画 */
.ant-form-item-explain {
  transition: none !important;
}

/* 优化加载按钮样式 */
.modern-login-btn.ant-btn-loading {
  pointer-events: none !important;
}

.modern-login-btn .ant-btn-loading-icon .anticon {
  color: #ffffff !important;
}
