/* 现代化浅绿主题样式 */

/* CSS变量定义 */
:root {
  /* 主色调 - 现代浅绿色系 */
  --primary-color: #10b981;
  --primary-light: #34d399;
  --primary-dark: #059669;
  --primary-darker: #047857;
  
  /* 渐变色 */
  --primary-gradient: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
  --light-gradient: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 50%, #a7f3d0 100%);
  --card-gradient: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
  
  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(16, 185, 129, 0.08);
  --shadow-md: 0 4px 15px rgba(16, 185, 129, 0.12);
  --shadow-lg: 0 8px 25px rgba(16, 185, 129, 0.15);
  --shadow-xl: 0 12px 35px rgba(16, 185, 129, 0.18);
  
  /* 文字颜色 */
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-light: #9ca3af;
  
  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-light: #f3f4f6;
}

/* 全局样式优化 */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 卡片样式优化 */
.ant-card {
  border-radius: 16px !important;
  border: 1px solid rgba(16, 185, 129, 0.08) !important;
  box-shadow: var(--shadow-sm) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  background: var(--card-gradient) !important;
}

.ant-card:hover {
  transform: translateY(-4px) !important;
  box-shadow: var(--shadow-lg) !important;
  border-color: rgba(16, 185, 129, 0.15) !important;
}

.ant-card-head {
  border-bottom: 1px solid rgba(16, 185, 129, 0.08) !important;
  border-radius: 16px 16px 0 0 !important;
}

.ant-card-body {
  padding: 24px !important;
}

/* 统计卡片样式 */
.stats-card {
  position: relative;
  overflow: hidden;
  border-radius: 16px !important;
  background: var(--card-gradient) !important;
  border: 1px solid rgba(16, 185, 129, 0.08) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: var(--light-gradient);
  border-radius: 50%;
  transform: translate(30px, -30px);
  opacity: 0.1;
  transition: all 0.3s ease;
}

.stats-card:hover::before {
  transform: translate(20px, -20px) scale(1.2);
  opacity: 0.15;
}

.stats-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: var(--shadow-xl) !important;
  border-color: rgba(16, 185, 129, 0.2) !important;
}

/* 统计数字样式 */
.ant-statistic-content-value {
  color: var(--primary-color) !important;
  font-weight: 700 !important;
  font-size: 32px !important;
}

.ant-statistic-title {
  color: var(--text-secondary) !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  margin-bottom: 8px !important;
}

/* 按钮样式优化 */
.ant-btn-primary {
  background: var(--primary-gradient) !important;
  border: none !important;
  border-radius: 12px !important;
  box-shadow: var(--shadow-sm) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  font-weight: 500 !important;
}

.ant-btn-primary:hover {
  transform: translateY(-2px) !important;
  box-shadow: var(--shadow-md) !important;
  background: linear-gradient(135deg, #059669 0%, #047857 50%, #065f46 100%) !important;
}

/* 快捷操作按钮 */
.quick-action-btn {
  border-radius: 16px !important;
  border: 1px solid rgba(16, 185, 129, 0.1) !important;
  background: var(--card-gradient) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative;
  overflow: hidden;
}

.quick-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.1), transparent);
  transition: left 0.6s ease;
}

.quick-action-btn:hover::before {
  left: 100%;
}

.quick-action-btn:hover {
  transform: translateY(-8px) scale(1.05) !important;
  box-shadow: var(--shadow-lg) !important;
  border-color: rgba(16, 185, 129, 0.2) !important;
}

/* 表格样式优化 - 纯白简洁版 */
.ant-table {
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: var(--shadow-sm) !important;
  background: #ffffff !important;
}

.ant-table-thead > tr > th {
  background: #ffffff !important;
  border-bottom: 2px solid #f0f0f0 !important;
  color: var(--text-primary) !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  padding: 16px 12px !important;
}

.ant-table-tbody > tr > td {
  background: #ffffff !important;
  border-bottom: 1px solid #f5f5f5 !important;
  padding: 16px 12px !important;
  transition: all 0.2s ease !important;
}

.ant-table-tbody > tr:hover > td {
  background: #fafafa !important;
  transform: none !important;
}

.ant-table-tbody > tr:nth-child(even) > td {
  background: #fafafa !important;
}

.ant-table-tbody > tr:nth-child(even):hover > td {
  background: #f0f0f0 !important;
}

/* 可选：整体渐变表格样式 - 如需使用请添加 .gradient-table 类名 */
.gradient-table.ant-table {
  background: linear-gradient(135deg, #ffffff 0%, #f8fffe 50%, #f0fdf4 100%) !important;
}

.gradient-table .ant-table-thead > tr > th {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 50%, #a7f3d0 100%) !important;
  border-bottom: 2px solid rgba(16, 185, 129, 0.2) !important;
  color: #047857 !important;
  font-weight: 700 !important;
}

.gradient-table .ant-table-tbody > tr > td {
  background: rgba(255, 255, 255, 0.8) !important;
  border-bottom: 1px solid rgba(16, 185, 129, 0.1) !important;
}

.gradient-table .ant-table-tbody > tr:hover > td {
  background: rgba(16, 185, 129, 0.05) !important;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.1) !important;
}

.gradient-table .ant-table-tbody > tr:nth-child(even) > td {
  background: rgba(240, 253, 244, 0.3) !important;
}

.gradient-table .ant-table-tbody > tr:nth-child(even):hover > td {
  background: rgba(16, 185, 129, 0.08) !important;
}

/* 进度条样式 */
.ant-progress-bg {
  background: var(--primary-gradient) !important;
  border-radius: 10px !important;
}

.ant-progress-inner {
  background: rgba(16, 185, 129, 0.1) !important;
  border-radius: 10px !important;
}

/* 标签样式 */
.ant-tag {
  border-radius: 8px !important;
  border: none !important;
  font-weight: 500 !important;
}

/* 输入框样式 */
.ant-input,
.ant-select-selector {
  border-radius: 12px !important;
  border: 1px solid rgba(16, 185, 129, 0.2) !important;
  transition: all 0.3s ease !important;
}

.ant-input:focus,
.ant-select-selector:focus,
.ant-input-focused,
.ant-select-focused .ant-select-selector {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1) !important;
}

/* 分页样式 */
.ant-pagination-item {
  border-radius: 8px !important;
  border: 1px solid rgba(16, 185, 129, 0.2) !important;
}

.ant-pagination-item-active {
  background: var(--primary-gradient) !important;
  border-color: var(--primary-color) !important;
}

.ant-pagination-item-active a {
  color: white !important;
}

/* 模态框样式 */
.ant-modal {
  border-radius: 16px !important;
  overflow: hidden !important;
}

.ant-modal-header {
  background: var(--light-gradient) !important;
  border-bottom: 1px solid rgba(16, 185, 129, 0.1) !important;
}

/* 抽屉样式 */
.ant-drawer-header {
  background: var(--light-gradient) !important;
  border-bottom: 1px solid rgba(16, 185, 129, 0.1) !important;
}

/* 通知样式 */
.ant-notification {
  border-radius: 12px !important;
  box-shadow: var(--shadow-lg) !important;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .stats-card {
    margin-bottom: 16px;
  }
  
  .quick-action-btn {
    height: 80px !important;
  }
  
  .ant-statistic-content-value {
    font-size: 24px !important;
  }
}

@media (max-width: 576px) {
  .ant-card-body {
    padding: 16px !important;
  }
  
  .ant-statistic-content-value {
    font-size: 20px !important;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(16, 185, 129, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(16, 185, 129, 0.3);
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(16, 185, 129, 0.5);
}

/* 加载动画优化 */
.ant-spin-dot-item {
  background-color: var(--primary-color) !important;
}

/* 面包屑样式 */
.ant-breadcrumb a {
  color: var(--primary-color) !important;
}

.ant-breadcrumb-separator {
  color: var(--text-light) !important;
}
