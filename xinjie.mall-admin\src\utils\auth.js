import axios from 'axios';
import authConfig from '../config/auth';

// 从配置文件获取常量
const {
  TOKEN_KEY,
  REFRESH_TOKEN_KEY,
  TOKEN_EXPIRY_KEY,
  USER_INFO_KEY
} = authConfig.token.storageKeys;

const TOKEN_EXPIRY_TIME = authConfig.token.expiryTime;

/**
 * 认证工具类
 */
class AuthService {
  /**
   * 保存认证信息
   * @param {Object} authData - 认证数据
   */
  static saveAuth(authData) {
    const { token, admin } = authData;
    const expiryTime = Date.now() + TOKEN_EXPIRY_TIME;
    
    localStorage.setItem(TOKEN_KEY, token);
    localStorage.setItem(TOKEN_EXPIRY_KEY, expiryTime.toString());
    localStorage.setItem(USER_INFO_KEY, JSON.stringify(admin));
    
    // 设置token到axios默认头
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  /**
   * 清除认证信息
   */
  static clearAuth() {
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
    localStorage.removeItem(TOKEN_EXPIRY_KEY);
    localStorage.removeItem(USER_INFO_KEY);
    
    // 清除axios默认头
    delete axios.defaults.headers.common['Authorization'];
  }

  /**
   * 获取token
   * @returns {string|null}
   */
  static getToken() {
    return localStorage.getItem(TOKEN_KEY);
  }

  /**
   * 获取用户信息
   * @returns {Object|null}
   */
  static getUserInfo() {
    const userInfo = localStorage.getItem(USER_INFO_KEY);
    return userInfo ? JSON.parse(userInfo) : null;
  }

  /**
   * 检查token是否过期
   * @returns {boolean}
   */
  static isTokenExpired() {
    const expiryTime = localStorage.getItem(TOKEN_EXPIRY_KEY);
    if (!expiryTime) return true;
    
    return Date.now() > parseInt(expiryTime);
  }

  /**
   * 检查是否已登录
   * @returns {boolean}
   */
  static isAuthenticated() {
    const token = this.getToken();
    if (!token) return false;
    
    return !this.isTokenExpired();
  }

  /**
   * 验证token有效性
   * @returns {Promise<boolean>}
   */
  static async validateToken() {
    try {
      const token = this.getToken();
      if (!token) return false;

      // 检查本地过期时间
      if (this.isTokenExpired()) {
        this.clearAuth();
        return false;
      }

      // 调用后端验证接口
      const response = await axios.get(authConfig.api.check, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      return response.data && response.data.success;
    } catch (error) {
      console.error('Token验证失败:', error);
      this.clearAuth();
      return false;
    }
  }

  /**
   * 刷新token
   * @returns {Promise<boolean>}
   */
  static async refreshToken() {
    try {
      const response = await axios.post(authConfig.api.refresh);
      if (response.data && response.data.success) {
        this.saveAuth(response.data.data);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Token刷新失败:', error);
      return false;
    }
  }

  /**
   * 自动处理token过期
   * @returns {Promise<boolean>}
   */
  static async handleTokenExpiry() {
    if (this.isTokenExpired()) {
      // 尝试刷新token
      const refreshed = await this.refreshToken();
      if (!refreshed) {
        this.clearAuth();
        return false;
      }
    }
    return true;
  }

  /**
   * 登出
   * @returns {Promise<void>}
   */
  static async logout() {
    try {
      // 调用后端登出接口
      await axios.post(authConfig.api.logout);
    } catch (error) {
      console.error('登出请求失败:', error);
    } finally {
      // 无论后端请求是否成功，都清除本地数据
      this.clearAuth();
    }
  }

  /**
   * 获取认证状态（包含用户信息）
   * @returns {Promise<Object>}
   */
  static async getAuthStatus() {
    const isAuth = this.isAuthenticated();
    
    if (!isAuth) {
      return {
        authenticated: false,
        user: null,
        token: null
      };
    }

    // 处理token过期
    const tokenValid = await this.handleTokenExpiry();
    if (!tokenValid) {
      return {
        authenticated: false,
        user: null,
        token: null
      };
    }

    return {
      authenticated: true,
      user: this.getUserInfo(),
      token: this.getToken()
    };
  }
}

// 设置axios拦截器
axios.interceptors.request.use(
  async (config) => {
    // 在发送请求前检查token
    if (AuthService.isAuthenticated()) {
      await AuthService.handleTokenExpiry();
      const token = AuthService.getToken();
      if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

axios.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    if (error.response && error.response.status === 401) {
      // Token无效，清除认证信息
      AuthService.clearAuth();
      
      // 如果不是登录页面，跳转到登录页
      if (!window.location.pathname.includes(authConfig.routes.login)) {
        window.location.href = authConfig.routes.login;
      }
    }
    return Promise.reject(error);
  }
);

export default AuthService; 