import axios from 'axios';
import AuthService from './auth';

const request = axios.create({
  baseURL: '/api',
  timeout: 10000,
  withCredentials: true, // 关键：允许发送cookie
});

// 请求拦截器
request.interceptors.request.use(
  async (config) => {
    // 检查认证状态并自动处理token过期
    if (AuthService.isAuthenticated()) {
      await AuthService.handleTokenExpiry();
      const token = AuthService.getToken();
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
      }
    }

    // 确保请求头包含必要的字段（但不要干扰FormData上传）
    if (!(config.data instanceof FormData)) {
      config.headers['X-Requested-With'] = 'XMLHttpRequest';
    }

    console.log('发送请求:', {
      url: config.url,
      method: config.method,
      hasToken: !!config.headers['Authorization'],
      withCredentials: config.withCredentials,
      isFormData: config.data instanceof FormData,
      contentType: config.headers['Content-Type']
    });

    return config;
  },
  (error) => {
    console.error('请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    console.log('收到响应:', {
      url: response.config.url,
      status: response.status,
      success: response.data?.success,
    });

    // 兼容后端返回格式 { success, data, message }
    if (response.data && response.data.success === false) {
      // 如果是认证失败，清除本地认证信息
      if (response.status === 401) {
        AuthService.clearAuth();
        if (!window.location.pathname.includes('/login')) {
          window.location.href = '/login';
        }
      }
      
      // 显示错误消息
      if (response.data.message) {
        window?.message?.error?.(response.data.message);
      }
      
      return Promise.reject(response.data);
    }
    
    return response.data;
  },
  (error) => {
    console.error('请求错误:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message,
    });

    // 处理401认证失败
    if (error.response && error.response.status === 401) {
      AuthService.clearAuth();

      // 如果不是登录页面，跳转到登录页
      if (!window.location.pathname.includes('/login')) {
        window.location.href = '/login';
      }
    }

    // 显示更详细的错误信息
    let errorMessage = '网络错误';
    if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (error.response?.data?.msg) {
      errorMessage = error.response.data.msg;
    } else if (error.response?.data?.error) {
      errorMessage = error.response.data.error;
    } else if (error.message) {
      errorMessage = error.message;
    }

    // 只在非401错误时显示错误消息
    if (error.response?.status !== 401) {
    window?.message?.error?.(errorMessage);
    }
    
    return Promise.reject(error);
  }
);

export default request;
