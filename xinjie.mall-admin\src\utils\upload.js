const path = require('path');
const fs = require('fs');
const crypto = require('crypto');
const config = require('../config');
const pinyin = require('pinyin');

class UploadUtil {
  constructor() {
    // 修改上传目录，指向mall-server的uploads目录
    this.uploadDir = path.join(__dirname, '../../../mall-server/uploads');
    this.tempDir = path.join(__dirname, '../../../mall-server/uploads/temp');
    this.maxSize = 20 * 1024 * 1024; // 20MB
    this.allowedTypes = config.upload.allowedTypes;
  }

  // 验证文件
  validateFile(file) {
    if (!file) {
      throw new Error('未提供文件');
    }

    if (file.size > this.maxSize) {
      throw new Error(`文件大小不能超过 ${this.maxSize / 1024 / 1024}MB`);
    }

    if (!this.allowedTypes.includes(file.mimetype)) {
      throw new Error('不支持的文件类型');
    }

    return true;
  }

  // 生成文件名
  generateFileName(originalName) {
    const ext = path.extname(originalName);
    const timestamp = Date.now();
    const random = crypto.randomBytes(8).toString('hex');
    let base = path.basename(originalName, ext);
    // 将中文转为拼音，非中文保持原样
    let safeBase = pinyin(base, { style: pinyin.STYLE_NORMAL })
      .flat()
      .join('');
    // 只保留字母数字下划线
    safeBase = safeBase.replace(/[^a-zA-Z0-9_]/g, '');
    return `${timestamp}_${random}_${safeBase}${ext}`;
  }

  // 确保目录存在
  ensureDir(dir) {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  }

  // 上传文件
  async uploadFile(file, subDir = '') {
    console.log('[唯一调试] uploadUtil.uploadFile 被调用，subDir:', subDir);
    try {
      this.validateFile(file);

      const fileName = this.generateFileName(file.name);
      const uploadPath = path.join(this.uploadDir, subDir);
      this.ensureDir(uploadPath);

      const filePath = path.join(uploadPath, fileName);
      console.log('[唯一调试] uploadUtil 保存路径:', filePath);
      await file.mv(filePath);
      console.log('[唯一调试] uploadUtil file.mv 执行完毕');

      // 自动同步到 mall-server 目录
      try {
        const mallServerBannerDir = path.join(__dirname, '../../../mall-server/uploads/banners');
        if (!fs.existsSync(mallServerBannerDir)) {
          fs.mkdirSync(mallServerBannerDir, { recursive: true });
        }
        const syncTarget = path.join(mallServerBannerDir, fileName);
        fs.copyFileSync(filePath, syncTarget);
        console.log('[唯一调试] uploadUtil 已自动同步到 mall-server:', syncTarget);
      } catch (syncErr) {
        console.error('[唯一调试] uploadUtil 自动同步到 mall-server 失败:', syncErr);
      }

      const relativePath = path.join(subDir, fileName).replace(/\\/g, '/');
      const url = `/uploads/${relativePath}`;
      console.log('[唯一调试] uploadUtil 返回 url:', url);

      return {
        success: true,
        fileName,
        filePath,
        url,
        size: file.size,
        mimetype: file.mimetype,
      };
    } catch (error) {
      console.error('[唯一调试] uploadUtil 文件上传失败:', error);
      throw new Error(`文件上传失败: ${error.message}`);
    }
  }

  // 删除文件
  async deleteFile(filePath) {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        return true;
      }
      return false;
    } catch (error) {
      console.error('删除文件失败:', error);
      return false;
    }
  }

  // 批量上传
  async uploadMultiple(files, subDir = '') {
    if (!Array.isArray(files)) {
      files = [files];
    }

    const results = [];
    for (const file of files) {
      try {
        const result = await this.uploadFile(file, subDir);
        results.push(result);
      } catch (error) {
        results.push({
          success: false,
          originalName: file.name,
          error: error.message,
        });
      }
    }

    return results;
  }

  // 获取文件信息
  getFileInfo(filePath) {
    try {
      const stats = fs.statSync(filePath);
      return {
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime,
        exists: true,
      };
    } catch (error) {
      return {
        exists: false,
        error: error.message,
      };
    }
  }
}

module.exports = new UploadUtil();
