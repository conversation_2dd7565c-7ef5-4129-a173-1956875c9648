const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'xinjie_mall',
  charset: 'utf8mb4'
};

async function testDiscountFlow() {
  let connection;
  
  try {
    console.log('开始测试折扣功能完整流程...');
    
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功');
    
    // 1. 创建测试用户
    console.log('\n1. 创建测试用户...');
    const [userResult] = await connection.execute(`
      INSERT INTO users (nickname, phone, created_at, updated_at)
      VALUES ('测试用户', '13800138000', NOW(), NOW())
      ON DUPLICATE KEY UPDATE id=LAST_INSERT_ID(id)
    `);
    const userId = userResult.insertId || userResult.insertId;
    console.log(`✓ 测试用户创建成功，ID: ${userId}`);
    
    // 2. 创建测试分类
    console.log('\n2. 创建测试分类...');
    const [categoryResult] = await connection.execute(`
      INSERT INTO categories (name, parent_id, level, status, created_at, updated_at)
      VALUES ('测试分类', 0, 1, 1, NOW(), NOW())
      ON DUPLICATE KEY UPDATE id=LAST_INSERT_ID(id)
    `);
    const categoryId = categoryResult.insertId || categoryResult.insertId;
    console.log(`✓ 测试分类创建成功，ID: ${categoryId}`);

    // 3. 创建测试商品
    console.log('\n3. 创建测试商品...');
    const [productResult] = await connection.execute(`
      INSERT INTO products (name, price, stock, category_id, status, image_url, description, updated_at)
      VALUES ('测试茗茶', 100.00, 50, ?, 1, '/images/test-tea.jpg', '这是一个测试商品', NOW())
      ON DUPLICATE KEY UPDATE id=LAST_INSERT_ID(id)
    `, [categoryId]);
    const productId = productResult.insertId || productResult.insertId;
    console.log(`✓ 测试商品创建成功，ID: ${productId}`);
    
    // 4. 创建测试折扣
    console.log('\n4. 创建测试折扣...');
    const [discountResult] = await connection.execute(`
      INSERT INTO discounts (
        name, description, type, value, min_amount, max_discount,
        start_time, end_time, usage_limit, user_limit, priority,
        applicable_to, status, created_at, updated_at
      ) VALUES (
        '测试折扣', '9折优惠', 1, 10, 0, NULL,
        NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), NULL, NULL, 1,
        1, 1, NOW(), NOW()
      )
      ON DUPLICATE KEY UPDATE id=LAST_INSERT_ID(id)
    `);
    const discountId = discountResult.insertId || discountResult.insertId;
    console.log(`✓ 测试折扣创建成功，ID: ${discountId}`);
    
    // 4. 测试添加商品到购物车
    console.log('\n4. 测试添加商品到购物车...');
    await connection.execute(`
      INSERT INTO cart_items (user_id, product_id, quantity, created_at, updated_at)
      VALUES (?, ?, 2, NOW(), NOW())
      ON DUPLICATE KEY UPDATE quantity = quantity + VALUES(quantity)
    `, [userId, productId]);
    console.log('✓ 商品添加到购物车成功');
    
    // 5. 测试获取购物车（模拟API调用）
    console.log('\n5. 测试获取购物车...');
    const [cartItems] = await connection.execute(`
      SELECT 
        c.*,
        p.name as product_name,
        p.image_url as product_image,
        p.price as product_price,
        p.stock as product_stock,
        p.status as product_status,
        p.category_id,
        cat.name as category_name
      FROM cart_items c
      LEFT JOIN products p ON c.product_id = p.id
      LEFT JOIN categories cat ON p.category_id = cat.id
      WHERE c.user_id = ? AND p.status = 1
    `, [userId]);
    
    console.log('购物车商品:', cartItems.map(item => ({
      name: item.product_name,
      price: item.product_price,
      quantity: item.quantity,
      subtotal: item.product_price * item.quantity
    })));
    
    // 6. 计算折扣（模拟折扣服务）
    console.log('\n6. 计算折扣...');
    const [activeDiscounts] = await connection.execute(`
      SELECT * FROM discounts 
      WHERE status = 1 
        AND start_time <= NOW() 
        AND end_time >= NOW()
        AND (usage_limit IS NULL OR used_count < usage_limit)
      ORDER BY priority DESC, created_at ASC
    `);
    
    let totalOriginal = 0;
    let totalDiscount = 0;
    const itemsWithDiscount = [];
    
    for (const item of cartItems) {
      const originalPrice = parseFloat(item.product_price);
      let discountPrice = originalPrice;
      let appliedDiscount = null;
      
      // 查找适用的折扣
      for (const discount of activeDiscounts) {
        if (discount.applicable_to === 1) { // 全部商品
          if (discount.type === 1) { // 百分比折扣
            discountPrice = originalPrice * (1 - discount.value / 100);
            appliedDiscount = discount;
            break;
          }
        }
      }
      
      const itemOriginal = originalPrice * item.quantity;
      const itemDiscounted = discountPrice * item.quantity;
      
      totalOriginal += itemOriginal;
      totalDiscount += itemDiscounted;
      
      itemsWithDiscount.push({
        ...item,
        originalPrice,
        discountPrice,
        discountAmount: originalPrice - discountPrice,
        itemTotal: itemDiscounted,
        appliedDiscount: appliedDiscount?.name
      });
    }
    
    console.log('折扣计算结果:');
    console.log(`原价总计: ¥${totalOriginal.toFixed(2)}`);
    console.log(`折扣总计: ¥${totalDiscount.toFixed(2)}`);
    console.log(`优惠金额: ¥${(totalOriginal - totalDiscount).toFixed(2)}`);
    
    itemsWithDiscount.forEach(item => {
      console.log(`- ${item.product_name}: 原价¥${item.originalPrice} → 折扣价¥${item.discountPrice.toFixed(2)} (${item.appliedDiscount || '无折扣'})`);
    });
    
    // 7. 测试创建订单
    console.log('\n7. 测试创建订单...');
    const orderNo = `ORD${Date.now()}${Math.floor(Math.random() * 1000)}`;
    const shippingFee = 0;
    const finalAmount = totalDiscount + shippingFee;
    
    const [orderResult] = await connection.execute(`
      INSERT INTO orders (
        user_id, order_no, total_amount, original_amount, discount_amount,
        shipping_fee, final_amount, receiver_name, receiver_phone,
        receiver_address, remark, order_status, pay_status, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0, 0, NOW(), NOW())
    `, [
      userId, orderNo, totalDiscount, totalOriginal, totalOriginal - totalDiscount,
      shippingFee, finalAmount, '测试收货人', '13800138000',
      '北京市朝阳区测试地址', '测试订单'
    ]);
    
    const orderId = orderResult.insertId;
    console.log(`✓ 订单创建成功，订单号: ${orderNo}, ID: ${orderId}`);
    
    // 8. 创建订单商品
    console.log('\n8. 创建订单商品...');
    for (const item of itemsWithDiscount) {
      await connection.execute(`
        INSERT INTO order_items (
          order_id, product_id, product_name, product_image, quantity,
          price, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, NOW())
      `, [
        orderId, item.product_id, item.product_name, item.product_image, item.quantity,
        item.discountPrice
      ]);
    }
    console.log('✓ 订单商品创建成功');
    
    // 9. 更新商品库存
    console.log('\n9. 更新商品库存...');
    for (const item of itemsWithDiscount) {
      await connection.execute(`
        UPDATE products 
        SET stock = stock - ?, updated_at = NOW()
        WHERE id = ?
      `, [item.quantity, item.product_id]);
    }
    console.log('✓ 商品库存更新成功');
    
    // 10. 清理购物车
    console.log('\n10. 清理购物车...');
    await connection.execute(`
      DELETE FROM cart_items WHERE user_id = ?
    `, [userId]);
    console.log('✓ 购物车清理成功');
    
    // 11. 验证最终结果
    console.log('\n11. 验证最终结果...');
    const [finalOrder] = await connection.execute(`
      SELECT * FROM orders WHERE id = ?
    `, [orderId]);
    
    const [finalOrderItems] = await connection.execute(`
      SELECT * FROM order_items WHERE order_id = ?
    `, [orderId]);
    
    console.log('最终订单信息:');
    console.log(`- 订单号: ${finalOrder[0].order_no}`);
    console.log(`- 原价总额: ¥${finalOrder[0].original_amount}`);
    console.log(`- 折扣金额: ¥${finalOrder[0].discount_amount}`);
    console.log(`- 实付金额: ¥${finalOrder[0].final_amount}`);
    console.log(`- 商品数量: ${finalOrderItems.length}`);
    
    console.log('\n🎉 折扣功能完整流程测试成功！');
    console.log('✅ 购物车 → 折扣计算 → 订单创建 → 支付金额 全流程正常');
    
  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

testDiscountFlow().catch(console.error);
