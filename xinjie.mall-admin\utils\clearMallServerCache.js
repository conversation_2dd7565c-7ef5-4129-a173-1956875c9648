const axios = require('axios');

/**
 * 清理mall-server的分类缓存
 */
async function clearMallServerCategoryCache() {
  try {
    // 调用mall-server的缓存清理接口
    const response = await axios.post('https://localhost:4443/api/admin/cache/clear', {
      type: 'category'
    }, {
      timeout: 5000
    });
    
    console.log('✅ mall-server分类缓存清理成功');
    return true;
  } catch (error) {
    console.log('⚠️ mall-server缓存清理失败，但不影响主要功能:', error.message);
    return false;
  }
}

module.exports = {
  clearMallServerCategoryCache
}; 