const path = require('path');
const fs = require('fs-extra');
const { deleteImageFromMallServer } = require('./syncImages');

/**
 * 统一删除并同步图片
 * @param {string} imageUrl - 图片URL，如 '/uploads/banners/image.jpg'
 * @param {string} type - 子目录，如 'products'、'categories'、'avatars'、'banners'
 * @returns {object} - { success, message }
 */
async function deleteAndSync(imageUrl, type) {
  if (!imageUrl) {
    return { success: false, message: '图片URL不能为空' };
  }

  try {
    console.log(`🗑️ 开始删除图片: ${imageUrl}, 类型: ${type}`);

    // 1. 删除管理后台的图片
    const adminImagePath = path.join(__dirname, '../public', imageUrl);
    if (await fs.pathExists(adminImagePath)) {
      await fs.remove(adminImagePath);
      console.log(`✅ 删除管理后台图片: ${adminImagePath}`);
    } else {
      console.log(`ℹ️ 管理后台图片不存在: ${adminImagePath}`);
    }

    // 2. 同步删除mall-server的图片
    const relativePath = imageUrl.replace('/uploads/', '');
    const syncResult = deleteImageFromMallServer(relativePath);
    
    if (syncResult) {
      console.log(`✅ 同步删除mall-server图片: ${relativePath}`);
    } else {
      console.log(`⚠️ 同步删除mall-server图片失败: ${relativePath}`);
    }

    return {
      success: true,
      message: '图片删除成功'
    };
  } catch (error) {
    console.error(`❌ 删除图片失败: ${imageUrl}`, error);
    return { 
      success: false, 
      message: `删除图片失败: ${error.message}` 
    };
  }
}

/**
 * 从图片URL中提取相对路径
 * @param {string} imageUrl - 图片URL
 * @returns {string} - 相对路径
 */
function extractRelativePath(imageUrl) {
  if (!imageUrl) return '';
  
  // 移除开头的 /uploads/
  if (imageUrl.startsWith('/uploads/')) {
    return imageUrl.replace('/uploads/', '');
  }
  
  // 如果已经是相对路径，直接返回
  if (!imageUrl.startsWith('http') && !imageUrl.startsWith('/uploads/')) {
    return imageUrl;
  }
  
  return imageUrl;
}

/**
 * 批量删除图片
 * @param {Array} imageUrls - 图片URL数组
 * @param {string} type - 子目录类型
 * @returns {object} - { success, message, deletedCount }
 */
async function batchDeleteAndSync(imageUrls, type) {
  if (!Array.isArray(imageUrls) || imageUrls.length === 0) {
    return { success: false, message: '图片URL列表不能为空' };
  }

  console.log(`🗑️ 开始批量删除图片: ${imageUrls.length} 个, 类型: ${type}`);

  let successCount = 0;
  let failCount = 0;
  const errors = [];

  for (const imageUrl of imageUrls) {
    try {
      const result = await deleteAndSync(imageUrl, type);
      if (result.success) {
        successCount++;
      } else {
        failCount++;
        errors.push(`${imageUrl}: ${result.message}`);
      }
    } catch (error) {
      failCount++;
      errors.push(`${imageUrl}: ${error.message}`);
    }
  }

  console.log(`📊 批量删除完成: 成功 ${successCount} 个, 失败 ${failCount} 个`);

  return {
    success: failCount === 0,
    message: `批量删除完成: 成功 ${successCount} 个, 失败 ${failCount} 个`,
    deletedCount: successCount,
    failCount,
    errors: errors.length > 0 ? errors : undefined
  };
}

module.exports = {
  deleteAndSync,
  extractRelativePath,
  batchDeleteAndSync
}; 