const fs = require('fs');
const path = require('path');

/**
 * 删除 mall-server/uploads 下的图片文件
 * @param {string} imageUrl - 形如 /uploads/categories/xxx.jpg
 * @returns {boolean} 是否删除成功
 */
function deleteImageFile(imageUrl) {
  if (!imageUrl || typeof imageUrl !== 'string') return false;
  // 只处理 /uploads/ 开头的路径
  if (!imageUrl.startsWith('/uploads/')) return false;
  // mall-server/uploads 目录
  const mallServerUploads = path.join(__dirname, '../../mall-server/uploads');
  // 去掉 /uploads/ 前缀
  const relativePath = imageUrl.replace('/uploads/', '');
  const filePath = path.join(mallServerUploads, relativePath);
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      console.log(`[图片删除] 已删除: ${filePath}`);
      return true;
    } else {
      console.log(`[图片删除] 文件不存在: ${filePath}`);
      return false;
    }
  } catch (err) {
    console.error(`[图片删除] 删除失败: ${filePath}`, err);
    return false;
  }
}

module.exports = deleteImageFile; 