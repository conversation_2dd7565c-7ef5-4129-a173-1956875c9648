const path = require('path');
const fs = require('fs-extra');

/**
 * 商品图片处理工具类
 */
class ProductImageUtils {
  
  /**
   * 验证图片路径是否安全
   * @param {string} imagePath - 图片路径
   * @returns {boolean} 是否安全
   */
  static isValidImagePath(imagePath) {
    if (!imagePath || typeof imagePath !== 'string') {
      return false;
    }
    
    // 检查是否为相对路径
    if (!imagePath.startsWith('/uploads/products/')) {
      return false;
    }
    
    // 检查文件名是否包含危险字符
    const fileName = path.basename(imagePath);
    const dangerousChars = /[<>:"|?*\x00-\x1f]/;
    if (dangerousChars.test(fileName)) {
      return false;
    }
    
    return true;
  }
  
  /**
   * 修复图片路径为安全格式
   * @param {string} imagePath - 原始图片路径
   * @returns {string} 修复后的安全路径
   */
  static fixImagePath(imagePath) {
    if (!imagePath || typeof imagePath !== 'string') {
      return '';
    }
    
    // 如果是完整URL，提取相对路径
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      const urlParts = imagePath.split('/uploads/');
      if (urlParts.length > 1) {
        return '/uploads/' + urlParts[1];
      }
      return '';
    }
    
    // 如果已经是相对路径，验证格式
    if (imagePath.startsWith('/uploads/')) {
      // 确保是products目录
      if (!imagePath.startsWith('/uploads/products/')) {
        // 如果不是products目录，转换为products目录
        const fileName = path.basename(imagePath);
        return `/uploads/products/${fileName}`;
      }
      return imagePath;
    }
    
    // 其他情况，返回空字符串
    return '';
  }
  
  /**
   * 检查图片文件是否存在
   * @param {string} imagePath - 图片路径
   * @returns {boolean} 文件是否存在
   */
  static async checkImageExists(imagePath) {
    if (!this.isValidImagePath(imagePath)) {
      return false;
    }
    
    try {
      // 检查mall-server中的文件
      const mallServerPath = path.join(__dirname, '../../mall-server', imagePath);
      const exists = await fs.pathExists(mallServerPath);
      
      if (!exists) {
        console.warn(`图片文件不存在: ${mallServerPath}`);
      }
      
      return exists;
    } catch (error) {
      console.error(`检查图片文件失败: ${imagePath}`, error);
      return false;
    }
  }
  
  /**
   * 生成默认商品图片路径
   * @returns {string} 默认图片路径
   */
  static getDefaultImagePath() {
    return '/uploads/products/default-product.jpg';
  }
  
  /**
   * 批量验证商品图片路径
   * @param {Array} products - 商品数组
   * @returns {Object} 验证结果
   */
  static async validateProductImages(products) {
    const results = {
      valid: [],
      invalid: [],
      missing: [],
      fixed: []
    };
    
    for (const product of products) {
      const imagePath = product.image_url || product.main_image;
      
      if (!imagePath) {
        results.invalid.push({
          id: product.id,
          name: product.name,
          reason: '无图片路径'
        });
        continue;
      }
      
      // 检查路径格式
      if (!this.isValidImagePath(imagePath)) {
        const fixedPath = this.fixImagePath(imagePath);
        if (fixedPath) {
          results.fixed.push({
            id: product.id,
            name: product.name,
            original: imagePath,
            fixed: fixedPath
          });
        } else {
          results.invalid.push({
            id: product.id,
            name: product.name,
            original: imagePath,
            reason: '路径格式不正确'
          });
        }
        continue;
      }
      
      // 检查文件是否存在
      const exists = await this.checkImageExists(imagePath);
      if (exists) {
        results.valid.push({
          id: product.id,
          name: product.name,
          path: imagePath
        });
      } else {
        results.missing.push({
          id: product.id,
          name: product.name,
          path: imagePath
        });
      }
    }
    
    return results;
  }
}

module.exports = ProductImageUtils; 