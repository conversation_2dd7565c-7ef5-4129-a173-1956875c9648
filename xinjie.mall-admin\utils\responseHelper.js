/**
 * API响应格式统一工具
 * 确保所有API返回格式一致
 */

class ResponseHelper {
  
  /**
   * 成功响应
   * @param {Object} res - Express响应对象
   * @param {*} data - 响应数据
   * @param {string} message - 响应消息
   * @param {number} code - 状态码
   */
  static success(res, data = null, message = '操作成功', code = 200) {
    return res.status(code).json({
      success: true,
      code,
      message,
      data,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 错误响应
   * @param {Object} res - Express响应对象
   * @param {string} message - 错误消息
   * @param {number} code - 状态码
   * @param {*} error - 错误详情
   */
  static error(res, message = '操作失败', code = 500, error = null) {
    const response = {
      success: false,
      code,
      message,
      timestamp: new Date().toISOString()
    };

    // 开发环境下返回错误详情
    if (process.env.NODE_ENV === 'development' && error) {
      response.error = error;
    }

    return res.status(code).json(response);
  }

  /**
   * 参数错误响应
   * @param {Object} res - Express响应对象
   * @param {string} message - 错误消息
   */
  static badRequest(res, message = '参数错误') {
    return this.error(res, message, 400);
  }

  /**
   * 未授权响应
   * @param {Object} res - Express响应对象
   * @param {string} message - 错误消息
   */
  static unauthorized(res, message = '用户未登录') {
    return this.error(res, message, 401);
  }

  /**
   * 禁止访问响应
   * @param {Object} res - Express响应对象
   * @param {string} message - 错误消息
   */
  static forbidden(res, message = '无权限访问') {
    return this.error(res, message, 403);
  }

  /**
   * 资源不存在响应
   * @param {Object} res - Express响应对象
   * @param {string} message - 错误消息
   */
  static notFound(res, message = '资源不存在') {
    return this.error(res, message, 404);
  }

  /**
   * 分页数据响应
   * @param {Object} res - Express响应对象
   * @param {Array} list - 数据列表
   * @param {number} total - 总数
   * @param {number} page - 当前页
   * @param {number} pageSize - 每页数量
   * @param {string} message - 响应消息
   */
  static paginated(res, list, total, page, pageSize, message = '获取数据成功') {
    const data = {
      list,
      pagination: {
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPages: Math.ceil(total / pageSize),
        hasNext: page * pageSize < total,
        hasPrev: page > 1
      }
    };

    return this.success(res, data, message);
  }

  /**
   * 处理异步控制器错误
   * @param {Function} fn - 异步控制器函数
   * @returns {Function} 包装后的控制器函数
   */
  static asyncHandler(fn) {
    return (req, res, next) => {
      Promise.resolve(fn(req, res, next)).catch(next);
    };
  }

  /**
   * 验证必需参数
   * @param {Object} data - 数据对象
   * @param {Array} requiredFields - 必需字段数组
   * @throws {Error} 参数缺失错误
   */
  static validateRequired(data, requiredFields) {
    const missing = requiredFields.filter(field => {
      const value = data[field];
      return value === undefined || value === null || value === '';
    });

    if (missing.length > 0) {
      throw new Error(`缺少必需参数: ${missing.join(', ')}`);
    }
  }

  /**
   * 验证数字参数
   * @param {*} value - 值
   * @param {string} fieldName - 字段名
   * @param {Object} options - 验证选项
   * @throws {Error} 参数无效错误
   */
  static validateNumber(value, fieldName, options = {}) {
    const { min, max, integer = false } = options;

    if (isNaN(value)) {
      throw new Error(`${fieldName}必须是数字`);
    }

    const num = parseFloat(value);

    if (integer && !Number.isInteger(num)) {
      throw new Error(`${fieldName}必须是整数`);
    }

    if (min !== undefined && num < min) {
      throw new Error(`${fieldName}不能小于${min}`);
    }

    if (max !== undefined && num > max) {
      throw new Error(`${fieldName}不能大于${max}`);
    }

    return num;
  }
}

module.exports = ResponseHelper;
