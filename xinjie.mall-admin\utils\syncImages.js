const fs = require('fs');
const path = require('path');

// 配置路径
const ADMIN_UPLOAD_DIR = path.join(__dirname, '../public/uploads');
const MALL_SERVER_UPLOAD_DIR = path.join(__dirname, '../../mall-server/uploads');

/**
 * 同步图片到mall-server目录
 * @param {string} imagePath - 相对于uploads的图片路径，如 'banners/image.png'
 * @returns {boolean} - 同步是否成功
 */
function syncImageToMallServer(imagePath) {
  try {
    // 构建完整路径
    const sourcePath = path.join(ADMIN_UPLOAD_DIR, imagePath);
    const targetPath = path.join(MALL_SERVER_UPLOAD_DIR, imagePath);
    
    // 检查源文件是否存在
    if (!fs.existsSync(sourcePath)) {
      console.log(`[图片同步] 源文件不存在: ${sourcePath}`);
      return false;
    }
    
    // 确保目标目录存在
    const targetDir = path.dirname(targetPath);
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
      console.log(`[图片同步] 创建目标目录: ${targetDir}`);
    }
    
    // 复制文件
    fs.copyFileSync(sourcePath, targetPath);
    console.log(`[图片同步] 成功同步图片: ${imagePath}`);
    console.log(`[图片同步] 从: ${sourcePath}`);
    console.log(`[图片同步] 到: ${targetPath}`);
    
    return true;
  } catch (error) {
    console.error(`[图片同步] 同步失败: ${imagePath}`, error);
    return false;
  }
}

/**
 * 删除mall-server中的图片
 * @param {string} imagePath - 相对于uploads的图片路径
 * @returns {boolean} - 删除是否成功
 */
function deleteImageFromMallServer(imagePath) {
  try {
    const targetPath = path.join(MALL_SERVER_UPLOAD_DIR, imagePath);
    
    if (fs.existsSync(targetPath)) {
      fs.unlinkSync(targetPath);
      console.log(`[图片同步] 成功删除图片: ${imagePath}`);
      return true;
    } else {
      console.log(`[图片同步] 目标文件不存在，无需删除: ${imagePath}`);
      return true;
    }
  } catch (error) {
    console.error(`[图片同步] 删除失败: ${imagePath}`, error);
    return false;
  }
}

/**
 * 批量同步目录中的所有图片
 * @param {string} subDir - 子目录名称，如 'banners', 'products'
 */
function syncDirectory(subDir) {
  try {
    const sourceDir = path.join(ADMIN_UPLOAD_DIR, subDir);
    const targetDir = path.join(MALL_SERVER_UPLOAD_DIR, subDir);
    
    if (!fs.existsSync(sourceDir)) {
      console.log(`[图片同步] 源目录不存在: ${sourceDir}`);
      return;
    }
    
    // 确保目标目录存在
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
    }
    
    const files = fs.readdirSync(sourceDir);
    let successCount = 0;
    
    files.forEach(file => {
      const sourcePath = path.join(sourceDir, file);
      const targetPath = path.join(targetDir, file);
      
      if (fs.statSync(sourcePath).isFile()) {
        try {
          fs.copyFileSync(sourcePath, targetPath);
          successCount++;
          console.log(`[图片同步] 同步文件: ${subDir}/${file}`);
        } catch (error) {
          console.error(`[图片同步] 同步文件失败: ${subDir}/${file}`, error);
        }
      }
    });
    
    console.log(`[图片同步] 目录 ${subDir} 同步完成，成功同步 ${successCount} 个文件`);
  } catch (error) {
    console.error(`[图片同步] 同步目录失败: ${subDir}`, error);
  }
}

module.exports = {
  syncImageToMallServer,
  deleteImageFromMallServer,
  syncDirectory
}; 