const path = require('path');
const fs = require('fs-extra');
const { syncImageToMallServer } = require('./syncImages');

/**
 * 统一上传并同步图片
 * @param {object} file - multer上传后的文件对象
 * @param {string} type - 子目录，如 'products'、'categories'、'avatars'、'banners'
 * @returns {object} - { url, filename, success, message }
 */
async function uploadAndSync(file, type) {
  if (!file) {
    return { success: false, message: '没有上传文件' };
  }
  try {
    // 目标目录
    const targetDir = path.join(__dirname, '../public/uploads', type);
    await fs.ensureDir(targetDir);

    // 新文件名
    const ext = path.extname(file.originalname);
    const newFilename = `${type}-${Date.now()}${ext}`;
    const newPath = path.join(targetDir, newFilename);

    // 移动文件
    await fs.move(file.path, newPath);

    // 生成URL
    const imageUrl = `/uploads/${type}/${newFilename}`;

    // 同步到mall-server
    const relativePath = `${type}/${newFilename}`;
    syncImageToMallServer(relativePath);

    return {
      success: true,
      url: imageUrl,
      filename: newFilename
    };
  } catch (error) {
    return { success: false, message: error.message };
  }
}

module.exports = { uploadAndSync }; 