<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>心洁茶叶后台管理系统</title>
  <style>
    body { font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; background: #f5f6fa; margin: 0; }
    .layout { display: flex; min-height: 100vh; }
    .sidebar {
      width: 220px;
      background: linear-gradient(180deg, #2d8cf0 80%, #1a5ca0 100%);
      color: #fff;
      display: flex;
      flex-direction: column;
      align-items: stretch;
      box-shadow: 2px 0 8px #e0e0e0;
      z-index: 2;
      transition: width 0.2s;
    }
    .sidebar.collapsed { width: 60px; }
    .sidebar-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 32px 0 16px 0;
      border-bottom: 1px solid #3a7bd5;
      transition: opacity 0.2s;
    }
    .sidebar.collapsed .sidebar-header { opacity: 0; height: 0; padding: 0; overflow: hidden; }
    .avatar {
      width: 64px;
      height: 64px;
      border-radius: 50%;
      background: #fff;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32px;
      color: #2d8cf0;
      font-weight: bold;
      box-shadow: 0 2px 8px #1a5ca033;
    }
    .sidebar-title {
      font-size: 20px;
      font-weight: bold;
      margin-bottom: 4px;
    }
    .sidebar-desc {
      font-size: 13px;
      color: #e0e0e0;
      margin-bottom: 8px;
    }
    .menu {
      flex: 1;
      display: flex;
      flex-direction: column;
      margin-top: 24px;
    }
    .menu a {
      color: #fff;
      text-decoration: none;
      font-size: 17px;
      padding: 14px 36px;
      border: none;
      background: none;
      text-align: left;
      transition: background 0.2s, color 0.2s, padding 0.2s, font-size 0.2s;
      border-radius: 0 24px 24px 0;
      margin-bottom: 4px;
      position: relative;
      letter-spacing: 1px;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .sidebar.collapsed .menu a { padding: 14px 10px; font-size: 0; justify-content: center; }
    .sidebar.collapsed .menu a span { display: none; }
    .menu a:hover, .menu a.active {
      background: #fff;
      color: #2d8cf0;
      font-weight: bold;
    }
    .menu a.active::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 4px;
      height: 100%;
      background: #2d8cf0;
      border-radius: 0 4px 4px 0;
    }

    /* 子菜单样式 */
    .menu-group {
      margin-bottom: 4px;
    }

    .menu-group-title {
      color: #fff;
      text-decoration: none;
      font-size: 17px;
      padding: 14px 36px;
      border: none;
      background: none;
      text-align: left;
      transition: background 0.2s, color 0.2s;
      border-radius: 0 24px 24px 0;
      position: relative;
      letter-spacing: 1px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
    }

    .menu-group-title:hover {
      background: rgba(255, 255, 255, 0.1);
    }

    .menu-arrow {
      font-size: 12px;
      transition: transform 0.2s;
    }

    .menu-group.expanded .menu-arrow {
      transform: rotate(180deg);
    }

    .menu-submenu {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease;
      background: rgba(0, 0, 0, 0.1);
    }

    .menu-group.expanded .menu-submenu {
      max-height: 200px;
    }

    .menu-submenu a {
      font-size: 15px;
      padding: 12px 50px;
      margin-bottom: 2px;
      border-radius: 0 20px 20px 0;
    }

    .sidebar.collapsed .menu-group-title {
      padding: 14px 10px;
      font-size: 0;
      justify-content: center;
    }

    .sidebar.collapsed .menu-group-title span {
      display: none;
    }

    .sidebar.collapsed .menu-submenu {
      display: none;
    }
    .collapse-btn {
      background: none;
      border: none;
      color: #fff;
      font-size: 20px;
      cursor: pointer;
      margin: 12px 0 0 0;
      align-self: flex-end;
      padding: 0 16px;
      transition: color 0.2s;
    }
    .collapse-btn:hover { color: #ffd700; }
    .logout {
      margin: 24px 0 16px 0;
      padding: 12px 36px;
      background: #fff;
      color: #2d8cf0;
      border: none;
      border-radius: 0 24px 24px 0;
      font-size: 17px;
      cursor: pointer;
      transition: background 0.2s, color 0.2s;
      text-align: left;
    }
    .sidebar.collapsed .logout { padding: 12px 10px; font-size: 0; }
    .logout:hover { background: #f0f7ff; color: #1a5ca0; }
    .main { flex: 1; display: flex; flex-direction: column; background: #f5f6fa; }
    .topbar { height: 56px; background: #fff; box-shadow: 0 2px 8px #e0e0e0; display: flex; align-items: center; justify-content: space-between; padding: 0 32px; z-index: 1; }
    .topbar-title { font-size: 22px; color: #2d8cf0; font-weight: bold; letter-spacing: 2px; }
    .topbar-user { font-size: 16px; color: #666; }
    .content { flex: 1; display: flex; align-items: center; justify-content: center; flex-direction: column; padding: 60px 32px; width: 100%; }
    .welcome { color: #333; font-size: 22px; margin-bottom: 12px; font-weight: bold; }
    .desc { color: #888; font-size: 16px; margin-bottom: 24px; }
    .section { background: #fff; border-radius: 12px; box-shadow: 0 2px 16px #e0e0e0; padding: 32px 40px; min-width: 320px; min-height: 180px; margin-bottom: 24px; width: 100%; max-width: 700px; }
    .section-title { font-size: 20px; color: #2d8cf0; font-weight: bold; margin-bottom: 18px; }
    .section-content { color: #444; font-size: 16px; }
    @media (max-width: 700px) {
      .layout { flex-direction: column; }
      .sidebar { width: 100vw; flex-direction: row; }
      .sidebar-header { display: none; }
      .menu { flex-direction: row; margin-top: 0; }
      .menu a { border-radius: 0; padding: 12px 10px; font-size: 0; }
      .menu a span { display: none; }
      .main { padding: 0; }
      .topbar { padding: 0 10px; }
      .section { min-width: 0; padding: 16px 8px; }
    }
  </style>
</head>
<body>
  <div class="layout">
    <nav class="sidebar" id="sidebar">
      <div class="sidebar-header">
        <div class="avatar" id="avatar">管</div>
        <div class="sidebar-title">心洁茶叶</div>
        <div class="sidebar-desc">后台管理系统</div>
      </div>
      <div class="menu">
        <a href="#" data-section="home" class="active"><span>后台首页</span></a>
        <a href="#" data-section="banners"><span>轮播图管理</span></a>

        <!-- 商品管理子菜单 -->
        <div class="menu-group">
          <a href="#" class="menu-group-title" onclick="toggleMenuGroup(this)">
            <span>商品管理</span>
            <span class="menu-arrow">▼</span>
          </a>
          <div class="menu-submenu">
            <a href="#" data-section="products"><span>商品列表</span></a>
            <a href="#" data-section="discounts"><span>折扣管理</span></a>
          </div>
        </div>

        <a href="#" data-section="orders"><span>订单管理</span></a>
        <a href="#" data-section="users"><span>用户管理</span></a>
        <a href="#" data-section="categories"><span>分类管理</span></a>
        <a href="#" data-section="settings"><span>系统设置</span></a>
        <a href="/" id="homeLink"><span>商城首页</span></a>
      </div>
      <button class="collapse-btn" id="collapseBtn" title="收起/展开菜单">☰</button>
      <button class="logout" onclick="logout()"><span>退出登录</span></button>
    </nav>
    <div class="main">
      <div class="topbar">
        <div class="topbar-title">心洁茶叶后台管理系统</div>
        <div class="topbar-user" id="topbarUser">欢迎您，管理员</div>
      </div>
      <div class="breadcrumb" id="breadcrumb" style="width:100%;max-width:700px;margin:24px auto 0 auto;font-size:15px;color:#888;letter-spacing:1px;">
        当前位置：<span id="breadcrumbText">后台首页</span>
      </div>
      <div class="content" id="contentArea">
        <div class="welcome">欢迎进入心洁茶叶后台管理系统</div>
        <div class="desc">请通过左侧菜单选择管理功能</div>
      </div>
    </div>
  </div>
  <script src="/js/dashboard.js"></script>
</body>
</html> 