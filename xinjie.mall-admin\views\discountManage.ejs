<div class="section">
  <div class="section-title">折扣管理</div>
  
  <!-- 搜索和操作区域 -->
  <div style="margin-bottom: 16px; display: flex; justify-content: space-between; align-items: center;">
    <div style="display: flex; gap: 12px; align-items: center;">
      <input type="text" id="searchName" placeholder="搜索折扣名称" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; width: 200px;">
      <select id="searchType" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
        <option value="">全部类型</option>
        <option value="1">百分比折扣</option>
        <option value="2">固定金额折扣</option>
        <option value="3">满减折扣</option>
      </select>
      <select id="searchStatus" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
        <option value="">全部状态</option>
        <option value="1">启用</option>
        <option value="0">禁用</option>
      </select>
      <button onclick="searchDiscounts()" style="background: #2d8cf0; color: #fff; border: none; border-radius: 4px; padding: 8px 16px; cursor: pointer;">搜索</button>
      <button onclick="resetSearch()" style="background: #f7f7f7; color: #666; border: 1px solid #ddd; border-radius: 4px; padding: 8px 16px; cursor: pointer;">重置</button>
    </div>
    <button onclick="showAddDiscountModal()" style="background: #2d8cf0; color: #fff; border: none; border-radius: 6px; padding: 8px 24px; font-size: 16px; cursor: pointer;">
      添加折扣
    </button>
  </div>

  <!-- 折扣列表表格 -->
  <table style="width: 100%; border-collapse: collapse; background: #fff; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
    <thead>
      <tr style="background: #f0f7ff;">
        <th style="padding: 12px 8px; text-align: left; font-weight: 600; color: #333;">ID</th>
        <th style="padding: 12px 8px; text-align: left; font-weight: 600; color: #333;">折扣名称</th>
        <th style="padding: 12px 8px; text-align: left; font-weight: 600; color: #333;">类型</th>
        <th style="padding: 12px 8px; text-align: left; font-weight: 600; color: #333;">折扣值</th>
        <th style="padding: 12px 8px; text-align: left; font-weight: 600; color: #333;">适用范围</th>
        <th style="padding: 12px 8px; text-align: left; font-weight: 600; color: #333;">有效期</th>
        <th style="padding: 12px 8px; text-align: left; font-weight: 600; color: #333;">状态</th>
        <th style="padding: 12px 8px; text-align: left; font-weight: 600; color: #333;">使用情况</th>
        <th style="padding: 12px 8px; text-align: left; font-weight: 600; color: #333;">操作</th>
      </tr>
    </thead>
    <tbody id="discountTableBody">
      <!-- 数据通过AJAX填充 -->
    </tbody>
  </table>

  <!-- 分页 -->
  <div id="discountPagination" style="margin-top: 16px; text-align: right;"></div>
</div>

<!-- 添加/编辑折扣模态框 -->
<div id="discountModal" class="modal" style="display: none;">
  <div class="modal-content" style="width: 800px; max-height: 90vh; overflow-y: auto;">
    <div class="modal-header">
      <h3 id="discountModalTitle">添加折扣</h3>
      <span class="close" onclick="closeDiscountModal()">&times;</span>
    </div>
    <div class="modal-body">
      <form id="discountForm">
        <input type="hidden" id="discountId" name="id">
        
        <!-- 基本信息 -->
        <div class="form-section">
          <h4>基本信息</h4>
          <div class="form-row">
            <div class="form-group">
              <label for="discountName">折扣名称 <span class="required">*</span></label>
              <input type="text" id="discountName" name="name" required placeholder="请输入折扣名称">
            </div>
            <div class="form-group">
              <label for="discountType">折扣类型 <span class="required">*</span></label>
              <select id="discountType" name="type" required onchange="onDiscountTypeChange()">
                <option value="">请选择折扣类型</option>
                <option value="1">百分比折扣</option>
                <option value="2">固定金额折扣</option>
                <option value="3">满减折扣</option>
              </select>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="discountValue">折扣值 <span class="required">*</span></label>
              <input type="number" id="discountValue" name="value" required step="0.01" min="0" placeholder="请输入折扣值">
              <small id="discountValueHint" class="form-hint"></small>
            </div>
            <div class="form-group" id="minAmountGroup" style="display: none;">
              <label for="minAmount">最低消费金额</label>
              <input type="number" id="minAmount" name="min_amount" step="0.01" min="0" placeholder="满减折扣的最低消费金额">
            </div>
          </div>

          <div class="form-row">
            <div class="form-group" id="maxDiscountGroup">
              <label for="maxDiscount">最大折扣金额</label>
              <input type="number" id="maxDiscount" name="max_discount" step="0.01" min="0" placeholder="限制最大优惠金额（可选）">
              <small class="form-hint">仅对百分比折扣有效，限制单次最大优惠金额</small>
            </div>
            <div class="form-group">
              <label for="priority">优先级</label>
              <input type="number" id="priority" name="priority" min="0" value="0" placeholder="数字越大优先级越高">
            </div>
          </div>

          <div class="form-group">
            <label for="description">折扣描述</label>
            <textarea id="description" name="description" rows="3" placeholder="请输入折扣描述"></textarea>
          </div>
        </div>

        <!-- 时间设置 -->
        <div class="form-section">
          <h4>时间设置</h4>
          <div class="form-row">
            <div class="form-group">
              <label for="startTime">开始时间 <span class="required">*</span></label>
              <input type="datetime-local" id="startTime" name="start_time" required>
            </div>
            <div class="form-group">
              <label for="endTime">结束时间 <span class="required">*</span></label>
              <input type="datetime-local" id="endTime" name="end_time" required>
            </div>
          </div>
        </div>

        <!-- 使用限制 -->
        <div class="form-section">
          <h4>使用限制</h4>
          <div class="form-row">
            <div class="form-group">
              <label for="usageLimit">总使用次数限制</label>
              <input type="number" id="usageLimit" name="usage_limit" min="1" placeholder="留空表示无限制">
            </div>
            <div class="form-group">
              <label for="userLimit">单用户使用次数限制</label>
              <input type="number" id="userLimit" name="user_limit" min="1" placeholder="留空表示无限制">
            </div>
          </div>
        </div>

        <!-- 适用范围 -->
        <div class="form-section">
          <h4>适用范围</h4>
          <div class="form-group">
            <label for="applicableTo">适用范围 <span class="required">*</span></label>
            <select id="applicableTo" name="applicable_to" required onchange="onApplicableToChange()">
              <option value="1">全部商品</option>
              <option value="2">指定商品</option>
              <option value="3">指定分类</option>
            </select>
          </div>
          
          <!-- 指定商品选择 -->
          <div id="productSelection" class="form-group" style="display: none;">
            <label>选择商品</label>
            <div style="border: 1px solid #ddd; border-radius: 4px; padding: 12px; max-height: 200px; overflow-y: auto;">
              <div id="productList">
                <!-- 商品列表通过AJAX加载 -->
              </div>
            </div>
          </div>

          <!-- 指定分类选择 -->
          <div id="categorySelection" class="form-group" style="display: none;">
            <label>选择分类</label>
            <div style="border: 1px solid #ddd; border-radius: 4px; padding: 12px; max-height: 200px; overflow-y: auto;">
              <div id="categoryList">
                <!-- 分类列表通过AJAX加载 -->
              </div>
            </div>
          </div>
        </div>

        <!-- 状态设置 -->
        <div class="form-section">
          <h4>状态设置</h4>
          <div class="form-group">
            <label>
              <input type="checkbox" id="discountStatus" name="status" value="1" checked>
              启用折扣
            </label>
          </div>
        </div>
      </form>
    </div>
    <div class="modal-footer">
      <button type="button" onclick="closeDiscountModal()" class="btn-secondary">取消</button>
      <button type="button" onclick="saveDiscount()" class="btn-primary">保存</button>
    </div>
  </div>
</div>

<style>
.modal {
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.modal-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.close {
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  color: #999;
}

.close:hover {
  color: #333;
}

.modal-body {
  padding: 20px 24px;
}

.modal-footer {
  padding: 16px 24px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.form-section {
  margin-bottom: 24px;
}

.form-section h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-group {
  flex: 1;
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  color: #333;
  font-weight: 500;
}

.required {
  color: #ff4d4f;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #2d8cf0;
  box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.1);
}

.form-hint {
  display: block;
  margin-top: 4px;
  color: #666;
  font-size: 12px;
}

.btn-primary {
  background: #2d8cf0;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 8px 24px;
  cursor: pointer;
  font-size: 14px;
}

.btn-primary:hover {
  background: #2b7ce9;
}

.btn-secondary {
  background: #f7f7f7;
  color: #666;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 24px;
  cursor: pointer;
  font-size: 14px;
}

.btn-secondary:hover {
  background: #f0f0f0;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-active {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-disabled {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.status-expired {
  background: #f0f0f0;
  color: #999;
  border: 1px solid #d9d9d9;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}
</style>

<script src="/js/discountManage.js"></script>
