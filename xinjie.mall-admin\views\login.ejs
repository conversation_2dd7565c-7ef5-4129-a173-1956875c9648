<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>登录 - 心洁茶叶后台管理</title>
  <style>
    body { font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; background: #f5f6fa; margin: 0; }
    .container { max-width: 400px; margin: 100px auto; background: #fff; border-radius: 12px; box-shadow: 0 2px 16px #e0e0e0; padding: 40px 32px; text-align: center; }
    h2 { color: #2d8cf0; margin-bottom: 32px; font-size: 2rem; }
    form { display: flex; flex-direction: column; gap: 18px; }
    input { padding: 10px 12px; border: 1px solid #d0d0d0; border-radius: 6px; font-size: 16px; }
    button { background: #2d8cf0; color: #fff; border: none; border-radius: 6px; padding: 12px; font-size: 18px; cursor: pointer; transition: background 0.2s; }
    button:hover { background: #1a5ca0; }
    .back { margin-top: 18px; display: block; color: #888; text-decoration: none; font-size: 15px; }
    .back:hover { color: #2d8cf0; }
  </style>
</head>
<body>
  <div class="container">
    <h2>登录后台管理</h2>
    <form id="loginForm">
      <input type="text" name="username" placeholder="用户名" required>
      <input type="password" name="password" placeholder="密码" required>
      <button type="submit">登录</button>
    </form>
    <a class="back" href="/">返回首页</a>
    <div id="errorModal" style="display:none;position:fixed;left:0;top:0;width:100vw;height:100vh;background:rgba(0,0,0,0.3);z-index:999;align-items:center;justify-content:center;">
      <div style="background:#fff;padding:24px 32px;border-radius:8px;min-width:220px;text-align:center;box-shadow:0 2px 16px #e0e0e0;">
        <div id="errorMsg" style="color:#d32f2f;font-size:16px;margin-bottom:16px;"></div>
        <button id="closeErrorModal" style="background:#2d8cf0;color:#fff;border:none;border-radius:6px;padding:8px 24px;font-size:16px;cursor:pointer;">关闭</button>
      </div>
    </div>
    <script src="/js/login.js"></script>
  </div>
</body>
</html> 