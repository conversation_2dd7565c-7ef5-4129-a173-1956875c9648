<div class="section">
  <div class="section-title">商品管理</div>
  <div style="margin-bottom:16px;">
    <button onclick="showAddProductModal()" style="background:#2d8cf0;color:#fff;border:none;border-radius:6px;padding:8px 24px;font-size:16px;cursor:pointer;">添加商品</button>
  </div>
  <table style="width:100%;border-collapse:collapse;">
    <thead>
      <tr style="background:#f0f7ff;">
        <th style="padding:8px 4px;">ID</th>
        <th>图片</th>
        <th>名称</th>
        <th>原价</th>
        <th>折扣价</th>
        <th>库存</th>
        <th>状态</th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody id="productTableBody">
      <!-- 数据通过AJAX填充 -->
    </tbody>
  </table>
  <div id="productPagination" style="margin-top:16px;text-align:right;"></div>
</div>

<!-- 添加商品模态框 -->
<div id="addProductModal" style="display:none;position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:1000;">
  <div style="position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:white;padding:24px;border-radius:8px;width:500px;max-height:80vh;overflow-y:auto;">
    <h3 style="margin:0 0 20px 0;">添加商品</h3>
    <form id="addProductForm">
      <div style="margin-bottom:16px;">
        <label style="display:block;margin-bottom:4px;">商品名称 *</label>
        <input type="text" name="name" required style="width:100%;padding:8px;border:1px solid #ddd;border-radius:4px;">
      </div>
      <div style="margin-bottom:16px;">
        <label style="display:block;margin-bottom:4px;">商品描述</label>
        <textarea name="description" style="width:100%;padding:8px;border:1px solid #ddd;border-radius:4px;height:80px;"></textarea>
      </div>
      <div style="margin-bottom:16px;">
        <label style="display:block;margin-bottom:4px;">价格 *</label>
        <input type="number" name="price" required step="0.01" min="0" style="width:100%;padding:8px;border:1px solid #ddd;border-radius:4px;">
      </div>
      <div style="margin-bottom:16px;">
        <label style="display:block;margin-bottom:4px;">原价</label>
        <input type="number" name="original_price" step="0.01" min="0" style="width:100%;padding:8px;border:1px solid #ddd;border-radius:4px;">
      </div>
      <div style="margin-bottom:16px;">
        <label style="display:block;margin-bottom:4px;">库存</label>
        <input type="number" name="stock" min="0" value="0" style="width:100%;padding:8px;border:1px solid #ddd;border-radius:4px;">
      </div>
      <div style="margin-bottom:16px;">
        <label style="display:block;margin-bottom:4px;">分类 *</label>
        <select name="category_id" required style="width:100%;padding:8px;border:1px solid #ddd;border-radius:4px;">
          <option value="">请选择分类</option>
          <!-- 分类选项将通过JS动态加载 -->
        </select>
      </div>
      <div style="margin-bottom:16px;">
        <label style="display:block;margin-bottom:4px;">商品图片 *</label>
        <input type="file" name="image" accept="image/*" required style="width:100%;padding:8px;border:1px solid #ddd;border-radius:4px;">
        <div id="imagePreview" style="margin-top:8px;"></div>
      </div>
      <div style="margin-bottom:16px;">
        <label style="display:flex;align-items:center;">
          <input type="checkbox" name="status" checked style="margin-right:8px;">
          上架状态
        </label>
      </div>
      <div style="margin-bottom:16px;">
        <label style="display:flex;align-items:center;">
          <input type="checkbox" name="is_hot" style="margin-right:8px;">
          热门商品
        </label>
      </div>
      <div style="margin-bottom:16px;">
        <label style="display:flex;align-items:center;">
          <input type="checkbox" name="is_recommend" style="margin-right:8px;">
          推荐商品
        </label>
      </div>
      <div style="text-align:right;">
        <button type="button" onclick="hideAddProductModal()" style="margin-right:8px;padding:8px 16px;background:#f7f7f7;border:1px solid #ddd;border-radius:4px;cursor:pointer;">取消</button>
        <button type="submit" style="padding:8px 16px;background:#2d8cf0;color:white;border:none;border-radius:4px;cursor:pointer;">添加</button>
      </div>
    </form>
  </div>
</div>

<script src="/js/productManage.js"></script>