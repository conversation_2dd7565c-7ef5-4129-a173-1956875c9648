# 心洁茗茶商城上线完整操作指南

## 🌐 第一步：域名购买与配置

### 1.1 域名购买
**推荐服务商：**
- 阿里云（万网）: https://wanwang.aliyun.com/
- 腾讯云: https://dnspod.cloud.tencent.com/
- 华为云: https://www.huaweicloud.com/

**域名选择：**
- 主域名: xinjie-tea.com (¥55/年)
- 子域名规划：
  ```
  ├── www.xinjie-tea.com     # 官网首页
  ├── api.xinjie-tea.com     # API接口服务
  └── admin.xinjie-tea.com   # 管理后台
  ```

### 1.2 域名解析配置
**购买域名后，在域名控制台添加DNS记录：**

| 记录类型 | 主机记录 | 记录值 | TTL |
|---------|---------|--------|-----|
| A | @ | 您的服务器IP地址 | 600 |
| A | www | 您的服务器IP地址 | 600 |
| A | api | 您的服务器IP地址 | 600 |
| A | admin | 您的服务器IP地址 | 600 |

**验证域名解析：**
```bash
# 检查域名是否解析成功
ping xinjie-tea.com
nslookup api.xinjie-tea.com
```

**注意事项：**
- DNS解析生效时间：10分钟-24小时
- 建议TTL设置为600秒，便于后续修改
- 确保所有子域名都指向同一个服务器IP

## 🖥️ 第二步：云服务器购买与配置

### 2.1 服务器购买
**推荐配置（阿里云ECS）：**
- CPU: 2核
- 内存: 4GB
- 硬盘: 40GB SSD
- 带宽: 5Mbps
- 操作系统: Ubuntu 20.04 LTS
- 地域: 华东1（杭州）
- 费用: ¥200-270/月

**购买后您会收到：**
- 服务器公网IP地址（如：123.456.789.10）
- 用户名：root
- 密码或SSH密钥文件

### 2.2 安全组配置
**在云服务器控制台配置安全组规则：**

| 方向 | 协议 | 端口 | 源地址 | 说明 |
|-----|------|------|--------|------|
| 入方向 | TCP | 22 | 0.0.0.0/0 | SSH连接 |
| 入方向 | TCP | 80 | 0.0.0.0/0 | HTTP访问 |
| 入方向 | TCP | 443 | 0.0.0.0/0 | HTTPS访问 |
| 入方向 | TCP | 4000 | 0.0.0.0/0 | API服务 |
| 入方向 | TCP | 8081 | 0.0.0.0/0 | 管理后台 |

### 2.3 首次连接服务器
**Windows用户：**
```bash
# 方法1：使用PowerShell（Windows 10+）
ssh root@您的服务器IP

# 方法2：下载PuTTY工具
# 下载地址：https://www.putty.org/
# 主机名：您的服务器IP
# 端口：22
# 连接类型：SSH
```

**Mac/Linux用户：**
```bash
# 终端连接
ssh root@您的服务器IP
# 输入密码后即可登录
```

**连接成功标志：**
```bash
Welcome to Ubuntu 20.04.x LTS (GNU/Linux)
root@your-server:~#
```

## ⚙️ 第三步：服务器环境配置

### 3.1 基础安全配置
```bash
# 连接服务器
ssh root@您的服务器IP地址

# 1. 更新系统
apt update && apt upgrade -y

# 2. 创建部署用户（安全考虑，不要一直用root）
adduser deploy
usermod -aG sudo deploy

# 3. 配置防火墙基础规则
ufw allow ssh
ufw allow 'Nginx Full'
ufw allow 4000
ufw allow 8081
ufw --force enable

# 4. 设置时区
timedatectl set-timezone Asia/Shanghai
```

### 3.2 安装运行环境
```bash
# 安装Node.js 18.x（推荐最新LTS版本）
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
apt install -y nodejs

# 验证安装
node --version  # 应显示 v18.x.x
npm --version   # 应显示对应版本

# 安装MySQL 8.0
apt install -y mysql-server
systemctl start mysql
systemctl enable mysql

# MySQL安全配置
mysql_secure_installation
# 按提示设置root密码，删除匿名用户等

# 安装Redis（缓存服务）
apt install -y redis-server
systemctl start redis-server
systemctl enable redis-server

# 安装Nginx（Web服务器）
apt install -y nginx
systemctl start nginx
systemctl enable nginx

# 安装PM2（进程管理器）
npm install -g pm2

# 安装SSL证书工具
apt install -y certbot python3-certbot-nginx

# 安装其他必要工具
apt install -y git vim curl wget unzip
```

### 3.3 验证环境安装
```bash
# 检查服务状态
systemctl status nginx    # 应显示 active (running)
systemctl status mysql    # 应显示 active (running)
systemctl status redis    # 应显示 active (running)

# 检查端口监听
netstat -tlnp | grep :80   # Nginx
netstat -tlnp | grep :3306 # MySQL
netstat -tlnp | grep :6379 # Redis
```

mysql -u root -p

CREATE DATABASE xinjie_mall DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'xinjie_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON xinjie_mall.* TO 'xinjie_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# 导入数据表结构
mysql -u xinjie_user -p xinjie_mall < 数据库建表语句.sql

第四步：代码部署
上传代码
# 创建项目目录
mkdir -p /var/www/xinjie-tea
cd /var/www/xinjie-tea

# 使用Git克隆代码
git clone https://github.com/your-username/xinjie-tea.git .

# 或使用SCP上传
scp -r /local/path/xinjie-tea root@服务器IP:/var/www/xinjie-tea
# 后端依赖
cd /var/www/xinjie-tea/mall-server
npm install --production

# 管理后台依赖
cd ../xinjie.mall-admin
npm install --production
npm run build

NODE_ENV=production
PORT=4000

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=xinjie_mall
DB_USER=root
DB_PASSWORD=

# 微信小程序配置
WX_APP_ID=wx8792033d9e7052f1
WX_APP_SECRET=5623b74c771d82f6184ec72d319688d4

# JWT配置
JWT_SECRET=your_production_jwt_secret_key

🌐 第五步：Nginx配置
Nginx的作用
反向代理: 转发请求到Node.js应用
静态文件服务: 直接提供图片等静态资源
SSL终止: 处理HTTPS加密解密
负载均衡: 分发请求到多个服务实例
安全防护: 防止恶意请求

nano /etc/nginx/sites-available/xinjie-tea

# 上游服务器配置
upstream xinjie_api {
    server 127.0.0.1:4000;
    keepalive 32;
}

upstream xinjie_admin {
    server 127.0.0.1:8081;
    keepalive 32;
}

# API服务器配置
server {
    listen 80;
    server_name api.xinjie-tea.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.xinjie-tea.com;

    # SSL证书配置
    ssl_certificate /etc/letsencrypt/live/api.xinjie-tea.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.xinjie-tea.com/privkey.pem;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # API代理
    location /api/ {
        proxy_pass http://xinjie_api;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态文件服务
    location /uploads/ {
        alias /var/www/xinjie-tea/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}

# 管理后台配置
server {
    listen 80;
    server_name admin.xinjie-tea.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name admin.xinjie-tea.com;

    ssl_certificate /etc/letsencrypt/live/admin.xinjie-tea.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/admin.xinjie-tea.com/privkey.pem;

    location / {
        proxy_pass http://xinjie_admin;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# 创建软链接
ln -s /etc/nginx/sites-available/xinjie-tea /etc/nginx/sites-enabled/

# 删除默认配置
rm /etc/nginx/sites-enabled/default

# 测试配置
nginx -t

# 重启Nginx
systemctl restart nginx

🔒 第六步：SSL证书配置
SSL证书的作用
数据加密: 保护用户数据传输安全
身份验证: 证明网站身份真实性
微信要求: 小程序必须使用HTTPS接口
SEO优势: 搜索引擎优先收录HTTPS网站

# 为API域名申请证书
certbot --nginx -d api.xinjie-tea.com

# 为管理后台域名申请证书
certbot --nginx -d admin.xinjie-tea.com

# 设置自动续期
crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet

# 检查证书状态
certbot certificates

# 测试续期
certbot renew --dry-run

🚀 第七步：启动应用服务
PM2配置文件
创建 ecosystem.config.js：

module.exports = {
  apps: [
    {
      name: 'xinjie-api',
      script: './mall-server/app.js',
      instances: 2,
      exec_mode: 'cluster',
      env_production: {
        NODE_ENV: 'production',
        PORT: 4000
      }
    },
    {
      name: 'xinjie-admin',
      script: './xinjie.mall-admin/app.js',
      instances: 1,
      env_production: {
        NODE_ENV: 'production',
        PORT: 8081
      }
    }
  ]
};

cd /var/www/xinjie-tea

# 启动应用
pm2 start ecosystem.config.js --env production

# 保存配置
pm2 save

# 设置开机自启
pm2 startup

# 查看状态
pm2 status
pm2 logs


🧪 第八步：测试验证
API接口测试

# 健康检查
curl https://api.xinjie-tea.com/health

# 分类接口
curl https://api.xinjie-tea.com/api/front/category/list

# 轮播图接口
curl https://api.xinjie-tea.com/api/front/banner/list

管理后台测试
浏览器访问：https://admin.xinjie-tea.com

小程序配置更新
修改 xinjie-mall-miniprogram/config/api.js：

const config = {
  baseURL: 'https://api.xinjie-tea.com/api',
  imageBaseURL: 'https://api.xinjie-tea.com/uploads',
  timeout: 10000
};


💰 费用明细
年度费用
项目	费用	说明
域名注册	¥55/年	.com域名
云服务器	¥2,400-3,240/年	2核4GB配置
SSL证书	¥0	Let's Encrypt免费
总计	¥2,455-3,295/年	平均¥205-275/月

可选费用
CDN加速：¥50-100/月
数据库备份：¥20/月
监控告警：¥30/月

📋 部署检查清单
基础设施
域名购买并解析到服务器IP
服务器购买并配置安全组
服务器环境安装完成

代码部署
代码上传到服务器
生产环境配置文件设置
数据库创建并导入表结构
项目依赖安装完成

服务配置
Nginx配置文件创建并启用
SSL证书申请并配置
PM2配置并启动应用
防火墙规则配置

功能测试
API接口访问正常
管理后台访问正常
HTTPS访问正常
数据库连接正常

小程序配置
小程序API地址更新
微信后台域名配置
支付功能配置（如需要）

微信后台配置
服务器域名配置:
request合法域名: https://api.xinjie-tea.com
uploadFile合法域名: https://api.xinjie-tea.com
downloadFile合法域名: https://api.xinjie-tea.com
业务域名配置: https://www.xinjie-tea.com
支付配置（如需要）:
申请微信支付商户号
配置支付回调URL: https://api.xinjie-tea.com/api/payment/wechat/notify

