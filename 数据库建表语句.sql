-- Active: 1751797958990@@127.0.0.1@3306@xinjie_mall
-- 心洁茶叶商城数据库建表语句
-- 数据库名：xinjie_mall

-- 创建数据库
CREATE DATABASE IF NOT EXISTS xinjie_mall DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE xinjie_mall;

-- 1. 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码(加密)',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar VARCHAR(255) COMMENT '头像URL',
    gender TINYINT DEFAULT 0 COMMENT '性别(0:未知 1:男 2:女)',
    birthday DATE COMMENT '生日',
    user_level TINYINT DEFAULT 1 COMMENT '用户等级(1:普通用户 2:VIP 3:钻石VIP)',
    points INT DEFAULT 0 COMMENT '积分',
    balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '账户余额',
    status TINYINT DEFAULT 1 COMMENT '状态(0:禁用 1:正常)',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_phone (phone),
    INDEX idx_username (username),
    INDEX idx_status (status)
) COMMENT '用户表';

-- 2. 管理后台用户表
CREATE TABLE admin_users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '管理员ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码(加密)',
    real_name VARCHAR(50) COMMENT '真实姓名',
    phone VARCHAR(20) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    avatar VARCHAR(255) COMMENT '头像URL',
    role_id BIGINT COMMENT '角色ID',
    status TINYINT DEFAULT 1 COMMENT '状态(0:禁用 1:正常)',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_username (username),
    INDEX idx_status (status)
) COMMENT '管理后台用户表';

-- 3. 商品分类表
CREATE TABLE categories (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    parent_id BIGINT DEFAULT 0 COMMENT '父级分类ID(0为顶级分类)',
    level TINYINT DEFAULT 1 COMMENT '分类层级',
    icon VARCHAR(255) COMMENT '分类图标',
    image VARCHAR(255) COMMENT '分类图片',
    sort_order INT DEFAULT 0 COMMENT '排序序号',
    status TINYINT DEFAULT 1 COMMENT '状态(0:禁用 1:正常)',
    description TEXT COMMENT '分类描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_parent_id (parent_id),
    INDEX idx_status (status),
    INDEX idx_sort_order (sort_order)
) COMMENT '商品分类表';

-- 4. 商品表
CREATE TABLE products (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '商品ID',
    name VARCHAR(200) NOT NULL COMMENT '商品名称',
    category_id BIGINT NOT NULL COMMENT '分类ID',
    brand VARCHAR(100) COMMENT '品牌',
    description TEXT COMMENT '商品描述',
    price DECIMAL(10,2) NOT NULL COMMENT '商品价格',
    original_price DECIMAL(10,2) COMMENT '原价',
    stock INT DEFAULT 0 COMMENT '库存数量',
    sales INT DEFAULT 0 COMMENT '销量',
    rating DECIMAL(3,2) DEFAULT 0.00 COMMENT '评分',
    review_count INT DEFAULT 0 COMMENT '评价数量',
    main_image VARCHAR(255) COMMENT '主图URL',
    images TEXT COMMENT '商品图片(JSON格式)',
    weight DECIMAL(8,2) COMMENT '重量(克)',
    unit VARCHAR(20) COMMENT '单位(包/盒/斤等)',
    is_hot TINYINT DEFAULT 0 COMMENT '是否热销(0:否 1:是)',
    is_recommend TINYINT DEFAULT 0 COMMENT '是否推荐(0:否 1:是)',
    status TINYINT DEFAULT 1 COMMENT '状态(0:下架 1:上架)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_category_id (category_id),
    INDEX idx_status (status),
    INDEX idx_is_hot (is_hot),
    INDEX idx_is_recommend (is_recommend),
    FOREIGN KEY (category_id) REFERENCES categories(id)
) COMMENT '商品表';

-- 5. 购物车表
CREATE TABLE cart_items (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '购物车项ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    quantity INT NOT NULL DEFAULT 1 COMMENT '商品数量',
    selected TINYINT DEFAULT 1 COMMENT '是否选中(0:否 1:是)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_product_id (product_id),
    UNIQUE KEY uk_user_product (user_id, product_id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
) COMMENT '购物车表';

-- 6. 收货地址表
CREATE TABLE addresses (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '地址ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    receiver VARCHAR(50) NOT NULL COMMENT '收货人姓名',
    phone VARCHAR(20) NOT NULL COMMENT '收货人手机号',
    province VARCHAR(50) NOT NULL COMMENT '省份',
    city VARCHAR(50) NOT NULL COMMENT '城市',
    district VARCHAR(50) NOT NULL COMMENT '区县',
    detail_address VARCHAR(255) NOT NULL COMMENT '详细地址',
    is_default TINYINT DEFAULT 0 COMMENT '是否默认地址(0:否 1:是)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_is_default (is_default),
    FOREIGN KEY (user_id) REFERENCES users(id)
) COMMENT '收货地址表';

-- 7. 订单表
CREATE TABLE orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单ID',
    order_no VARCHAR(50) NOT NULL UNIQUE COMMENT '订单号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '订单总金额',
    pay_amount DECIMAL(10,2) NOT NULL COMMENT '实付金额',
    freight_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '运费',
    discount_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠金额',
    receiver_name VARCHAR(50) NOT NULL COMMENT '收货人姓名',
    receiver_phone VARCHAR(20) NOT NULL COMMENT '收货人手机号',
    receiver_address TEXT NOT NULL COMMENT '收货地址',
    delivery_company VARCHAR(50) COMMENT '快递公司',
    delivery_no VARCHAR(50) COMMENT '快递单号',
    pay_type TINYINT COMMENT '支付方式(1:微信 2:支付宝 3:银行卡)',
    pay_status TINYINT DEFAULT 0 COMMENT '支付状态(0:未支付 1:已支付 2:已退款)',
    order_status TINYINT DEFAULT 0 COMMENT '订单状态(0:待付款 1:待发货 2:待收货 3:已完成 4:已取消)',
    pay_time DATETIME COMMENT '支付时间',
    delivery_time DATETIME COMMENT '发货时间',
    receive_time DATETIME COMMENT '收货时间',
    remark TEXT COMMENT '订单备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '下单时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_order_no (order_no),
    INDEX idx_pay_status (pay_status),
    INDEX idx_order_status (order_status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id)
) COMMENT '订单表';

-- 8. 订单商品表
CREATE TABLE order_items (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单商品ID',
    order_id BIGINT NOT NULL COMMENT '订单ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    product_name VARCHAR(200) NOT NULL COMMENT '商品名称',
    product_image VARCHAR(255) COMMENT '商品图片',
    price DECIMAL(10,2) NOT NULL COMMENT '商品单价',
    quantity INT NOT NULL COMMENT '购买数量',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '商品总金额',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_order_id (order_id),
    INDEX idx_product_id (product_id),
    FOREIGN KEY (order_id) REFERENCES orders(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
) COMMENT '订单商品表';

-- 9. 商品评价表
CREATE TABLE reviews (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '评价ID',
    order_id BIGINT NOT NULL COMMENT '订单ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    rating TINYINT NOT NULL COMMENT '评分(1-5星)',
    content TEXT COMMENT '评价内容',
    images TEXT COMMENT '评价图片(JSON格式)',
    is_anonymous TINYINT DEFAULT 0 COMMENT '是否匿名(0:否 1:是)',
    reply_content TEXT COMMENT '商家回复内容',
    reply_time DATETIME COMMENT '回复时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '评价时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_order_id (order_id),
    INDEX idx_product_id (product_id),
    INDEX idx_user_id (user_id),
    INDEX idx_rating (rating),
    FOREIGN KEY (order_id) REFERENCES orders(id),
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
) COMMENT '商品评价表';

-- 10. 轮播图表
CREATE TABLE banners (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '轮播图ID',
    title VARCHAR(100) COMMENT '标题',
    image_url VARCHAR(255) NOT NULL COMMENT '图片URL',
    link_url VARCHAR(255) COMMENT '跳转链接',
    sort_order INT DEFAULT 0 COMMENT '排序序号',
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    status TINYINT DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_status (status),
    INDEX idx_sort_order (sort_order),
    INDEX idx_start_time (start_time),
    INDEX idx_end_time (end_time)
) COMMENT '轮播图表';

-- 10. 角色表
CREATE TABLE roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',
    name VARCHAR(50) NOT NULL UNIQUE COMMENT '角色名称',
    description VARCHAR(255) COMMENT '角色描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '角色表';

-- 11. 权限表
CREATE TABLE permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',
    name VARCHAR(50) NOT NULL UNIQUE COMMENT '权限名称',
    code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限编码',
    description VARCHAR(255) COMMENT '权限描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '权限表';

-- 12. 角色-权限关联表
CREATE TABLE role_permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    permission_id BIGINT NOT NULL COMMENT '权限ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_role_permission (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id),
    FOREIGN KEY (permission_id) REFERENCES permissions(id)
) COMMENT '角色-权限关联表';

-- 插入管理后台用户数据
-- 用户名：admin，密码：admin (使用MD5加密)
INSERT INTO admin_users (username, password, real_name, status) 
VALUES ('admin', MD5('admin'), '系统管理员', 1);

-- 插入示例分类数据
INSERT INTO categories (name, parent_id, level, sort_order, status, description) VALUES
('绿茶', 0, 1, 1, 1, '绿茶类茶叶'),
('红茶', 0, 1, 2, 1, '红茶类茶叶'),
('乌龙茶', 0, 1, 3, 1, '乌龙茶类茶叶'),
('普洱茶', 0, 1, 4, 1, '普洱茶类茶叶'),
('花茶', 0, 1, 5, 1, '花茶类茶叶'),
('龙井', 1, 2, 1, 1, '西湖龙井'),
('碧螺春', 1, 2, 2, 1, '碧螺春茶'),
('铁观音', 3, 2, 1, 1, '安溪铁观音'),
('大红袍', 3, 2, 2, 1, '武夷大红袍');

-- 插入示例轮播图数据
INSERT INTO banners (title, image_url, link_url, sort_order, status) VALUES
('心洁茶叶', '/images/banner1.jpg', '/pages/index/index', 1, 1),
('新品上市', '/images/banner2.jpg', '/pages/category/category', 2, 1),
('限时优惠', '/images/banner3.jpg', '/pages/activity/activity', 3, 1);

-- 创建索引优化查询性能
CREATE INDEX idx_products_category_status ON products(category_id, status);
CREATE INDEX idx_orders_user_status ON orders(user_id, order_status);
CREATE INDEX idx_cart_user_selected ON cart_items(user_id, selected); 

-- 13. 系统设置表
CREATE TABLE settings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '设置ID',
    `key` VARCHAR(100) NOT NULL UNIQUE COMMENT '设置键',
    `value` TEXT COMMENT '设置值',
    description VARCHAR(255) COMMENT '设置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_key (`key`)
) COMMENT '系统设置表';

-- products
ALTER TABLE products ADD COLUMN sort_order INT DEFAULT 0;
ALTER TABLE products ADD COLUMN sales_count INT DEFAULT 0 COMMENT '销量';
ALTER TABLE products ADD COLUMN view_count INT DEFAULT 0 COMMENT '浏览量';

-- categories
ALTER TABLE categories ADD COLUMN image_url VARCHAR(255);
ALTER TABLE categories ADD COLUMN IF NOT EXISTS sort_order INT DEFAULT 0;

-- banners
ALTER TABLE banners ADD COLUMN sort_order INT DEFAULT 0;

-- admins
ALTER TABLE admins ADD COLUMN avatar VARCHAR(255);
ALTER TABLE admins ADD COLUMN role_id INT DEFAULT 1;
ALTER TABLE admins ADD COLUMN status TINYINT DEFAULT 1 COMMENT '1:启用 0:禁用';
ALTER TABLE admins ADD COLUMN last_login_at DATETIME;

-- users
ALTER TABLE users ADD COLUMN avatar VARCHAR(255);
ALTER TABLE users ADD COLUMN phone VARCHAR(20);
ALTER TABLE users ADD COLUMN gender TINYINT DEFAULT 0 COMMENT '0:未知 1:男 2:女';
ALTER TABLE users ADD COLUMN birthday DATE;
ALTER TABLE users ADD COLUMN status TINYINT DEFAULT 1 COMMENT '1:正常 0:禁用';
ALTER TABLE users ADD COLUMN last_login_at DATETIME;

-- 插入系统设置初始数据
INSERT INTO settings (`key`, `value`, description) VALUES
-- 基础设置
('basic_site_name', '心洁茶叶商城', '网站名称'),
('basic_site_logo', '/images/logo.png', '网站Logo'),
('basic_site_keywords', '茶叶,绿茶,红茶,乌龙茶,普洱茶,花茶', '网站关键词'),
('basic_site_description', '心洁茶叶商城 - 专业的茶叶销售平台', '网站描述'),
('basic_contact_phone', '************', '联系电话'),
('basic_contact_email', '<EMAIL>', '联系邮箱'),
('basic_contact_address', '浙江省杭州市西湖区', '联系地址'),
('basic_icp_number', '浙ICP备12345678号', 'ICP备案号'),

-- 支付设置
('payment_wechat_enabled', '1', '微信支付是否启用'),
('payment_wechat_appid', '', '微信支付AppID'),
('payment_wechat_mchid', '', '微信支付商户号'),
('payment_wechat_key', '', '微信支付密钥'),
('payment_alipay_enabled', '1', '支付宝是否启用'),
('payment_alipay_appid', '', '支付宝AppID'),
('payment_alipay_private_key', '', '支付宝私钥'),
('payment_alipay_public_key', '', '支付宝公钥'),

-- 配送设置
('shipping_free_amount', '99', '免运费金额'),
('shipping_default_fee', '10', '默认运费'),
('shipping_express_companies', '["顺丰速运","申通快递","圆通速递","中通快递","韵达快递","百世汇通","德邦物流"]', '快递公司列表'),
('shipping_auto_delivery', '0', '是否自动发货'),

-- 短信设置
('sms_enabled', '0', '短信服务是否启用'),
('sms_provider', 'aliyun', '短信服务商'),
('sms_access_key', '', '短信AccessKey'),
('sms_secret_key', '', '短信SecretKey'),
('sms_sign_name', '心洁茶叶', '短信签名'),
('sms_template_code', '', '短信模板代码'),

-- 邮件设置
('email_enabled', '0', '邮件服务是否启用'),
('email_smtp_host', 'smtp.163.com', 'SMTP服务器'),
('email_smtp_port', '465', 'SMTP端口'),
('email_smtp_username', '', 'SMTP用户名'),
('email_smtp_password', '', 'SMTP密码'),
('email_from_name', '心洁茶叶商城', '发件人名称'),
('email_from_address', '', '发件人邮箱');

ALTER USER 'root'@'localhost' IDENTIFIED BY 'ZCaini10000nian';
FLUSH PRIVILEGES;