#!/bin/bash

# 心洁茶叶商城服务器环境安装脚本
# 在Ubuntu 20.04上运行

echo "🚀 开始安装服务器环境..."

# 更新系统
apt update && apt upgrade -y

# 安装基础工具
apt install -y curl wget git vim unzip

# 1. 安装Node.js 16.x
echo "📦 安装Node.js..."
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
apt install -y nodejs

# 验证安装
node --version
npm --version

# 2. 安装MySQL 8.0
echo "🗄️ 安装MySQL..."
apt install -y mysql-server

# 启动MySQL服务
systemctl start mysql
systemctl enable mysql

# 安全配置MySQL
mysql_secure_installation

# 3. 安装Redis
echo "📊 安装Redis..."
apt install -y redis-server

# 启动Redis服务
systemctl start redis-server
systemctl enable redis-server

# 4. 安装Nginx
echo "🌐 安装Nginx..."
apt install -y nginx

# 启动Nginx服务
systemctl start nginx
systemctl enable nginx

# 5. 安装PM2
echo "⚙️ 安装PM2..."
npm install -g pm2

# 6. 安装SSL证书工具
echo "🔒 安装Certbot..."
apt install -y certbot python3-certbot-nginx

# 7. 创建项目目录
echo "📁 创建项目目录..."
mkdir -p /var/www/xinjie-tea
mkdir -p /var/log/xinjie-tea
mkdir -p /var/backups/xinjie-tea

# 设置目录权限
chown -R www-data:www-data /var/www/xinjie-tea
chmod -R 755 /var/www/xinjie-tea

# 8. 配置防火墙
echo "🔥 配置防火墙..."
ufw allow ssh
ufw allow 'Nginx Full'
ufw allow 4000
ufw allow 8081
ufw --force enable

echo "✅ 服务器环境安装完成！"
echo "Node.js版本: $(node --version)"
echo "MySQL状态: $(systemctl is-active mysql)"
echo "Redis状态: $(systemctl is-active redis-server)"
echo "Nginx状态: $(systemctl is-active nginx)"
