# 🖥️ 服务器连接与操作完整指南

## 📋 购买后您会收到的信息

购买云服务器后，服务商会提供：
- **服务器公网IP地址**：如 `123.456.789.10`
- **用户名**：通常是 `root`
- **密码**：随机生成的密码，或您自己设置的密码
- **SSH端口**：默认是 `22`

## 🔌 连接服务器的方法

### Windows用户连接方式

#### **方法1：使用PowerShell（推荐）**
Windows 10及以上版本自带SSH客户端：

```bash
# 打开PowerShell（以管理员身份运行）
# 连接命令
ssh root@您的服务器IP

# 示例
ssh root@123.456.789.10

# 首次连接会提示确认，输入 yes
# 然后输入密码
```

#### **方法2：使用PuTTY工具**
1. **下载PuTTY**：https://www.putty.org/
2. **安装并打开PuTTY**
3. **配置连接**：
   - Host Name: `您的服务器IP`
   - Port: `22`
   - Connection type: `SSH`
4. **点击Open连接**
5. **输入用户名**：`root`
6. **输入密码**

#### **方法3：使用VS Code（开发者推荐）**
1. **安装VS Code**
2. **安装Remote-SSH插件**
3. **配置SSH连接**：
   - 按 `Ctrl+Shift+P`
   - 输入 `Remote-SSH: Connect to Host`
   - 添加新主机：`root@您的服务器IP`

### Mac/Linux用户连接方式

```bash
# 打开终端
# 连接命令
ssh root@您的服务器IP

# 示例
ssh root@123.456.789.10

# 首次连接确认指纹，输入 yes
# 输入密码登录
```

## 🔐 连接成功的标志

连接成功后，您会看到类似这样的欢迎信息：

```bash
Welcome to Ubuntu 20.04.6 LTS (GNU/Linux 5.4.0-150-generic x86_64)

 * Documentation:  https://help.ubuntu.com
 * Management:     https://landscape.canonical.com
 * Support:        https://ubuntu.com/advantage

Last login: Mon Jan 15 10:30:45 2024 from *************
root@your-server:~#
```

命令提示符变为：`root@your-server:~#`

## 🛠️ 首次登录必做的安全配置

### 1. 更新系统
```bash
# 更新软件包列表
apt update

# 升级所有软件包
apt upgrade -y

# 重启系统（如果需要）
reboot
```

### 2. 创建新用户（安全考虑）
```bash
# 创建部署用户
adduser deploy

# 设置密码（按提示输入）
# 其他信息可以直接回车跳过

# 将用户添加到sudo组
usermod -aG sudo deploy

# 切换到新用户
su - deploy

# 测试sudo权限
sudo whoami  # 应该显示 root
```

### 3. 配置SSH密钥登录（可选但推荐）
```bash
# 在本地电脑生成SSH密钥对
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 将公钥复制到服务器
ssh-copy-id deploy@您的服务器IP

# 或者手动复制
# 1. 查看本地公钥
cat ~/.ssh/id_rsa.pub

# 2. 在服务器上创建.ssh目录
mkdir -p ~/.ssh
chmod 700 ~/.ssh

# 3. 将公钥内容添加到authorized_keys
echo "您的公钥内容" >> ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys
```

### 4. 配置防火墙
```bash
# 启用防火墙
ufw enable

# 允许SSH连接
ufw allow ssh

# 允许HTTP和HTTPS
ufw allow 80
ufw allow 443

# 允许应用端口
ufw allow 4000  # API服务
ufw allow 8081  # 管理后台

# 查看防火墙状态
ufw status
```

## 📁 文件传输方法

### 1. 使用SCP命令
```bash
# 从本地上传文件到服务器
scp /local/path/file.txt root@服务器IP:/remote/path/

# 上传整个文件夹
scp -r /local/folder root@服务器IP:/remote/path/

# 从服务器下载文件到本地
scp root@服务器IP:/remote/path/file.txt /local/path/
```

### 2. 使用SFTP
```bash
# 连接SFTP
sftp root@服务器IP

# SFTP命令
put local_file.txt          # 上传文件
get remote_file.txt         # 下载文件
ls                          # 列出远程目录
lls                         # 列出本地目录
cd /path/to/directory       # 切换远程目录
lcd /local/path             # 切换本地目录
quit                        # 退出
```

### 3. 使用Git（推荐）
```bash
# 在服务器上克隆代码
git clone https://github.com/your-username/your-repo.git

# 更新代码
cd your-repo
git pull origin main
```

## 🔍 常用服务器管理命令

### 系统信息查看
```bash
# 查看系统信息
uname -a                    # 系统内核信息
lsb_release -a             # 系统版本信息
df -h                      # 磁盘使用情况
free -h                    # 内存使用情况
top                        # 实时进程监控
htop                       # 更友好的进程监控
```

### 服务管理
```bash
# 查看服务状态
systemctl status nginx
systemctl status mysql

# 启动/停止/重启服务
systemctl start nginx
systemctl stop nginx
systemctl restart nginx

# 设置开机自启
systemctl enable nginx
systemctl disable nginx
```

### 日志查看
```bash
# 查看系统日志
journalctl -f              # 实时查看系统日志
journalctl -u nginx        # 查看nginx服务日志

# 查看文件末尾
tail -f /var/log/nginx/access.log
tail -100 /var/log/nginx/error.log
```

### 进程管理
```bash
# 查看端口占用
netstat -tlnp | grep :80
lsof -i :3306

# 查找进程
ps aux | grep nginx
pgrep -f node

# 杀死进程
kill -9 进程ID
pkill -f node
```

## ⚠️ 常见问题解决

### 1. 连接被拒绝
```bash
# 检查SSH服务状态
systemctl status ssh

# 重启SSH服务
systemctl restart ssh

# 检查防火墙设置
ufw status
```

### 2. 权限不足
```bash
# 切换到root用户
sudo su -

# 修改文件权限
chmod 755 filename
chown user:group filename
```

### 3. 磁盘空间不足
```bash
# 查看磁盘使用情况
df -h

# 查找大文件
find / -type f -size +100M

# 清理系统缓存
apt autoremove
apt autoclean
```

### 4. 内存不足
```bash
# 查看内存使用
free -h

# 查看占用内存最多的进程
ps aux --sort=-%mem | head

# 重启服务释放内存
systemctl restart nginx
```

## 🎯 下一步操作

连接成功并完成基础配置后，您可以：

1. **安装运行环境**：Node.js、MySQL、Nginx等
2. **上传项目代码**：使用Git或SCP
3. **配置数据库**：创建数据库和用户
4. **配置域名解析**：将域名指向服务器IP
5. **申请SSL证书**：启用HTTPS访问
6. **启动应用服务**：使用PM2管理进程

---

## 📞 技术支持

如果在连接或操作过程中遇到问题：

1. **查看服务商文档**：每个云服务商都有详细的连接指南
2. **检查网络连接**：确保本地网络正常
3. **验证服务器状态**：在云服务商控制台查看服务器状态
4. **联系技术支持**：云服务商通常提供24小时技术支持

记住：**安全第一**，不要在公共网络环境下进行敏感操作，定期备份重要数据！
