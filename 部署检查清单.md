# ✅ 心洁茗茶商城部署检查清单

## 📋 部署前准备清单

### 🌐 域名和服务器
- [ ] **域名购买完成**
  - [ ] 域名：xinjie-tea.com 已购买
  - [ ] 域名实名认证已通过
  - [ ] 域名状态正常（未锁定）

- [ ] **服务器购买完成**
  - [ ] 云服务器已购买（推荐：2核4G，40G SSD）
  - [ ] 获得服务器IP地址
  - [ ] 获得root用户密码
  - [ ] 安全组已配置（开放22,80,443,4000,8081端口）

### 🔧 DNS解析配置
- [ ] **A记录配置完成**
  - [ ] @ → 服务器IP
  - [ ] www → 服务器IP
  - [ ] api → 服务器IP
  - [ ] admin → 服务器IP

- [ ] **DNS解析验证**
  - [ ] ping xinjie-tea.com 能通
  - [ ] nslookup api.xinjie-tea.com 正确解析
  - [ ] 全球DNS检测通过

## 🖥️ 服务器环境配置清单

### 🔐 基础安全配置
- [ ] **服务器连接成功**
  - [ ] SSH连接正常：`ssh root@服务器IP`
  - [ ] 能够正常登录服务器

- [ ] **系统更新完成**
  ```bash
  - [ ] apt update && apt upgrade -y
  - [ ] 系统重启（如需要）
  ```

- [ ] **用户和权限配置**
  ```bash
  - [ ] 创建deploy用户：adduser deploy
  - [ ] 添加sudo权限：usermod -aG sudo deploy
  - [ ] 测试sudo权限正常
  ```

- [ ] **防火墙配置**
  ```bash
  - [ ] ufw allow ssh
  - [ ] ufw allow 80
  - [ ] ufw allow 443
  - [ ] ufw allow 4000
  - [ ] ufw allow 8081
  - [ ] ufw enable
  ```

### ⚙️ 运行环境安装
- [ ] **Node.js安装**
  ```bash
  - [ ] Node.js 18.x 安装完成
  - [ ] node --version 显示正确版本
  - [ ] npm --version 显示正确版本
  ```

- [ ] **数据库安装**
  ```bash
  - [ ] MySQL 8.0 安装完成
  - [ ] systemctl status mysql 显示运行中
  - [ ] mysql_secure_installation 安全配置完成
  - [ ] root密码设置完成
  ```

- [ ] **Web服务器安装**
  ```bash
  - [ ] Nginx 安装完成
  - [ ] systemctl status nginx 显示运行中
  - [ ] 浏览器访问 http://服务器IP 显示Nginx欢迎页
  ```

- [ ] **进程管理器安装**
  ```bash
  - [ ] PM2 全局安装：npm install -g pm2
  - [ ] pm2 --version 显示正确版本
  ```

- [ ] **其他工具安装**
  ```bash
  - [ ] Redis 安装并运行
  - [ ] Git 安装完成
  - [ ] Certbot SSL工具安装
  ```

## 📁 代码部署清单

### 📤 代码上传
- [ ] **项目目录创建**
  ```bash
  - [ ] mkdir -p /var/www/xinjie-tea
  - [ ] cd /var/www/xinjie-tea
  ```

- [ ] **代码上传完成**
  - [ ] 使用Git克隆：`git clone 仓库地址 .`
  - [ ] 或使用SCP上传：`scp -r 本地路径 root@服务器IP:/var/www/xinjie-tea`
  - [ ] 代码文件完整性检查

### 🗄️ 数据库配置
- [ ] **数据库创建**
  ```sql
  - [ ] CREATE DATABASE xinjie_mall CHARACTER SET utf8mb4;
  - [ ] CREATE USER 'xinjie_user'@'localhost' IDENTIFIED BY 'password';
  - [ ] GRANT ALL PRIVILEGES ON xinjie_mall.* TO 'xinjie_user'@'localhost';
  - [ ] FLUSH PRIVILEGES;
  ```

- [ ] **数据表创建**
  ```bash
  - [ ] 导入数据库结构：mysql -u xinjie_user -p xinjie_mall < 数据库建表语句.sql
  - [ ] 验证表创建成功：SHOW TABLES;
  ```

### 📦 依赖安装
- [ ] **后端依赖安装**
  ```bash
  - [ ] cd /var/www/xinjie-tea/mall-server
  - [ ] npm install --production
  - [ ] 检查node_modules文件夹存在
  ```

- [ ] **管理后台构建**
  ```bash
  - [ ] cd /var/www/xinjie-tea/xinjie.mall-admin
  - [ ] npm install
  - [ ] npm run build
  - [ ] 检查build文件夹生成
  ```

### ⚙️ 配置文件设置
- [ ] **环境变量配置**
  ```bash
  - [ ] 创建.env文件
  - [ ] NODE_ENV=production
  - [ ] 数据库连接信息正确
  - [ ] JWT密钥设置
  - [ ] 微信小程序配置
  ```

## 🌐 Web服务器配置清单

### 🔧 Nginx配置
- [ ] **配置文件创建**
  ```bash
  - [ ] nano /etc/nginx/sites-available/xinjie-tea
  - [ ] 配置文件内容正确
  ```

- [ ] **站点启用**
  ```bash
  - [ ] ln -s /etc/nginx/sites-available/xinjie-tea /etc/nginx/sites-enabled/
  - [ ] rm /etc/nginx/sites-enabled/default
  ```

- [ ] **配置验证**
  ```bash
  - [ ] nginx -t 配置测试通过
  - [ ] systemctl restart nginx 重启成功
  ```

### 🔒 SSL证书配置
- [ ] **证书申请**
  ```bash
  - [ ] certbot --nginx -d api.xinjie-tea.com
  - [ ] certbot --nginx -d admin.xinjie-tea.com
  - [ ] 证书申请成功
  ```

- [ ] **HTTPS验证**
  - [ ] https://api.xinjie-tea.com 可访问
  - [ ] https://admin.xinjie-tea.com 可访问
  - [ ] SSL证书有效期检查

- [ ] **自动续期设置**
  ```bash
  - [ ] crontab -e 添加续期任务
  - [ ] certbot renew --dry-run 测试成功
  ```

## 🚀 应用启动清单

### 📋 PM2配置
- [ ] **配置文件创建**
  ```bash
  - [ ] 创建ecosystem.config.js
  - [ ] 配置API服务
  - [ ] 配置管理后台服务
  ```

- [ ] **应用启动**
  ```bash
  - [ ] pm2 start ecosystem.config.js --env production
  - [ ] pm2 save 保存配置
  - [ ] pm2 startup 设置开机自启
  ```

- [ ] **服务状态检查**
  ```bash
  - [ ] pm2 status 显示所有服务运行中
  - [ ] pm2 logs 无错误日志
  ```

## 🧪 功能测试清单

### 🔌 API接口测试
- [ ] **基础接口测试**
  ```bash
  - [ ] curl https://api.xinjie-tea.com/health 返回正常
  - [ ] curl https://api.xinjie-tea.com/api/front/category/list 返回数据
  - [ ] curl https://api.xinjie-tea.com/api/front/banner/list 返回数据
  ```

### 💻 管理后台测试
- [ ] **页面访问测试**
  - [ ] https://admin.xinjie-tea.com 能正常打开
  - [ ] 登录页面显示正常
  - [ ] 能够正常登录管理后台

- [ ] **功能测试**
  - [ ] 轮播图管理功能正常
  - [ ] 商品分类管理功能正常
  - [ ] 商品管理功能正常
  - [ ] 订单管理功能正常

### 📱 小程序配置
- [ ] **微信后台配置**
  - [ ] 登录微信公众平台
  - [ ] 配置服务器域名
  - [ ] request合法域名：https://api.xinjie-tea.com
  - [ ] uploadFile合法域名：https://api.xinjie-tea.com
  - [ ] downloadFile合法域名：https://api.xinjie-tea.com

- [ ] **小程序代码更新**
  - [ ] 修改API地址为正式域名
  - [ ] 重新编译小程序
  - [ ] 提交审核发布

## 🔍 性能和安全检查

### ⚡ 性能检查
- [ ] **服务器性能**
  - [ ] CPU使用率正常（<80%）
  - [ ] 内存使用率正常（<80%）
  - [ ] 磁盘空间充足（>20%剩余）

- [ ] **网站速度**
  - [ ] 页面加载时间<3秒
  - [ ] API响应时间<1秒
  - [ ] 图片加载正常

### 🔐 安全检查
- [ ] **服务器安全**
  - [ ] SSH端口修改（可选）
  - [ ] 禁用root远程登录（可选）
  - [ ] 防火墙规则正确
  - [ ] 定期安全更新计划

- [ ] **应用安全**
  - [ ] 数据库用户权限最小化
  - [ ] JWT密钥足够复杂
  - [ ] 敏感信息不在代码中硬编码
  - [ ] HTTPS强制跳转

## 📊 监控和备份

### 📈 监控设置
- [ ] **服务监控**
  - [ ] PM2监控正常
  - [ ] Nginx访问日志记录
  - [ ] 错误日志监控

- [ ] **性能监控**
  - [ ] 服务器资源监控
  - [ ] 数据库性能监控
  - [ ] API响应时间监控

### 💾 备份策略
- [ ] **数据备份**
  - [ ] 数据库定期备份脚本
  - [ ] 代码文件备份
  - [ ] 配置文件备份

- [ ] **恢复测试**
  - [ ] 备份文件完整性验证
  - [ ] 恢复流程测试

## 🎉 上线完成确认

### ✅ 最终检查
- [ ] **所有域名正常访问**
  - [ ] https://api.xinjie-tea.com ✅
  - [ ] https://admin.xinjie-tea.com ✅

- [ ] **所有功能正常**
  - [ ] API接口响应正常 ✅
  - [ ] 管理后台功能完整 ✅
  - [ ] 数据库连接正常 ✅
  - [ ] SSL证书有效 ✅

- [ ] **小程序配置完成**
  - [ ] 域名配置正确 ✅
  - [ ] 小程序能正常调用API ✅

### 📝 文档整理
- [ ] **部署文档**
  - [ ] 服务器信息记录
  - [ ] 数据库连接信息
  - [ ] 域名和SSL证书信息
  - [ ] 应用配置信息

- [ ] **运维手册**
  - [ ] 常用命令整理
  - [ ] 故障排查指南
  - [ ] 备份恢复流程
  - [ ] 更新部署流程

---

## 🎯 恭喜！部署完成

当所有检查项都完成后，您的心洁茗茶商城就成功上线了！

**下一步可以考虑：**
- 🚀 性能优化（CDN、缓存等）
- 📊 数据分析（用户行为、销售数据）
- 🔄 持续集成/持续部署（CI/CD）
- 📱 移动端优化
- 🛡️ 安全加固

记住定期检查服务器状态，及时更新系统和应用，确保网站稳定运行！
