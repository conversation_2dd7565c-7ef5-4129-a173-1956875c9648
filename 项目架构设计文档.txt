心洁茶叶商城项目架构设计文档

==========================================
1. 项目概述
==========================================

心洁茶叶商城是一个完整的电商系统，包含三个核心组件：
- 微信小程序（前端用户界面）
- 管理后台（商家管理界面）
- API服务（后端数据服务）

==========================================
2. 整体架构设计
==========================================

2.1 架构图
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   微信小程序    │    │   管理后台      │    │   API服务       │
│   (前端)        │    │   (前端)        │    │   (后端)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   负载均衡器    │
                    │   (Nginx)       │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   数据库集群    │
                    │   (MySQL)       │
                    └─────────────────┘

2.2 技术栈选择
- 小程序前端：微信小程序原生开发
- 管理后台：Vue.js + Element-UI
- 后端API：Node.js + Koa2
- 数据库：MySQL
- 缓存：Redis
- 文件存储：阿里云OSS
- 负载均衡：Nginx
- 消息队列：RabbitMQ

==========================================
3. 高并发处理方案
==========================================

3.1 缓存策略
- 使用Redis缓存热点数据（商品信息、用户信息、购物车）
- 缓存层级：浏览器缓存 → CDN缓存 → 应用缓存 → 数据库
- 缓存更新策略：先更新数据库，再删除缓存

3.2 数据库优化
- 主从分离：读写分离，主库写，从库读
- 分库分表：按用户ID分库，按时间分表
- 索引优化：为常用查询字段建立索引
- 连接池：使用数据库连接池减少连接开销

3.3 负载均衡
- 使用Nginx做负载均衡
- 多台服务器部署API服务
- 健康检查确保服务可用性

3.4 异步处理
- 使用消息队列处理非实时任务（订单处理、邮件发送）
- 图片上传异步处理
- 数据统计异步计算

==========================================
4. 安全防护方案
==========================================

4.1 身份认证
- JWT Token认证
- 微信小程序登录态验证
- 管理员Session认证
- 密码加密存储（bcrypt）

4.2 数据安全
- 数据库连接加密
- 敏感数据脱敏
- SQL注入防护（参数化查询）
- XSS攻击防护（输入过滤）

4.3 接口安全
- API接口签名验证
- 请求频率限制（Rate Limiting）
- CORS跨域配置
- HTTPS强制使用

4.4 文件安全
- 文件上传类型限制
- 文件大小限制
- 文件存储路径隔离
- 图片水印防盗链

4.5 业务安全
- 订单防重复提交
- 库存超卖防护
- 支付安全验证
- 用户权限控制

==========================================
5. 系统模块设计
==========================================

5.1 用户模块
- 用户注册/登录
- 个人信息管理
- 地址管理
- 权限控制

5.2 商品模块
- 商品信息管理
- 分类管理
- 库存管理
- 搜索功能

5.3 订单模块
- 购物车管理
- 订单创建/支付
- 订单状态管理
- 物流跟踪

5.4 支付模块
- 微信支付集成
- 支付宝集成
- 支付回调处理
- 退款处理

5.5 管理模块
- 商品管理
- 订单管理
- 用户管理
- 数据统计

==========================================
6. 部署架构
==========================================

6.1 开发环境
- 本地开发环境(development)
  - 配置文件: .config.development.js
  - API地址: http://localhost:3000/api
  - 开发服务器配置
    - 端口: 8080
    - 代理配置: /api -> http://localhost:3000
  - 数据库配置
    - 主机: localhost
    - 端口: 3306
    - 数据库: xingjie_dev
    - 用户名: dev_user
    - 密码: dev_password
    - 连接池: 10
  - 调试工具配置
  - 热更新配置
  - 日志级别: debug
  - 环境变量:
    - NODE_ENV=development
    - PORT=8080
    - API_URL=http://localhost:3000/api
    - DB_HOST=localhost
    - DB_PORT=3306
    - DB_NAME=xingjie_dev
    - DB_USER=dev_user
    - DB_PASSWORD=dev_password
  - 配置文件: .config.staging.js

  - 生产服务器配置
    - 域名: www.xingjie.com
    - 端口: 443
    - API地址：https://api.xingjie.com
  - 数据库配置
    - 主库配置
      - 主机: master-db.xingjie.com
      - 端口: 3306
      - 数据库: xingjie_prod
      - 用户名: prod_user
      - 密码: prod_password
      - 连接池: 50
    - 从库配置
      - 主机: slave-db.xingjie.com
      - 端口: 3306
      - 数据库: xingjie_prod_slave
      - 用户名: prod_read_user
      - 密码: prod_read_password
      - 连接池: 100
  - 日志级别: debug
  - CDN配置
  - 监控告警配置
  - 环境变量:
    - NODE_ENV=production
    - PORT=443
    - API_URL=https://api.xingjie.com
    - DB_MASTER_HOST=master-db.xingjie.com
    - DB_MASTER_PORT=3306
    - DB_MASTER_NAME=xingjie_prod
    - DB_MASTER_USER=prod_user
    - DB_MASTER_PASSWORD=prod_password
    - DB_SLAVE_HOST=slave-db.xingjie.com
    - DB_SLAVE_PORT=3306
    - DB_SLAVE_NAME=xingjie_prod_slave
    - DB_SLAVE_USER=prod_read_user
    - DB_SLAVE_PASSWORD=prod_read_password

- 环境配置说明
  - 通过NODE_ENV环境变量区分环境
    - development: 开发环境
    - staging: 测试环境 
    - production: 生产环境
  - 使用dotenv加载对应环境配置
  - 配置文件中包含:
    - 服务器配置(端口、域名)
    - 数据库连接信息
    - API基础地址
    - 密钥信息
    - 日志配置
    - 第三方服务配置

6. 环境配置管理

6.1 配置文件结构
- 配置文件目录结构
  ```
  config/
  ├── default.js          # 默认配置
  ├── development.js      # 开发环境配置
  ├── staging.js          # 测试环境配置  
  ├── production.js       # 生产环境配置
  └── custom-env.js       # 自定义环境配置
  ```

- 配置加载流程
  1. 加载默认配置(default.js)
  2. 根据NODE_ENV加载对应环境配置
  3. 加载自定义环境配置(如果存在)
  4. 合并配置,环境配置覆盖默认配置

- 配置加载示例代码
  ```javascript
  const config = require('config');
  
  // 根据环境变量加载配置
  const env = process.env.NODE_ENV || 'development';
  const envConfig = require(`./config/${env}`);
  
  // 尝试加载自定义配置
  try {
    const customConfig = require('./config/custom-env');
    Object.assign(envConfig, customConfig);
  } catch (err) {
    // 自定义配置不存在,忽略
  }
  
  // 导出最终配置
  module.exports = {
    ...config,
    ...envConfig
  };
  ```

- 配置使用方式
  - 通过环境变量指定环境
    ```bash
    # 开发环境
    NODE_ENV=development npm start
    
    # 测试环境
    NODE_ENV=staging npm start
    
    # 生产环境
    NODE_ENV=production npm start
    ```
  
  - 在代码中使用配置
    ```javascript
    const config = require('../config');
    
    // 使用配置项
    const dbConfig = config.database;
    const apiUrl = config.api.baseUrl;
    ```

- 敏感配置处理
  - 敏感信息(密码、密钥等)通过环境变量注入
  - 生产环境敏感配置单独管理
  - 开发环境可使用.env文件
  - 生产环境的.env文件不提交到代码仓库
6.2 生产环境
- 云服务器部署
- 容器化部署（Docker）
- 自动化部署（CI/CD）
- 监控告警系统
- 应用服务器：Node.js服务器
- 数据库服务器：MySQL集群

6.3 备份策略
- 数据库定时备份
- 文件定期备份
- 灾难恢复方案

6.4 开发规范

- 代码规范
  - 使用ESLint进行代码检查
  - 遵循Airbnb JavaScript规范
  - 使用Prettier进行代码格式化
  - 统一的命名规范和注释规范
  - Git提交信息规范

- 前端开发规范
  - Vue组件命名采用大驼峰命名
  - CSS类名采用BEM命名规范
  - 组件目录结构规范
  - 状态管理规范
  - 路由配置规范
  - UI组件库使用规范

- 后端开发规范
  - RESTful API设计规范
  - 统一的错误码规范
  - 数据库命名规范
  - 接口文档规范
  - 单元测试规范
  - 日志记录规范

- 项目管理规范
  - Git分支管理规范
  - 版本发布规范
  - 文档编写规范
  - 代码审查规范
  - 测试流程规范

- 安全规范
  - 密码加密规范
  - 权限验证规范
  - 数据安全规范
  - XSS/CSRF防护规范
  - SQL注入防护规范


==========================================
7. 性能优化
==========================================

7.1 前端优化
- 图片懒加载
- 代码压缩
- CDN加速
- 浏览器缓存

7.2 后端优化
- 接口响应时间优化
- 数据库查询优化
- 缓存命中率优化
- 服务器资源优化

7.3 监控指标
- 响应时间监控
- 错误率监控
- 并发量监控
- 资源使用率监控

==========================================
8. 扩展性设计
==========================================

8.1 水平扩展
- 无状态服务设计
- 数据库读写分离
- 缓存集群扩展
- 负载均衡扩展

8.2 功能扩展
- 模块化设计
- 插件化架构
- API版本控制
- 微服务拆分准备

==========================================
9. 运维支持
==========================================

9.1 日志管理
- 访问日志记录
- 错误日志记录
- 业务日志记录
- 日志分析工具

9.2 监控告警
- 服务器监控
- 应用监控
- 数据库监控
- 业务监控

9.3 故障处理
- 故障检测机制
- 自动恢复机制
- 人工干预流程
- 故障复盘机制

==========================================
10. 项目风险控制
==========================================

10.1 技术风险
- 技术选型风险
- 性能风险
- 安全风险
- 兼容性风险

10.2 业务风险
- 需求变更风险
- 进度风险
- 质量风险
- 成本风险

10.3 风险应对
- 技术预研
- 原型验证
- 分阶段交付
- 应急预案

==========================================
11. 数据存储设计
==========================================

11.1 数据库设计
- 采用MySQL InnoDB存储引擎
- 主从复制架构
- 分库分表策略
- 数据库备份方案
- 字符集统一使用utf8mb4
- 数据表设计
  - 用户表(users)
    - 用户ID、用户名、密码、手机号、注册时间等基本信息
    - 用户等级、积分、余额等会员信息
    - 最后登录时间、登录IP等状态信息
  
  - 商品表(products) 
    - 商品ID、名称、描述、价格、库存等基本信息
    - 商品分类、品牌、规格等属性信息
    - 销量、评分、上架状态等统计信息
  
  - 订单表(orders)
    - 订单ID、用户ID、订单金额、支付状态等基本信息
    - 收货人信息、配送方式、支付方式等订单信息
    - 下单时间、支付时间、发货时间等状态信息
  
  - 订单商品表(order_items)
    - 订单ID、商品ID、购买数量、商品价格等
    - 商品规格、商品快照等信息
  
  - 购物车表(cart_items)
    - 用户ID、商品ID、商品数量
    - 商品规格、加入时间
  
  - 收货地址表(addresses)
    - 用户ID、收货人、手机号
    - 省市区、详细地址、是否默认
  
  - 商品分类表(categories)
    - 分类ID、分类名称、父级ID
    - 排序、图标、状态信息
  
  - 商品评价表(reviews)
    - 订单ID、商品ID、用户ID
    - 评分、评价内容、评价时间
    - 图片、追评等信息

  - 轮播图表(banners)
    - 轮播图ID、标题、图片URL
    - 跳转链接、排序序号
    - 开始时间、结束时间
    - 状态(启用/禁用)
    - 创建时间、更新时间


11.2 文件存储
- 阿里云OSS对象存储
- 图片处理服务
- CDN加速分发
- 文件分类存储
- 存储空间监控
- 商品图片存储
  - 原图存储
  - 缩略图自动生成
  - 图片水印处理
  - 图片格式优化

- 商品详情图存储
  - 富文本编辑器图片
  - 商品主图
  - 商品规格图
  - 商品详情长图

- 用户头像存储
  - 头像原图
  - 头像缩略图

- 评价图片存储
  - 评价图片原图
  - 评价图片缩略图
  - 图片审核机制

- 系统文件存储
  - 系统配置文件
  - 日志文件
  - 临时文件
  - 备份文件

- 存储策略
  - 按日期目录存储
  - 按用途分类存储
  - 文件命名规范
  - 存储空间清理

- 访问控制
  - 私有读写权限
  - 临时访问URL
  - 防盗链设置
  - IP白名单

11.3 数据备份
- 数据库定时备份
- 文件存储备份
- 异地容灾备份
- 备份数据验证
- 恢复演练机制

11.4 数据安全
- 数据加密存储
- 敏感信息脱敏
- 数据访问控制
- 操作日志记录
- 数据合规性


==========================================
12. 总结
==========================================

本架构设计采用成熟稳定的技术栈，通过合理的分层设计、缓存策略、安全防护和性能优化，确保系统能够稳定运行，支持高并发访问，并具备良好的扩展性。整个架构简洁明了，便于理解和维护，为项目的成功实施提供了坚实的技术基础。 