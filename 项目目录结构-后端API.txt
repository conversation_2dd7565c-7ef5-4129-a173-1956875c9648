后端API服务目录结构

==========================================
项目根目录：server/
==========================================

server/
├── package.json              # 项目依赖配置
├── package-lock.json         # 依赖锁定文件
├── .env                      # 环境变量配置
├── .env.development          # 开发环境配置
├── .env.production           # 生产环境配置
├── .env.test                 # 测试环境配置
├── .gitignore               # Git忽略文件
├── README.md                # 项目说明文档
├── app.js                   # 应用入口文件
├── ecosystem.config.js       # PM2配置文件
├── .eslintrc.js             # ESLint配置
├── .prettierrc              # Prettier配置
├── jest.config.js           # Jest测试配置
├── nodemon.json             # Nodemon配置
│
├── src/                      # 源代码目录
│   ├── app.js               # Koa应用主文件
│   ├── config/              # 配置文件目录
│   │   ├── index.js         # 配置主文件
│   │   ├── database.js      # 数据库配置
│   │   ├── redis.js         # Redis配置
│   │   ├── jwt.js           # JWT配置
│   │   ├── upload.js        # 文件上传配置
│   │   ├── payment.js       # 支付配置
│   │   └── email.js         # 邮件配置
│   │
│   ├── middleware/          # 中间件目录
│   │   ├── index.js         # 中间件入口
│   │   ├── auth.js          # 认证中间件
│   │   ├── cors.js          # 跨域中间件
│   │   ├── error.js         # 错误处理中间件
│   │   ├── logger.js        # 日志中间件
│   │   ├── rateLimit.js     # 限流中间件
│   │   ├── validate.js      # 参数验证中间件
│   │   ├── upload.js        # 文件上传中间件
│   │   └── cache.js         # 缓存中间件
│   │
│   ├── routes/              # 路由目录
│   │   ├── index.js         # 路由入口
│   │   ├── front/           # 前端API路由
│   │   │   ├── index.js     # 前端路由入口
│   │   │   ├── user.js      # 用户相关路由
│   │   │   ├── product.js   # 商品相关路由
│   │   │   ├── cart.js      # 购物车相关路由
│   │   │   ├── order.js     # 订单相关路由
│   │   │   ├── address.js   # 地址相关路由
│   │   │   ├── payment.js   # 支付相关路由
│   │   │   ├── banner.js    # 轮播图相关路由
│   │   │   ├── category.js  # 分类相关路由
│   │   │   └── search.js    # 搜索相关路由
│   │   │
│   │   └── admin/           # 管理后台API路由
│   │       ├── index.js     # 管理后台路由入口
│   │       ├── auth.js      # 管理员认证路由
│   │       ├── user.js      # 用户管理路由
│   │       ├── product.js   # 商品管理路由
│   │       ├── order.js     # 订单管理路由
│   │       ├── category.js  # 分类管理路由
│   │       ├── banner.js    # 轮播图管理路由
│   │       ├── statistics.js # 统计分析路由
│   │       ├── settings.js  # 系统设置路由
│   │       └── upload.js    # 文件上传路由
│   │
│   ├── controllers/         # 控制器目录
│   │   ├── front/           # 前端控制器
│   │   │   ├── user.js      # 用户控制器
│   │   │   ├── product.js   # 商品控制器
│   │   │   ├── cart.js      # 购物车控制器
│   │   │   ├── order.js     # 订单控制器
│   │   │   ├── address.js   # 地址控制器
│   │   │   ├── payment.js   # 支付控制器
│   │   │   ├── banner.js    # 轮播图控制器
│   │   │   ├── category.js  # 分类控制器
│   │   │   └── search.js    # 搜索控制器
│   │   │
│   │   └── admin/           # 管理后台控制器
│   │       ├── auth.js      # 管理员认证控制器
│   │       ├── user.js      # 用户管理控制器
│   │       ├── product.js   # 商品管理控制器
│   │       ├── order.js     # 订单管理控制器
│   │       ├── category.js  # 分类管理控制器
│   │       ├── banner.js    # 轮播图管理控制器
│   │       ├── statistics.js # 统计分析控制器
│   │       ├── settings.js  # 系统设置控制器
│   │       └── upload.js    # 文件上传控制器
│   │
│   ├── services/            # 服务层目录
│   │   ├── user.js          # 用户服务
│   │   ├── product.js       # 商品服务
│   │   ├── cart.js          # 购物车服务
│   │   ├── order.js         # 订单服务
│   │   ├── address.js       # 地址服务
│   │   ├── payment.js       # 支付服务
│   │   ├── banner.js        # 轮播图服务
│   │   ├── category.js      # 分类服务
│   │   ├── search.js        # 搜索服务
│   │   ├── statistics.js    # 统计服务
│   │   ├── settings.js      # 设置服务
│   │   ├── upload.js        # 上传服务
│   │   ├── email.js         # 邮件服务
│   │   ├── sms.js           # 短信服务
│   │   └── cache.js         # 缓存服务
│   │
│   ├── models/              # 数据模型目录
│   │   ├── index.js         # 模型入口
│   │   ├── user.js          # 用户模型
│   │   ├── product.js       # 商品模型
│   │   ├── category.js      # 分类模型
│   │   ├── order.js         # 订单模型
│   │   ├── orderItem.js     # 订单项模型
│   │   ├── cart.js          # 购物车模型
│   │   ├── address.js       # 地址模型
│   │   ├── banner.js        # 轮播图模型
│   │   ├── payment.js       # 支付模型
│   │   ├── settings.js      # 设置模型
│   │   ├── admin.js         # 管理员模型
│   │   └── log.js           # 日志模型
│   │
│   ├── utils/               # 工具函数目录
│   │   ├── database.js      # 数据库工具
│   │   ├── redis.js         # Redis工具
│   │   ├── jwt.js           # JWT工具
│   │   ├── crypto.js        # 加密工具
│   │   ├── validate.js      # 验证工具
│   │   ├── format.js        # 格式化工具
│   │   ├── upload.js        # 上传工具
│   │   ├── email.js         # 邮件工具
│   │   ├── sms.js           # 短信工具
│   │   ├── payment.js       # 支付工具
│   │   ├── logger.js        # 日志工具
│   │   ├── constants.js     # 常量定义
│   │   └── helpers.js       # 辅助函数
│   │
│   ├── validators/          # 参数验证目录
│   │   ├── user.js          # 用户参数验证
│   │   ├── product.js       # 商品参数验证
│   │   ├── order.js         # 订单参数验证
│   │   ├── address.js       # 地址参数验证
│   │   ├── payment.js       # 支付参数验证
│   │   └── common.js        # 通用参数验证
│   │
│   ├── jobs/                # 定时任务目录
│   │   ├── index.js         # 任务入口
│   │   ├── order.js         # 订单相关任务
│   │   ├── statistics.js    # 统计相关任务
│   │   ├── backup.js        # 备份任务
│   │   └── cleanup.js       # 清理任务
│   │
│   ├── events/              # 事件处理目录
│   │   ├── index.js         # 事件入口
│   │   ├── order.js         # 订单事件
│   │   ├── payment.js       # 支付事件
│   │   └── user.js          # 用户事件
│   │
│   └── types/               # 类型定义目录
│       ├── user.js          # 用户类型
│       ├── product.js       # 商品类型
│       ├── order.js         # 订单类型
│       └── common.js        # 通用类型
│
├── database/                # 数据库相关目录
│   ├── migrations/          # 数据库迁移文件
│   │   ├── 001_create_users.js
│   │   ├── 002_create_products.js
│   │   ├── 003_create_categories.js
│   │   ├── 004_create_orders.js
│   │   ├── 005_create_carts.js
│   │   ├── 006_create_addresses.js
│   │   ├── 007_create_banners.js
│   │   ├── 008_create_payments.js
│   │   ├── 009_create_settings.js
│   │   └── 010_create_admins.js
│   │
│   ├── seeds/               # 数据库种子文件
│   │   ├── users.js         # 用户数据
│   │   ├── products.js      # 商品数据
│   │   ├── categories.js    # 分类数据
│   │   ├── banners.js       # 轮播图数据
│   │   ├── settings.js      # 设置数据
│   │   └── admins.js        # 管理员数据
│   │
│   └── schemas/             # 数据库模式文件
│       ├── user.sql
│       ├── product.sql
│       ├── category.sql
│       ├── order.sql
│       ├── cart.sql
│       ├── address.sql
│       ├── banner.sql
│       ├── payment.sql
│       ├── setting.sql
│       └── admin.sql
│
├── logs/                    # 日志文件目录
│   ├── app.log              # 应用日志
│   ├── error.log            # 错误日志
│   ├── access.log           # 访问日志
│   └── payment.log          # 支付日志
│
├── uploads/                 # 上传文件目录
│   ├── products/            # 商品图片
│   ├── banners/             # 轮播图
│   ├── categories/          # 分类图片
│   ├── avatars/             # 用户头像
│   └── temp/                # 临时文件
│
├── tests/                   # 测试文件目录
│   ├── unit/                # 单元测试
│   │   ├── controllers/     # 控制器测试
│   │   ├── services/        # 服务测试
│   │   ├── models/          # 模型测试
│   │   └── utils/           # 工具测试
│   │
│   ├── integration/         # 集成测试
│   │   ├── api/             # API测试
│   │   ├── database/        # 数据库测试
│   │   └── auth/            # 认证测试
│   │
│   ├── fixtures/            # 测试数据
│   └── helpers/             # 测试辅助函数
│
├── docs/                    # 文档目录
│   ├── api.md               # API文档
│   ├── database.md          # 数据库文档
│   ├── deployment.md        # 部署文档
│   ├── development.md       # 开发文档
│   └── testing.md           # 测试文档
│
└── scripts/                 # 脚本文件目录
    ├── start.js             # 启动脚本
    ├── build.js             # 构建脚本
    ├── deploy.js            # 部署脚本
    ├── migrate.js           # 数据库迁移脚本
    ├── seed.js              # 数据库种子脚本
    └── backup.js            # 备份脚本

==========================================
文件说明
==========================================

1. 配置文件
   - package.json: 项目依赖和脚本配置
   - app.js: Koa应用主文件
   - config/: 各种配置文件
   - ecosystem.config.js: PM2进程管理配置

2. 中间件
   - auth.js: JWT认证中间件
   - cors.js: 跨域处理中间件
   - error.js: 统一错误处理中间件
   - logger.js: 日志记录中间件
   - rateLimit.js: 请求频率限制中间件

3. 路由
   - front/: 前端API路由
   - admin/: 管理后台API路由
   - 按业务模块组织

4. 控制器
   - 处理HTTP请求和响应
   - 参数验证和业务逻辑调用
   - 错误处理和响应格式化

5. 服务层
   - 业务逻辑处理
   - 数据库操作封装
   - 第三方服务集成

6. 数据模型
   - 数据库表结构定义
   - 数据验证规则
   - 关联关系定义

7. 工具函数
   - 数据库连接和操作
   - Redis缓存操作
   - JWT令牌处理
   - 文件上传处理

8. 参数验证
   - 请求参数验证规则
   - 数据格式验证
   - 业务规则验证

==========================================
开发规范
==========================================

1. 命名规范
   - 文件夹：小写字母，用连字符分隔
   - 文件：小写字母，用连字符分隔
   - 变量：camelCase命名
   - 常量：UPPER_SNAKE_CASE命名
   - 类：PascalCase命名

2. 代码组织
   - 按功能模块组织
   - 分层架构设计
   - 单一职责原则

3. 错误处理
   - 统一错误处理中间件
   - 详细的错误日志记录
   - 友好的错误响应格式

4. 安全规范
   - 输入参数验证
   - SQL注入防护
   - XSS攻击防护
   - CSRF防护
   - 权限控制

5. 性能优化
   - 数据库查询优化
   - Redis缓存使用
   - 连接池管理
   - 异步处理

6. 日志管理
   - 分级日志记录
   - 日志轮转
   - 错误日志告警

7. 测试规范
   - 单元测试覆盖
   - 集成测试
   - API测试
   - 性能测试

8. 部署规范
   - 环境配置分离
   - 进程管理
   - 健康检查
   - 监控告警 