微信小程序目录结构

==========================================
项目根目录：miniprogram/
==========================================

miniprogram/
├── app.js                    # 小程序入口文件
├── app.json                  # 小程序配置文件
├── app.wxss                  # 全局样式文件
├── project.config.json       # 项目配置文件
├── sitemap.json             # 站点地图配置
│
├── pages/                    # 页面目录
│   ├── index/               # 首页
│   │   ├── index.js
│   │   ├── index.json
│   │   ├── index.wxml
│   │   └── index.wxss
│   │
│   ├── category/            # 分类页面
│   │   ├── category.js
│   │   ├── category.json
│   │   ├── category.wxml
│   │   └── category.wxss
│   │
│   ├── product-list/        # 商品列表页
│   │   ├── product-list.js
│   │   ├── product-list.json
│   │   ├── product-list.wxml
│   │   └── product-list.wxss
│   │
│   ├── product-detail/      # 商品详情页
│   │   ├── product-detail.js
│   │   ├── product-detail.json
│   │   ├── product-detail.wxml
│   │   └── product-detail.wxss
│   │
│   ├── cart/               # 购物车页面
│   │   ├── cart.js
│   │   ├── cart.json
│   │   ├── cart.wxml
│   │   └── cart.wxss
│   │
│   ├── order-confirm/      # 订单确认页
│   │   ├── order-confirm.js
│   │   ├── order-confirm.json
│   │   ├── order-confirm.wxml
│   │   └── order-confirm.wxss
│   │
│   ├── order-list/         # 订单列表页
│   │   ├── order-list.js
│   │   ├── order-list.json
│   │   ├── order-list.wxml
│   │   └── order-list.wxss
│   │
│   ├── order-detail/       # 订单详情页
│   │   ├── order-detail.js
│   │   ├── order-detail.json
│   │   ├── order-detail.wxml
│   │   └── order-detail.wxss
│   │
│   ├── address-list/       # 收货地址列表页
│   │   ├── address-list.js
│   │   ├── address-list.json
│   │   ├── address-list.wxml
│   │   └── address-list.wxss
│   │
│   ├── address-edit/       # 收货地址编辑页
│   │   ├── address-edit.js
│   │   ├── address-edit.json
│   │   ├── address-edit.wxml
│   │   └── address-edit.wxss
│   │
│   ├── user/               # 用户中心页
│   │   ├── user.js
│   │   ├── user.json
│   │   ├── user.wxml
│   │   └── user.wxss
│   │
│   ├── login/              # 登录页面
│   │   ├── login.js
│   │   ├── login.json
│   │   ├── login.wxml
│   │   └── login.wxss
│   │
│   └── search/             # 搜索页面
│       ├── search.js
│       ├── search.json
│       ├── search.wxml
│       └── search.wxss
│
├── components/              # 自定义组件目录
│   ├── product-card/       # 商品卡片组件
│   │   ├── product-card.js
│   │   ├── product-card.json
│   │   ├── product-card.wxml
│   │   └── product-card.wxss
│   │
│   ├── cart-item/          # 购物车商品组件
│   │   ├── cart-item.js
│   │   ├── cart-item.json
│   │   ├── cart-item.wxml
│   │   └── cart-item.wxss
│   │
│   ├── order-item/         # 订单商品组件
│   │   ├── order-item.js
│   │   ├── order-item.json
│   │   ├── order-item.wxml
│   │   └── order-item.wxss
│   │
│   ├── address-item/       # 地址项组件
│   │   ├── address-item.js
│   │   ├── address-item.json
│   │   ├── address-item.wxml
│   │   └── address-item.wxss
│   │
│   ├── loading/            # 加载组件
│   │   ├── loading.js
│   │   ├── loading.json
│   │   ├── loading.wxml
│   │   └── loading.wxss
│   │
│   └── empty/              # 空状态组件
│       ├── empty.js
│       ├── empty.json
│       ├── empty.wxml
│       └── empty.wxss
│
├── utils/                   # 工具函数目录
│   ├── request.js          # 网络请求封装
│   ├── auth.js             # 认证相关工具
│   ├── storage.js          # 本地存储工具
│   ├── format.js           # 格式化工具
│   ├── validate.js         # 验证工具
│   └── constants.js        # 常量定义
│
├── services/               # 服务层目录
│   ├── api.js             # API接口定义
│   ├── user.js            # 用户相关服务
│   ├── product.js         # 商品相关服务
│   ├── cart.js            # 购物车相关服务
│   ├── order.js           # 订单相关服务
│   ├── address.js         # 地址相关服务
│   └── payment.js         # 支付相关服务
│
├── styles/                 # 样式文件目录
│   ├── common.wxss        # 公共样式
│   ├── variables.wxss     # 样式变量
│   └── mixins.wxss        # 样式混入
│
├── images/                 # 图片资源目录
│   ├── icons/             # 图标文件
│   ├── banners/           # 轮播图
│   └── common/            # 通用图片
│
└── config/                 # 配置文件目录
    ├── api.js             # API配置
    ├── app.js             # 应用配置
    └── env.js             # 环境配置

==========================================
文件说明
==========================================

1. 页面文件（.js/.json/.wxml/.wxss）
   - .js: 页面逻辑文件
   - .json: 页面配置文件
   - .wxml: 页面结构文件
   - .wxss: 页面样式文件

2. 组件文件
   - 可复用的UI组件
   - 每个组件包含完整的四个文件

3. 工具函数
   - request.js: 封装wx.request，统一处理请求
   - auth.js: 处理登录认证相关逻辑
   - storage.js: 封装本地存储操作
   - format.js: 数据格式化工具
   - validate.js: 数据验证工具

4. 服务层
   - 封装业务逻辑
   - 调用API接口
   - 数据处理和转换

5. 样式文件
   - 全局样式定义
   - 样式变量和混入
   - 响应式设计支持

==========================================
开发规范
==========================================

1. 命名规范
   - 文件夹：小写字母，用连字符分隔
   - 文件：小写字母，用连字符分隔
   - 变量：驼峰命名法
   - 常量：大写字母，用下划线分隔

2. 代码组织
   - 每个页面独立文件夹
   - 公共组件放在components目录
   - 工具函数放在utils目录
   - 业务逻辑放在services目录

3. 样式规范
   - 使用BEM命名规范
   - 样式文件模块化
   - 避免样式冲突

4. 性能优化
   - 图片压缩和懒加载
   - 代码分包加载
   - 减少不必要的网络请求 