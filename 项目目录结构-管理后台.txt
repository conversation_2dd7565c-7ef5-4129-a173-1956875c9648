管理后台目录结构

==========================================
项目根目录：admin/
==========================================

admin/
├── package.json              # 项目依赖配置
├── package-lock.json         # 依赖锁定文件
├── .env                      # 环境变量配置
├── .env.development          # 开发环境配置
├── .env.production           # 生产环境配置
├── .gitignore               # Git忽略文件
├── README.md                # 项目说明文档
├── .eslintrc.js             # ESLint配置
├── .prettierrc              # Prettier配置
│
├── public/                   # 静态资源目录
│   ├── index.html           # HTML模板
│   ├── favicon.ico          # 网站图标
│   ├── logo.png             # Logo图片
│   └── manifest.json        # PWA配置
│
├── src/                      # 源代码目录
│   ├── main.jsx             # 应用入口文件（React）
│   ├── App.jsx              # 根组件
│   ├── router/              # 路由配置目录（React Router）
│   │   ├── index.jsx        # 路由主文件
│   │   ├── modules/         # 路由模块
│   │   │   ├── dashboard.jsx # 仪表板路由
│   │   │   ├── product.jsx   # 商品管理路由
│   │   │   ├── order.jsx     # 订单管理路由
│   │   │   ├── user.jsx      # 用户管理路由
│   │   │   ├── system.jsx    # 系统管理路由
│   │   │   └── statistics.jsx # 统计分析路由
│   │   └── guards.jsx       # 路由守卫（如有）
│   │
│   ├── store/               # 状态管理目录（如 Redux 或 Zustand）
│   │   ├── index.js         # Store主文件
│   │   ├── modules/         # 状态模块
│   │   │   ├── user.js      # 用户状态
│   │   │   ├── app.js       # 应用状态
│   │   │   ├── permission.js # 权限状态
│   │   │   └── settings.js  # 设置状态
│   │   └── plugins/         # Store插件
│   │       └── persist.js   # 状态持久化
│   │
│   ├── views/               # 页面组件目录
│   │   ├── layout/          # 布局组件
│   │   │   ├── index.jsx    # 主布局
│   │   │   ├── components/  # 布局子组件
│   │   │   │   ├── Sidebar.jsx    # 侧边栏
│   │   │   │   ├── Navbar.jsx     # 顶部导航
│   │   │   │   ├── AppMain.jsx    # 主内容区
│   │   │   │   └── TagsView.jsx   # 标签页
│   │   │   └── mixin/       # 布局混入（如有）
│   │   │
│   │   ├── login/           # 登录页面
│   │   │   ├── index.jsx    # 登录主页面
│   │   │   └── components/  # 登录组件
│   │   │
│   │   ├── dashboard/       # 仪表板
│   │   │   ├── index.jsx    # 仪表板主页
│   │   │   └── components/  # 仪表板组件
│   │   │       ├── SalesChart.jsx    # 销售图表
│   │   │       ├── OrderStats.jsx    # 订单统计
│   │   │       ├── ProductRank.jsx   # 商品排行
│   │   │       └── UserGrowth.jsx    # 用户增长
│   │   │
│   │   ├── product/         # 商品管理
│   │   │   ├── index.jsx    # 商品列表页
│   │   │   ├── create.jsx   # 商品创建页
│   │   │   ├── edit.jsx     # 商品编辑页
│   │   │   └── components/  # 商品组件
│   │   │       ├── ProductForm.jsx   # 商品表单
│   │   │       ├── ImageUpload.jsx   # 图片上传
│   │   │       └── CategorySelect.jsx # 分类选择
│   │   │
│   │   ├── category/        # 分类管理
│   │   │   ├── index.jsx    # 分类列表页
│   │   │   ├── create.jsx   # 分类创建页
│   │   │   ├── edit.jsx     # 分类编辑页
│   │   │   └── components/  # 分类组件
│   │   │
│   │   ├── order/           # 订单管理
│   │   │   ├── index.jsx    # 订单列表页
│   │   │   ├── detail.jsx   # 订单详情页
│   │   │   └── components/  # 订单组件
│   │   │       ├── OrderTable.jsx    # 订单表格
│   │   │       ├── OrderDetail.jsx   # 订单详情
│   │   │       └── OrderStatus.jsx   # 订单状态
│   │   │
│   │   ├── user/            # 用户管理
│   │   │   ├── index.jsx    # 用户列表页
│   │   │   ├── detail.jsx   # 用户详情页
│   │   │   └── components/  # 用户组件
│   │   │
│   │   ├── banner/          # 轮播图管理
│   │   │   ├── index.jsx    # 轮播图列表页
│   │   │   ├── create.jsx   # 轮播图创建页
│   │   │   ├── edit.jsx     # 轮播图编辑页
│   │   │   └── components/  # 轮播图组件
│   │   │
│   │   ├── statistics/      # 统计分析
│   │   │   ├── index.jsx    # 统计主页
│   │   │   ├── sales.jsx    # 销售统计
│   │   │   ├── orders.jsx   # 订单统计
│   │   │   └── users.jsx    # 用户统计
│   │   │
│   │   ├── settings/        # 系统设置
│   │   │   ├── index.jsx    # 设置主页
│   │   │   ├── basic.jsx    # 基本设置
│   │   │   ├── payment.jsx  # 支付设置
│   │   │   ├── shipping.jsx # 物流设置
│   │   │   └── sms.jsx      # 短信设置
│   │   │
│   │   ├── profile/         # 个人中心
│   │   │   ├── index.jsx    # 个人信息页
│   │   │   └── password.jsx # 修改密码页
│   │   │
│   │   └── error/           # 错误页面
│   │       ├── 404.jsx      # 404页面
│   │       └── 403.jsx      # 403页面
│   │
│   ├── components/          # 公共组件目录
│   │   ├── common/          # 通用组件
│   │   │   ├── SearchForm.jsx       # 搜索表单
│   │   │   ├── DataTable.jsx        # 数据表格
│   │   │   ├── Pagination.jsx       # 分页组件
│   │   │   ├── Upload.jsx           # 上传组件
│   │   │   ├── RichText.jsx         # 富文本编辑器
│   │   │   ├── DatePicker.jsx       # 日期选择器
│   │   │   ├── Select.jsx           # 下拉选择器
│   │   │   └── Dialog.jsx           # 对话框组件
│   │   │
│   │   ├── charts/          # 图表组件
│   │   │   ├── LineChart.jsx        # 折线图
│   │   │   ├── BarChart.jsx         # 柱状图
│   │   │   ├── PieChart.jsx         # 饼图
│   │   │   └── RadarChart.jsx       # 雷达图
│   │   │
│   │   └── business/        # 业务组件
│   │       ├── ProductCard.jsx      # 商品卡片
│   │       ├── OrderCard.jsx        # 订单卡片
│   │       ├── UserCard.jsx         # 用户卡片
│   │       └── StatusTag.jsx        # 状态标签
│   │
│   ├── utils/               # 工具函数目录
│   │   ├── request.js       # HTTP请求封装（如 axios）
│   │   ├── auth.js          # 认证相关工具
│   │   ├── storage.js       # 本地存储工具
│   │   ├── validate.js      # 验证工具
│   │   ├── format.js        # 格式化工具
│   │   ├── download.js      # 下载工具
│   │   ├── permission.js    # 权限工具
│   │   └── constants.js     # 常量定义
│   │
│   ├── api/                 # API接口目录
│   │   ├── index.js         # API主文件
│   │   ├── user.js          # 用户相关API
│   │   ├── product.js       # 商品相关API
│   │   ├── order.js         # 订单相关API
│   │   ├── category.js      # 分类相关API
│   │   ├── banner.js        # 轮播图相关API
│   │   ├── statistics.js    # 统计相关API
│   │   ├── settings.js      # 设置相关API
│   │   └── upload.js        # 上传相关API
│   │
│   ├── styles/              # 样式文件目录
│   │   ├── index.scss       # 样式入口文件
│   │   ├── variables.scss   # 样式变量
│   │   ├── mixins.scss      # 样式混入
│   │   ├── common.scss      # 公共样式
│   │   ├── ant-design.scss  # Ant Design样式覆盖
│   │   ├── transition.scss  # 过渡动画样式
│   │   └── sidebar.scss     # 侧边栏样式
│   │
│   ├── icons/               # 图标文件目录
│   │   ├── svg/             # SVG图标
│   │   └── index.js         # 图标注册
│   │
│   ├── directives/          # 自定义指令目录（如有）
│   │   ├── permission.js    # 权限指令
│   │   ├── clipboard.js     # 复制指令
│   │   └── waves.js         # 波纹指令
│   │
│   ├── filters/             # 过滤器目录（如有）
│   │   ├── index.js         # 过滤器入口
│   │   ├── date.js          # 日期过滤器
│   │   ├── currency.js      # 货币过滤器
│   │   └── status.js        # 状态过滤器
│   │
│   └── mixins/              # 混入目录（如有）
│       ├── list.js          # 列表混入
│       ├── form.js          # 表单混入
│       └── chart.js         # 图表混入
│
├── tests/                   # 测试文件目录
│   ├── unit/                # 单元测试
│   ├── e2e/                 # 端到端测试
│   └── jest.config.js       # Jest配置
│
├── docs/                    # 文档目录
│   ├── api.md               # API文档
│   ├── deployment.md        # 部署文档
│   └── development.md       # 开发文档
│
└── scripts/                 # 脚本文件目录
    ├── build.js             # 构建脚本
    ├── deploy.js            # 部署脚本
    └── generate.js          # 代码生成脚本

==========================================
文件说明
==========================================

1. 配置文件
   - package.json: 项目依赖和脚本配置
   - .env: 环境变量配置
   - .eslintrc.js: 代码规范配置

2. 页面组件
   - 按功能模块组织
   - 每个页面包含主文件和子组件
   - 使用 React 组件（JSX）

3. 公共组件
   - common: 通用UI组件
   - charts: 图表组件
   - business: 业务组件

4. 工具函数
   - request.js: 封装 axios，统一处理请求
   - auth.js: 处理登录认证
   - storage.js: 本地存储操作
   - validate.js: 数据验证

5. API接口
   - 按业务模块组织
   - 统一错误处理
   - 请求拦截和响应拦截

6. 样式文件
   - 使用 SCSS 预处理器
   - 模块化样式组织
   - 响应式设计支持

==========================================
开发规范
==========================================

1. 命名规范
   - 文件夹：小写字母，用连字符分隔
   - 组件：PascalCase命名
   - 变量：camelCase命名
   - 常量：UPPER_SNAKE_CASE命名

2. 代码组织
   - 按功能模块组织文件
   - 组件化开发
   - 单一职责原则

3. 样式规范
   - 使用 BEM 命名规范
   - 样式模块化
   - 避免样式冲突

4. 性能优化
   - 路由懒加载
   - 组件按需加载
   - 图片懒加载
   - 代码分割

5. 安全规范
   - 输入验证
   - XSS 防护
   - CSRF 防护
   - 权限控制 